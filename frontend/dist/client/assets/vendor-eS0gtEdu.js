/**
* @vue/shared v3.5.20
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function cs(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const Y={},bt=[],He=()=>{},Pr=()=>!1,vn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),fs=e=>e.startsWith("onUpdate:"),fe=Object.assign,us=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Zi=Object.prototype.hasOwnProperty,W=(e,t)=>Zi.call(e,t),D=Array.isArra<PERSON>,vt=e=>Xt(e)==="[object Map]",xn=e=>Xt(e)==="[object Set]",Is=e=>Xt(e)==="[object Date]",L=e=>typeof e=="function",te=e=>typeof e=="string",Ce=e=>typeof e=="symbol",Z=e=>e!==null&&typeof e=="object",Ar=e=>(Z(e)||L(e))&&L(e.then)&&L(e.catch),Or=Object.prototype.toString,Xt=e=>Or.call(e),eo=e=>Xt(e).slice(8,-1),Tr=e=>Xt(e)==="[object Object]",as=e=>te(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,jt=cs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Sn=e=>{const t=Object.create(null);return(n=>t[n]||(t[n]=e(n)))},to=/-(\w)/g,we=Sn(e=>e.replace(to,(t,n)=>n?n.toUpperCase():"")),no=/\B([A-Z])/g,ht=Sn(e=>e.replace(no,"-$1").toLowerCase()),wn=Sn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Nn=Sn(e=>e?"on".concat(wn(e)):""),rt=(e,t)=>!Object.is(e,t),on=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Mr=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},an=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Fs;const En=()=>Fs||(Fs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function hs(e){if(D(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=te(s)?oo(s):hs(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(te(e)||Z(e))return e}const so=/;(?![^(]*\))/g,ro=/:([^]+)/,io=/\/\*[^]*?\*\//g;function oo(e){const t={};return e.replace(io,"").split(so).forEach(n=>{if(n){const s=n.split(ro);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function ds(e){let t="";if(te(e))t=e;else if(D(e))for(let n=0;n<e.length;n++){const s=ds(e[n]);s&&(t+=s+" ")}else if(Z(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const lo="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",co=cs(lo);function Ir(e){return!!e||e===""}function fo(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Rn(e[s],t[s]);return n}function Rn(e,t){if(e===t)return!0;let n=Is(e),s=Is(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Ce(e),s=Ce(t),n||s)return e===t;if(n=D(e),s=D(t),n||s)return n&&s?fo(e,t):!1;if(n=Z(e),s=Z(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const o in e){const l=e.hasOwnProperty(o),c=t.hasOwnProperty(o);if(l&&!c||!l&&c||!Rn(e[o],t[o]))return!1}}return String(e)===String(t)}function uo(e,t){return e.findIndex(n=>Rn(n,t))}const Fr=e=>!!(e&&e.__v_isRef===!0),ao=e=>te(e)?e:e==null?"":D(e)||Z(e)&&(e.toString===Or||!L(e.toString))?Fr(e)?ao(e.value):JSON.stringify(e,Nr,2):String(e),Nr=(e,t)=>Fr(t)?Nr(e,t.value):vt(t)?{["Map(".concat(t.size,")")]:[...t.entries()].reduce((n,[s,r],i)=>(n[$n(s,i)+" =>"]=r,n),{})}:xn(t)?{["Set(".concat(t.size,")")]:[...t.values()].map(n=>$n(n))}:Ce(t)?$n(t):Z(t)&&!D(t)&&!Tr(t)?String(t):t,$n=(e,t="")=>{var n;return Ce(e)?"Symbol(".concat((n=e.description)!=null?n:t,")"):e};/**
* @vue/reactivity v3.5.20
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ge;class $r{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ge,!t&&ge&&(this.index=(ge.scopes||(ge.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ge;try{return ge=this,t()}finally{ge=n}}}on(){++this._on===1&&(this.prevScope=ge,ge=this)}off(){this._on>0&&--this._on===0&&(ge=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ho(e){return new $r(e)}function po(){return ge}let X;const jn=new WeakSet;class jr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ge&&ge.active&&ge.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,jn.has(this)&&(jn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Hr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ns(this),Lr(this);const t=X,n=Re;X=this,Re=!0;try{return this.fn()}finally{Vr(this),X=t,Re=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ms(t);this.deps=this.depsTail=void 0,Ns(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?jn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Gn(this)&&this.run()}get dirty(){return Gn(this)}}let Dr=0,Dt,Ht;function Hr(e,t=!1){if(e.flags|=8,t){e.next=Ht,Ht=e;return}e.next=Dt,Dt=e}function ps(){Dr++}function gs(){if(--Dr>0)return;if(Ht){let t=Ht;for(Ht=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Dt;){let t=Dt;for(Dt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Lr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Vr(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),ms(s),go(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Gn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Kr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Kr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===kt)||(e.globalVersion=kt,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Gn(e))))return;e.flags|=2;const t=e.dep,n=X,s=Re;X=e,Re=!0;try{Lr(e);const r=e.fn(e._value);(t.version===0||rt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{X=n,Re=s,Vr(e),e.flags&=-3}}function ms(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)ms(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function go(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Re=!0;const Br=[];function Ge(){Br.push(Re),Re=!1}function ze(){const e=Br.pop();Re=e===void 0?!0:e}function Ns(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=X;X=void 0;try{t()}finally{X=n}}}let kt=0;class mo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class _s{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!X||!Re||X===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==X)n=this.activeLink=new mo(X,this),X.deps?(n.prevDep=X.depsTail,X.depsTail.nextDep=n,X.depsTail=n):X.deps=X.depsTail=n,Ur(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=X.depsTail,n.nextDep=void 0,X.depsTail.nextDep=n,X.depsTail=n,X.deps===n&&(X.deps=s)}return n}trigger(t){this.version++,kt++,this.notify(t)}notify(t){ps();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{gs()}}}function Ur(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Ur(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const zn=new WeakMap,at=Symbol(""),Qn=Symbol(""),Wt=Symbol("");function oe(e,t,n){if(Re&&X){let s=zn.get(e);s||zn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new _s),r.map=s,r.key=n),r.track()}}function ke(e,t,n,s,r,i){const o=zn.get(e);if(!o){kt++;return}const l=c=>{c&&c.trigger()};if(ps(),t==="clear")o.forEach(l);else{const c=D(e),h=c&&as(n);if(c&&n==="length"){const u=Number(s);o.forEach((d,g)=>{(g==="length"||g===Wt||!Ce(g)&&g>=u)&&l(d)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),h&&l(o.get(Wt)),t){case"add":c?h&&l(o.get("length")):(l(o.get(at)),vt(e)&&l(o.get(Qn)));break;case"delete":c||(l(o.get(at)),vt(e)&&l(o.get(Qn)));break;case"set":vt(e)&&l(o.get(at));break}}gs()}function mt(e){const t=k(e);return t===e?t:(oe(t,"iterate",Wt),Se(e)?t:t.map(re))}function Cn(e){return oe(e=k(e),"iterate",Wt),e}const _o={__proto__:null,[Symbol.iterator](){return Dn(this,Symbol.iterator,re)},concat(...e){return mt(this).concat(...e.map(t=>D(t)?mt(t):t))},entries(){return Dn(this,"entries",e=>(e[1]=re(e[1]),e))},every(e,t){return Ke(this,"every",e,t,void 0,arguments)},filter(e,t){return Ke(this,"filter",e,t,n=>n.map(re),arguments)},find(e,t){return Ke(this,"find",e,t,re,arguments)},findIndex(e,t){return Ke(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ke(this,"findLast",e,t,re,arguments)},findLastIndex(e,t){return Ke(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ke(this,"forEach",e,t,void 0,arguments)},includes(...e){return Hn(this,"includes",e)},indexOf(...e){return Hn(this,"indexOf",e)},join(e){return mt(this).join(e)},lastIndexOf(...e){return Hn(this,"lastIndexOf",e)},map(e,t){return Ke(this,"map",e,t,void 0,arguments)},pop(){return It(this,"pop")},push(...e){return It(this,"push",e)},reduce(e,...t){return $s(this,"reduce",e,t)},reduceRight(e,...t){return $s(this,"reduceRight",e,t)},shift(){return It(this,"shift")},some(e,t){return Ke(this,"some",e,t,void 0,arguments)},splice(...e){return It(this,"splice",e)},toReversed(){return mt(this).toReversed()},toSorted(e){return mt(this).toSorted(e)},toSpliced(...e){return mt(this).toSpliced(...e)},unshift(...e){return It(this,"unshift",e)},values(){return Dn(this,"values",re)}};function Dn(e,t,n){const s=Cn(e),r=s[t]();return s!==e&&!Se(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const yo=Array.prototype;function Ke(e,t,n,s,r,i){const o=Cn(e),l=o!==e&&!Se(e),c=o[t];if(c!==yo[t]){const d=c.apply(e,i);return l?re(d):d}let h=n;o!==e&&(l?h=function(d,g){return n.call(this,re(d),g,e)}:n.length>2&&(h=function(d,g){return n.call(this,d,g,e)}));const u=c.call(o,h,s);return l&&r?r(u):u}function $s(e,t,n,s){const r=Cn(e);let i=n;return r!==e&&(Se(e)?n.length>3&&(i=function(o,l,c){return n.call(this,o,l,c,e)}):i=function(o,l,c){return n.call(this,o,re(l),c,e)}),r[t](i,...s)}function Hn(e,t,n){const s=k(e);oe(s,"iterate",Wt);const r=s[t](...n);return(r===-1||r===!1)&&vs(n[0])?(n[0]=k(n[0]),s[t](...n)):r}function It(e,t,n=[]){Ge(),ps();const s=k(e)[t].apply(e,n);return gs(),ze(),s}const bo=cs("__proto__,__v_isRef,__isVue"),kr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ce));function vo(e){Ce(e)||(e=String(e));const t=k(this);return oe(t,"has",e),t.hasOwnProperty(e)}class Wr{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?To:Qr:i?zr:Gr).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=D(t);if(!r){let c;if(o&&(c=_o[n]))return c;if(n==="hasOwnProperty")return vo}const l=Reflect.get(t,n,ce(t)?t:s);return(Ce(n)?kr.has(n):bo(n))||(r||oe(t,"get",n),i)?l:ce(l)?o&&as(n)?l:l.value:Z(l)?r?Jr(l):Pn(l):l}}class qr extends Wr{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const c=it(i);if(!Se(s)&&!it(s)&&(i=k(i),s=k(s)),!D(t)&&ce(i)&&!ce(s))return c||(i.value=s),!0}const o=D(t)&&as(n)?Number(n)<t.length:W(t,n),l=Reflect.set(t,n,s,ce(t)?t:r);return t===k(r)&&(o?rt(s,i)&&ke(t,"set",n,s):ke(t,"add",n,s)),l}deleteProperty(t,n){const s=W(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&ke(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Ce(n)||!kr.has(n))&&oe(t,"has",n),s}ownKeys(t){return oe(t,"iterate",D(t)?"length":at),Reflect.ownKeys(t)}}class xo extends Wr{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const So=new qr,wo=new xo,Eo=new qr(!0);const Yn=e=>e,nn=e=>Reflect.getPrototypeOf(e);function Ro(e,t,n){return function(...s){const r=this.__v_raw,i=k(r),o=vt(i),l=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,h=r[e](...s),u=n?Yn:t?hn:re;return!t&&oe(i,"iterate",c?Qn:at),{next(){const{value:d,done:g}=h.next();return g?{value:d,done:g}:{value:l?[u(d[0]),u(d[1])]:u(d),done:g}},[Symbol.iterator](){return this}}}}function sn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Co(e,t){const n={get(r){const i=this.__v_raw,o=k(i),l=k(r);e||(rt(r,l)&&oe(o,"get",r),oe(o,"get",l));const{has:c}=nn(o),h=t?Yn:e?hn:re;if(c.call(o,r))return h(i.get(r));if(c.call(o,l))return h(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&oe(k(r),"iterate",at),r.size},has(r){const i=this.__v_raw,o=k(i),l=k(r);return e||(rt(r,l)&&oe(o,"has",r),oe(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,c=k(l),h=t?Yn:e?hn:re;return!e&&oe(c,"iterate",at),l.forEach((u,d)=>r.call(i,h(u),h(d),o))}};return fe(n,e?{add:sn("add"),set:sn("set"),delete:sn("delete"),clear:sn("clear")}:{add(r){!t&&!Se(r)&&!it(r)&&(r=k(r));const i=k(this);return nn(i).has.call(i,r)||(i.add(r),ke(i,"add",r,r)),this},set(r,i){!t&&!Se(i)&&!it(i)&&(i=k(i));const o=k(this),{has:l,get:c}=nn(o);let h=l.call(o,r);h||(r=k(r),h=l.call(o,r));const u=c.call(o,r);return o.set(r,i),h?rt(i,u)&&ke(o,"set",r,i):ke(o,"add",r,i),this},delete(r){const i=k(this),{has:o,get:l}=nn(i);let c=o.call(i,r);c||(r=k(r),c=o.call(i,r)),l&&l.call(i,r);const h=i.delete(r);return c&&ke(i,"delete",r,void 0),h},clear(){const r=k(this),i=r.size!==0,o=r.clear();return i&&ke(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Ro(r,e,t)}),n}function ys(e,t){const n=Co(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(W(n,r)&&r in s?n:s,r,i)}const Po={get:ys(!1,!1)},Ao={get:ys(!1,!0)},Oo={get:ys(!0,!1)};const Gr=new WeakMap,zr=new WeakMap,Qr=new WeakMap,To=new WeakMap;function Mo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Io(e){return e.__v_skip||!Object.isExtensible(e)?0:Mo(eo(e))}function Pn(e){return it(e)?e:bs(e,!1,So,Po,Gr)}function Yr(e){return bs(e,!1,Eo,Ao,zr)}function Jr(e){return bs(e,!0,wo,Oo,Qr)}function bs(e,t,n,s,r){if(!Z(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Io(e);if(i===0)return e;const o=r.get(e);if(o)return o;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function xt(e){return it(e)?xt(e.__v_raw):!!(e&&e.__v_isReactive)}function it(e){return!!(e&&e.__v_isReadonly)}function Se(e){return!!(e&&e.__v_isShallow)}function vs(e){return e?!!e.__v_raw:!1}function k(e){const t=e&&e.__v_raw;return t?k(t):e}function Xr(e){return!W(e,"__v_skip")&&Object.isExtensible(e)&&Mr(e,"__v_skip",!0),e}const re=e=>Z(e)?Pn(e):e,hn=e=>Z(e)?Jr(e):e;function ce(e){return e?e.__v_isRef===!0:!1}function Zr(e){return ei(e,!1)}function Fo(e){return ei(e,!0)}function ei(e,t){return ce(e)?e:new No(e,t)}class No{constructor(t,n){this.dep=new _s,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:k(t),this._value=n?t:re(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Se(t)||it(t);t=s?t:k(t),rt(t,n)&&(this._rawValue=t,this._value=s?t:re(t),this.dep.trigger())}}function St(e){return ce(e)?e.value:e}const $o={get:(e,t,n)=>t==="__v_raw"?e:St(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ce(r)&&!ce(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function ti(e){return xt(e)?e:new Proxy(e,$o)}class jo{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new _s(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=kt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&X!==this)return Hr(this,!0),!0}get value(){const t=this.dep.track();return Kr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Do(e,t,n=!1){let s,r;return L(e)?s=e:(s=e.get,r=e.set),new jo(s,r,n)}const rn={},dn=new WeakMap;let ft;function Ho(e,t=!1,n=ft){if(n){let s=dn.get(n);s||dn.set(n,s=[]),s.push(e)}}function Lo(e,t,n=Y){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:c}=n,h=M=>r?M:Se(M)||r===!1||r===0?We(M,1):We(M);let u,d,g,m,P=!1,O=!1;if(ce(e)?(d=()=>e.value,P=Se(e)):xt(e)?(d=()=>h(e),P=!0):D(e)?(O=!0,P=e.some(M=>xt(M)||Se(M)),d=()=>e.map(M=>{if(ce(M))return M.value;if(xt(M))return h(M);if(L(M))return c?c(M,2):M()})):L(e)?t?d=c?()=>c(e,2):e:d=()=>{if(g){Ge();try{g()}finally{ze()}}const M=ft;ft=u;try{return c?c(e,3,[m]):e(m)}finally{ft=M}}:d=He,t&&r){const M=d,z=r===!0?1/0:r;d=()=>We(M(),z)}const V=po(),N=()=>{u.stop(),V&&V.active&&us(V.effects,u)};if(i&&t){const M=t;t=(...z)=>{M(...z),N()}}let T=O?new Array(e.length).fill(rn):rn;const $=M=>{if(!(!(u.flags&1)||!u.dirty&&!M))if(t){const z=u.run();if(r||P||(O?z.some((se,ee)=>rt(se,T[ee])):rt(z,T))){g&&g();const se=ft;ft=u;try{const ee=[z,T===rn?void 0:O&&T[0]===rn?[]:T,m];T=z,c?c(t,3,ee):t(...ee)}finally{ft=se}}}else u.run()};return l&&l($),u=new jr(d),u.scheduler=o?()=>o($,!1):$,m=M=>Ho(M,!1,u),g=u.onStop=()=>{const M=dn.get(u);if(M){if(c)c(M,4);else for(const z of M)z();dn.delete(u)}},t?s?$(!0):T=u.run():o?o($.bind(null,!0),!0):u.run(),N.pause=u.pause.bind(u),N.resume=u.resume.bind(u),N.stop=N,N}function We(e,t=1/0,n){if(t<=0||!Z(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ce(e))We(e.value,t,n);else if(D(e))for(let s=0;s<e.length;s++)We(e[s],t,n);else if(xn(e)||vt(e))e.forEach(s=>{We(s,t,n)});else if(Tr(e)){for(const s in e)We(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&We(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.20
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Zt(e,t,n,s){try{return s?e(...s):e()}catch(r){An(r,t,n)}}function Le(e,t,n,s){if(L(e)){const r=Zt(e,t,n,s);return r&&Ar(r)&&r.catch(i=>{An(i,t,n)}),r}if(D(e)){const r=[];for(let i=0;i<e.length;i++)r.push(Le(e[i],t,n,s));return r}}function An(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||Y;if(t){let l=t.parent;const c=t.proxy,h="https://vuejs.org/error-reference/#runtime-".concat(n);for(;l;){const u=l.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,c,h)===!1)return}l=l.parent}if(i){Ge(),Zt(i,null,10,[e,c,h]),ze();return}}Vo(e,n,r,s,o)}function Vo(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const ae=[];let je=-1;const wt=[];let tt=null,_t=0;const ni=Promise.resolve();let pn=null;function xs(e){const t=pn||ni;return e?t.then(this?e.bind(this):e):t}function Ko(e){let t=je+1,n=ae.length;for(;t<n;){const s=t+n>>>1,r=ae[s],i=qt(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function Ss(e){if(!(e.flags&1)){const t=qt(e),n=ae[ae.length-1];!n||!(e.flags&2)&&t>=qt(n)?ae.push(e):ae.splice(Ko(t),0,e),e.flags|=1,si()}}function si(){pn||(pn=ni.then(ii))}function Bo(e){D(e)?wt.push(...e):tt&&e.id===-1?tt.splice(_t+1,0,e):e.flags&1||(wt.push(e),e.flags|=1),si()}function js(e,t,n=je+1){for(;n<ae.length;n++){const s=ae[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;ae.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ri(e){if(wt.length){const t=[...new Set(wt)].sort((n,s)=>qt(n)-qt(s));if(wt.length=0,tt){tt.push(...t);return}for(tt=t,_t=0;_t<tt.length;_t++){const n=tt[_t];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}tt=null,_t=0}}const qt=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ii(e){try{for(je=0;je<ae.length;je++){const t=ae[je];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Zt(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;je<ae.length;je++){const t=ae[je];t&&(t.flags&=-2)}je=-1,ae.length=0,ri(),pn=null,(ae.length||wt.length)&&ii()}}let ie=null,oi=null;function gn(e){const t=ie;return ie=e,oi=e&&e.type.__scopeId||null,t}function Uo(e,t=ie,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&qs(-1);const i=gn(t);let o;try{o=e(...r)}finally{gn(i),s._d&&qs(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function Ef(e,t){if(ie===null)return e;const n=In(ie),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,o,l,c=Y]=t[r];i&&(L(i)&&(i={mounted:i,updated:i}),i.deep&&We(o),s.push({dir:i,instance:n,value:o,oldValue:void 0,arg:l,modifiers:c}))}return e}function lt(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let c=l.dir[s];c&&(Ge(),Le(c,n,8,[e.el,l,e,t]),ze())}}const ko=Symbol("_vte"),Wo=e=>e.__isTeleport,qo=Symbol("_leaveCb");function ws(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ws(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function li(e,t){return L(e)?fe({name:e.name},t,{setup:e}):e}function ci(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Lt(e,t,n,s,r=!1){if(D(e)){e.forEach((P,O)=>Lt(P,t&&(D(t)?t[O]:t),n,s,r));return}if(Et(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&Lt(e,t,n,s.component.subTree);return}const i=s.shapeFlag&4?In(s.component):s.el,o=r?null:i,{i:l,r:c}=e,h=t&&t.r,u=l.refs===Y?l.refs={}:l.refs,d=l.setupState,g=k(d),m=d===Y?Pr:P=>W(g,P);if(h!=null&&h!==c){if(te(h))u[h]=null,m(h)&&(d[h]=null);else if(ce(h)){h.value=null;const P=t;P.k&&(u[P.k]=null)}}if(L(c))Zt(c,l,12,[o,u]);else{const P=te(c),O=ce(c);if(P||O){const V=()=>{if(e.f){const N=P?m(c)?d[c]:u[c]:c.value;if(r)D(N)&&us(N,i);else if(D(N))N.includes(i)||N.push(i);else if(P)u[c]=[i],m(c)&&(d[c]=u[c]);else{const T=[i];c.value=T,e.k&&(u[e.k]=T)}}else P?(u[c]=o,m(c)&&(d[c]=o)):O&&(c.value=o,e.k&&(u[e.k]=o))};o?(V.id=-1,ye(V,n)):V()}}}En().requestIdleCallback;En().cancelIdleCallback;const Et=e=>!!e.type.__asyncLoader,fi=e=>e.type.__isKeepAlive;function Go(e,t){ui(e,"a",t)}function zo(e,t){ui(e,"da",t)}function ui(e,t,n=le){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(On(t,s,n),n){let r=n.parent;for(;r&&r.parent;)fi(r.parent.vnode)&&Qo(s,t,n,r),r=r.parent}}function Qo(e,t,n,s){const r=On(t,e,s,!0);ai(()=>{us(s[t],r)},n)}function On(e,t,n=le,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Ge();const l=en(n),c=Le(t,n,e,o);return l(),ze(),c});return s?r.unshift(i):r.push(i),i}}const Ye=e=>(t,n=le)=>{(!Qt||e==="sp")&&On(e,(...s)=>t(...s),n)},Yo=Ye("bm"),Jo=Ye("m"),Xo=Ye("bu"),Zo=Ye("u"),el=Ye("bum"),ai=Ye("um"),tl=Ye("sp"),nl=Ye("rtg"),sl=Ye("rtc");function rl(e,t=le){On("ec",e,t)}const hi="components";function Rf(e,t){return pi(hi,e,!0,t)||e}const di=Symbol.for("v-ndc");function Cf(e){return te(e)?pi(hi,e,!1)||e:e||di}function pi(e,t,n=!0,s=!1){const r=ie||le;if(r){const i=r.type;{const l=ql(i,!1);if(l&&(l===t||l===we(t)||l===wn(we(t))))return i}const o=Ds(r[e]||i[e],t)||Ds(r.appContext[e],t);return!o&&s?i:o}}function Ds(e,t){return e&&(e[t]||e[we(t)]||e[wn(we(t))])}function Pf(e,t,n,s){let r;const i=n,o=D(e);if(o||te(e)){const l=o&&xt(e);let c=!1,h=!1;l&&(c=!Se(e),h=it(e),e=Cn(e)),r=new Array(e.length);for(let u=0,d=e.length;u<d;u++)r[u]=t(c?h?hn(re(e[u])):re(e[u]):e[u],u,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(Z(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,h=l.length;c<h;c++){const u=l[c];r[c]=t(e[u],u,c,i)}}else r=[];return r}function Af(e,t,n={},s,r){if(ie.ce||ie.parent&&Et(ie.parent)&&ie.parent.ce)return ts(),ns(xe,null,[me("slot",n,s)],64);let i=e[t];i&&i._c&&(i._d=!1),ts();const o=i&&gi(i(n)),l=n.key||o&&o.key,c=ns(xe,{key:(l&&!Ce(l)?l:"_".concat(t))+(!o&&s?"_fb":"")},o||[],o&&e._===1?64:-2);return i&&i._c&&(i._d=!0),c}function gi(e){return e.some(t=>zt(t)?!(t.type===Qe||t.type===xe&&!gi(t.children)):!0)?e:null}const Jn=e=>e?ji(e)?In(e):Jn(e.parent):null,Vt=fe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Jn(e.parent),$root:e=>Jn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>_i(e),$forceUpdate:e=>e.f||(e.f=()=>{Ss(e.update)}),$nextTick:e=>e.n||(e.n=xs.bind(e.proxy)),$watch:e=>Rl.bind(e)}),Ln=(e,t)=>e!==Y&&!e.__isScriptSetup&&W(e,t),il={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:c}=e;let h;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Ln(s,t))return o[t]=1,s[t];if(r!==Y&&W(r,t))return o[t]=2,r[t];if((h=e.propsOptions[0])&&W(h,t))return o[t]=3,i[t];if(n!==Y&&W(n,t))return o[t]=4,n[t];Xn&&(o[t]=0)}}const u=Vt[t];let d,g;if(u)return t==="$attrs"&&oe(e.attrs,"get",""),u(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==Y&&W(n,t))return o[t]=4,n[t];if(g=c.config.globalProperties,W(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Ln(r,t)?(r[t]=n,!0):s!==Y&&W(s,t)?(s[t]=n,!0):W(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i,type:o}},l){let c,h;return!!(n[l]||e!==Y&&l[0]!=="$"&&W(e,l)||Ln(t,l)||(c=i[0])&&W(c,l)||W(s,l)||W(Vt,l)||W(r.config.globalProperties,l)||(h=o.__cssModules)&&h[l])},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:W(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Hs(e){return D(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Xn=!0;function ol(e){const t=_i(e),n=e.proxy,s=e.ctx;Xn=!1,t.beforeCreate&&Ls(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:c,inject:h,created:u,beforeMount:d,mounted:g,beforeUpdate:m,updated:P,activated:O,deactivated:V,beforeDestroy:N,beforeUnmount:T,destroyed:$,unmounted:M,render:z,renderTracked:se,renderTriggered:ee,errorCaptured:Ae,serverPrefetch:Je,expose:Oe,inheritAttrs:Xe,components:ot,directives:Te,filters:Tt}=t;if(h&&ll(h,s,null),o)for(const G in o){const B=o[G];L(B)&&(s[G]=B.bind(n))}if(r){const G=r.call(n,n);Z(G)&&(e.data=Pn(G))}if(Xn=!0,i)for(const G in i){const B=i[G],Ve=L(B)?B.bind(n,n):L(B.get)?B.get.bind(n,n):He,Ze=!L(B)&&L(B.set)?B.set.bind(n):He,Me=Ee({get:Ve,set:Ze});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>Me.value,set:he=>Me.value=he})}if(l)for(const G in l)mi(l[G],s,n,G);if(c){const G=L(c)?c.call(n):c;Reflect.ownKeys(G).forEach(B=>{ln(B,G[B])})}u&&Ls(u,e,"c");function ne(G,B){D(B)?B.forEach(Ve=>G(Ve.bind(n))):B&&G(B.bind(n))}if(ne(Yo,d),ne(Jo,g),ne(Xo,m),ne(Zo,P),ne(Go,O),ne(zo,V),ne(rl,Ae),ne(sl,se),ne(nl,ee),ne(el,T),ne(ai,M),ne(tl,Je),D(Oe))if(Oe.length){const G=e.exposed||(e.exposed={});Oe.forEach(B=>{Object.defineProperty(G,B,{get:()=>n[B],set:Ve=>n[B]=Ve,enumerable:!0})})}else e.exposed||(e.exposed={});z&&e.render===He&&(e.render=z),Xe!=null&&(e.inheritAttrs=Xe),ot&&(e.components=ot),Te&&(e.directives=Te),Je&&ci(e)}function ll(e,t,n=He){D(e)&&(e=Zn(e));for(const s in e){const r=e[s];let i;Z(r)?"default"in r?i=qe(r.from||s,r.default,!0):i=qe(r.from||s):i=qe(r),ce(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function Ls(e,t,n){Le(D(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function mi(e,t,n,s){let r=s.includes(".")?Ti(n,s):()=>n[s];if(te(e)){const i=t[e];L(i)&&cn(r,i)}else if(L(e))cn(r,e.bind(n));else if(Z(e))if(D(e))e.forEach(i=>mi(i,t,n,s));else{const i=L(e.handler)?e.handler.bind(n):t[e.handler];L(i)&&cn(r,i,e)}}function _i(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(h=>mn(c,h,o,!0)),mn(c,t,o)),Z(t)&&i.set(t,c),c}function mn(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&mn(e,i,n,!0),r&&r.forEach(o=>mn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=cl[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const cl={data:Vs,props:Ks,emits:Ks,methods:$t,computed:$t,beforeCreate:ue,created:ue,beforeMount:ue,mounted:ue,beforeUpdate:ue,updated:ue,beforeDestroy:ue,beforeUnmount:ue,destroyed:ue,unmounted:ue,activated:ue,deactivated:ue,errorCaptured:ue,serverPrefetch:ue,components:$t,directives:$t,watch:ul,provide:Vs,inject:fl};function Vs(e,t){return t?e?function(){return fe(L(e)?e.call(this,this):e,L(t)?t.call(this,this):t)}:t:e}function fl(e,t){return $t(Zn(e),Zn(t))}function Zn(e){if(D(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ue(e,t){return e?[...new Set([].concat(e,t))]:t}function $t(e,t){return e?fe(Object.create(null),e,t):t}function Ks(e,t){return e?D(e)&&D(t)?[...new Set([...e,...t])]:fe(Object.create(null),Hs(e),Hs(t!=null?t:{})):t}function ul(e,t){if(!e)return t;if(!t)return e;const n=fe(Object.create(null),e);for(const s in t)n[s]=ue(e[s],t[s]);return n}function yi(){return{app:null,config:{isNativeTag:Pr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let al=0;function hl(e,t){return function(s,r=null){L(s)||(s=fe({},s)),r!=null&&!Z(r)&&(r=null);const i=yi(),o=new WeakSet,l=[];let c=!1;const h=i.app={_uid:al++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:zl,get config(){return i.config},set config(u){},use(u,...d){return o.has(u)||(u&&L(u.install)?(o.add(u),u.install(h,...d)):L(u)&&(o.add(u),u(h,...d))),h},mixin(u){return i.mixins.includes(u)||i.mixins.push(u),h},component(u,d){return d?(i.components[u]=d,h):i.components[u]},directive(u,d){return d?(i.directives[u]=d,h):i.directives[u]},mount(u,d,g){if(!c){const m=h._ceVNode||me(s,r);return m.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),e(m,u,g),c=!0,h._container=u,u.__vue_app__=h,In(m.component)}},onUnmount(u){l.push(u)},unmount(){c&&(Le(l,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(u,d){return i.provides[u]=d,h},runWithContext(u){const d=Rt;Rt=h;try{return u()}finally{Rt=d}}};return h}}let Rt=null;function ln(e,t){if(le){let n=le.provides;const s=le.parent&&le.parent.provides;s===n&&(n=le.provides=Object.create(s)),n[e]=t}}function qe(e,t,n=!1){const s=Kl();if(s||Rt){let r=Rt?Rt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&L(t)?t.call(s&&s.proxy):t}}const bi={},vi=()=>Object.create(bi),xi=e=>Object.getPrototypeOf(e)===bi;function dl(e,t,n,s=!1){const r={},i=vi();e.propsDefaults=Object.create(null),Si(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Yr(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function pl(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=k(r),[c]=e.propsOptions;let h=!1;if((s||o>0)&&!(o&16)){if(o&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let g=u[d];if(Tn(e.emitsOptions,g))continue;const m=t[g];if(c)if(W(i,g))m!==i[g]&&(i[g]=m,h=!0);else{const P=we(g);r[P]=es(c,l,P,m,e,!1)}else m!==i[g]&&(i[g]=m,h=!0)}}}else{Si(e,t,r,i)&&(h=!0);let u;for(const d in l)(!t||!W(t,d)&&((u=ht(d))===d||!W(t,u)))&&(c?n&&(n[d]!==void 0||n[u]!==void 0)&&(r[d]=es(c,l,d,void 0,e,!0)):delete r[d]);if(i!==l)for(const d in i)(!t||!W(t,d))&&(delete i[d],h=!0)}h&&ke(e.attrs,"set","")}function Si(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let c in t){if(jt(c))continue;const h=t[c];let u;r&&W(r,u=we(c))?!i||!i.includes(u)?n[u]=h:(l||(l={}))[u]=h:Tn(e.emitsOptions,c)||(!(c in s)||h!==s[c])&&(s[c]=h,o=!0)}if(i){const c=k(n),h=l||Y;for(let u=0;u<i.length;u++){const d=i[u];n[d]=es(r,c,d,h[d],e,!W(h,d))}}return o}function es(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=W(o,"default");if(l&&s===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&L(c)){const{propsDefaults:h}=r;if(n in h)s=h[n];else{const u=en(r);s=h[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===ht(n))&&(s=!0))}return s}const gl=new WeakMap;function wi(e,t,n=!1){const s=n?gl:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let c=!1;if(!L(e)){const u=d=>{c=!0;const[g,m]=wi(d,t,!0);fe(o,g),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!c)return Z(e)&&s.set(e,bt),bt;if(D(i))for(let u=0;u<i.length;u++){const d=we(i[u]);Bs(d)&&(o[d]=Y)}else if(i)for(const u in i){const d=we(u);if(Bs(d)){const g=i[u],m=o[d]=D(g)||L(g)?{type:g}:fe({},g),P=m.type;let O=!1,V=!0;if(D(P))for(let N=0;N<P.length;++N){const T=P[N],$=L(T)&&T.name;if($==="Boolean"){O=!0;break}else $==="String"&&(V=!1)}else O=L(P)&&P.name==="Boolean";m[0]=O,m[1]=V,(O||W(m,"default"))&&l.push(d)}}const h=[o,l];return Z(e)&&s.set(e,h),h}function Bs(e){return e[0]!=="$"&&!jt(e)}const Es=e=>e==="_"||e==="_ctx"||e==="$stable",Rs=e=>D(e)?e.map(De):[De(e)],ml=(e,t,n)=>{if(t._n)return t;const s=Uo((...r)=>Rs(t(...r)),n);return s._c=!1,s},Ei=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Es(r))continue;const i=e[r];if(L(i))t[r]=ml(r,i,s);else if(i!=null){const o=Rs(i);t[r]=()=>o}}},Ri=(e,t)=>{const n=Rs(t);e.slots.default=()=>n},Ci=(e,t,n)=>{for(const s in t)(n||!Es(s))&&(e[s]=t[s])},_l=(e,t,n)=>{const s=e.slots=vi();if(e.vnode.shapeFlag&32){const r=t._;r?(Ci(s,t,n),n&&Mr(s,"_",r,!0)):Ei(t,s)}else t&&Ri(e,t)},yl=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=Y;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Ci(r,t,n):(i=!t.$stable,Ei(t,r)),o=t}else t&&(Ri(e,t),o={default:1});if(i)for(const l in r)!Es(l)&&o[l]==null&&delete r[l]},ye=Il;function bl(e){return vl(e)}function vl(e,t){const n=En();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:c,setText:h,setElementText:u,parentNode:d,nextSibling:g,setScopeId:m=He,insertStaticContent:P}=e,O=(f,a,p,y=null,v=null,_=null,E=void 0,w=null,S=!!a.dynamicChildren)=>{if(f===a)return;f&&!Ft(f,a)&&(y=b(f),he(f,v,_,!0),f=null),a.patchFlag===-2&&(S=!1,a.dynamicChildren=null);const{type:x,ref:j,shapeFlag:C}=a;switch(x){case Mn:V(f,a,p,y);break;case Qe:N(f,a,p,y);break;case Kn:f==null&&T(a,p,y,E);break;case xe:ot(f,a,p,y,v,_,E,w,S);break;default:C&1?z(f,a,p,y,v,_,E,w,S):C&6?Te(f,a,p,y,v,_,E,w,S):(C&64||C&128)&&x.process(f,a,p,y,v,_,E,w,S,I)}j!=null&&v?Lt(j,f&&f.ref,_,a||f,!a):j==null&&f&&f.ref!=null&&Lt(f.ref,null,_,f,!0)},V=(f,a,p,y)=>{if(f==null)s(a.el=l(a.children),p,y);else{const v=a.el=f.el;a.children!==f.children&&h(v,a.children)}},N=(f,a,p,y)=>{f==null?s(a.el=c(a.children||""),p,y):a.el=f.el},T=(f,a,p,y)=>{[f.el,f.anchor]=P(f.children,a,p,y,f.el,f.anchor)},$=({el:f,anchor:a},p,y)=>{let v;for(;f&&f!==a;)v=g(f),s(f,p,y),f=v;s(a,p,y)},M=({el:f,anchor:a})=>{let p;for(;f&&f!==a;)p=g(f),r(f),f=p;r(a)},z=(f,a,p,y,v,_,E,w,S)=>{a.type==="svg"?E="svg":a.type==="math"&&(E="mathml"),f==null?se(a,p,y,v,_,E,w,S):Je(f,a,v,_,E,w,S)},se=(f,a,p,y,v,_,E,w)=>{let S,x;const{props:j,shapeFlag:C,transition:F,dirs:H}=f;if(S=f.el=o(f.type,_,j&&j.is,j),C&8?u(S,f.children):C&16&&Ae(f.children,S,null,y,v,Vn(f,_),E,w),H&&lt(f,null,y,"created"),ee(S,f,f.scopeId,E,y),j){for(const J in j)J!=="value"&&!jt(J)&&i(S,J,null,j[J],_,y);"value"in j&&i(S,"value",null,j.value,_),(x=j.onVnodeBeforeMount)&&$e(x,y,f)}H&&lt(f,null,y,"beforeMount");const K=xl(v,F);K&&F.beforeEnter(S),s(S,a,p),((x=j&&j.onVnodeMounted)||K||H)&&ye(()=>{x&&$e(x,y,f),K&&F.enter(S),H&&lt(f,null,y,"mounted")},v)},ee=(f,a,p,y,v)=>{if(p&&m(f,p),y)for(let _=0;_<y.length;_++)m(f,y[_]);if(v){let _=v.subTree;if(a===_||Ii(_.type)&&(_.ssContent===a||_.ssFallback===a)){const E=v.vnode;ee(f,E,E.scopeId,E.slotScopeIds,v.parent)}}},Ae=(f,a,p,y,v,_,E,w,S=0)=>{for(let x=S;x<f.length;x++){const j=f[x]=w?nt(f[x]):De(f[x]);O(null,j,a,p,y,v,_,E,w)}},Je=(f,a,p,y,v,_,E)=>{const w=a.el=f.el;let{patchFlag:S,dynamicChildren:x,dirs:j}=a;S|=f.patchFlag&16;const C=f.props||Y,F=a.props||Y;let H;if(p&&ct(p,!1),(H=F.onVnodeBeforeUpdate)&&$e(H,p,a,f),j&&lt(a,f,p,"beforeUpdate"),p&&ct(p,!0),(C.innerHTML&&F.innerHTML==null||C.textContent&&F.textContent==null)&&u(w,""),x?Oe(f.dynamicChildren,x,w,p,y,Vn(a,v),_):E||B(f,a,w,null,p,y,Vn(a,v),_,!1),S>0){if(S&16)Xe(w,C,F,p,v);else if(S&2&&C.class!==F.class&&i(w,"class",null,F.class,v),S&4&&i(w,"style",C.style,F.style,v),S&8){const K=a.dynamicProps;for(let J=0;J<K.length;J++){const q=K[J],de=C[q],pe=F[q];(pe!==de||q==="value")&&i(w,q,de,pe,v,p)}}S&1&&f.children!==a.children&&u(w,a.children)}else!E&&x==null&&Xe(w,C,F,p,v);((H=F.onVnodeUpdated)||j)&&ye(()=>{H&&$e(H,p,a,f),j&&lt(a,f,p,"updated")},y)},Oe=(f,a,p,y,v,_,E)=>{for(let w=0;w<a.length;w++){const S=f[w],x=a[w],j=S.el&&(S.type===xe||!Ft(S,x)||S.shapeFlag&198)?d(S.el):p;O(S,x,j,null,y,v,_,E,!0)}},Xe=(f,a,p,y,v)=>{if(a!==p){if(a!==Y)for(const _ in a)!jt(_)&&!(_ in p)&&i(f,_,a[_],null,v,y);for(const _ in p){if(jt(_))continue;const E=p[_],w=a[_];E!==w&&_!=="value"&&i(f,_,w,E,v,y)}"value"in p&&i(f,"value",a.value,p.value,v)}},ot=(f,a,p,y,v,_,E,w,S)=>{const x=a.el=f?f.el:l(""),j=a.anchor=f?f.anchor:l("");let{patchFlag:C,dynamicChildren:F,slotScopeIds:H}=a;H&&(w=w?w.concat(H):H),f==null?(s(x,p,y),s(j,p,y),Ae(a.children||[],p,j,v,_,E,w,S)):C>0&&C&64&&F&&f.dynamicChildren?(Oe(f.dynamicChildren,F,p,v,_,E,w),(a.key!=null||v&&a===v.subTree)&&Pi(f,a,!0)):B(f,a,p,j,v,_,E,w,S)},Te=(f,a,p,y,v,_,E,w,S)=>{a.slotScopeIds=w,f==null?a.shapeFlag&512?v.ctx.activate(a,p,y,E,S):Tt(a,p,y,v,_,E,S):dt(f,a,S)},Tt=(f,a,p,y,v,_,E)=>{const w=f.component=Vl(f,y,v);if(fi(f)&&(w.ctx.renderer=I),Bl(w,!1,E),w.asyncDep){if(v&&v.registerDep(w,ne,E),!f.el){const S=w.subTree=me(Qe);N(null,S,a,p),f.placeholder=S.el}}else ne(w,f,a,p,v,_,E)},dt=(f,a,p)=>{const y=a.component=f.component;if(Tl(f,a,p))if(y.asyncDep&&!y.asyncResolved){G(y,a,p);return}else y.next=a,y.update();else a.el=f.el,y.vnode=a},ne=(f,a,p,y,v,_,E)=>{const w=()=>{if(f.isMounted){let{next:C,bu:F,u:H,parent:K,vnode:J}=f;{const Fe=Ai(f);if(Fe){C&&(C.el=J.el,G(f,C,E)),Fe.asyncDep.then(()=>{f.isUnmounted||w()});return}}let q=C,de;ct(f,!1),C?(C.el=J.el,G(f,C,E)):C=J,F&&on(F),(de=C.props&&C.props.onVnodeBeforeUpdate)&&$e(de,K,C,J),ct(f,!0);const pe=ks(f),Ie=f.subTree;f.subTree=pe,O(Ie,pe,d(Ie.el),b(Ie),f,v,_),C.el=pe.el,q===null&&Ml(f,pe.el),H&&ye(H,v),(de=C.props&&C.props.onVnodeUpdated)&&ye(()=>$e(de,K,C,J),v)}else{let C;const{el:F,props:H}=a,{bm:K,m:J,parent:q,root:de,type:pe}=f,Ie=Et(a);ct(f,!1),K&&on(K),!Ie&&(C=H&&H.onVnodeBeforeMount)&&$e(C,q,a),ct(f,!0);{de.ce&&de.ce._def.shadowRoot!==!1&&de.ce._injectChildStyle(pe);const Fe=f.subTree=ks(f);O(null,Fe,p,y,f,v,_),a.el=Fe.el}if(J&&ye(J,v),!Ie&&(C=H&&H.onVnodeMounted)){const Fe=a;ye(()=>$e(C,q,Fe),v)}(a.shapeFlag&256||q&&Et(q.vnode)&&q.vnode.shapeFlag&256)&&f.a&&ye(f.a,v),f.isMounted=!0,a=p=y=null}};f.scope.on();const S=f.effect=new jr(w);f.scope.off();const x=f.update=S.run.bind(S),j=f.job=S.runIfDirty.bind(S);j.i=f,j.id=f.uid,S.scheduler=()=>Ss(j),ct(f,!0),x()},G=(f,a,p)=>{a.component=f;const y=f.vnode.props;f.vnode=a,f.next=null,pl(f,a.props,y,p),yl(f,a.children,p),Ge(),js(f),ze()},B=(f,a,p,y,v,_,E,w,S=!1)=>{const x=f&&f.children,j=f?f.shapeFlag:0,C=a.children,{patchFlag:F,shapeFlag:H}=a;if(F>0){if(F&128){Ze(x,C,p,y,v,_,E,w,S);return}else if(F&256){Ve(x,C,p,y,v,_,E,w,S);return}}H&8?(j&16&&ve(x,v,_),C!==x&&u(p,C)):j&16?H&16?Ze(x,C,p,y,v,_,E,w,S):ve(x,v,_,!0):(j&8&&u(p,""),H&16&&Ae(C,p,y,v,_,E,w,S))},Ve=(f,a,p,y,v,_,E,w,S)=>{f=f||bt,a=a||bt;const x=f.length,j=a.length,C=Math.min(x,j);let F;for(F=0;F<C;F++){const H=a[F]=S?nt(a[F]):De(a[F]);O(f[F],H,p,null,v,_,E,w,S)}x>j?ve(f,v,_,!0,!1,C):Ae(a,p,y,v,_,E,w,S,C)},Ze=(f,a,p,y,v,_,E,w,S)=>{let x=0;const j=a.length;let C=f.length-1,F=j-1;for(;x<=C&&x<=F;){const H=f[x],K=a[x]=S?nt(a[x]):De(a[x]);if(Ft(H,K))O(H,K,p,null,v,_,E,w,S);else break;x++}for(;x<=C&&x<=F;){const H=f[C],K=a[F]=S?nt(a[F]):De(a[F]);if(Ft(H,K))O(H,K,p,null,v,_,E,w,S);else break;C--,F--}if(x>C){if(x<=F){const H=F+1,K=H<j?a[H].el:y;for(;x<=F;)O(null,a[x]=S?nt(a[x]):De(a[x]),p,K,v,_,E,w,S),x++}}else if(x>F)for(;x<=C;)he(f[x],v,_,!0),x++;else{const H=x,K=x,J=new Map;for(x=K;x<=F;x++){const _e=a[x]=S?nt(a[x]):De(a[x]);_e.key!=null&&J.set(_e.key,x)}let q,de=0;const pe=F-K+1;let Ie=!1,Fe=0;const Mt=new Array(pe);for(x=0;x<pe;x++)Mt[x]=0;for(x=H;x<=C;x++){const _e=f[x];if(de>=pe){he(_e,v,_,!0);continue}let Ne;if(_e.key!=null)Ne=J.get(_e.key);else for(q=K;q<=F;q++)if(Mt[q-K]===0&&Ft(_e,a[q])){Ne=q;break}Ne===void 0?he(_e,v,_,!0):(Mt[Ne-K]=x+1,Ne>=Fe?Fe=Ne:Ie=!0,O(_e,a[Ne],p,null,v,_,E,w,S),de++)}const Os=Ie?Sl(Mt):bt;for(q=Os.length-1,x=pe-1;x>=0;x--){const _e=K+x,Ne=a[_e],Ts=a[_e+1],Ms=_e+1<j?Ts.el||Ts.placeholder:y;Mt[x]===0?O(null,Ne,p,Ms,v,_,E,w,S):Ie&&(q<0||x!==Os[q]?Me(Ne,p,Ms,2):q--)}}},Me=(f,a,p,y,v=null)=>{const{el:_,type:E,transition:w,children:S,shapeFlag:x}=f;if(x&6){Me(f.component.subTree,a,p,y);return}if(x&128){f.suspense.move(a,p,y);return}if(x&64){E.move(f,a,p,I);return}if(E===xe){s(_,a,p);for(let C=0;C<S.length;C++)Me(S[C],a,p,y);s(f.anchor,a,p);return}if(E===Kn){$(f,a,p);return}if(y!==2&&x&1&&w)if(y===0)w.beforeEnter(_),s(_,a,p),ye(()=>w.enter(_),v);else{const{leave:C,delayLeave:F,afterLeave:H}=w,K=()=>{f.ctx.isUnmounted?r(_):s(_,a,p)},J=()=>{_._isLeaving&&_[qo](!0),C(_,()=>{K(),H&&H()})};F?F(_,K,J):J()}else s(_,a,p)},he=(f,a,p,y=!1,v=!1)=>{const{type:_,props:E,ref:w,children:S,dynamicChildren:x,shapeFlag:j,patchFlag:C,dirs:F,cacheIndex:H}=f;if(C===-2&&(v=!1),w!=null&&(Ge(),Lt(w,null,p,f,!0),ze()),H!=null&&(a.renderCache[H]=void 0),j&256){a.ctx.deactivate(f);return}const K=j&1&&F,J=!Et(f);let q;if(J&&(q=E&&E.onVnodeBeforeUnmount)&&$e(q,a,f),j&6)tn(f.component,p,y);else{if(j&128){f.suspense.unmount(p,y);return}K&&lt(f,null,a,"beforeUnmount"),j&64?f.type.remove(f,a,p,I,y):x&&!x.hasOnce&&(_!==xe||C>0&&C&64)?ve(x,a,p,!1,!0):(_===xe&&C&384||!v&&j&16)&&ve(S,a,p),y&&pt(f)}(J&&(q=E&&E.onVnodeUnmounted)||K)&&ye(()=>{q&&$e(q,a,f),K&&lt(f,null,a,"unmounted")},p)},pt=f=>{const{type:a,el:p,anchor:y,transition:v}=f;if(a===xe){gt(p,y);return}if(a===Kn){M(f);return}const _=()=>{r(p),v&&!v.persisted&&v.afterLeave&&v.afterLeave()};if(f.shapeFlag&1&&v&&!v.persisted){const{leave:E,delayLeave:w}=v,S=()=>E(p,_);w?w(f.el,_,S):S()}else _()},gt=(f,a)=>{let p;for(;f!==a;)p=g(f),r(f),f=p;r(a)},tn=(f,a,p)=>{const{bum:y,scope:v,job:_,subTree:E,um:w,m:S,a:x}=f;Us(S),Us(x),y&&on(y),v.stop(),_&&(_.flags|=8,he(E,f,a,p)),w&&ye(w,a),ye(()=>{f.isUnmounted=!0},a)},ve=(f,a,p,y=!1,v=!1,_=0)=>{for(let E=_;E<f.length;E++)he(f[E],a,p,y,v)},b=f=>{if(f.shapeFlag&6)return b(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const a=g(f.anchor||f.el),p=a&&a[ko];return p?g(p):a};let A=!1;const R=(f,a,p)=>{f==null?a._vnode&&he(a._vnode,null,null,!0):O(a._vnode||null,f,a,null,null,null,p),a._vnode=f,A||(A=!0,js(),ri(),A=!1)},I={p:O,um:he,m:Me,r:pt,mt:Tt,mc:Ae,pc:B,pbc:Oe,n:b,o:e};return{render:R,hydrate:void 0,createApp:hl(R)}}function Vn({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function ct({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function xl(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Pi(e,t,n=!1){const s=e.children,r=t.children;if(D(s)&&D(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=nt(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&Pi(o,l)),l.type===Mn&&l.patchFlag!==-1&&(l.el=o.el),l.type===Qe&&!l.el&&(l.el=o.el)}}function Sl(e){const t=e.slice(),n=[0];let s,r,i,o,l;const c=e.length;for(s=0;s<c;s++){const h=e[s];if(h!==0){if(r=n[n.length-1],e[r]<h){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<h?i=l+1:o=l;h<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function Ai(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ai(t)}function Us(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const wl=Symbol.for("v-scx"),El=()=>qe(wl);function cn(e,t,n){return Oi(e,t,n)}function Oi(e,t,n=Y){const{immediate:s,deep:r,flush:i,once:o}=n,l=fe({},n),c=t&&s||!t&&i!=="post";let h;if(Qt){if(i==="sync"){const m=El();h=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=He,m.resume=He,m.pause=He,m}}const u=le;l.call=(m,P,O)=>Le(m,u,P,O);let d=!1;i==="post"?l.scheduler=m=>{ye(m,u&&u.suspense)}:i!=="sync"&&(d=!0,l.scheduler=(m,P)=>{P?m():Ss(m)}),l.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const g=Lo(e,t,l);return Qt&&(h?h.push(g):c&&g()),g}function Rl(e,t,n){const s=this.proxy,r=te(e)?e.includes(".")?Ti(s,e):()=>s[e]:e.bind(s,s);let i;L(t)?i=t:(i=t.handler,n=t);const o=en(this),l=Oi(r,i.bind(s),n);return o(),l}function Ti(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const Cl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e["".concat(t,"Modifiers")]||e["".concat(we(t),"Modifiers")]||e["".concat(ht(t),"Modifiers")];function Pl(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||Y;let r=n;const i=t.startsWith("update:"),o=i&&Cl(s,t.slice(7));o&&(o.trim&&(r=n.map(u=>te(u)?u.trim():u)),o.number&&(r=n.map(an)));let l,c=s[l=Nn(t)]||s[l=Nn(we(t))];!c&&i&&(c=s[l=Nn(ht(t))]),c&&Le(c,e,6,r);const h=s[l+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Le(h,e,6,r)}}function Mi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!L(e)){const c=h=>{const u=Mi(h,t,!0);u&&(l=!0,fe(o,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!i&&!l?(Z(e)&&s.set(e,null),null):(D(i)?i.forEach(c=>o[c]=null):fe(o,i),Z(e)&&s.set(e,o),o)}function Tn(e,t){return!e||!vn(t)?!1:(t=t.slice(2).replace(/Once$/,""),W(e,t[0].toLowerCase()+t.slice(1))||W(e,ht(t))||W(e,t))}function ks(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:c,render:h,renderCache:u,props:d,data:g,setupState:m,ctx:P,inheritAttrs:O}=e,V=gn(e);let N,T;try{if(n.shapeFlag&4){const M=r||s,z=M;N=De(h.call(z,M,u,d,m,g,P)),T=l}else{const M=t;N=De(M.length>1?M(d,{attrs:l,slots:o,emit:c}):M(d,null)),T=t.props?l:Al(l)}}catch(M){Kt.length=0,An(M,e,1),N=me(Qe)}let $=N;if(T&&O!==!1){const M=Object.keys(T),{shapeFlag:z}=$;M.length&&z&7&&(i&&M.some(fs)&&(T=Ol(T,i)),$=Pt($,T,!1,!0))}return n.dirs&&($=Pt($,null,!1,!0),$.dirs=$.dirs?$.dirs.concat(n.dirs):n.dirs),n.transition&&ws($,n.transition),N=$,gn(V),N}const Al=e=>{let t;for(const n in e)(n==="class"||n==="style"||vn(n))&&((t||(t={}))[n]=e[n]);return t},Ol=(e,t)=>{const n={};for(const s in e)(!fs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Tl(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:c}=t,h=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Ws(s,o,h):!!o;if(c&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const g=u[d];if(o[g]!==s[g]&&!Tn(h,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?Ws(s,o,h):!0:!!o;return!1}function Ws(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!Tn(n,i))return!0}return!1}function Ml({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Ii=e=>e.__isSuspense;function Il(e,t){t&&t.pendingBranch?D(e)?t.effects.push(...e):t.effects.push(e):Bo(e)}const xe=Symbol.for("v-fgt"),Mn=Symbol.for("v-txt"),Qe=Symbol.for("v-cmt"),Kn=Symbol.for("v-stc"),Kt=[];let be=null;function ts(e=!1){Kt.push(be=e?null:[])}function Fl(){Kt.pop(),be=Kt[Kt.length-1]||null}let Gt=1;function qs(e,t=!1){Gt+=e,e<0&&be&&t&&(be.hasOnce=!0)}function Fi(e){return e.dynamicChildren=Gt>0?be||bt:null,Fl(),Gt>0&&be&&be.push(e),e}function Of(e,t,n,s,r,i){return Fi($i(e,t,n,s,r,i,!0))}function ns(e,t,n,s,r){return Fi(me(e,t,n,s,r,!0))}function zt(e){return e?e.__v_isVNode===!0:!1}function Ft(e,t){return e.type===t.type&&e.key===t.key}const Ni=({key:e})=>e!=null?e:null,fn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?te(e)||ce(e)||L(e)?{i:ie,r:e,k:t,f:!!n}:e:null);function $i(e,t=null,n=null,s=0,r=null,i=e===xe?0:1,o=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ni(t),ref:t&&fn(t),scopeId:oi,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ie};return l?(Cs(c,n),i&128&&e.normalize(c)):n&&(c.shapeFlag|=te(n)?8:16),Gt>0&&!o&&be&&(c.patchFlag>0||i&6)&&c.patchFlag!==32&&be.push(c),c}const me=Nl;function Nl(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===di)&&(e=Qe),zt(e)){const l=Pt(e,t,!0);return n&&Cs(l,n),Gt>0&&!i&&be&&(l.shapeFlag&6?be[be.indexOf(e)]=l:be.push(l)),l.patchFlag=-2,l}if(Gl(e)&&(e=e.__vccOpts),t){t=$l(t);let{class:l,style:c}=t;l&&!te(l)&&(t.class=ds(l)),Z(c)&&(vs(c)&&!D(c)&&(c=fe({},c)),t.style=hs(c))}const o=te(e)?1:Ii(e)?128:Wo(e)?64:Z(e)?4:L(e)?2:0;return $i(e,t,n,s,r,o,i,!0)}function $l(e){return e?vs(e)||xi(e)?fe({},e):e:null}function Pt(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:c}=e,h=t?Dl(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&Ni(h),ref:t&&t.ref?n&&i?D(i)?i.concat(fn(t)):[i,fn(t)]:fn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==xe?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Pt(e.ssContent),ssFallback:e.ssFallback&&Pt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&ws(u,c.clone(u)),u}function jl(e=" ",t=0){return me(Mn,null,e,t)}function Tf(e="",t=!1){return t?(ts(),ns(Qe,null,e)):me(Qe,null,e)}function De(e){return e==null||typeof e=="boolean"?me(Qe):D(e)?me(xe,null,e.slice()):zt(e)?nt(e):me(Mn,null,String(e))}function nt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Pt(e)}function Cs(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(D(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Cs(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!xi(t)?t._ctx=ie:r===3&&ie&&(ie.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else L(t)?(t={default:t,_ctx:ie},n=32):(t=String(t),s&64?(n=16,t=[jl(t)]):n=8);e.children=t,e.shapeFlag|=n}function Dl(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=ds([t.class,s.class]));else if(r==="style")t.style=hs([t.style,s.style]);else if(vn(r)){const i=t[r],o=s[r];o&&i!==o&&!(D(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function $e(e,t,n,s=null){Le(e,t,7,[n,s])}const Hl=yi();let Ll=0;function Vl(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Hl,i={uid:Ll++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new $r(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:wi(s,r),emitsOptions:Mi(s,r),emit:null,emitted:null,propsDefaults:Y,inheritAttrs:s.inheritAttrs,ctx:Y,data:Y,props:Y,attrs:Y,slots:Y,refs:Y,setupState:Y,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Pl.bind(null,i),e.ce&&e.ce(i),i}let le=null;const Kl=()=>le||ie;let _n,ss;{const e=En(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};_n=t("__VUE_INSTANCE_SETTERS__",n=>le=n),ss=t("__VUE_SSR_SETTERS__",n=>Qt=n)}const en=e=>{const t=le;return _n(e),e.scope.on(),()=>{e.scope.off(),_n(t)}},Gs=()=>{le&&le.scope.off(),_n(null)};function ji(e){return e.vnode.shapeFlag&4}let Qt=!1;function Bl(e,t=!1,n=!1){t&&ss(t);const{props:s,children:r}=e.vnode,i=ji(e);dl(e,s,i,t),_l(e,r,n||t);const o=i?Ul(e,t):void 0;return t&&ss(!1),o}function Ul(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,il);const{setup:s}=n;if(s){Ge();const r=e.setupContext=s.length>1?Wl(e):null,i=en(e),o=Zt(s,e,0,[e.props,r]),l=Ar(o);if(ze(),i(),(l||e.sp)&&!Et(e)&&ci(e),l){if(o.then(Gs,Gs),t)return o.then(c=>{zs(e,c)}).catch(c=>{An(c,e,0)});e.asyncDep=o}else zs(e,o)}else Di(e)}function zs(e,t,n){L(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Z(t)&&(e.setupState=ti(t)),Di(e)}function Di(e,t,n){const s=e.type;e.render||(e.render=s.render||He);{const r=en(e);Ge();try{ol(e)}finally{ze(),r()}}}const kl={get(e,t){return oe(e,"get",""),e[t]}};function Wl(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,kl),slots:e.slots,emit:e.emit,expose:t}}function In(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ti(Xr(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Vt)return Vt[n](e)},has(t,n){return n in t||n in Vt}})):e.proxy}function ql(e,t=!0){return L(e)?e.displayName||e.name:e.name||t&&e.__name}function Gl(e){return L(e)&&"__vccOpts"in e}const Ee=(e,t)=>Do(e,t,Qt);function Hi(e,t,n){const s=arguments.length;return s===2?Z(t)&&!D(t)?zt(t)?me(e,null,[t]):me(e,t):me(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&zt(n)&&(n=[n]),me(e,t,n))}const zl="3.5.20";/**
* @vue/runtime-dom v3.5.20
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let rs;const Qs=typeof window<"u"&&window.trustedTypes;if(Qs)try{rs=Qs.createPolicy("vue",{createHTML:e=>e})}catch(e){}const Li=rs?e=>rs.createHTML(e):e=>e,Ql="http://www.w3.org/2000/svg",Yl="http://www.w3.org/1998/Math/MathML",Ue=typeof document<"u"?document:null,Ys=Ue&&Ue.createElement("template"),Jl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?Ue.createElementNS(Ql,e):t==="mathml"?Ue.createElementNS(Yl,e):n?Ue.createElement(e,{is:n}):Ue.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>Ue.createTextNode(e),createComment:e=>Ue.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ue.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Ys.innerHTML=Li(s==="svg"?"<svg>".concat(e,"</svg>"):s==="mathml"?"<math>".concat(e,"</math>"):e);const l=Ys.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Xl=Symbol("_vtc");function Zl(e,t,n){const s=e[Xl];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Js=Symbol("_vod"),ec=Symbol("_vsh"),tc=Symbol(""),nc=/(^|;)\s*display\s*:/;function sc(e,t,n){const s=e.style,r=te(n);let i=!1;if(n&&!r){if(t)if(te(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&un(s,l,"")}else for(const o in t)n[o]==null&&un(s,o,"");for(const o in n)o==="display"&&(i=!0),un(s,o,n[o])}else if(r){if(t!==n){const o=s[tc];o&&(n+=";"+o),s.cssText=n,i=nc.test(n)}}else t&&e.removeAttribute("style");Js in e&&(e[Js]=i?s.display:"",e[ec]&&(s.display="none"))}const Xs=/\s*!important$/;function un(e,t,n){if(D(n))n.forEach(s=>un(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=rc(e,t);Xs.test(n)?e.setProperty(ht(s),n.replace(Xs,""),"important"):e[s]=n}}const Zs=["Webkit","Moz","ms"],Bn={};function rc(e,t){const n=Bn[t];if(n)return n;let s=we(t);if(s!=="filter"&&s in e)return Bn[t]=s;s=wn(s);for(let r=0;r<Zs.length;r++){const i=Zs[r]+s;if(i in e)return Bn[t]=i}return t}const er="http://www.w3.org/1999/xlink";function tr(e,t,n,s,r,i=co(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(er,t.slice(6,t.length)):e.setAttributeNS(er,t,n):n==null||i&&!Ir(n)?e.removeAttribute(t):e.setAttribute(t,i?"":Ce(n)?String(n):n)}function nr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Li(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Ir(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch(l){}o&&e.removeAttribute(r||t)}function ut(e,t,n,s){e.addEventListener(t,n,s)}function ic(e,t,n,s){e.removeEventListener(t,n,s)}const sr=Symbol("_vei");function oc(e,t,n,s,r=null){const i=e[sr]||(e[sr]={}),o=i[t];if(s&&o)o.value=s;else{const[l,c]=lc(t);if(s){const h=i[t]=uc(s,r);ut(e,l,h,c)}else o&&(ic(e,l,o,c),i[t]=void 0)}}const rr=/(?:Once|Passive|Capture)$/;function lc(e){let t;if(rr.test(e)){t={};let s;for(;s=e.match(rr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):ht(e.slice(2)),t]}let Un=0;const cc=Promise.resolve(),fc=()=>Un||(cc.then(()=>Un=0),Un=Date.now());function uc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Le(ac(s,n.value),t,5,[s])};return n.value=e,n.attached=fc(),n}function ac(e,t){if(D(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const ir=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,hc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Zl(e,s,o):t==="style"?sc(e,n,s):vn(t)?fs(t)||oc(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):dc(e,t,s,o))?(nr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&tr(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!te(s))?nr(e,we(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),tr(e,t,s,o))};function dc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ir(t)&&L(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return ir(t)&&te(n)?!1:t in e}const yn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return D(t)?n=>on(t,n):t};function pc(e){e.target.composing=!0}function or(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ct=Symbol("_assign"),Mf={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Ct]=yn(r);const i=s||r.props&&r.props.type==="number";ut(e,t?"change":"input",o=>{if(o.target.composing)return;let l=e.value;n&&(l=l.trim()),i&&(l=an(l)),e[Ct](l)}),n&&ut(e,"change",()=>{e.value=e.value.trim()}),t||(ut(e,"compositionstart",pc),ut(e,"compositionend",or),ut(e,"change",or))},mounted(e,{value:t}){e.value=t==null?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:i}},o){if(e[Ct]=yn(o),e.composing)return;const l=(i||e.type==="number")&&!/^0\d/.test(e.value)?an(e.value):e.value,c=t==null?"":t;l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},If={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=xn(t);ut(e,"change",()=>{const i=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?an(bn(o)):bn(o));e[Ct](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,xs(()=>{e._assigning=!1})}),e[Ct]=yn(s)},mounted(e,{value:t}){lr(e,t)},beforeUpdate(e,t,n){e[Ct]=yn(n)},updated(e,{value:t}){e._assigning||lr(e,t)}};function lr(e,t){const n=e.multiple,s=D(t);if(!(n&&!s&&!xn(t))){for(let r=0,i=e.options.length;r<i;r++){const o=e.options[r],l=bn(o);if(n)if(s){const c=typeof l;c==="string"||c==="number"?o.selected=t.some(h=>String(h)===String(l)):o.selected=uo(t,l)>-1}else o.selected=t.has(l);else if(Rn(bn(o),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function bn(e){return"_value"in e?e._value:e.value}const gc=["ctrl","shift","alt","meta"],mc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>gc.some(n=>e["".concat(n,"Key")]&&!t.includes(n))},Ff=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=((r,...i)=>{for(let o=0;o<t.length;o++){const l=mc[t[o]];if(l&&l(r,t))return}return e(r,...i)}))},_c=fe({patchProp:hc},Jl);let cr;function yc(){return cr||(cr=bl(_c))}const Nf=((...e)=>{const t=yc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=vc(s);if(!r)return;const i=t._component;!L(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,bc(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t});function bc(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function vc(e){return te(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const xc=Symbol();var fr;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(fr||(fr={}));function $f(){const e=ho(!0),t=e.run(()=>Zr({}));let n=[],s=[];const r=Xr({install(i){r._a=i,i.provide(xc,r),i.config.globalProperties.$pinia=r,s.forEach(o=>n.push(o)),s=[]},use(i){return this._a?n.push(i):s.push(i),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const yt=typeof document<"u";function Vi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Sc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Vi(e.default)}const U=Object.assign;function kn(e,t){const n={};for(const s in t){const r=t[s];n[s]=Pe(r)?r.map(e):e(r)}return n}const Bt=()=>{},Pe=Array.isArray,Ki=/#/g,wc=/&/g,Ec=/\//g,Rc=/=/g,Cc=/\?/g,Bi=/\+/g,Pc=/%5B/g,Ac=/%5D/g,Ui=/%5E/g,Oc=/%60/g,ki=/%7B/g,Tc=/%7C/g,Wi=/%7D/g,Mc=/%20/g;function Ps(e){return encodeURI(""+e).replace(Tc,"|").replace(Pc,"[").replace(Ac,"]")}function Ic(e){return Ps(e).replace(ki,"{").replace(Wi,"}").replace(Ui,"^")}function is(e){return Ps(e).replace(Bi,"%2B").replace(Mc,"+").replace(Ki,"%23").replace(wc,"%26").replace(Oc,"`").replace(ki,"{").replace(Wi,"}").replace(Ui,"^")}function Fc(e){return is(e).replace(Rc,"%3D")}function Nc(e){return Ps(e).replace(Ki,"%23").replace(Cc,"%3F")}function $c(e){return e==null?"":Nc(e).replace(Ec,"%2F")}function Yt(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const jc=/\/$/,Dc=e=>e.replace(jc,"");function Wn(e,t,n="/"){let s,r={},i="",o="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),i=t.slice(c+1,l>-1?l:t.length),r=e(i)),l>-1&&(s=s||t.slice(0,l),o=t.slice(l,t.length)),s=Kc(s!=null?s:t,n),{fullPath:s+(i&&"?")+i+o,path:s,query:r,hash:Yt(o)}}function Hc(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ur(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Lc(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&At(t.matched[s],n.matched[r])&&qi(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function At(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function qi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!Vc(e[n],t[n]))return!1;return!0}function Vc(e,t){return Pe(e)?ar(e,t):Pe(t)?ar(t,e):e===t}function ar(e,t){return Pe(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Kc(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let i=n.length-1,o,l;for(o=0;o<s.length;o++)if(l=s[o],l!==".")if(l==="..")i>1&&i--;else break;return n.slice(0,i).join("/")+"/"+s.slice(o).join("/")}const et={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Jt;(function(e){e.pop="pop",e.push="push"})(Jt||(Jt={}));var Ut;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Ut||(Ut={}));function Bc(e){if(!e)if(yt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Dc(e)}const Uc=/^[^#]+#/;function kc(e,t){return e.replace(Uc,"#")+t}function Wc(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const Fn=()=>({left:window.scrollX,top:window.scrollY});function qc(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Wc(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function hr(e,t){return(history.state?history.state.position-t:-1)+e}const os=new Map;function Gc(e,t){os.set(e,t)}function zc(e){const t=os.get(e);return os.delete(e),t}let Qc=()=>location.protocol+"//"+location.host;function Gi(e,t){const{pathname:n,search:s,hash:r}=t,i=e.indexOf("#");if(i>-1){let l=r.includes(e.slice(i))?e.slice(i).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),ur(c,"")}return ur(n,e)+s+r}function Yc(e,t,n,s){let r=[],i=[],o=null;const l=({state:g})=>{const m=Gi(e,location),P=n.value,O=t.value;let V=0;if(g){if(n.value=m,t.value=g,o&&o===P){o=null;return}V=O?g.position-O.position:0}else s(m);r.forEach(N=>{N(n.value,P,{delta:V,type:Jt.pop,direction:V?V>0?Ut.forward:Ut.back:Ut.unknown})})};function c(){o=n.value}function h(g){r.push(g);const m=()=>{const P=r.indexOf(g);P>-1&&r.splice(P,1)};return i.push(m),m}function u(){const{history:g}=window;g.state&&g.replaceState(U({},g.state,{scroll:Fn()}),"")}function d(){for(const g of i)g();i=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:h,destroy:d}}function dr(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?Fn():null}}function Jc(e){const{history:t,location:n}=window,s={value:Gi(e,n)},r={value:t.state};r.value||i(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(c,h,u){const d=e.indexOf("#"),g=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+c:Qc()+e+c;try{t[u?"replaceState":"pushState"](h,"",g),r.value=h}catch(m){console.error(m),n[u?"replace":"assign"](g)}}function o(c,h){const u=U({},t.state,dr(r.value.back,c,r.value.forward,!0),h,{position:r.value.position});i(c,u,!0),s.value=c}function l(c,h){const u=U({},r.value,t.state,{forward:c,scroll:Fn()});i(u.current,u,!0);const d=U({},dr(s.value,c,null),{position:u.position+1},h);i(c,d,!1),s.value=c}return{location:s,state:r,push:l,replace:o}}function jf(e){e=Bc(e);const t=Jc(e),n=Yc(e,t.state,t.location,t.replace);function s(i,o=!0){o||n.pauseListeners(),history.go(i)}const r=U({location:"",base:e,go:s,createHref:kc.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Xc(e){return typeof e=="string"||e&&typeof e=="object"}function zi(e){return typeof e=="string"||typeof e=="symbol"}const Qi=Symbol("");var pr;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(pr||(pr={}));function Ot(e,t){return U(new Error,{type:e,[Qi]:!0},t)}function Be(e,t){return e instanceof Error&&Qi in e&&(t==null||!!(e.type&t))}const gr="[^/]+?",Zc={sensitive:!1,strict:!1,start:!0,end:!0},ef=/[.+*?^${}()[\]/\\]/g;function tf(e,t){const n=U({},Zc,t),s=[];let r=n.start?"^":"";const i=[];for(const h of e){const u=h.length?[]:[90];n.strict&&!h.length&&(r+="/");for(let d=0;d<h.length;d++){const g=h[d];let m=40+(n.sensitive?.25:0);if(g.type===0)d||(r+="/"),r+=g.value.replace(ef,"\\$&"),m+=40;else if(g.type===1){const{value:P,repeatable:O,optional:V,regexp:N}=g;i.push({name:P,repeatable:O,optional:V});const T=N||gr;if(T!==gr){m+=10;try{new RegExp("(".concat(T,")"))}catch(M){throw new Error('Invalid custom RegExp for param "'.concat(P,'" (').concat(T,"): ")+M.message)}}let $=O?"((?:".concat(T,")(?:/(?:").concat(T,"))*)"):"(".concat(T,")");d||($=V&&h.length<2?"(?:/".concat($,")"):"/"+$),V&&($+="?"),r+=$,m+=20,V&&(m+=-8),O&&(m+=-20),T===".*"&&(m+=-50)}u.push(m)}s.push(u)}if(n.strict&&n.end){const h=s.length-1;s[h][s[h].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const o=new RegExp(r,n.sensitive?"":"i");function l(h){const u=h.match(o),d={};if(!u)return null;for(let g=1;g<u.length;g++){const m=u[g]||"",P=i[g-1];d[P.name]=m&&P.repeatable?m.split("/"):m}return d}function c(h){let u="",d=!1;for(const g of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const m of g)if(m.type===0)u+=m.value;else if(m.type===1){const{value:P,repeatable:O,optional:V}=m,N=P in h?h[P]:"";if(Pe(N)&&!O)throw new Error('Provided param "'.concat(P,'" is an array but it is not repeatable (* or + modifiers)'));const T=Pe(N)?N.join("/"):N;if(!T)if(V)g.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error('Missing required param "'.concat(P,'"'));u+=T}}return u||"/"}return{re:o,score:s,keys:i,parse:l,stringify:c}}function nf(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Yi(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const i=nf(s[n],r[n]);if(i)return i;n++}if(Math.abs(r.length-s.length)===1){if(mr(s))return 1;if(mr(r))return-1}return r.length-s.length}function mr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const sf={type:0,value:""},rf=/[a-zA-Z0-9_]/;function of(e){if(!e)return[[]];if(e==="/")return[[sf]];if(!e.startsWith("/"))throw new Error('Invalid path "'.concat(e,'"'));function t(m){throw new Error("ERR (".concat(n,')/"').concat(h,'": ').concat(m))}let n=0,s=n;const r=[];let i;function o(){i&&r.push(i),i=[]}let l=0,c,h="",u="";function d(){h&&(n===0?i.push({type:0,value:h}):n===1||n===2||n===3?(i.length>1&&(c==="*"||c==="+")&&t("A repeatable param (".concat(h,") must be alone in its segment. eg: '/:ids+.")),i.push({type:1,value:h,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),h="")}function g(){h+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(h&&d(),o()):c===":"?(d(),n=1):g();break;case 4:g(),n=s;break;case 1:c==="("?n=2:rf.test(c)?g():(d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t('Unfinished custom RegExp for param "'.concat(h,'"')),d(),o(),r}function lf(e,t,n){const s=tf(of(e.path),n),r=U(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function cf(e,t){const n=[],s=new Map;t=vr({strict:!1,end:!0,sensitive:!1},t);function r(d){return s.get(d)}function i(d,g,m){const P=!m,O=yr(d);O.aliasOf=m&&m.record;const V=vr(t,d),N=[O];if("alias"in d){const M=typeof d.alias=="string"?[d.alias]:d.alias;for(const z of M)N.push(yr(U({},O,{components:m?m.record.components:O.components,path:z,aliasOf:m?m.record:O})))}let T,$;for(const M of N){const{path:z}=M;if(g&&z[0]!=="/"){const se=g.record.path,ee=se[se.length-1]==="/"?"":"/";M.path=g.record.path+(z&&ee+z)}if(T=lf(M,g,V),m?m.alias.push(T):($=$||T,$!==T&&$.alias.push(T),P&&d.name&&!br(T)&&o(d.name)),Ji(T)&&c(T),O.children){const se=O.children;for(let ee=0;ee<se.length;ee++)i(se[ee],T,m&&m.children[ee])}m=m||T}return $?()=>{o($)}:Bt}function o(d){if(zi(d)){const g=s.get(d);g&&(s.delete(d),n.splice(n.indexOf(g),1),g.children.forEach(o),g.alias.forEach(o))}else{const g=n.indexOf(d);g>-1&&(n.splice(g,1),d.record.name&&s.delete(d.record.name),d.children.forEach(o),d.alias.forEach(o))}}function l(){return n}function c(d){const g=af(d,n);n.splice(g,0,d),d.record.name&&!br(d)&&s.set(d.record.name,d)}function h(d,g){let m,P={},O,V;if("name"in d&&d.name){if(m=s.get(d.name),!m)throw Ot(1,{location:d});V=m.record.name,P=U(_r(g.params,m.keys.filter($=>!$.optional).concat(m.parent?m.parent.keys.filter($=>$.optional):[]).map($=>$.name)),d.params&&_r(d.params,m.keys.map($=>$.name))),O=m.stringify(P)}else if(d.path!=null)O=d.path,m=n.find($=>$.re.test(O)),m&&(P=m.parse(O),V=m.record.name);else{if(m=g.name?s.get(g.name):n.find($=>$.re.test(g.path)),!m)throw Ot(1,{location:d,currentLocation:g});V=m.record.name,P=U({},g.params,d.params),O=m.stringify(P)}const N=[];let T=m;for(;T;)N.unshift(T.record),T=T.parent;return{name:V,path:O,params:P,matched:N,meta:uf(N)}}e.forEach(d=>i(d));function u(){n.length=0,s.clear()}return{addRoute:i,resolve:h,removeRoute:o,clearRoutes:u,getRoutes:l,getRecordMatcher:r}}function _r(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function yr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ff(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ff(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function br(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function uf(e){return e.reduce((t,n)=>U(t,n.meta),{})}function vr(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function af(e,t){let n=0,s=t.length;for(;n!==s;){const i=n+s>>1;Yi(e,t[i])<0?s=i:n=i+1}const r=hf(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function hf(e){let t=e;for(;t=t.parent;)if(Ji(t)&&Yi(e,t)===0)return t}function Ji({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function df(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const i=s[r].replace(Bi," "),o=i.indexOf("="),l=Yt(o<0?i:i.slice(0,o)),c=o<0?null:Yt(i.slice(o+1));if(l in t){let h=t[l];Pe(h)||(h=t[l]=[h]),h.push(c)}else t[l]=c}return t}function xr(e){let t="";for(let n in e){const s=e[n];if(n=Fc(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(Pe(s)?s.map(i=>i&&is(i)):[s&&is(s)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+n,i!=null&&(t+="="+i))})}return t}function pf(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=Pe(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const gf=Symbol(""),Sr=Symbol(""),As=Symbol(""),Xi=Symbol(""),ls=Symbol("");function Nt(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function st(e,t,n,s,r,i=o=>o()){const o=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const h=g=>{g===!1?c(Ot(4,{from:n,to:t})):g instanceof Error?c(g):Xc(g)?c(Ot(2,{from:t,to:g})):(o&&s.enterCallbacks[r]===o&&typeof g=="function"&&o.push(g),l())},u=i(()=>e.call(s&&s.instances[r],t,n,h));let d=Promise.resolve(u);e.length<3&&(d=d.then(h)),d.catch(g=>c(g))})}function qn(e,t,n,s,r=i=>i()){const i=[];for(const o of e)for(const l in o.components){let c=o.components[l];if(!(t!=="beforeRouteEnter"&&!o.instances[l]))if(Vi(c)){const u=(c.__vccOpts||c)[t];u&&i.push(st(u,n,s,o,l,r))}else{let h=c();i.push(()=>h.then(u=>{if(!u)throw new Error("Couldn't resolve component \"".concat(l,'" at "').concat(o.path,'"'));const d=Sc(u)?u.default:u;o.mods[l]=u,o.components[l]=d;const m=(d.__vccOpts||d)[t];return m&&st(m,n,s,o,l,r)()}))}}return i}function wr(e){const t=qe(As),n=qe(Xi),s=Ee(()=>{const c=St(e.to);return t.resolve(c)}),r=Ee(()=>{const{matched:c}=s.value,{length:h}=c,u=c[h-1],d=n.matched;if(!u||!d.length)return-1;const g=d.findIndex(At.bind(null,u));if(g>-1)return g;const m=Er(c[h-2]);return h>1&&Er(u)===m&&d[d.length-1].path!==m?d.findIndex(At.bind(null,c[h-2])):g}),i=Ee(()=>r.value>-1&&vf(n.params,s.value.params)),o=Ee(()=>r.value>-1&&r.value===n.matched.length-1&&qi(n.params,s.value.params));function l(c={}){if(bf(c)){const h=t[St(e.replace)?"replace":"push"](St(e.to)).catch(Bt);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>h),h}return Promise.resolve()}return{route:s,href:Ee(()=>s.value.href),isActive:i,isExactActive:o,navigate:l}}function mf(e){return e.length===1?e[0]:e}const _f=li({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:wr,setup(e,{slots:t}){const n=Pn(wr(e)),{options:s}=qe(As),r=Ee(()=>({[Rr(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Rr(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const i=t.default&&mf(t.default(n));return e.custom?i:Hi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},i)}}}),yf=_f;function bf(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function vf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!Pe(r)||r.length!==s.length||s.some((i,o)=>i!==r[o]))return!1}return!0}function Er(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Rr=(e,t,n)=>e!=null?e:t!=null?t:n,xf=li({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=qe(ls),r=Ee(()=>e.route||s.value),i=qe(Sr,0),o=Ee(()=>{let h=St(i);const{matched:u}=r.value;let d;for(;(d=u[h])&&!d.components;)h++;return h}),l=Ee(()=>r.value.matched[o.value]);ln(Sr,Ee(()=>o.value+1)),ln(gf,l),ln(ls,r);const c=Zr();return cn(()=>[c.value,l.value,e.name],([h,u,d],[g,m,P])=>{u&&(u.instances[d]=h,m&&m!==u&&h&&h===g&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),h&&u&&(!m||!At(u,m)||!g)&&(u.enterCallbacks[d]||[]).forEach(O=>O(h))},{flush:"post"}),()=>{const h=r.value,u=e.name,d=l.value,g=d&&d.components[u];if(!g)return Cr(n.default,{Component:g,route:h});const m=d.props[u],P=m?m===!0?h.params:typeof m=="function"?m(h):m:null,V=Hi(g,U({},P,t,{onVnodeUnmounted:N=>{N.component.isUnmounted&&(d.instances[u]=null)},ref:c}));return Cr(n.default,{Component:V,route:h})||V}}});function Cr(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Sf=xf;function Df(e){const t=cf(e.routes,e),n=e.parseQuery||df,s=e.stringifyQuery||xr,r=e.history,i=Nt(),o=Nt(),l=Nt(),c=Fo(et);let h=et;yt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=kn.bind(null,b=>""+b),d=kn.bind(null,$c),g=kn.bind(null,Yt);function m(b,A){let R,I;return zi(b)?(R=t.getRecordMatcher(b),I=A):I=b,t.addRoute(I,R)}function P(b){const A=t.getRecordMatcher(b);A&&t.removeRoute(A)}function O(){return t.getRoutes().map(b=>b.record)}function V(b){return!!t.getRecordMatcher(b)}function N(b,A){if(A=U({},A||c.value),typeof b=="string"){const p=Wn(n,b,A.path),y=t.resolve({path:p.path},A),v=r.createHref(p.fullPath);return U(p,y,{params:g(y.params),hash:Yt(p.hash),redirectedFrom:void 0,href:v})}let R;if(b.path!=null)R=U({},b,{path:Wn(n,b.path,A.path).path});else{const p=U({},b.params);for(const y in p)p[y]==null&&delete p[y];R=U({},b,{params:d(p)}),A.params=d(A.params)}const I=t.resolve(R,A),Q=b.hash||"";I.params=u(g(I.params));const f=Hc(s,U({},b,{hash:Ic(Q),path:I.path})),a=r.createHref(f);return U({fullPath:f,hash:Q,query:s===xr?pf(b.query):b.query||{}},I,{redirectedFrom:void 0,href:a})}function T(b){return typeof b=="string"?Wn(n,b,c.value.path):U({},b)}function $(b,A){if(h!==b)return Ot(8,{from:A,to:b})}function M(b){return ee(b)}function z(b){return M(U(T(b),{replace:!0}))}function se(b){const A=b.matched[b.matched.length-1];if(A&&A.redirect){const{redirect:R}=A;let I=typeof R=="function"?R(b):R;return typeof I=="string"&&(I=I.includes("?")||I.includes("#")?I=T(I):{path:I},I.params={}),U({query:b.query,hash:b.hash,params:I.path!=null?{}:b.params},I)}}function ee(b,A){const R=h=N(b),I=c.value,Q=b.state,f=b.force,a=b.replace===!0,p=se(R);if(p)return ee(U(T(p),{state:typeof p=="object"?U({},Q,p.state):Q,force:f,replace:a}),A||R);const y=R;y.redirectedFrom=A;let v;return!f&&Lc(s,I,R)&&(v=Ot(16,{to:y,from:I}),Me(I,I,!0,!1)),(v?Promise.resolve(v):Oe(y,I)).catch(_=>Be(_)?Be(_,2)?_:Ze(_):B(_,y,I)).then(_=>{if(_){if(Be(_,2))return ee(U({replace:a},T(_.to),{state:typeof _.to=="object"?U({},Q,_.to.state):Q,force:f}),A||y)}else _=ot(y,I,!0,a,Q);return Xe(y,I,_),_})}function Ae(b,A){const R=$(b,A);return R?Promise.reject(R):Promise.resolve()}function Je(b){const A=gt.values().next().value;return A&&typeof A.runWithContext=="function"?A.runWithContext(b):b()}function Oe(b,A){let R;const[I,Q,f]=wf(b,A);R=qn(I.reverse(),"beforeRouteLeave",b,A);for(const p of I)p.leaveGuards.forEach(y=>{R.push(st(y,b,A))});const a=Ae.bind(null,b,A);return R.push(a),ve(R).then(()=>{R=[];for(const p of i.list())R.push(st(p,b,A));return R.push(a),ve(R)}).then(()=>{R=qn(Q,"beforeRouteUpdate",b,A);for(const p of Q)p.updateGuards.forEach(y=>{R.push(st(y,b,A))});return R.push(a),ve(R)}).then(()=>{R=[];for(const p of f)if(p.beforeEnter)if(Pe(p.beforeEnter))for(const y of p.beforeEnter)R.push(st(y,b,A));else R.push(st(p.beforeEnter,b,A));return R.push(a),ve(R)}).then(()=>(b.matched.forEach(p=>p.enterCallbacks={}),R=qn(f,"beforeRouteEnter",b,A,Je),R.push(a),ve(R))).then(()=>{R=[];for(const p of o.list())R.push(st(p,b,A));return R.push(a),ve(R)}).catch(p=>Be(p,8)?p:Promise.reject(p))}function Xe(b,A,R){l.list().forEach(I=>Je(()=>I(b,A,R)))}function ot(b,A,R,I,Q){const f=$(b,A);if(f)return f;const a=A===et,p=yt?history.state:{};R&&(I||a?r.replace(b.fullPath,U({scroll:a&&p&&p.scroll},Q)):r.push(b.fullPath,Q)),c.value=b,Me(b,A,R,a),Ze()}let Te;function Tt(){Te||(Te=r.listen((b,A,R)=>{if(!tn.listening)return;const I=N(b),Q=se(I);if(Q){ee(U(Q,{replace:!0,force:!0}),I).catch(Bt);return}h=I;const f=c.value;yt&&Gc(hr(f.fullPath,R.delta),Fn()),Oe(I,f).catch(a=>Be(a,12)?a:Be(a,2)?(ee(U(T(a.to),{force:!0}),I).then(p=>{Be(p,20)&&!R.delta&&R.type===Jt.pop&&r.go(-1,!1)}).catch(Bt),Promise.reject()):(R.delta&&r.go(-R.delta,!1),B(a,I,f))).then(a=>{a=a||ot(I,f,!1),a&&(R.delta&&!Be(a,8)?r.go(-R.delta,!1):R.type===Jt.pop&&Be(a,20)&&r.go(-1,!1)),Xe(I,f,a)}).catch(Bt)}))}let dt=Nt(),ne=Nt(),G;function B(b,A,R){Ze(b);const I=ne.list();return I.length?I.forEach(Q=>Q(b,A,R)):console.error(b),Promise.reject(b)}function Ve(){return G&&c.value!==et?Promise.resolve():new Promise((b,A)=>{dt.add([b,A])})}function Ze(b){return G||(G=!b,Tt(),dt.list().forEach(([A,R])=>b?R(b):A()),dt.reset()),b}function Me(b,A,R,I){const{scrollBehavior:Q}=e;if(!yt||!Q)return Promise.resolve();const f=!R&&zc(hr(b.fullPath,0))||(I||!R)&&history.state&&history.state.scroll||null;return xs().then(()=>Q(b,A,f)).then(a=>a&&qc(a)).catch(a=>B(a,b,A))}const he=b=>r.go(b);let pt;const gt=new Set,tn={currentRoute:c,listening:!0,addRoute:m,removeRoute:P,clearRoutes:t.clearRoutes,hasRoute:V,getRoutes:O,resolve:N,options:e,push:M,replace:z,go:he,back:()=>he(-1),forward:()=>he(1),beforeEach:i.add,beforeResolve:o.add,afterEach:l.add,onError:ne.add,isReady:Ve,install(b){const A=this;b.component("RouterLink",yf),b.component("RouterView",Sf),b.config.globalProperties.$router=A,Object.defineProperty(b.config.globalProperties,"$route",{enumerable:!0,get:()=>St(c)}),yt&&!pt&&c.value===et&&(pt=!0,M(r.location).catch(Q=>{}));const R={};for(const Q in et)Object.defineProperty(R,Q,{get:()=>c.value[Q],enumerable:!0});b.provide(As,A),b.provide(Xi,Yr(R)),b.provide(ls,c);const I=b.unmount;gt.add(b),b.unmount=function(){gt.delete(b),gt.size<1&&(h=et,Te&&Te(),Te=null,c.value=et,pt=!1,G=!1),I()}}};function ve(b){return b.reduce((A,R)=>A.then(()=>Je(R)),Promise.resolve())}return tn}function wf(e,t){const n=[],s=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let o=0;o<i;o++){const l=t.matched[o];l&&(e.matched.find(h=>At(h,l))?s.push(l):n.push(l));const c=e.matched[o];c&&(t.matched.find(h=>At(h,c))||r.push(c))}return[n,s,r]}export{xe as F,$i as a,me as b,Of as c,li as d,ns as e,Cf as f,Df as g,jf as h,Nf as i,$f as j,Af as k,Tf as l,Ef as m,Pf as n,ts as o,If as p,jl as q,Rf as r,Ff as s,ao as t,Mf as v,Uo as w};
