System.register(["./index-legacy-HSZdlha3.js","./vendor-legacy-wqB_kNia.js"],function(t,e){"use strict";var n,r,i,a,o,s,l,h,c,u,f,d,p,g,m,b;return{setters:[t=>{n=t.a,r=t._},t=>{i=t.c,a=t.a,o=t.m,s=t.p,l=t.F,h=t.n,c=t.v,u=t.o,f=t.t,d=t.q,p=t.l,g=t.s,m=t.b,b=t.r}],execute:function(){var v=document.createElement("style");function y(e){return t("_",y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t}),y(e)}v.textContent="section[data-v-b60a221c]{width:100%;display:flex;justify-content:center}.filter-item[data-v-b60a221c]{display:flex;flex-direction:column;gap:15px;background-color:#fff;padding:20px;box-shadow:0 4px 8px rgba(0,0,0,.1);border-radius:20px;margin-bottom:20px;max-width:550px;width:100%;margin-left:auto;margin-right:auto}.filter-item div[data-v-b60a221c]{display:flex;flex-direction:column}.filter-item label[data-v-b60a221c]{margin-bottom:5px;font-weight:700}select[data-v-b60a221c],input[data-v-b60a221c]{border:1px solid #ccc;padding:10px;font-size:16px;background-color:#f9f9f9;-webkit-appearance:none;appearance:none;border-radius:20px;box-sizing:border-box;width:100%;height:45px;margin-top:0;margin-bottom:0}@media (max-width: 768px){.filter-item[data-v-b60a221c]{flex-direction:column;max-width:100%}}.statistics-section[data-v-2f07d2d7]{margin:15px 0}.stats-container[data-v-2f07d2d7]{display:flex;gap:10px;justify-content:center;flex-wrap:wrap;max-width:800px;margin:0 auto}.stat-card[data-v-2f07d2d7]{background-color:#fff;padding:12px 16px;border-radius:12px;box-shadow:0 2px 6px rgba(0,0,0,.1);text-align:center;min-width:100px;flex:1}.stat-card h3[data-v-2f07d2d7]{margin:0 0 6px;font-size:12px;color:#666;font-weight:600}.stat-number[data-v-2f07d2d7]{margin:0;font-size:22px;font-weight:700;color:#2e5a35}@media (max-width: 768px){.stats-container[data-v-2f07d2d7]{flex-direction:column;gap:8px}.stat-card[data-v-2f07d2d7]{min-width:auto;padding:10px 14px}.stat-number[data-v-2f07d2d7]{font-size:20px}}.table-container[data-v-78faf7ea]{width:100%;max-width:768px;overflow-x:auto;border:1px solid #ddd;border-radius:10px;margin:0 auto}table[data-v-78faf7ea]{width:100%;border-collapse:collapse;table-layout:auto;min-width:100%}th[data-v-78faf7ea],td[data-v-78faf7ea]{padding:15px;text-align:left;border-bottom:1px solid #ddd;white-space:nowrap}th[data-v-78faf7ea]{background-color:#f4f4f4;cursor:pointer;position:relative;user-select:none;transition:background-color .2s}th[data-v-78faf7ea]:hover{background-color:#e0e0e0}th span[data-v-78faf7ea]{margin-left:5px;color:#666}th[data-v-78faf7ea]:first-child,th[data-v-78faf7ea]:nth-child(2){position:sticky;left:0;background-color:#fff;z-index:20}td[data-v-78faf7ea]:first-child,td[data-v-78faf7ea]:nth-child(2){position:sticky;left:0;background-color:#fff;z-index:10}tr:last-child td[data-v-78faf7ea]{border-bottom:none}th[data-v-78faf7ea]:first-child{cursor:default}th[data-v-78faf7ea]:first-child:hover{background-color:#f4f4f4}.clickable-row[data-v-78faf7ea]{cursor:pointer;transition:background-color .2s}.clickable-row[data-v-78faf7ea]:hover{background-color:#f8f9fa}.modal-overlay[data-v-30322fc9]{position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.7);display:flex;justify-content:center;align-items:center;z-index:1000;backdrop-filter:blur(5px)}.modal-content[data-v-30322fc9]{background-color:#fff;border-radius:15px;max-width:95vw;max-height:95vh;overflow-y:auto;box-shadow:0 10px 30px rgba(0,0,0,.5);border:2px solid #2e5a35}.modal-header[data-v-30322fc9]{display:flex;justify-content:space-between;align-items:center;padding:20px 25px;border-bottom:2px solid #e9ecef;background-color:#f8f9fa;border-radius:15px 15px 0 0}.modal-header h3[data-v-30322fc9]{margin:0;color:#2e5a35;font-size:20px}.close-button[data-v-30322fc9]{background:#f44;color:#fff;border:none;font-size:18px;cursor:pointer;width:35px;height:35px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;transition:all .2s}.close-button[data-v-30322fc9]:hover{background:#f66;transform:scale(1.1)}.modal-body[data-v-30322fc9]{padding:25px}.detail-grid[data-v-30322fc9]{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.detail-section[data-v-30322fc9]{background-color:#fff;padding:15px;border-radius:10px;box-shadow:0 2px 4px rgba(0,0,0,.1)}.detail-section h4[data-v-30322fc9]{margin:0 0 15px;color:#2e5a35;font-size:16px;border-bottom:2px solid #e9ecef;padding-bottom:8px}.detail-section p[data-v-30322fc9]{margin:8px 0;font-size:14px;line-height:1.4}.detail-section strong[data-v-30322fc9]{color:#495057}.photo-section[data-v-30322fc9]{text-align:center}.biodata-photo[data-v-30322fc9]{max-width:200px;max-height:250px;border-radius:10px;box-shadow:0 4px 8px rgba(0,0,0,.1);object-fit:cover}.no-photo[data-v-30322fc9]{color:#6c757d;font-style:italic;margin-top:10px}.loading-content[data-v-30322fc9]{text-align:center;padding:40px;color:#6c757d}.loading-photo[data-v-30322fc9]{text-align:center;padding:20px;color:#6c757d;font-style:italic}@media (max-width: 768px){.modal-content[data-v-30322fc9]{max-width:95vw;max-height:95vh}.modal-header[data-v-30322fc9]{padding:15px 20px}.modal-header h3[data-v-30322fc9]{font-size:18px}.modal-body[data-v-30322fc9]{padding:20px}.close-button[data-v-30322fc9]{width:30px;height:30px;font-size:16px}.detail-grid[data-v-30322fc9]{grid-template-columns:1fr;gap:15px}.biodata-photo[data-v-30322fc9]{max-width:150px;max-height:200px}.detail-section[data-v-30322fc9]{padding:12px}}body{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif}h1{font-size:2.5em;text-align:center;margin-bottom:20px}button{border:1px solid #ccc;font-size:16px;background-color:#f9f9f9;-webkit-appearance:none;appearance:none;border-radius:20px;box-sizing:border-box;width:auto;margin:20px 10px;padding:0 20px;display:inline-block;cursor:pointer;height:50px}.button-container{text-align:center;margin-top:20px;width:100%;max-width:550px;margin-left:auto;margin-right:auto}@media (max-width: 768px){button{width:100%}}.modal-header h3{margin:0;color:#2e5a35;font-size:20px}.close-button{background:none;border:none;font-size:28px;cursor:pointer;color:#666;padding:0;width:35px;height:35px;border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all .2s}.close-button:hover{background-color:#e9ecef;color:#2e5a35}.modal-body{padding:25px}.detail-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:20px}.detail-section{background-color:#fff;padding:15px;border-radius:10px;box-shadow:0 2px 4px rgba(0,0,0,.1)}.detail-section h4{margin:0 0 15px;color:#2e5a35;font-size:16px;border-bottom:2px solid #e9ecef;padding-bottom:8px}.detail-section p{margin:8px 0;font-size:14px;line-height:1.4}.detail-section strong{color:#495057}.photo-section{text-align:center}.biodata-photo{max-width:200px;max-height:250px;border-radius:10px;box-shadow:0 4px 8px rgba(0,0,0,.1);object-fit:cover}.no-photo{color:#6c757d;font-style:italic;margin-top:10px}.loading-content{text-align:center;padding:40px;color:#6c757d}\n/*$vite$:1*/",document.head.appendChild(v),t("_",y);var w=Uint8Array,x=Uint16Array,_=Int32Array,A=new w([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),L=new w([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),N=new w([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),S=function(t,e){for(var n=new x(31),r=0;r<31;++r)n[r]=e+=1<<t[r-1];var i=new _(n[30]);for(r=1;r<30;++r)for(var a=n[r];a<n[r+1];++a)i[a]=a-n[r]<<5|r;return{b:n,r:i}},k=S(A,2),P=k.b,F=k.r;P[28]=258,F[258]=28;for(var C=S(L,0).r,I=new x(32768),j=0;j<32768;++j){var O=(43690&j)>>1|(21845&j)<<1;O=(61680&(O=(52428&O)>>2|(13107&O)<<2))>>4|(3855&O)<<4,I[j]=((65280&O)>>8|(255&O)<<8)>>1}var D=function(t,e,n){for(var r=t.length,i=0,a=new x(e);i<r;++i)t[i]&&++a[t[i]-1];var o,s=new x(e);for(i=1;i<e;++i)s[i]=s[i-1]+a[i-1]<<1;if(n){o=new x(1<<e);var l=15-e;for(i=0;i<r;++i)if(t[i])for(var h=i<<4|t[i],c=e-t[i],u=s[t[i]-1]++<<c,f=u|(1<<c)-1;u<=f;++u)o[I[u]>>l]=h}else for(o=new x(r),i=0;i<r;++i)t[i]&&(o[i]=I[s[t[i]-1]++]>>15-t[i]);return o},E=new w(288);for(j=0;j<144;++j)E[j]=8;for(j=144;j<256;++j)E[j]=9;for(j=256;j<280;++j)E[j]=7;for(j=280;j<288;++j)E[j]=8;var B=new w(32);for(j=0;j<32;++j)B[j]=5;var M=D(E,9,0),T=D(B,5,0),R=function(t){return(t+7)/8|0},q=function(t,e,n){n<<=7&e;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8},U=function(t,e,n){n<<=7&e;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8,t[r+2]|=n>>16},z=function(t,e){for(var n=[],r=0;r<t.length;++r)t[r]&&n.push({s:r,f:t[r]});var i=n.length,a=n.slice();if(!i)return{t:J,l:0};if(1==i){var o=new w(n[0].s+1);return o[n[0].s]=1,{t:o,l:1}}n.sort(function(t,e){return t.f-e.f}),n.push({s:-1,f:25001});var s=n[0],l=n[1],h=0,c=1,u=2;for(n[0]={s:-1,f:s.f+l.f,l:s,r:l};c!=i-1;)s=n[n[h].f<n[u].f?h++:u++],l=n[h!=c&&n[h].f<n[u].f?h++:u++],n[c++]={s:-1,f:s.f+l.f,l:s,r:l};var f=a[0].s;for(r=1;r<i;++r)a[r].s>f&&(f=a[r].s);var d=new x(f+1),p=H(n[c-1],d,0);if(p>e){r=0;var g=0,m=p-e,b=1<<m;for(a.sort(function(t,e){return d[e.s]-d[t.s]||t.f-e.f});r<i;++r){var v=a[r].s;if(!(d[v]>e))break;g+=b-(1<<p-d[v]),d[v]=e}for(g>>=m;g>0;){var y=a[r].s;d[y]<e?g-=1<<e-d[y]++-1:++r}for(;r>=0&&g;--r){var _=a[r].s;d[_]==e&&(--d[_],++g)}p=e}return{t:new w(d),l:p}},H=function(t,e,n){return-1==t.s?Math.max(H(t.l,e,n+1),H(t.r,e,n+1)):e[t.s]=n},W=function(t){for(var e=t.length;e&&!t[--e];);for(var n=new x(++e),r=0,i=t[0],a=1,o=function(t){n[r++]=t},s=1;s<=e;++s)if(t[s]==i&&s!=e)++a;else{if(!i&&a>2){for(;a>138;a-=138)o(32754);a>2&&(o(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(o(i),--a;a>6;a-=6)o(8304);a>2&&(o(a-3<<5|8208),a=0)}for(;a--;)o(i);a=1,i=t[s]}return{c:n.subarray(0,r),n:e}},G=function(t,e){for(var n=0,r=0;r<e.length;++r)n+=t[r]*e[r];return n},V=function(t,e,n){var r=n.length,i=R(e+2);t[i]=255&r,t[i+1]=r>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var a=0;a<r;++a)t[i+a+4]=n[a];return 8*(i+4+r)},K=function(t,e,n,r,i,a,o,s,l,h,c){q(e,c++,n),++i[256];for(var u=z(i,15),f=u.t,d=u.l,p=z(a,15),g=p.t,m=p.l,b=W(f),v=b.c,y=b.n,w=W(g),_=w.c,S=w.n,k=new x(19),P=0;P<v.length;++P)++k[31&v[P]];for(P=0;P<_.length;++P)++k[31&_[P]];for(var F=z(k,7),C=F.t,I=F.l,j=19;j>4&&!C[N[j-1]];--j);var O,R,H,K,Y=h+5<<3,J=G(i,E)+G(a,B)+o,X=G(i,f)+G(a,g)+o+14+3*j+G(k,C)+2*k[16]+3*k[17]+7*k[18];if(l>=0&&Y<=J&&Y<=X)return V(e,c,t.subarray(l,l+h));if(q(e,c,1+(X<J)),c+=2,X<J){O=D(f,d,0),R=f,H=D(g,m,0),K=g;var $=D(C,I,0);for(q(e,c,y-257),q(e,c+5,S-1),q(e,c+10,j-4),c+=14,P=0;P<j;++P)q(e,c+3*P,C[N[P]]);c+=3*j;for(var Z=[v,_],Q=0;Q<2;++Q){var tt=Z[Q];for(P=0;P<tt.length;++P){var et=31&tt[P];q(e,c,$[et]),c+=C[et],et>15&&(q(e,c,tt[P]>>5&127),c+=tt[P]>>12)}}}else O=M,R=E,H=T,K=B;for(P=0;P<s;++P){var nt=r[P];if(nt>255){U(e,c,O[257+(et=nt>>18&31)]),c+=R[et+257],et>7&&(q(e,c,nt>>23&31),c+=A[et]);var rt=31&nt;U(e,c,H[rt]),c+=K[rt],rt>3&&(U(e,c,nt>>5&8191),c+=L[rt])}else U(e,c,O[nt]),c+=R[nt]}return U(e,c,O[256]),c+R[256]},Y=new _([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),J=new w(0),X=function(t,e,n,r,i,a){var o=a.z||t.length,s=new w(r+o+5*(1+Math.ceil(o/7e3))+i),l=s.subarray(r,s.length-i),h=a.l,c=7&(a.r||0);if(e){c&&(l[0]=a.r>>3);for(var u=Y[e-1],f=u>>13,d=8191&u,p=(1<<n)-1,g=a.p||new x(32768),m=a.h||new x(p+1),b=Math.ceil(n/3),v=2*b,y=function(e){return(t[e]^t[e+1]<<b^t[e+2]<<v)&p},N=new _(25e3),S=new x(288),k=new x(32),P=0,I=0,j=a.i||0,O=0,D=a.w||0,E=0;j+2<o;++j){var B=y(j),M=32767&j,T=m[B];if(g[M]=T,m[B]=M,D<=j){var q=o-j;if((P>7e3||O>24576)&&(q>423||!h)){c=K(t,l,0,N,S,k,I,O,E,j-E,c),O=P=I=0,E=j;for(var U=0;U<286;++U)S[U]=0;for(U=0;U<30;++U)k[U]=0}var z=2,H=0,W=d,G=M-T&32767;if(q>2&&B==y(j-G))for(var J=Math.min(f,q)-1,X=Math.min(32767,j),$=Math.min(258,q);G<=X&&--W&&M!=T;){if(t[j+z]==t[j+z-G]){for(var Z=0;Z<$&&t[j+Z]==t[j+Z-G];++Z);if(Z>z){if(z=Z,H=G,Z>J)break;var Q=Math.min(G,Z-2),tt=0;for(U=0;U<Q;++U){var et=j-G+U&32767,nt=et-g[et]&32767;nt>tt&&(tt=nt,T=et)}}}G+=(M=T)-(T=g[M])&32767}if(H){N[O++]=268435456|F[z]<<18|C[H];var rt=31&F[z],it=31&C[H];I+=A[rt]+L[it],++S[257+rt],++k[it],D=j+z,++P}else N[O++]=t[j],++S[t[j]]}}for(j=Math.max(j,D);j<o;++j)N[O++]=t[j],++S[t[j]];c=K(t,l,h,N,S,k,I,O,E,j-E,c),h||(a.r=7&c|l[c/8|0]<<3,c-=7,a.h=m,a.p=g,a.i=j,a.w=D)}else{for(j=a.w||0;j<o+h;j+=65535){var at=j+65535;at>=o&&(l[c/8|0]=h,at=o),c=V(l,c+1,t.subarray(j,at))}a.i=o}return function(t,e,n){return(null==n||n>t.length)&&(n=t.length),new w(t.subarray(e,n))}(s,0,r+R(c)+i)},$=function(){var t=1,e=0;return{p:function(n){for(var r=t,i=e,a=0|n.length,o=0;o!=a;){for(var s=Math.min(o+2655,a);o<s;++o)i+=r+=n[o];r=(65535&r)+15*(r>>16),i=(65535&i)+15*(i>>16)}t=r,e=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(e%=65521))<<8|e>>8}}},Z=function(t,e,n){for(;n;++e)t[e]=n,n>>>=8};function Q(t,e){e||(e={});var n=$();n.p(t);var r=function(t,e,n,r,i){if(!i&&(i={l:1},e.dictionary)){var a=e.dictionary.subarray(-32768),o=new w(a.length+t.length);o.set(a),o.set(t,a.length),t=o,i.w=a.length}return X(t,null==e.level?6:e.level,null==e.mem?i.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):20:12+e.mem,n,r,i)}(t,e,e.dictionary?6:2,4);return function(t,e){var n=e.level,r=0==n?0:n<6?1:9==n?3:2;if(t[0]=120,t[1]=r<<6|(e.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,e.dictionary){var i=$();i.p(e.dictionary),Z(t,2,i.d())}}(r,e),Z(r,r.length-4,n.d()),r}var tt="undefined"!=typeof TextDecoder&&new TextDecoder;try{tt.decode(J,{stream:!0})}catch(Rr){}function et(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=Array(e);n<e;n++)r[n]=t[n];return r}function nt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,i,a,o,s=[],l=!0,h=!1;try{if(a=(n=n.call(t)).next,0===e);else for(;!(l=(r=a.call(n)).done)&&(s.push(r.value),s.length!==e);l=!0);}catch(t){h=!0,i=t}finally{try{if(!l&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(h)throw i}}return s}}(t,e)||function(t,e){if(t){if("string"==typeof t)return et(t,e);var n={}.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?et(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rt(t,e="utf8"){return new TextDecoder(e).decode(t)}const it=new TextEncoder,at=(()=>{const t=new Uint8Array(4);return!((new Uint32Array(t.buffer)[0]=1)&t[0])})(),ot={int8:globalThis.Int8Array,uint8:globalThis.Uint8Array,int16:globalThis.Int16Array,uint16:globalThis.Uint16Array,int32:globalThis.Int32Array,uint32:globalThis.Uint32Array,uint64:globalThis.BigUint64Array,int64:globalThis.BigInt64Array,float32:globalThis.Float32Array,float64:globalThis.Float64Array};class st{buffer;byteLength;byteOffset;length;offset;lastWrittenByte;littleEndian;_data;_mark;_marks;constructor(t=8192,e={}){let n=!1;"number"==typeof t?t=new ArrayBuffer(t):(n=!0,this.lastWrittenByte=t.byteLength);const r=e.offset?e.offset>>>0:0,i=t.byteLength-r;let a=r;(ArrayBuffer.isView(t)||t instanceof st)&&(t.byteLength!==t.buffer.byteLength&&(a=t.byteOffset+r),t=t.buffer),this.lastWrittenByte=n?i:0,this.buffer=t,this.length=i,this.byteLength=i,this.byteOffset=a,this.offset=0,this.littleEndian=!0,this._data=new DataView(this.buffer,a,i),this._mark=0,this._marks=[]}available(t=1){return this.offset+t<=this.length}isLittleEndian(){return this.littleEndian}setLittleEndian(){return this.littleEndian=!0,this}isBigEndian(){return!this.littleEndian}setBigEndian(){return this.littleEndian=!1,this}skip(t=1){return this.offset+=t,this}back(t=1){return this.offset-=t,this}seek(t){return this.offset=t,this}mark(){return this._mark=this.offset,this}reset(){return this.offset=this._mark,this}pushMark(){return this._marks.push(this.offset),this}popMark(){const t=this._marks.pop();if(void 0===t)throw new Error("Mark stack empty");return this.seek(t),this}rewind(){return this.offset=0,this}ensureAvailable(t=1){if(!this.available(t)){const e=2*(this.offset+t),n=new Uint8Array(e);n.set(new Uint8Array(this.buffer)),this.buffer=n.buffer,this.length=e,this.byteLength=e,this._data=new DataView(this.buffer)}return this}readBoolean(){return 0!==this.readUint8()}readInt8(){return this._data.getInt8(this.offset++)}readUint8(){return this._data.getUint8(this.offset++)}readByte(){return this.readUint8()}readBytes(t=1){return this.readArray(t,"uint8")}readArray(t,e){const n=ot[e].BYTES_PER_ELEMENT*t,r=this.byteOffset+this.offset,i=this.buffer.slice(r,r+n);if(this.littleEndian===at&&"uint8"!==e&&"int8"!==e){const t=new Uint8Array(this.buffer.slice(r,r+n));t.reverse();const i=new ot[e](t.buffer);return this.offset+=n,i.reverse(),i}const a=new ot[e](i);return this.offset+=n,a}readInt16(){const t=this._data.getInt16(this.offset,this.littleEndian);return this.offset+=2,t}readUint16(){const t=this._data.getUint16(this.offset,this.littleEndian);return this.offset+=2,t}readInt32(){const t=this._data.getInt32(this.offset,this.littleEndian);return this.offset+=4,t}readUint32(){const t=this._data.getUint32(this.offset,this.littleEndian);return this.offset+=4,t}readFloat32(){const t=this._data.getFloat32(this.offset,this.littleEndian);return this.offset+=4,t}readFloat64(){const t=this._data.getFloat64(this.offset,this.littleEndian);return this.offset+=8,t}readBigInt64(){const t=this._data.getBigInt64(this.offset,this.littleEndian);return this.offset+=8,t}readBigUint64(){const t=this._data.getBigUint64(this.offset,this.littleEndian);return this.offset+=8,t}readChar(){return String.fromCharCode(this.readInt8())}readChars(t=1){let e="";for(let n=0;n<t;n++)e+=this.readChar();return e}readUtf8(t=1){return rt(this.readBytes(t))}decodeText(t=1,e="utf8"){return rt(this.readBytes(t),e)}writeBoolean(t){return this.writeUint8(t?255:0),this}writeInt8(t){return this.ensureAvailable(1),this._data.setInt8(this.offset++,t),this._updateLastWrittenByte(),this}writeUint8(t){return this.ensureAvailable(1),this._data.setUint8(this.offset++,t),this._updateLastWrittenByte(),this}writeByte(t){return this.writeUint8(t)}writeBytes(t){this.ensureAvailable(t.length);for(let e=0;e<t.length;e++)this._data.setUint8(this.offset++,t[e]);return this._updateLastWrittenByte(),this}writeInt16(t){return this.ensureAvailable(2),this._data.setInt16(this.offset,t,this.littleEndian),this.offset+=2,this._updateLastWrittenByte(),this}writeUint16(t){return this.ensureAvailable(2),this._data.setUint16(this.offset,t,this.littleEndian),this.offset+=2,this._updateLastWrittenByte(),this}writeInt32(t){return this.ensureAvailable(4),this._data.setInt32(this.offset,t,this.littleEndian),this.offset+=4,this._updateLastWrittenByte(),this}writeUint32(t){return this.ensureAvailable(4),this._data.setUint32(this.offset,t,this.littleEndian),this.offset+=4,this._updateLastWrittenByte(),this}writeFloat32(t){return this.ensureAvailable(4),this._data.setFloat32(this.offset,t,this.littleEndian),this.offset+=4,this._updateLastWrittenByte(),this}writeFloat64(t){return this.ensureAvailable(8),this._data.setFloat64(this.offset,t,this.littleEndian),this.offset+=8,this._updateLastWrittenByte(),this}writeBigInt64(t){return this.ensureAvailable(8),this._data.setBigInt64(this.offset,t,this.littleEndian),this.offset+=8,this._updateLastWrittenByte(),this}writeBigUint64(t){return this.ensureAvailable(8),this._data.setBigUint64(this.offset,t,this.littleEndian),this.offset+=8,this._updateLastWrittenByte(),this}writeChar(t){return this.writeUint8(t.charCodeAt(0))}writeChars(t){for(let e=0;e<t.length;e++)this.writeUint8(t.charCodeAt(e));return this}writeUtf8(t){return this.writeBytes(function(t){return it.encode(t)}(t))}toArray(){return new Uint8Array(this.buffer,this.byteOffset,this.lastWrittenByte)}getWrittenByteLength(){return this.lastWrittenByte-this.byteOffset}_updateLastWrittenByte(){this.offset>this.lastWrittenByte&&(this.lastWrittenByte=this.offset)}}function lt(t){let e=t.length;for(;--e>=0;)t[e]=0}lt(new Array(576)),lt(new Array(60)),lt(new Array(512)),lt(new Array(256)),lt(new Array(29)),lt(new Array(30));var ht=(t,e,n,r)=>{let i=65535&t,a=t>>>16&65535,o=0;for(;0!==n;){o=n>2e3?2e3:n,n-=o;do{i=i+e[r++]|0,a=a+i|0}while(--o);i%=65521,a%=65521}return i|a<<16};const ct=new Uint32Array((()=>{let t,e=[];for(var n=0;n<256;n++){t=n;for(var r=0;r<8;r++)t=1&t?3988292384^t>>>1:t>>>1;e[n]=t}return e})());var ut=(t,e,n,r)=>{const i=ct,a=r+n;t^=-1;for(let o=r;o<a;o++)t=t>>>8^i[255&(t^e[o])];return-1^t},ft={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},dt={Z_NO_FLUSH:0,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_DEFLATED:8};const pt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var gt=function(t){const e=Array.prototype.slice.call(arguments,1);for(;e.length;){const n=e.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(const e in n)pt(n,e)&&(t[e]=n[e])}}return t},mt=t=>{let e=0;for(let r=0,i=t.length;r<i;r++)e+=t[r].length;const n=new Uint8Array(e);for(let r=0,i=0,a=t.length;r<a;r++){let e=t[r];n.set(e,i),i+=e.length}return n};let bt=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(Io){bt=!1}const vt=new Uint8Array(256);for(let t=0;t<256;t++)vt[t]=t>=252?6:t>=248?5:t>=240?4:t>=224?3:t>=192?2:1;vt[254]=vt[254]=1;var yt=t=>{if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(t);let e,n,r,i,a,o=t.length,s=0;for(i=0;i<o;i++)n=t.charCodeAt(i),55296==(64512&n)&&i+1<o&&(r=t.charCodeAt(i+1),56320==(64512&r)&&(n=65536+(n-55296<<10)+(r-56320),i++)),s+=n<128?1:n<2048?2:n<65536?3:4;for(e=new Uint8Array(s),a=0,i=0;a<s;i++)n=t.charCodeAt(i),55296==(64512&n)&&i+1<o&&(r=t.charCodeAt(i+1),56320==(64512&r)&&(n=65536+(n-55296<<10)+(r-56320),i++)),n<128?e[a++]=n:n<2048?(e[a++]=192|n>>>6,e[a++]=128|63&n):n<65536?(e[a++]=224|n>>>12,e[a++]=128|n>>>6&63,e[a++]=128|63&n):(e[a++]=240|n>>>18,e[a++]=128|n>>>12&63,e[a++]=128|n>>>6&63,e[a++]=128|63&n);return e},wt=(t,e)=>{const n=e||t.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(t.subarray(0,e));let r,i;const a=new Array(2*n);for(i=0,r=0;r<n;){let e=t[r++];if(e<128){a[i++]=e;continue}let o=vt[e];if(o>4)a[i++]=65533,r+=o-1;else{for(e&=2===o?31:3===o?15:7;o>1&&r<n;)e=e<<6|63&t[r++],o--;o>1?a[i++]=65533:e<65536?a[i++]=e:(e-=65536,a[i++]=55296|e>>10&1023,a[i++]=56320|1023&e)}}return((t,e)=>{if(e<65534&&t.subarray&&bt)return String.fromCharCode.apply(null,t.length===e?t:t.subarray(0,e));let n="";for(let r=0;r<e;r++)n+=String.fromCharCode(t[r]);return n})(a,i)},xt=(t,e)=>{(e=e||t.length)>t.length&&(e=t.length);let n=e-1;for(;n>=0&&128==(192&t[n]);)n--;return n<0||0===n?e:n+vt[t[n]]>e?n:e},_t=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0};const At=16209;var Lt=function(t,e){let n,r,i,a,o,s,l,h,c,u,f,d,p,g,m,b,v,y,w,x,_,A,L,N;const S=t.state;n=t.next_in,L=t.input,r=n+(t.avail_in-5),i=t.next_out,N=t.output,a=i-(e-t.avail_out),o=i+(t.avail_out-257),s=S.dmax,l=S.wsize,h=S.whave,c=S.wnext,u=S.window,f=S.hold,d=S.bits,p=S.lencode,g=S.distcode,m=(1<<S.lenbits)-1,b=(1<<S.distbits)-1;t:do{d<15&&(f+=L[n++]<<d,d+=8,f+=L[n++]<<d,d+=8),v=p[f&m];e:for(;;){if(y=v>>>24,f>>>=y,d-=y,y=v>>>16&255,0===y)N[i++]=65535&v;else{if(!(16&y)){if(64&y){if(32&y){S.mode=16191;break t}t.msg="invalid literal/length code",S.mode=At;break t}v=p[(65535&v)+(f&(1<<y)-1)];continue e}for(w=65535&v,y&=15,y&&(d<y&&(f+=L[n++]<<d,d+=8),w+=f&(1<<y)-1,f>>>=y,d-=y),d<15&&(f+=L[n++]<<d,d+=8,f+=L[n++]<<d,d+=8),v=g[f&b];;){if(y=v>>>24,f>>>=y,d-=y,y=v>>>16&255,16&y){if(x=65535&v,y&=15,d<y&&(f+=L[n++]<<d,d+=8,d<y&&(f+=L[n++]<<d,d+=8)),x+=f&(1<<y)-1,x>s){t.msg="invalid distance too far back",S.mode=At;break t}if(f>>>=y,d-=y,y=i-a,x>y){if(y=x-y,y>h&&S.sane){t.msg="invalid distance too far back",S.mode=At;break t}if(_=0,A=u,0===c){if(_+=l-y,y<w){w-=y;do{N[i++]=u[_++]}while(--y);_=i-x,A=N}}else if(c<y){if(_+=l+c-y,y-=c,y<w){w-=y;do{N[i++]=u[_++]}while(--y);if(_=0,c<w){y=c,w-=y;do{N[i++]=u[_++]}while(--y);_=i-x,A=N}}}else if(_+=c-y,y<w){w-=y;do{N[i++]=u[_++]}while(--y);_=i-x,A=N}for(;w>2;)N[i++]=A[_++],N[i++]=A[_++],N[i++]=A[_++],w-=3;w&&(N[i++]=A[_++],w>1&&(N[i++]=A[_++]))}else{_=i-x;do{N[i++]=N[_++],N[i++]=N[_++],N[i++]=N[_++],w-=3}while(w>2);w&&(N[i++]=N[_++],w>1&&(N[i++]=N[_++]))}break}if(64&y){t.msg="invalid distance code",S.mode=At;break t}v=g[(65535&v)+(f&(1<<y)-1)]}}break}}while(n<r&&i<o);w=d>>3,n-=w,d-=w<<3,f&=(1<<d)-1,t.next_in=n,t.next_out=i,t.avail_in=n<r?r-n+5:5-(n-r),t.avail_out=i<o?o-i+257:257-(i-o),S.hold=f,S.bits=d};const Nt=15,St=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),kt=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Pt=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Ft=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]);var Ct=(t,e,n,r,i,a,o,s)=>{const l=s.bits;let h,c,u,f,d,p,g=0,m=0,b=0,v=0,y=0,w=0,x=0,_=0,A=0,L=0,N=null;const S=new Uint16Array(16),k=new Uint16Array(16);let P,F,C,I=null;for(g=0;g<=Nt;g++)S[g]=0;for(m=0;m<r;m++)S[e[n+m]]++;for(y=l,v=Nt;v>=1&&0===S[v];v--);if(y>v&&(y=v),0===v)return i[a++]=20971520,i[a++]=20971520,s.bits=1,0;for(b=1;b<v&&0===S[b];b++);for(y<b&&(y=b),_=1,g=1;g<=Nt;g++)if(_<<=1,_-=S[g],_<0)return-1;if(_>0&&(0===t||1!==v))return-1;for(k[1]=0,g=1;g<Nt;g++)k[g+1]=k[g]+S[g];for(m=0;m<r;m++)0!==e[n+m]&&(o[k[e[n+m]]++]=m);if(0===t?(N=I=o,p=20):1===t?(N=St,I=kt,p=257):(N=Pt,I=Ft,p=0),L=0,m=0,g=b,d=a,w=y,x=0,u=-1,A=1<<y,f=A-1,1===t&&A>852||2===t&&A>592)return 1;for(;;){P=g-x,o[m]+1<p?(F=0,C=o[m]):o[m]>=p?(F=I[o[m]-p],C=N[o[m]-p]):(F=96,C=0),h=1<<g-x,c=1<<w,b=c;do{c-=h,i[d+(L>>x)+c]=P<<24|F<<16|C}while(0!==c);for(h=1<<g-1;L&h;)h>>=1;if(0!==h?(L&=h-1,L+=h):L=0,m++,0===--S[g]){if(g===v)break;g=e[n+o[m]]}if(g>y&&(L&f)!==u){for(0===x&&(x=y),d+=b,w=g-x,_=1<<w;w+x<v&&(_-=S[w+x],!(_<=0));)w++,_<<=1;if(A+=1<<w,1===t&&A>852||2===t&&A>592)return 1;u=L&f,i[u]=y<<24|w<<16|d-a}}return 0!==L&&(i[d+L]=g-x<<24|64<<16),s.bits=y,0};const{Z_FINISH:It,Z_BLOCK:jt,Z_TREES:Ot,Z_OK:Dt,Z_STREAM_END:Et,Z_NEED_DICT:Bt,Z_STREAM_ERROR:Mt,Z_DATA_ERROR:Tt,Z_MEM_ERROR:Rt,Z_BUF_ERROR:qt,Z_DEFLATED:Ut}=dt,zt=16180,Ht=16190,Wt=16191,Gt=16192,Vt=16194,Kt=16199,Yt=16200,Jt=16206,Xt=16209,$t=t=>(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24);function Zt(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const Qt=t=>{if(!t)return 1;const e=t.state;return!e||e.strm!==t||e.mode<zt||e.mode>16211?1:0},te=t=>{if(Qt(t))return Mt;const e=t.state;return t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=zt,e.last=0,e.havedict=0,e.flags=-1,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new Int32Array(852),e.distcode=e.distdyn=new Int32Array(592),e.sane=1,e.back=-1,Dt},ee=t=>{if(Qt(t))return Mt;const e=t.state;return e.wsize=0,e.whave=0,e.wnext=0,te(t)},ne=(t,e)=>{let n;if(Qt(t))return Mt;const r=t.state;return e<0?(n=0,e=-e):(n=5+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?Mt:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=n,r.wbits=e,ee(t))},re=(t,e)=>{if(!t)return Mt;const n=new Zt;t.state=n,n.strm=t,n.window=null,n.mode=zt;const r=ne(t,e);return r!==Dt&&(t.state=null),r};let ie,ae,oe=!0;const se=t=>{if(oe){ie=new Int32Array(512),ae=new Int32Array(32);let e=0;for(;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(Ct(1,t.lens,0,288,ie,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;Ct(2,t.lens,0,32,ae,0,t.work,{bits:5}),oe=!1}t.lencode=ie,t.lenbits=9,t.distcode=ae,t.distbits=5},le=(t,e,n,r)=>{let i;const a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new Uint8Array(a.wsize)),r>=a.wsize?(a.window.set(e.subarray(n-a.wsize,n),0),a.wnext=0,a.whave=a.wsize):(i=a.wsize-a.wnext,i>r&&(i=r),a.window.set(e.subarray(n-r,n-r+i),a.wnext),(r-=i)?(a.window.set(e.subarray(n-r,n),0),a.wnext=r,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0};var he={inflateReset:ee,inflateReset2:ne,inflateResetKeep:te,inflateInit:t=>re(t,15),inflateInit2:re,inflate:(t,e)=>{let n,r,i,a,o,s,l,h,c,u,f,d,p,g,m,b,v,y,w,x,_,A,L=0;const N=new Uint8Array(4);let S,k;const P=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(Qt(t)||!t.output||!t.input&&0!==t.avail_in)return Mt;n=t.state,n.mode===Wt&&(n.mode=Gt),o=t.next_out,i=t.output,l=t.avail_out,a=t.next_in,r=t.input,s=t.avail_in,h=n.hold,c=n.bits,u=s,f=l,A=Dt;t:for(;;)switch(n.mode){case zt:if(0===n.wrap){n.mode=Gt;break}for(;c<16;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(2&n.wrap&&35615===h){0===n.wbits&&(n.wbits=15),n.check=0,N[0]=255&h,N[1]=h>>>8&255,n.check=ut(n.check,N,2,0),h=0,c=0,n.mode=16181;break}if(n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&h)<<8)+(h>>8))%31){t.msg="incorrect header check",n.mode=Xt;break}if((15&h)!==Ut){t.msg="unknown compression method",n.mode=Xt;break}if(h>>>=4,c-=4,_=8+(15&h),0===n.wbits&&(n.wbits=_),_>15||_>n.wbits){t.msg="invalid window size",n.mode=Xt;break}n.dmax=1<<n.wbits,n.flags=0,t.adler=n.check=1,n.mode=512&h?16189:Wt,h=0,c=0;break;case 16181:for(;c<16;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(n.flags=h,(255&n.flags)!==Ut){t.msg="unknown compression method",n.mode=Xt;break}if(57344&n.flags){t.msg="unknown header flags set",n.mode=Xt;break}n.head&&(n.head.text=h>>8&1),512&n.flags&&4&n.wrap&&(N[0]=255&h,N[1]=h>>>8&255,n.check=ut(n.check,N,2,0)),h=0,c=0,n.mode=16182;case 16182:for(;c<32;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}n.head&&(n.head.time=h),512&n.flags&&4&n.wrap&&(N[0]=255&h,N[1]=h>>>8&255,N[2]=h>>>16&255,N[3]=h>>>24&255,n.check=ut(n.check,N,4,0)),h=0,c=0,n.mode=16183;case 16183:for(;c<16;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}n.head&&(n.head.xflags=255&h,n.head.os=h>>8),512&n.flags&&4&n.wrap&&(N[0]=255&h,N[1]=h>>>8&255,n.check=ut(n.check,N,2,0)),h=0,c=0,n.mode=16184;case 16184:if(1024&n.flags){for(;c<16;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}n.length=h,n.head&&(n.head.extra_len=h),512&n.flags&&4&n.wrap&&(N[0]=255&h,N[1]=h>>>8&255,n.check=ut(n.check,N,2,0)),h=0,c=0}else n.head&&(n.head.extra=null);n.mode=16185;case 16185:if(1024&n.flags&&(d=n.length,d>s&&(d=s),d&&(n.head&&(_=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Uint8Array(n.head.extra_len)),n.head.extra.set(r.subarray(a,a+d),_)),512&n.flags&&4&n.wrap&&(n.check=ut(n.check,r,d,a)),s-=d,a+=d,n.length-=d),n.length))break t;n.length=0,n.mode=16186;case 16186:if(2048&n.flags){if(0===s)break t;d=0;do{_=r[a+d++],n.head&&_&&n.length<65536&&(n.head.name+=String.fromCharCode(_))}while(_&&d<s);if(512&n.flags&&4&n.wrap&&(n.check=ut(n.check,r,d,a)),s-=d,a+=d,_)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=16187;case 16187:if(4096&n.flags){if(0===s)break t;d=0;do{_=r[a+d++],n.head&&_&&n.length<65536&&(n.head.comment+=String.fromCharCode(_))}while(_&&d<s);if(512&n.flags&&4&n.wrap&&(n.check=ut(n.check,r,d,a)),s-=d,a+=d,_)break t}else n.head&&(n.head.comment=null);n.mode=16188;case 16188:if(512&n.flags){for(;c<16;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(4&n.wrap&&h!==(65535&n.check)){t.msg="header crc mismatch",n.mode=Xt;break}h=0,c=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),t.adler=n.check=0,n.mode=Wt;break;case 16189:for(;c<32;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}t.adler=n.check=$t(h),h=0,c=0,n.mode=Ht;case Ht:if(0===n.havedict)return t.next_out=o,t.avail_out=l,t.next_in=a,t.avail_in=s,n.hold=h,n.bits=c,Bt;t.adler=n.check=1,n.mode=Wt;case Wt:if(e===jt||e===Ot)break t;case Gt:if(n.last){h>>>=7&c,c-=7&c,n.mode=Jt;break}for(;c<3;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}switch(n.last=1&h,h>>>=1,c-=1,3&h){case 0:n.mode=16193;break;case 1:if(se(n),n.mode=Kt,e===Ot){h>>>=2,c-=2;break t}break;case 2:n.mode=16196;break;case 3:t.msg="invalid block type",n.mode=Xt}h>>>=2,c-=2;break;case 16193:for(h>>>=7&c,c-=7&c;c<32;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if((65535&h)!=(h>>>16^65535)){t.msg="invalid stored block lengths",n.mode=Xt;break}if(n.length=65535&h,h=0,c=0,n.mode=Vt,e===Ot)break t;case Vt:n.mode=16195;case 16195:if(d=n.length,d){if(d>s&&(d=s),d>l&&(d=l),0===d)break t;i.set(r.subarray(a,a+d),o),s-=d,a+=d,l-=d,o+=d,n.length-=d;break}n.mode=Wt;break;case 16196:for(;c<14;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(n.nlen=257+(31&h),h>>>=5,c-=5,n.ndist=1+(31&h),h>>>=5,c-=5,n.ncode=4+(15&h),h>>>=4,c-=4,n.nlen>286||n.ndist>30){t.msg="too many length or distance symbols",n.mode=Xt;break}n.have=0,n.mode=16197;case 16197:for(;n.have<n.ncode;){for(;c<3;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}n.lens[P[n.have++]]=7&h,h>>>=3,c-=3}for(;n.have<19;)n.lens[P[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,S={bits:n.lenbits},A=Ct(0,n.lens,0,19,n.lencode,0,n.work,S),n.lenbits=S.bits,A){t.msg="invalid code lengths set",n.mode=Xt;break}n.have=0,n.mode=16198;case 16198:for(;n.have<n.nlen+n.ndist;){for(;L=n.lencode[h&(1<<n.lenbits)-1],m=L>>>24,b=L>>>16&255,v=65535&L,!(m<=c);){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(v<16)h>>>=m,c-=m,n.lens[n.have++]=v;else{if(16===v){for(k=m+2;c<k;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(h>>>=m,c-=m,0===n.have){t.msg="invalid bit length repeat",n.mode=Xt;break}_=n.lens[n.have-1],d=3+(3&h),h>>>=2,c-=2}else if(17===v){for(k=m+3;c<k;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}h>>>=m,c-=m,_=0,d=3+(7&h),h>>>=3,c-=3}else{for(k=m+7;c<k;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}h>>>=m,c-=m,_=0,d=11+(127&h),h>>>=7,c-=7}if(n.have+d>n.nlen+n.ndist){t.msg="invalid bit length repeat",n.mode=Xt;break}for(;d--;)n.lens[n.have++]=_}}if(n.mode===Xt)break;if(0===n.lens[256]){t.msg="invalid code -- missing end-of-block",n.mode=Xt;break}if(n.lenbits=9,S={bits:n.lenbits},A=Ct(1,n.lens,0,n.nlen,n.lencode,0,n.work,S),n.lenbits=S.bits,A){t.msg="invalid literal/lengths set",n.mode=Xt;break}if(n.distbits=6,n.distcode=n.distdyn,S={bits:n.distbits},A=Ct(2,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,S),n.distbits=S.bits,A){t.msg="invalid distances set",n.mode=Xt;break}if(n.mode=Kt,e===Ot)break t;case Kt:n.mode=Yt;case Yt:if(s>=6&&l>=258){t.next_out=o,t.avail_out=l,t.next_in=a,t.avail_in=s,n.hold=h,n.bits=c,Lt(t,f),o=t.next_out,i=t.output,l=t.avail_out,a=t.next_in,r=t.input,s=t.avail_in,h=n.hold,c=n.bits,n.mode===Wt&&(n.back=-1);break}for(n.back=0;L=n.lencode[h&(1<<n.lenbits)-1],m=L>>>24,b=L>>>16&255,v=65535&L,!(m<=c);){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(b&&!(240&b)){for(y=m,w=b,x=v;L=n.lencode[x+((h&(1<<y+w)-1)>>y)],m=L>>>24,b=L>>>16&255,v=65535&L,!(y+m<=c);){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}h>>>=y,c-=y,n.back+=y}if(h>>>=m,c-=m,n.back+=m,n.length=v,0===b){n.mode=16205;break}if(32&b){n.back=-1,n.mode=Wt;break}if(64&b){t.msg="invalid literal/length code",n.mode=Xt;break}n.extra=15&b,n.mode=16201;case 16201:if(n.extra){for(k=n.extra;c<k;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}n.length+=h&(1<<n.extra)-1,h>>>=n.extra,c-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=16202;case 16202:for(;L=n.distcode[h&(1<<n.distbits)-1],m=L>>>24,b=L>>>16&255,v=65535&L,!(m<=c);){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(!(240&b)){for(y=m,w=b,x=v;L=n.distcode[x+((h&(1<<y+w)-1)>>y)],m=L>>>24,b=L>>>16&255,v=65535&L,!(y+m<=c);){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}h>>>=y,c-=y,n.back+=y}if(h>>>=m,c-=m,n.back+=m,64&b){t.msg="invalid distance code",n.mode=Xt;break}n.offset=v,n.extra=15&b,n.mode=16203;case 16203:if(n.extra){for(k=n.extra;c<k;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}n.offset+=h&(1<<n.extra)-1,h>>>=n.extra,c-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){t.msg="invalid distance too far back",n.mode=Xt;break}n.mode=16204;case 16204:if(0===l)break t;if(d=f-l,n.offset>d){if(d=n.offset-d,d>n.whave&&n.sane){t.msg="invalid distance too far back",n.mode=Xt;break}d>n.wnext?(d-=n.wnext,p=n.wsize-d):p=n.wnext-d,d>n.length&&(d=n.length),g=n.window}else g=i,p=o-n.offset,d=n.length;d>l&&(d=l),l-=d,n.length-=d;do{i[o++]=g[p++]}while(--d);0===n.length&&(n.mode=Yt);break;case 16205:if(0===l)break t;i[o++]=n.length,l--,n.mode=Yt;break;case Jt:if(n.wrap){for(;c<32;){if(0===s)break t;s--,h|=r[a++]<<c,c+=8}if(f-=l,t.total_out+=f,n.total+=f,4&n.wrap&&f&&(t.adler=n.check=n.flags?ut(n.check,i,f,o-f):ht(n.check,i,f,o-f)),f=l,4&n.wrap&&(n.flags?h:$t(h))!==n.check){t.msg="incorrect data check",n.mode=Xt;break}h=0,c=0}n.mode=16207;case 16207:if(n.wrap&&n.flags){for(;c<32;){if(0===s)break t;s--,h+=r[a++]<<c,c+=8}if(4&n.wrap&&h!==(**********&n.total)){t.msg="incorrect length check",n.mode=Xt;break}h=0,c=0}n.mode=16208;case 16208:A=Et;break t;case Xt:A=Tt;break t;case 16210:return Rt;default:return Mt}return t.next_out=o,t.avail_out=l,t.next_in=a,t.avail_in=s,n.hold=h,n.bits=c,(n.wsize||f!==t.avail_out&&n.mode<Xt&&(n.mode<Jt||e!==It))&&le(t,t.output,t.next_out,f-t.avail_out),u-=t.avail_in,f-=t.avail_out,t.total_in+=u,t.total_out+=f,n.total+=f,4&n.wrap&&f&&(t.adler=n.check=n.flags?ut(n.check,i,f,t.next_out-f):ht(n.check,i,f,t.next_out-f)),t.data_type=n.bits+(n.last?64:0)+(n.mode===Wt?128:0)+(n.mode===Kt||n.mode===Vt?256:0),(0===u&&0===f||e===It)&&A===Dt&&(A=qt),A},inflateEnd:t=>{if(Qt(t))return Mt;let e=t.state;return e.window&&(e.window=null),t.state=null,Dt},inflateGetHeader:(t,e)=>{if(Qt(t))return Mt;const n=t.state;return 2&n.wrap?(n.head=e,e.done=!1,Dt):Mt},inflateSetDictionary:(t,e)=>{const n=e.length;let r,i,a;return Qt(t)?Mt:(r=t.state,0!==r.wrap&&r.mode!==Ht?Mt:r.mode===Ht&&(i=1,i=ht(i,e,n,0),i!==r.check)?Tt:(a=le(t,e,n,n),a?(r.mode=16210,Rt):(r.havedict=1,Dt)))},inflateInfo:"pako inflate (from Nodeca project)"},ce=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1};const ue=Object.prototype.toString,{Z_NO_FLUSH:fe,Z_FINISH:de,Z_OK:pe,Z_STREAM_END:ge,Z_NEED_DICT:me,Z_STREAM_ERROR:be,Z_DATA_ERROR:ve,Z_MEM_ERROR:ye}=dt;function we(t){this.options=gt({chunkSize:65536,windowBits:15,to:""},t||{});const e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&(15&e.windowBits||(e.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new _t,this.strm.avail_out=0;let n=he.inflateInit2(this.strm,e.windowBits);if(n!==pe)throw new Error(ft[n]);if(this.header=new ce,he.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=yt(e.dictionary):"[object ArrayBuffer]"===ue.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw&&(n=he.inflateSetDictionary(this.strm,e.dictionary),n!==pe)))throw new Error(ft[n])}we.prototype.push=function(t,e){const n=this.strm,r=this.options.chunkSize,i=this.options.dictionary;let a,o,s;if(this.ended)return!1;for(o=e===~~e?e:!0===e?de:fe,"[object ArrayBuffer]"===ue.call(t)?n.input=new Uint8Array(t):n.input=t,n.next_in=0,n.avail_in=n.input.length;;){for(0===n.avail_out&&(n.output=new Uint8Array(r),n.next_out=0,n.avail_out=r),a=he.inflate(n,o),a===me&&i&&(a=he.inflateSetDictionary(n,i),a===pe?a=he.inflate(n,o):a===ve&&(a=me));n.avail_in>0&&a===ge&&n.state.wrap>0&&0!==t[n.next_in];)he.inflateReset(n),a=he.inflate(n,o);switch(a){case be:case ve:case me:case ye:return this.onEnd(a),this.ended=!0,!1}if(s=n.avail_out,n.next_out&&(0===n.avail_out||a===ge))if("string"===this.options.to){let t=xt(n.output,n.next_out),e=n.next_out-t,i=wt(n.output,t);n.next_out=e,n.avail_out=r-e,e&&n.output.set(n.output.subarray(t,t+e),0),this.onData(i)}else this.onData(n.output.length===n.next_out?n.output:n.output.subarray(0,n.next_out));if(a!==pe||0!==s){if(a===ge)return a=he.inflateEnd(this.strm),this.onEnd(a),this.ended=!0,!0;if(0===n.avail_in)break}}return!0},we.prototype.onData=function(t){this.chunks.push(t)},we.prototype.onEnd=function(t){t===pe&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=mt(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg};var xe={Inflate:we,inflate:function(t,e){const n=new we(e);if(n.push(t),n.err)throw n.msg||ft[n.err];return n.result}};const{Inflate:_e,inflate:Ae}=xe;var Le=_e,Ne=Ae;const Se=[];for(let t=0;t<256;t++){let e=t;for(let t=0;t<8;t++)1&e?e=3988292384^e>>>1:e>>>=1;Se[t]=e}const ke=**********;function Pe(t,e){return(function(t,e,n){let r=t;for(let i=0;i<n;i++)r=Se[255&(r^e[i])]^r>>>8;return r}(ke,t,e)^ke)>>>0}function Fe(t,e,n){const r=t.readUint32(),i=Pe(new Uint8Array(t.buffer,t.byteOffset+t.offset-e-4,e),e);if(i!==r)throw new Error(`CRC mismatch for chunk ${n}. Expected ${r}, found ${i}`)}function Ce(t,e,n){for(let r=0;r<n;r++)e[r]=t[r]}function Ie(t,e,n,r){let i=0;for(;i<r;i++)e[i]=t[i];for(;i<n;i++)e[i]=t[i]+e[i-r]&255}function je(t,e,n,r){let i=0;if(0===n.length)for(;i<r;i++)e[i]=t[i];else for(;i<r;i++)e[i]=t[i]+n[i]&255}function Oe(t,e,n,r,i){let a=0;if(0===n.length){for(;a<i;a++)e[a]=t[a];for(;a<r;a++)e[a]=t[a]+(e[a-i]>>1)&255}else{for(;a<i;a++)e[a]=t[a]+(n[a]>>1)&255;for(;a<r;a++)e[a]=t[a]+(e[a-i]+n[a]>>1)&255}}function De(t,e,n,r,i){let a=0;if(0===n.length){for(;a<i;a++)e[a]=t[a];for(;a<r;a++)e[a]=t[a]+e[a-i]&255}else{for(;a<i;a++)e[a]=t[a]+n[a]&255;for(;a<r;a++)e[a]=t[a]+Ee(e[a-i],n[a],n[a-i])&255}}function Ee(t,e,n){const r=t+e-n,i=Math.abs(r-t),a=Math.abs(r-e),o=Math.abs(r-n);return i<=a&&i<=o?t:a<=o?e:n}function Be(t,e,n,r,i,a){switch(t){case 0:Ce(e,n,i);break;case 1:Ie(e,n,i,a);break;case 2:je(e,n,r,i);break;case 3:Oe(e,n,r,i,a);break;case 4:De(e,n,r,i,a);break;default:throw new Error(`Unsupported filter: ${t}`)}}const Me=new Uint16Array([255]),Te=255===new Uint8Array(Me.buffer)[0];function Re(t){return(255&t)<<8|t>>8&255}const qe=new Uint16Array([255]),Ue=255===new Uint8Array(qe.buffer)[0],ze=new Uint8Array(0);function He(t){const{data:e,width:n,height:r,channels:i,depth:a}=t,o=Math.ceil(a/8)*i,s=Math.ceil(a/8*i*n),l=new Uint8Array(r*s);let h,c,u=ze,f=0;for(let d=0;d<r;d++){switch(h=e.subarray(f+1,f+1+s),c=l.subarray(d*s,(d+1)*s),e[f]){case 0:Ce(h,c,s);break;case 1:Ie(h,c,s,o);break;case 2:je(h,c,u,s);break;case 3:Oe(h,c,u,s,o);break;case 4:De(h,c,u,s,o);break;default:throw new Error(`Unsupported filter: ${e[f]}`)}u=c,f+=s+1}if(16===a){const t=new Uint16Array(l.buffer);if(Ue)for(let e=0;e<t.length;e++)t[e]=We(t[e]);return t}return l}function We(t){return(255&t)<<8|t>>8&255}const Ge=Uint8Array.of(137,80,78,71,13,10,26,10);function Ve(t){if(!function(t){if(t.length<Ge.length)return!1;for(let e=0;e<Ge.length;e++)if(t[e]!==Ge[e])return!1;return!0}(t.readBytes(Ge.length)))throw new Error("wrong PNG signature")}const Ke=new TextDecoder("latin1");function Ye(t){if(function(t){if(!Je.test(t))throw new Error("invalid latin1 text")}(t),0===t.length||t.length>79)throw new Error("keyword length must be between 1 and 79")}const Je=/^[\u0000-\u00FF]*$/;function Xe(t,e,n){const r=$e(e);t[r]=function(t,e){return Ke.decode(t.readBytes(e))}(e,n-r.length-1)}function $e(t){for(t.mark();0!==t.readByte(););const e=t.offset;t.reset();const n=Ke.decode(t.readBytes(e-t.offset-1));return t.skip(1),Ye(n),n}const Ze=-1,Qe=0,tn=2,en=3,nn=4,rn=6,an=-1,on=0,sn=-1,ln=0,hn=-1,cn=0,un=1,fn=0,dn=1,pn=2,gn=0,mn=1;class bn extends st{_checkCrc;_inflator;_png;_apng;_end;_hasPalette;_palette;_hasTransparency;_transparency;_compressionMethod;_filterMethod;_interlaceMethod;_colorType;_isAnimated;_numberOfFrames;_numberOfPlays;_frames;_writingDataChunks;constructor(t,e={}){super(t);const{checkCrc:n=!1}=e;this._checkCrc=n,this._inflator=new Le,this._png={width:-1,height:-1,channels:-1,data:new Uint8Array(0),depth:1,text:{}},this._apng={width:-1,height:-1,channels:-1,depth:1,numberOfFrames:1,numberOfPlays:0,text:{},frames:[]},this._end=!1,this._hasPalette=!1,this._palette=[],this._hasTransparency=!1,this._transparency=new Uint16Array(0),this._compressionMethod=an,this._filterMethod=sn,this._interlaceMethod=hn,this._colorType=Ze,this._isAnimated=!1,this._numberOfFrames=1,this._numberOfPlays=0,this._frames=[],this._writingDataChunks=!1,this.setBigEndian()}decode(){for(Ve(this);!this._end;){const t=this.readUint32(),e=this.readChars(4);this.decodeChunk(t,e)}return this.decodeImage(),this._png}decodeApng(){for(Ve(this);!this._end;){const t=this.readUint32(),e=this.readChars(4);this.decodeApngChunk(t,e)}return this.decodeApngImage(),this._apng}decodeChunk(t,e){const n=this.offset;switch(e){case"IHDR":this.decodeIHDR();break;case"PLTE":this.decodePLTE(t);break;case"IDAT":this.decodeIDAT(t);break;case"IEND":this._end=!0;break;case"tRNS":this.decodetRNS(t);break;case"iCCP":this.decodeiCCP(t);break;case"tEXt":Xe(this._png.text,this,t);break;case"pHYs":this.decodepHYs();break;default:this.skip(t)}if(this.offset-n!==t)throw new Error(`Length mismatch while decoding chunk ${e}`);this._checkCrc?Fe(this,t+4,e):this.skip(4)}decodeApngChunk(t,e){const n=this.offset;switch("fdAT"!==e&&"IDAT"!==e&&this._writingDataChunks&&this.pushDataToFrame(),e){case"acTL":this.decodeACTL();break;case"fcTL":this.decodeFCTL();break;case"fdAT":this.decodeFDAT(t);break;default:this.decodeChunk(t,e),this.offset=n+t}if(this.offset-n!==t)throw new Error(`Length mismatch while decoding chunk ${e}`);this._checkCrc?Fe(this,t+4,e):this.skip(4)}decodeIHDR(){const t=this._png;t.width=this.readUint32(),t.height=this.readUint32(),t.depth=function(t){if(1!==t&&2!==t&&4!==t&&8!==t&&16!==t)throw new Error(`invalid bit depth: ${t}`);return t}(this.readUint8());const e=this.readUint8();let n;switch(this._colorType=e,e){case Qe:n=1;break;case tn:n=3;break;case en:n=1;break;case nn:n=2;break;case rn:n=4;break;default:throw new Error(`Unknown color type: ${e}`)}if(this._png.channels=n,this._compressionMethod=this.readUint8(),this._compressionMethod!==on)throw new Error(`Unsupported compression method: ${this._compressionMethod}`);this._filterMethod=this.readUint8(),this._interlaceMethod=this.readUint8()}decodeACTL(){this._numberOfFrames=this.readUint32(),this._numberOfPlays=this.readUint32(),this._isAnimated=!0}decodeFCTL(){const t={sequenceNumber:this.readUint32(),width:this.readUint32(),height:this.readUint32(),xOffset:this.readUint32(),yOffset:this.readUint32(),delayNumber:this.readUint16(),delayDenominator:this.readUint16(),disposeOp:this.readUint8(),blendOp:this.readUint8(),data:new Uint8Array(0)};this._frames.push(t)}decodePLTE(t){if(t%3!=0)throw new RangeError(`PLTE field length must be a multiple of 3. Got ${t}`);const e=t/3;this._hasPalette=!0;const n=[];this._palette=n;for(let r=0;r<e;r++)n.push([this.readUint8(),this.readUint8(),this.readUint8()])}decodeIDAT(t){this._writingDataChunks=!0;const e=t,n=this.offset+this.byteOffset;if(this._inflator.push(new Uint8Array(this.buffer,n,e)),this._inflator.err)throw new Error(`Error while decompressing the data: ${this._inflator.err}`);this.skip(t)}decodeFDAT(t){this._writingDataChunks=!0;let e=t,n=this.offset+this.byteOffset;if(n+=4,e-=4,this._inflator.push(new Uint8Array(this.buffer,n,e)),this._inflator.err)throw new Error(`Error while decompressing the data: ${this._inflator.err}`);this.skip(t)}decodetRNS(t){switch(this._colorType){case Qe:case tn:if(t%2!=0)throw new RangeError(`tRNS chunk length must be a multiple of 2. Got ${t}`);if(t/2>this._png.width*this._png.height)throw new Error(`tRNS chunk contains more alpha values than there are pixels (${t/2} vs ${this._png.width*this._png.height})`);this._hasTransparency=!0,this._transparency=new Uint16Array(t/2);for(let e=0;e<t/2;e++)this._transparency[e]=this.readUint16();break;case en:{if(t>this._palette.length)throw new Error(`tRNS chunk contains more alpha values than there are palette colors (${t} vs ${this._palette.length})`);let e=0;for(;e<t;e++){const t=this.readByte();this._palette[e].push(t)}for(;e<this._palette.length;e++)this._palette[e].push(255);break}default:throw new Error(`tRNS chunk is not supported for color type ${this._colorType}`)}}decodeiCCP(t){const e=$e(this),n=this.readUint8();if(n!==on)throw new Error(`Unsupported iCCP compression method: ${n}`);const r=this.readBytes(t-e.length-2);this._png.iccEmbeddedProfile={name:e,profile:Ne(r)}}decodepHYs(){const t=this.readUint32(),e=this.readUint32(),n=this.readByte();this._png.resolution={x:t,y:e,unit:n}}decodeApngImage(){this._apng.width=this._png.width,this._apng.height=this._png.height,this._apng.channels=this._png.channels,this._apng.depth=this._png.depth,this._apng.numberOfFrames=this._numberOfFrames,this._apng.numberOfPlays=this._numberOfPlays,this._apng.text=this._png.text,this._apng.resolution=this._png.resolution;for(let t=0;t<this._numberOfFrames;t++){const e={sequenceNumber:this._frames[t].sequenceNumber,delayNumber:this._frames[t].delayNumber,delayDenominator:this._frames[t].delayDenominator,data:8===this._apng.depth?new Uint8Array(this._apng.width*this._apng.height*this._apng.channels):new Uint16Array(this._apng.width*this._apng.height*this._apng.channels)},n=this._frames.at(t);if(n){if(n.data=He({data:n.data,width:n.width,height:n.height,channels:this._apng.channels,depth:this._apng.depth}),this._hasPalette&&(this._apng.palette=this._palette),this._hasTransparency&&(this._apng.transparency=this._transparency),0===t||0===n.xOffset&&0===n.yOffset&&n.width===this._png.width&&n.height===this._png.height)e.data=n.data;else{const r=this._apng.frames.at(t-1);this.disposeFrame(n,r,e),this.addFrameDataToCanvas(e,n)}this._apng.frames.push(e)}}return this._apng}disposeFrame(t,e,n){switch(t.disposeOp){case fn:break;case dn:for(let e=0;e<this._png.height;e++)for(let r=0;r<this._png.width;r++){const i=(e*t.width+r)*this._png.channels;for(let t=0;t<this._png.channels;t++)n.data[i+t]=0}break;case pn:n.data.set(e.data);break;default:throw new Error("Unknown disposeOp")}}addFrameDataToCanvas(t,e){const n=1<<this._png.depth,r=(t,n)=>({index:((t+e.yOffset)*this._png.width+e.xOffset+n)*this._png.channels,frameIndex:(t*e.width+n)*this._png.channels});switch(e.blendOp){case gn:for(let n=0;n<e.height;n++)for(let i=0;i<e.width;i++){const{index:a,frameIndex:o}=r(n,i);for(let n=0;n<this._png.channels;n++)t.data[a+n]=e.data[o+n]}break;case mn:for(let i=0;i<e.height;i++)for(let a=0;a<e.width;a++){const{index:o,frameIndex:s}=r(i,a);for(let r=0;r<this._png.channels;r++){const i=e.data[s+this._png.channels-1]/n,a=r%(this._png.channels-1)==0?1:e.data[s+r],l=Math.floor(i*a+(1-i)*t.data[o+r]);t.data[o+r]+=l}}break;default:throw new Error("Unknown blendOp")}}decodeImage(){if(this._inflator.err)throw new Error(`Error while decompressing the data: ${this._inflator.err}`);const t=this._isAnimated?(this._frames?.at(0)).data:this._inflator.result;if(this._filterMethod!==ln)throw new Error(`Filter method ${this._filterMethod} not supported`);if(this._interlaceMethod===cn)this._png.data=He({data:t,width:this._png.width,height:this._png.height,channels:this._png.channels,depth:this._png.depth});else{if(this._interlaceMethod!==un)throw new Error(`Interlace method ${this._interlaceMethod} not supported`);this._png.data=function(t){const{data:e,width:n,height:r,channels:i,depth:a}=t,o=[{x:0,y:0,xStep:8,yStep:8},{x:4,y:0,xStep:8,yStep:8},{x:0,y:4,xStep:4,yStep:8},{x:2,y:0,xStep:4,yStep:4},{x:0,y:2,xStep:2,yStep:4},{x:1,y:0,xStep:2,yStep:2},{x:0,y:1,xStep:1,yStep:2}],s=Math.ceil(a/8)*i,l=new Uint8Array(r*n*s);let h=0;for(let c=0;c<7;c++){const t=o[c],i=Math.ceil((n-t.x)/t.xStep),a=Math.ceil((r-t.y)/t.yStep);if(i<=0||a<=0)continue;const u=i*s,f=new Uint8Array(u);for(let o=0;o<a;o++){const a=e[h++],c=e.subarray(h,h+u);h+=u;const d=new Uint8Array(u);Be(a,c,d,f,u,s),f.set(d);for(let e=0;e<i;e++){const i=t.x+e*t.xStep,a=t.y+o*t.yStep;if(!(i>=n||a>=r))for(let t=0;t<s;t++)l[(a*n+i)*s+t]=d[e*s+t]}}}if(16===a){const t=new Uint16Array(l.buffer);if(Te)for(let e=0;e<t.length;e++)t[e]=Re(t[e]);return t}return l}({data:t,width:this._png.width,height:this._png.height,channels:this._png.channels,depth:this._png.depth})}this._hasPalette&&(this._png.palette=this._palette),this._hasTransparency&&(this._png.transparency=this._transparency)}pushDataToFrame(){const t=this._inflator.result,e=this._frames.at(-1);e?e.data=t:this._frames.push({sequenceNumber:0,width:this._png.width,height:this._png.height,xOffset:0,yOffset:0,delayNumber:0,delayDenominator:0,disposeOp:fn,blendOp:gn,data:t}),this._inflator=new Le,this._writingDataChunks=!1}}var vn;!function(t){t[t.UNKNOWN=0]="UNKNOWN",t[t.METRE=1]="METRE"}(vn||(vn={}));var yn=function(){return"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this}();function wn(){yn.console&&"function"==typeof yn.console.log&&yn.console.log.apply(yn.console,arguments)}var xn={log:wn,warn:function(t){yn.console&&("function"==typeof yn.console.warn?yn.console.warn.apply(yn.console,arguments):wn.call(null,arguments))},error:function(t){yn.console&&("function"==typeof yn.console.error?yn.console.error.apply(yn.console,arguments):wn(t))}};function _n(t,e,n){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){Nn(r.response,e,n)},r.onerror=function(){xn.error("could not download file")},r.send()}function An(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(n){}return e.status>=200&&e.status<=299}function Ln(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(n){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var Nn=yn.saveAs||("object"!==("undefined"==typeof window?"undefined":y(window))||window!==yn?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(t,e,n){var r=yn.URL||yn.webkitURL,i=document.createElement("a");e=e||t.name||"download",i.download=e,i.rel="noopener","string"==typeof t?(i.href=t,i.origin!==location.origin?An(i.href)?_n(t,e,n):Ln(i,i.target="_blank"):Ln(i)):(i.href=r.createObjectURL(t),setTimeout(function(){r.revokeObjectURL(i.href)},4e4),setTimeout(function(){Ln(i)},0))}:"msSaveOrOpenBlob"in navigator?function(t,e,n){if(e=e||t.name||"download","string"==typeof t)if(An(t))_n(t,e,n);else{var r=document.createElement("a");r.href=t,r.target="_blank",setTimeout(function(){Ln(r)})}else navigator.msSaveOrOpenBlob(function(t,e){return void 0===e?e={autoBom:!1}:"object"!==y(e)&&(xn.warn("Deprecated: Expected third argument to be a object"),e={autoBom:!e}),e.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t}(t,n),e)}:function(t,e,n,r){if((r=r||open("","_blank"))&&(r.document.title=r.document.body.innerText="downloading..."),"string"==typeof t)return _n(t,e,n);var i="application/octet-stream"===t.type,a=/constructor/i.test(yn.HTMLElement)||yn.safari,o=/CriOS\/[\d]+/.test(navigator.userAgent);if((o||i&&a)&&"object"===("undefined"==typeof FileReader?"undefined":y(FileReader))){var s=new FileReader;s.onloadend=function(){var t=s.result;t=o?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),r?r.location.href=t:location=t,r=null},s.readAsDataURL(t)}else{var l=yn.URL||yn.webkitURL,h=l.createObjectURL(t);r?r.location=h:location.href=h,r=null,setTimeout(function(){l.revokeObjectURL(h)},4e4)}});
/**
       * A class to parse color values
       * <AUTHOR> Stefanov <<EMAIL>>
       * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
       * @license Use it if you like it
       */function Sn(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],r=0;r<n.length;r++){var i=n[r].re,a=n[r].process,o=i.exec(t);o&&(e=a(o),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),n=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==n.length&&(n="0"+n),"#"+t+e+n}}var kn=yn.atob.bind(yn),Pn=yn.btoa.bind(yn);
/**
       * @license
       * Joseph Myers does not specify a particular license for his work.
       *
       * Author: Joseph Myers
       * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
       *
       * Modified by: Owen Leong
       */function Fn(t,e){var n=t[0],r=t[1],i=t[2],a=t[3];n=In(n,r,i,a,e[0],7,-680876936),a=In(a,n,r,i,e[1],12,-389564586),i=In(i,a,n,r,e[2],17,606105819),r=In(r,i,a,n,e[3],22,-**********),n=In(n,r,i,a,e[4],7,-176418897),a=In(a,n,r,i,e[5],12,**********),i=In(i,a,n,r,e[6],17,-**********),r=In(r,i,a,n,e[7],22,-45705983),n=In(n,r,i,a,e[8],7,**********),a=In(a,n,r,i,e[9],12,-**********),i=In(i,a,n,r,e[10],17,-42063),r=In(r,i,a,n,e[11],22,-**********),n=In(n,r,i,a,e[12],7,**********),a=In(a,n,r,i,e[13],12,-40341101),i=In(i,a,n,r,e[14],17,-**********),n=jn(n,r=In(r,i,a,n,e[15],22,**********),i,a,e[1],5,-165796510),a=jn(a,n,r,i,e[6],9,-**********),i=jn(i,a,n,r,e[11],14,643717713),r=jn(r,i,a,n,e[0],20,-373897302),n=jn(n,r,i,a,e[5],5,-701558691),a=jn(a,n,r,i,e[10],9,38016083),i=jn(i,a,n,r,e[15],14,-660478335),r=jn(r,i,a,n,e[4],20,-405537848),n=jn(n,r,i,a,e[9],5,568446438),a=jn(a,n,r,i,e[14],9,-1019803690),i=jn(i,a,n,r,e[3],14,-187363961),r=jn(r,i,a,n,e[8],20,1163531501),n=jn(n,r,i,a,e[13],5,-1444681467),a=jn(a,n,r,i,e[2],9,-51403784),i=jn(i,a,n,r,e[7],14,1735328473),n=On(n,r=jn(r,i,a,n,e[12],20,-1926607734),i,a,e[5],4,-378558),a=On(a,n,r,i,e[8],11,-2022574463),i=On(i,a,n,r,e[11],16,1839030562),r=On(r,i,a,n,e[14],23,-35309556),n=On(n,r,i,a,e[1],4,-1530992060),a=On(a,n,r,i,e[4],11,1272893353),i=On(i,a,n,r,e[7],16,-155497632),r=On(r,i,a,n,e[10],23,-1094730640),n=On(n,r,i,a,e[13],4,681279174),a=On(a,n,r,i,e[0],11,-358537222),i=On(i,a,n,r,e[3],16,-722521979),r=On(r,i,a,n,e[6],23,76029189),n=On(n,r,i,a,e[9],4,-640364487),a=On(a,n,r,i,e[12],11,-421815835),i=On(i,a,n,r,e[15],16,530742520),n=Dn(n,r=On(r,i,a,n,e[2],23,-995338651),i,a,e[0],6,-198630844),a=Dn(a,n,r,i,e[7],10,1126891415),i=Dn(i,a,n,r,e[14],15,-1416354905),r=Dn(r,i,a,n,e[5],21,-57434055),n=Dn(n,r,i,a,e[12],6,1700485571),a=Dn(a,n,r,i,e[3],10,-1894986606),i=Dn(i,a,n,r,e[10],15,-1051523),r=Dn(r,i,a,n,e[1],21,-2054922799),n=Dn(n,r,i,a,e[8],6,1873313359),a=Dn(a,n,r,i,e[15],10,-30611744),i=Dn(i,a,n,r,e[6],15,-1560198380),r=Dn(r,i,a,n,e[13],21,1309151649),n=Dn(n,r,i,a,e[4],6,-145523070),a=Dn(a,n,r,i,e[11],10,-1120210379),i=Dn(i,a,n,r,e[2],15,718787259),r=Dn(r,i,a,n,e[9],21,-343485551),t[0]=zn(n,t[0]),t[1]=zn(r,t[1]),t[2]=zn(i,t[2]),t[3]=zn(a,t[3])}function Cn(t,e,n,r,i,a){return e=zn(zn(e,t),zn(r,a)),zn(e<<i|e>>>32-i,n)}function In(t,e,n,r,i,a,o){return Cn(e&n|~e&r,t,e,i,a,o)}function jn(t,e,n,r,i,a,o){return Cn(e&r|n&~r,t,e,i,a,o)}function On(t,e,n,r,i,a,o){return Cn(e^n^r,t,e,i,a,o)}function Dn(t,e,n,r,i,a,o){return Cn(n^(e|~r),t,e,i,a,o)}function En(t){var e,n=t.length,r=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=t.length;e+=64)Fn(r,Bn(t.substring(e-64,e)));t=t.substring(e-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<t.length;e++)i[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(Fn(r,i),e=0;e<16;e++)i[e]=0;return i[14]=8*n,Fn(r,i),r}function Bn(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}var Mn="0123456789abcdef".split("");function Tn(t){for(var e="",n=0;n<4;n++)e+=Mn[t>>8*n+4&15]+Mn[t>>8*n&15];return e}function Rn(t){return String.fromCharCode(255&t,(65280&t)>>8,(16711680&t)>>16,(**********&t)>>24)}function qn(t){return En(t).map(Rn).join("")}var Un="5d41402abc4b2a76b9719d911017c592"!=function(t){for(var e=0;e<t.length;e++)t[e]=Tn(t[e]);return t.join("")}(En("hello"));function zn(t,e){if(Un){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}return t+e&**********}
/**
       * @license
       * FPDF is released under a permissive license: there is no usage restriction.
       * You may embed it freely in your application (commercial or not), with or
       * without modifications.
       *
       * Reference: http://www.fpdf.org/en/script/script37.php
       */function Hn(t,e){var n,r,i,a;if(t!==n){for(var o=(i=t,a=1+(256/t.length|0),new Array(a+1).join(i)),s=[],l=0;l<256;l++)s[l]=l;var h=0;for(l=0;l<256;l++){var c=s[l];h=(h+c+o.charCodeAt(l))%256,s[l]=s[h],s[h]=c}n=t,r=s}else s=r;var u=e.length,f=0,d=0,p="";for(l=0;l<u;l++)d=(d+(c=s[f=(f+1)%256]))%256,s[f]=s[d],s[d]=c,o=s[(s[f]+s[d])%256],p+=String.fromCharCode(e.charCodeAt(l)^o);return p}
/**
       * @license
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       * Author: Owen Leong (@owenl131)
       * Date: 15 Oct 2020
       * References:
       * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
       * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
       * http://www.fpdf.org/en/script/script37.php
       */var Wn={print:4,modify:8,copy:16,"annot-forms":32};function Gn(t,e,n,r){this.v=1,this.r=2;var i=192;t.forEach(function(t){if(void 0!==Wn.perm)throw new Error("Invalid permission: "+t);i+=Wn[t]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var a=(e+this.padding).substr(0,32),o=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(a,o),this.P=-(1+(255^i)),this.encryptionKey=qn(a+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(r)).substr(0,5),this.U=Hn(this.encryptionKey,this.padding)}function Vn(t){if(/[^\u0000-\u00ff]/.test(t))throw new Error("Invalid PDF Name Object: "+t+", Only accept ASCII characters.");for(var e="",n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);e+=i<33||35===i||37===i||40===i||41===i||47===i||60===i||62===i||91===i||93===i||123===i||125===i||i>126?"#"+("0"+i.toString(16)).slice(-2):t[r]}return e}function Kn(t){if("object"!==y(t))throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(t,n,r){if(r=r||!1,"string"!=typeof t||"function"!=typeof n||"boolean"!=typeof r)throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(t)||(e[t]={});var i=Math.random().toString(35);return e[t][i]=[n,!!r],i},this.unsubscribe=function(t){for(var n in e)if(e[n][t])return delete e[n][t],0===Object.keys(e[n]).length&&delete e[n],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var r=Array.prototype.slice.call(arguments,1),i=[];for(var a in e[n]){var o=e[n][a];try{o[0].apply(t,r)}catch(Ln){yn.console&&xn.error("jsPDF PubSub Error",Ln.message,Ln)}o[1]&&i.push(a)}i.length&&i.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function Yn(t){if(!(this instanceof Yn))return new Yn(t);var e="opacity,stroke-opacity".split(",");for(var n in t)t.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=t[n]);this.id="",this.objectNumber=-1}function Jn(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function Xn(t,e,n,r,i){if(!(this instanceof Xn))return new Xn(t,e,n,r,i);this.type="axial"===t?2:3,this.coords=e,this.colors=n,Jn.call(this,r,i)}function $n(t,e,n,r,i){if(!(this instanceof $n))return new $n(t,e,n,r,i);this.boundingBox=t,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,Jn.call(this,r,i)}function Zn(t){var e,n="string"==typeof arguments[0]?arguments[0]:"p",r=arguments[1],i=arguments[2],a=arguments[3],o=[],s=1,l=16,h="S",c=null;"object"===y(t=t||{})&&(n=t.orientation,r=t.unit||r,i=t.format||i,a=t.compress||t.compressPdf||a,null!==(c=t.encryption||null)&&(c.userPassword=c.userPassword||"",c.ownerPassword=c.ownerPassword||"",c.userPermissions=c.userPermissions||[]),s="number"==typeof t.userUnit?Math.abs(t.userUnit):1,void 0!==t.precision&&(e=t.precision),void 0!==t.floatPrecision&&(l=t.floatPrecision),h=t.defaultPathOperation||"S"),o=t.filters||(!0===a?["FlateEncode"]:o),r=r||"mm",n=(""+(n||"P")).toLowerCase();var u=t.putOnlyUsedFonts||!1,f={},d={internal:{},__private__:{}};d.__private__.PubSub=Kn;var p="1.3",g=d.__private__.getPdfVersion=function(){return p};d.__private__.setPdfVersion=function(t){p=t};var m={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};d.__private__.getPageFormats=function(){return m};var b=d.__private__.getPageFormat=function(t){return m[t]};i=i||"a4";var v="compat",w="advanced",x=v;function _(){this.saveGraphicsState(),Z(new jt(dt,0,0,-dt,0,cn()*dt).toString()+" cm"),this.setFontSize(this.getFontSize()/dt),h="n",x=w}function A(){this.restoreGraphicsState(),h="S",x=v}var L=d.__private__.combineFontStyleAndFontWeight=function(t,e){if("bold"==t&&"normal"==e||"bold"==t&&400==e||"normal"==t&&"italic"==e||"bold"==t&&"italic"==e)throw new Error("Invalid Combination of fontweight and fontstyle");return e&&(t=400==e||"normal"===e?"italic"===t?"italic":"normal":700!=e&&"bold"!==e||"normal"!==t?(700==e?"bold":e)+""+t:"bold"),t};d.advancedAPI=function(t){var e=x===v;return e&&_.call(this),"function"!=typeof t||(t(this),e&&A.call(this)),this},d.compatAPI=function(t){var e=x===w;return e&&A.call(this),"function"!=typeof t||(t(this),e&&_.call(this)),this},d.isAdvancedAPI=function(){return x===w};var N,S=function(t){if(x!==w)throw new Error(t+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},k=d.roundToPrecision=d.__private__.roundToPrecision=function(t,n){var r=e||n;if(isNaN(t)||isNaN(r))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return t.toFixed(r).replace(/0+$/,"")};N=d.hpf=d.__private__.hpf="number"==typeof l?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return k(t,l)}:"smart"===l?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return k(t,t>-1&&t<1?16:5)}:function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return k(t,16)};var P=d.f2=d.__private__.f2=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return k(t,2)},F=d.__private__.f3=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f3");return k(t,3)},C=d.scale=d.__private__.scale=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.scale");return x===v?t*dt:x===w?t:void 0},I=function(t){return C(function(t){return x===v?cn()-t:x===w?t:void 0}(t))};d.__private__.setPrecision=d.setPrecision=function(t){"number"==typeof parseInt(t,10)&&(e=parseInt(t,10))};var j,O="00000000000000000000000000000000",D=d.__private__.getFileId=function(){return O},E=d.__private__.setFileId=function(t){return O=void 0!==t&&/^[a-fA-F0-9]{32}$/.test(t)?t.toUpperCase():O.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),null!==c&&(ye=new Gn(c.userPermissions,c.userPassword,c.ownerPassword,O)),O};d.setFileId=function(t){return E(t),this},d.getFileId=function(){return D()};var B=d.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),n=e<0?"+":"-",r=Math.floor(Math.abs(e/60)),i=Math.abs(e%60),a=[n,U(r),"'",U(i),"'"].join("");return["D:",t.getFullYear(),U(t.getMonth()+1),U(t.getDate()),U(t.getHours()),U(t.getMinutes()),U(t.getSeconds()),a].join("")},M=d.__private__.convertPDFDateToDate=function(t){var e=parseInt(t.substr(2,4),10),n=parseInt(t.substr(6,2),10)-1,r=parseInt(t.substr(8,2),10),i=parseInt(t.substr(10,2),10),a=parseInt(t.substr(12,2),10),o=parseInt(t.substr(14,2),10);return new Date(e,n,r,i,a,o,0)},T=d.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),t instanceof Date)e=B(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(t))throw new Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return j=e},R=d.__private__.getCreationDate=function(t){var e=j;return"jsDate"===t&&(e=M(j)),e};d.setCreationDate=function(t){return T(t),this},d.getCreationDate=function(t){return R(t)};var q,U=d.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},z=d.__private__.padd2Hex=function(t){return("00"+(t=t.toString())).substr(t.length)},H=0,W=[],G=[],V=0,K=[],Y=[],J=!1,X=G;d.__private__.setCustomOutputDestination=function(t){J=!0,X=t};var $=function(t){J||(X=t)};d.__private__.resetCustomOutputDestination=function(){J=!1,X=G};var Z=d.__private__.out=function(t){return t=t.toString(),V+=t.length+1,X.push(t),X},Q=d.__private__.write=function(t){return Z(1===arguments.length?t.toString():Array.prototype.join.call(arguments," "))},tt=d.__private__.getArrayBuffer=function(t){for(var e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n);e--;)r[e]=t.charCodeAt(e);return n},et=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];d.__private__.getStandardFonts=function(){return et};var nt=t.fontSize||16;d.__private__.setFontSize=d.setFontSize=function(t){return nt=x===w?t/dt:t,this};var rt,it=d.__private__.getFontSize=d.getFontSize=function(){return x===v?nt:nt*dt},at=t.R2L||!1;d.__private__.setR2L=d.setR2L=function(t){return at=t,this},d.__private__.getR2L=d.getR2L=function(){return at};var ot,st=d.__private__.setZoomMode=function(t){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(t))rt=t;else if(isNaN(t)){if(-1===[void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(t))throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');rt=t}else rt=parseInt(t,10)};d.__private__.getZoomMode=function(){return rt};var lt,ht=d.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');ot=t};d.__private__.getPageMode=function(){return ot};var ct=d.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');lt=t};d.__private__.getLayoutMode=function(){return lt},d.__private__.setDisplayMode=d.setDisplayMode=function(t,e,n){return st(t),ct(e),ht(n),this};var ut={title:"",subject:"",author:"",keywords:"",creator:""};d.__private__.getDocumentProperty=function(t){if(-1===Object.keys(ut).indexOf(t))throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return ut[t]},d.__private__.getDocumentProperties=function(){return ut},d.__private__.setDocumentProperties=d.setProperties=d.setDocumentProperties=function(t){for(var e in ut)ut.hasOwnProperty(e)&&t[e]&&(ut[e]=t[e]);return this},d.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(ut).indexOf(t))throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return ut[t]=e};var ft,dt,pt,gt,mt,bt={},vt={},yt=[],wt={},xt={},_t={},At={},Lt=null,Nt=0,St=[],kt=new Kn(d),Pt=t.hotfixes||[],Ft={},Ct={},It=[],jt=function t(e,n,r,i,a,o){if(!(this instanceof t))return new t(e,n,r,i,a,o);isNaN(e)&&(e=1),isNaN(n)&&(n=0),isNaN(r)&&(r=0),isNaN(i)&&(i=1),isNaN(a)&&(a=0),isNaN(o)&&(o=0),this._matrix=[e,n,r,i,a,o]};Object.defineProperty(jt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(jt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(jt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(jt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(jt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(jt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(jt.prototype,"a",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(jt.prototype,"b",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(jt.prototype,"c",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(jt.prototype,"d",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(jt.prototype,"e",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(jt.prototype,"f",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(jt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(jt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(jt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(jt.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),jt.prototype.join=function(t){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(N).join(t)},jt.prototype.multiply=function(t){var e=t.sx*this.sx+t.shy*this.shx,n=t.sx*this.shy+t.shy*this.sy,r=t.shx*this.sx+t.sy*this.shx,i=t.shx*this.shy+t.sy*this.sy,a=t.tx*this.sx+t.ty*this.shx+this.tx,o=t.tx*this.shy+t.ty*this.sy+this.ty;return new jt(e,n,r,i,a,o)},jt.prototype.decompose=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,a=this.ty,o=Math.sqrt(t*t+e*e),s=(t/=o)*n+(e/=o)*r;n-=t*s,r-=e*s;var l=Math.sqrt(n*n+r*r);return s/=l,t*(r/=l)<e*(n/=l)&&(t=-t,e=-e,s=-s,o=-o),{scale:new jt(o,0,0,l,0,0),translate:new jt(1,0,0,1,i,a),rotate:new jt(t,e,-e,t,0,0),skew:new jt(1,0,s,1,0,0)}},jt.prototype.toString=function(t){return this.join(" ")},jt.prototype.inversed=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,a=this.ty,o=1/(t*r-e*n),s=r*o,l=-e*o,h=-n*o,c=t*o;return new jt(s,l,h,c,-s*i-h*a,-l*i-c*a)},jt.prototype.applyToPoint=function(t){var e=t.x*this.sx+t.y*this.shx+this.tx,n=t.x*this.shy+t.y*this.sy+this.ty;return new nn(e,n)},jt.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),n=this.applyToPoint(new nn(t.x+t.w,t.y+t.h));return new rn(e.x,e.y,n.x-e.x,n.y-e.y)},jt.prototype.clone=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,a=this.ty;return new jt(t,e,n,r,i,a)},d.Matrix=jt;var Ot=d.matrixMult=function(t,e){return e.multiply(t)},Dt=new jt(1,0,0,1,0,0);d.unitMatrix=d.identityMatrix=Dt;var Et=function(t,e){if(!xt[t]){var n=(e instanceof Xn?"Sh":"P")+(Object.keys(wt).length+1).toString(10);e.id=n,xt[t]=n,wt[n]=e,kt.publish("addPattern",e)}};d.ShadingPattern=Xn,d.TilingPattern=$n,d.addShadingPattern=function(t,e){return S("addShadingPattern()"),Et(t,e),this},d.beginTilingPattern=function(t){S("beginTilingPattern()"),on(t.boundingBox[0],t.boundingBox[1],t.boundingBox[2]-t.boundingBox[0],t.boundingBox[3]-t.boundingBox[1],t.matrix)},d.endTilingPattern=function(t,e){S("endTilingPattern()"),e.stream=Y[q].join("\n"),Et(t,e),kt.publish("endTilingPattern",e),It.pop().restore()};var Bt,Mt=d.__private__.newObject=function(){var t=Tt();return Rt(t,!0),t},Tt=d.__private__.newObjectDeferred=function(){return H++,W[H]=function(){return V},H},Rt=function(t,e){return e="boolean"==typeof e&&e,W[t]=V,e&&Z(t+" 0 obj"),t},qt=d.__private__.newAdditionalObject=function(){var t={objId:Tt(),content:""};return K.push(t),t},Ut=Tt(),zt=Tt(),Ht=d.__private__.decodeColorString=function(t){var e=t.split(" ");if(2!==e.length||"g"!==e[1]&&"G"!==e[1])5!==e.length||"k"!==e[4]&&"K"!==e[4]||(e=[(1-e[0])*(1-e[3]),(1-e[1])*(1-e[3]),(1-e[2])*(1-e[3]),"r"]);else{var n=parseFloat(e[0]);e=[n,n,n,"r"]}for(var r="#",i=0;i<3;i++)r+=("0"+Math.floor(255*parseFloat(e[i])).toString(16)).slice(-2);return r},Wt=d.__private__.encodeColorString=function(t){var e;"string"==typeof t&&(t={ch1:t});var n=t.ch1,r=t.ch2,i=t.ch3,a=t.ch4,o="draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof n&&"#"!==n.charAt(0)){var s=new Sn(n);if(s.ok)n=s.toHex();else if(!/^\d*\.?\d*$/.test(n))throw new Error('Invalid color "'+n+'" passed to jsPDF.encodeColorString.')}if("string"==typeof n&&/^#[0-9A-Fa-f]{3}$/.test(n)&&(n="#"+n[1]+n[1]+n[2]+n[2]+n[3]+n[3]),"string"==typeof n&&/^#[0-9A-Fa-f]{6}$/.test(n)){var l=parseInt(n.substr(1),16);n=l>>16&255,r=l>>8&255,i=255&l}if(void 0===r||void 0===a&&n===r&&r===i)e="string"==typeof n?n+" "+o[0]:2===t.precision?P(n/255)+" "+o[0]:F(n/255)+" "+o[0];else if(void 0===a||"object"===y(a)){if(a&&!isNaN(a.a)&&0===a.a)return["1.","1.","1.",o[1]].join(" ");e="string"==typeof n?[n,r,i,o[1]].join(" "):2===t.precision?[P(n/255),P(r/255),P(i/255),o[1]].join(" "):[F(n/255),F(r/255),F(i/255),o[1]].join(" ")}else e="string"==typeof n?[n,r,i,a,o[2]].join(" "):2===t.precision?[P(n),P(r),P(i),P(a),o[2]].join(" "):[F(n),F(r),F(i),F(a),o[2]].join(" ");return e},Gt=d.__private__.getFilters=function(){return o},Vt=d.__private__.putStream=function(t){var e=(t=t||{}).data||"",n=t.filters||Gt(),r=t.alreadyAppliedFilters||[],i=t.addLength1||!1,a=e.length,o=t.objectId,s=function(t){return t};if(null!==c&&void 0===o)throw new Error("ObjectId must be passed to putStream for file encryption");null!==c&&(s=ye.encryptor(o,0));var l={};!0===n&&(n=["FlateEncode"]);var h=t.additionalKeyValues||[],u=(l=void 0!==Zn.API.processDataByFilters?Zn.API.processDataByFilters(e,n):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(r)?r.join(" "):r.toString());if(0!==l.data.length&&(h.push({key:"Length",value:l.data.length}),!0===i&&h.push({key:"Length1",value:a})),0!=u.length)if(u.split("/").length-1==1)h.push({key:"Filter",value:u});else{h.push({key:"Filter",value:"["+u+"]"});for(var f=0;f<h.length;f+=1)if("DecodeParms"===h[f].key){for(var d=[],p=0;p<l.reverseChain.split("/").length-1;p+=1)d.push("null");d.push(h[f].value),h[f].value="["+d.join(" ")+"]"}}Z("<<");for(var g=0;g<h.length;g++)Z("/"+h[g].key+" "+h[g].value);Z(">>"),0!==l.data.length&&(Z("stream"),Z(s(l.data)),Z("endstream"))},Kt=d.__private__.putPage=function(t){var e=t.number,n=t.data,r=t.objId,i=t.contentsObjId;Rt(r,!0),Z("<</Type /Page"),Z("/Parent "+t.rootDictionaryObjId+" 0 R"),Z("/Resources "+t.resourceDictionaryObjId+" 0 R"),Z("/MediaBox ["+parseFloat(N(t.mediaBox.bottomLeftX))+" "+parseFloat(N(t.mediaBox.bottomLeftY))+" "+N(t.mediaBox.topRightX)+" "+N(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&Z("/CropBox ["+N(t.cropBox.bottomLeftX)+" "+N(t.cropBox.bottomLeftY)+" "+N(t.cropBox.topRightX)+" "+N(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&Z("/BleedBox ["+N(t.bleedBox.bottomLeftX)+" "+N(t.bleedBox.bottomLeftY)+" "+N(t.bleedBox.topRightX)+" "+N(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&Z("/TrimBox ["+N(t.trimBox.bottomLeftX)+" "+N(t.trimBox.bottomLeftY)+" "+N(t.trimBox.topRightX)+" "+N(t.trimBox.topRightY)+"]"),null!==t.artBox&&Z("/ArtBox ["+N(t.artBox.bottomLeftX)+" "+N(t.artBox.bottomLeftY)+" "+N(t.artBox.topRightX)+" "+N(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&Z("/UserUnit "+t.userUnit),kt.publish("putPage",{objId:r,pageContext:St[e],pageNumber:e,page:n}),Z("/Contents "+i+" 0 R"),Z(">>"),Z("endobj");var a=n.join("\n");return x===w&&(a+="\nQ"),Rt(i,!0),Vt({data:a,filters:Gt(),objectId:i}),Z("endobj"),r},Yt=d.__private__.putPages=function(){var t,e,n=[];for(t=1;t<=Nt;t++)St[t].objId=Tt(),St[t].contentsObjId=Tt();for(t=1;t<=Nt;t++)n.push(Kt({number:t,data:Y[t],objId:St[t].objId,contentsObjId:St[t].contentsObjId,mediaBox:St[t].mediaBox,cropBox:St[t].cropBox,bleedBox:St[t].bleedBox,trimBox:St[t].trimBox,artBox:St[t].artBox,userUnit:St[t].userUnit,rootDictionaryObjId:Ut,resourceDictionaryObjId:zt}));Rt(Ut,!0),Z("<</Type /Pages");var r="/Kids [";for(e=0;e<Nt;e++)r+=n[e]+" 0 R ";Z(r+"]"),Z("/Count "+Nt),Z(">>"),Z("endobj"),kt.publish("postPutPages")},Jt=function(t){kt.publish("putFont",{font:t,out:Z,newObject:Mt,putStream:Vt}),!0!==t.isAlreadyPutted&&(t.objectNumber=Mt(),Z("<<"),Z("/Type /Font"),Z("/BaseFont /"+Vn(t.postScriptName)),Z("/Subtype /Type1"),"string"==typeof t.encoding&&Z("/Encoding /"+t.encoding),Z("/FirstChar 32"),Z("/LastChar 255"),Z(">>"),Z("endobj"))},Xt=function(t){t.objectNumber=Mt();var e=[];e.push({key:"Type",value:"/XObject"}),e.push({key:"Subtype",value:"/Form"}),e.push({key:"BBox",value:"["+[N(t.x),N(t.y),N(t.x+t.width),N(t.y+t.height)].join(" ")+"]"}),e.push({key:"Matrix",value:"["+t.matrix.toString()+"]"});var n=t.pages[1].join("\n");Vt({data:n,additionalKeyValues:e,objectId:t.objectNumber}),Z("endobj")},$t=function(t,e){e||(e=21);var n=Mt(),r=function(t,e){var n,r=[],i=1/(e-1);for(n=0;n<1;n+=i)r.push(n);if(r.push(1),0!=t[0].offset){var a={offset:0,color:t[0].color};t.unshift(a)}if(1!=t[t.length-1].offset){var o={offset:1,color:t[t.length-1].color};t.push(o)}for(var s="",l=0,h=0;h<r.length;h++){for(n=r[h];n>t[l+1].offset;)l++;var c=t[l].offset,u=(n-c)/(t[l+1].offset-c),f=t[l].color,d=t[l+1].color;s+=z(Math.round((1-u)*f[0]+u*d[0]).toString(16))+z(Math.round((1-u)*f[1]+u*d[1]).toString(16))+z(Math.round((1-u)*f[2]+u*d[2]).toString(16))}return s.trim()}(t.colors,e),i=[];i.push({key:"FunctionType",value:"0"}),i.push({key:"Domain",value:"[0.0 1.0]"}),i.push({key:"Size",value:"["+e+"]"}),i.push({key:"BitsPerSample",value:"8"}),i.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),i.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Vt({data:r,additionalKeyValues:i,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:n}),Z("endobj"),t.objectNumber=Mt(),Z("<< /ShadingType "+t.type),Z("/ColorSpace /DeviceRGB");var a="/Coords ["+N(parseFloat(t.coords[0]))+" "+N(parseFloat(t.coords[1]))+" ";2===t.type?a+=N(parseFloat(t.coords[2]))+" "+N(parseFloat(t.coords[3])):a+=N(parseFloat(t.coords[2]))+" "+N(parseFloat(t.coords[3]))+" "+N(parseFloat(t.coords[4]))+" "+N(parseFloat(t.coords[5])),Z(a+="]"),t.matrix&&Z("/Matrix ["+t.matrix.toString()+"]"),Z("/Function "+n+" 0 R"),Z("/Extend [true true]"),Z(">>"),Z("endobj")},Zt=function(t,e){var n=Tt(),r=Mt();e.push({resourcesOid:n,objectOid:r}),t.objectNumber=r;var i=[];i.push({key:"Type",value:"/Pattern"}),i.push({key:"PatternType",value:"1"}),i.push({key:"PaintType",value:"1"}),i.push({key:"TilingType",value:"1"}),i.push({key:"BBox",value:"["+t.boundingBox.map(N).join(" ")+"]"}),i.push({key:"XStep",value:N(t.xStep)}),i.push({key:"YStep",value:N(t.yStep)}),i.push({key:"Resources",value:n+" 0 R"}),t.matrix&&i.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),Vt({data:t.stream,additionalKeyValues:i,objectId:t.objectNumber}),Z("endobj")},Qt=function(t){for(var e in t.objectNumber=Mt(),Z("<<"),t)switch(e){case"opacity":Z("/ca "+P(t[e]));break;case"stroke-opacity":Z("/CA "+P(t[e]))}Z(">>"),Z("endobj")},te=function(t){Rt(t.resourcesOid,!0),Z("<<"),Z("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),function(){for(var t in Z("/Font <<"),bt)bt.hasOwnProperty(t)&&(!1===u||!0===u&&f.hasOwnProperty(t))&&Z("/"+t+" "+bt[t].objectNumber+" 0 R");Z(">>")}(),function(){if(Object.keys(wt).length>0){for(var t in Z("/Shading <<"),wt)wt.hasOwnProperty(t)&&wt[t]instanceof Xn&&wt[t].objectNumber>=0&&Z("/"+t+" "+wt[t].objectNumber+" 0 R");kt.publish("putShadingPatternDict"),Z(">>")}}(),function(t){if(Object.keys(wt).length>0){for(var e in Z("/Pattern <<"),wt)wt.hasOwnProperty(e)&&wt[e]instanceof d.TilingPattern&&wt[e].objectNumber>=0&&wt[e].objectNumber<t&&Z("/"+e+" "+wt[e].objectNumber+" 0 R");kt.publish("putTilingPatternDict"),Z(">>")}}(t.objectOid),function(){if(Object.keys(_t).length>0){var t;for(t in Z("/ExtGState <<"),_t)_t.hasOwnProperty(t)&&_t[t].objectNumber>=0&&Z("/"+t+" "+_t[t].objectNumber+" 0 R");kt.publish("putGStateDict"),Z(">>")}}(),function(){for(var t in Z("/XObject <<"),Ft)Ft.hasOwnProperty(t)&&Ft[t].objectNumber>=0&&Z("/"+t+" "+Ft[t].objectNumber+" 0 R");kt.publish("putXobjectDict"),Z(">>")}(),Z(">>"),Z("endobj")},ee=function(t){vt[t.fontName]=vt[t.fontName]||{},vt[t.fontName][t.fontStyle]=t.id},ne=function(t,e,n,r,i){var a={id:"F"+(Object.keys(bt).length+1).toString(10),postScriptName:t,fontName:e,fontStyle:n,encoding:r,isStandardFont:i||!1,metadata:{}};return kt.publish("addFont",{font:a,instance:this}),bt[a.id]=a,ee(a),a.id},re=d.__private__.pdfEscape=d.pdfEscape=function(t,e){return function(t,e){var n,r,i,a,o,s,l,h,c;if(i=(e=e||{}).sourceEncoding||"Unicode",o=e.outputEncoding,(e.autoencode||o)&&bt[ft].metadata&&bt[ft].metadata[i]&&bt[ft].metadata[i].encoding&&(a=bt[ft].metadata[i].encoding,!o&&bt[ft].encoding&&(o=bt[ft].encoding),!o&&a.codePages&&(o=a.codePages[0]),"string"==typeof o&&(o=a[o]),o)){for(l=!1,s=[],n=0,r=t.length;n<r;n++)(h=o[t.charCodeAt(n)])?s.push(String.fromCharCode(h)):s.push(t[n]),s[n].charCodeAt(0)>>8&&(l=!0);t=s.join("")}for(n=t.length;void 0===l&&0!==n;)t.charCodeAt(n-1)>>8&&(l=!0),n--;if(!l)return t;for(s=e.noBOM?[]:[254,255],n=0,r=t.length;n<r;n++){if((c=(h=t.charCodeAt(n))>>8)>>8)throw new Error("Character at position "+n+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");s.push(c),s.push(h-(c<<8))}return String.fromCharCode.apply(void 0,s)}(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},ie=d.__private__.beginPage=function(t){Y[++Nt]=[],St[Nt]={objId:0,contentsObjId:0,userUnit:Number(s),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t[0]),topRightY:Number(t[1])}},se(Nt),$(Y[q])},ae=function(t,e){var r,a,o;switch(n=e||n,"string"==typeof t&&(r=b(t.toLowerCase()),Array.isArray(r)&&(a=r[0],o=r[1])),Array.isArray(t)&&(a=t[0]*dt,o=t[1]*dt),isNaN(a)&&(a=i[0],o=i[1]),(a>14400||o>14400)&&(xn.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),a=Math.min(14400,a),o=Math.min(14400,o)),i=[a,o],n.substr(0,1)){case"l":o>a&&(i=[o,a]);break;case"p":a>o&&(i=[o,a])}ie(i),Re(Me),Z(Ke),0!==Qe&&Z(Qe+" J"),0!==tn&&Z(tn+" j"),kt.publish("addPage",{pageNumber:Nt})},oe=function(t){t>0&&t<=Nt&&(Y.splice(t,1),St.splice(t,1),Nt--,q>Nt&&(q=Nt),this.setPage(q))},se=function(t){t>0&&t<=Nt&&(q=t)},le=d.__private__.getNumberOfPages=d.getNumberOfPages=function(){return Y.length-1},he=function(t,e,n){var r,i=void 0;return n=n||{},t=void 0!==t?t:bt[ft].fontName,e=void 0!==e?e:bt[ft].fontStyle,r=t.toLowerCase(),void 0!==vt[r]&&void 0!==vt[r][e]?i=vt[r][e]:void 0!==vt[t]&&void 0!==vt[t][e]?i=vt[t][e]:!1===n.disableWarning&&xn.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),i||n.noFallback||null==(i=vt.times[e])&&(i=vt.times.normal),i},ce=d.__private__.putInfo=function(){var t=Mt(),e=function(t){return t};for(var n in null!==c&&(e=ye.encryptor(t,0)),Z("<<"),Z("/Producer ("+re(e("jsPDF "+Zn.version))+")"),ut)ut.hasOwnProperty(n)&&ut[n]&&Z("/"+n.substr(0,1).toUpperCase()+n.substr(1)+" ("+re(e(ut[n]))+")");Z("/CreationDate ("+re(e(j))+")"),Z(">>"),Z("endobj")},ue=d.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||Ut;switch(Mt(),Z("<<"),Z("/Type /Catalog"),Z("/Pages "+e+" 0 R"),rt||(rt="fullwidth"),rt){case"fullwidth":Z("/OpenAction [3 0 R /FitH null]");break;case"fullheight":Z("/OpenAction [3 0 R /FitV null]");break;case"fullpage":Z("/OpenAction [3 0 R /Fit]");break;case"original":Z("/OpenAction [3 0 R /XYZ null null 1]");break;default:var n=""+rt;"%"===n.substr(n.length-1)&&(rt=parseInt(rt)/100),"number"==typeof rt&&Z("/OpenAction [3 0 R /XYZ null null "+P(rt)+"]")}switch(lt||(lt="continuous"),lt){case"continuous":Z("/PageLayout /OneColumn");break;case"single":Z("/PageLayout /SinglePage");break;case"two":case"twoleft":Z("/PageLayout /TwoColumnLeft");break;case"tworight":Z("/PageLayout /TwoColumnRight")}ot&&Z("/PageMode /"+ot),kt.publish("putCatalog"),Z(">>"),Z("endobj")},fe=d.__private__.putTrailer=function(){Z("trailer"),Z("<<"),Z("/Size "+(H+1)),Z("/Root "+H+" 0 R"),Z("/Info "+(H-1)+" 0 R"),null!==c&&Z("/Encrypt "+ye.oid+" 0 R"),Z("/ID [ <"+O+"> <"+O+"> ]"),Z(">>")},de=d.__private__.putHeader=function(){Z("%PDF-"+p),Z("%ºß¬à")},pe=d.__private__.putXRef=function(){var t="0000000000";Z("xref"),Z("0 "+(H+1)),Z("0000000000 65535 f ");for(var e=1;e<=H;e++)"function"==typeof W[e]?Z((t+W[e]()).slice(-10)+" 00000 n "):void 0!==W[e]?Z((t+W[e]).slice(-10)+" 00000 n "):Z("0000000000 00000 n ")},ge=d.__private__.buildDocument=function(){var t;H=0,V=0,G=[],W=[],K=[],Ut=Tt(),zt=Tt(),$(G),kt.publish("buildDocument"),de(),Yt(),function(){kt.publish("putAdditionalObjects");for(var t=0;t<K.length;t++){var e=K[t];Rt(e.objId,!0),Z(e.content),Z("endobj")}kt.publish("postPutAdditionalObjects")}(),t=[],function(){for(var t in bt)bt.hasOwnProperty(t)&&(!1===u||!0===u&&f.hasOwnProperty(t))&&Jt(bt[t])}(),function(){var t;for(t in _t)_t.hasOwnProperty(t)&&Qt(_t[t])}(),function(){for(var t in Ft)Ft.hasOwnProperty(t)&&Xt(Ft[t])}(),function(t){var e;for(e in wt)wt.hasOwnProperty(e)&&(wt[e]instanceof Xn?$t(wt[e]):wt[e]instanceof $n&&Zt(wt[e],t))}(t),kt.publish("putResources"),t.forEach(te),te({resourcesOid:zt,objectOid:Number.MAX_SAFE_INTEGER}),kt.publish("postPutResources"),null!==c&&(ye.oid=Mt(),Z("<<"),Z("/Filter /Standard"),Z("/V "+ye.v),Z("/R "+ye.r),Z("/U <"+ye.toHexString(ye.U)+">"),Z("/O <"+ye.toHexString(ye.O)+">"),Z("/P "+ye.P),Z(">>"),Z("endobj")),ce(),ue();var e=V;return pe(),fe(),Z("startxref"),Z(""+e),Z("%%EOF"),$(Y[q]),G.join("\n")},me=d.__private__.getBlob=function(t){return new Blob([tt(t)],{type:"application/pdf"})},be=d.output=d.__private__.output=(Bt=function(t,e){switch("string"==typeof(e=e||{})?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return ge();case"save":d.save(e.filename);break;case"arraybuffer":return tt(ge());case"blob":return me(ge());case"bloburi":case"bloburl":if(void 0!==yn.URL&&"function"==typeof yn.URL.createObjectURL)return yn.URL&&yn.URL.createObjectURL(me(ge()))||void 0;xn.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var n="",r=ge();try{n=Pn(r)}catch(c){n=Pn(unescape(encodeURIComponent(r)))}return"data:application/pdf;filename="+e.filename+";base64,"+n;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(yn)){var i="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",a=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';e.pdfObjectUrl&&(i=e.pdfObjectUrl,a="");var o='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+i+'"'+a+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(e)+");<\/script></body></html>",s=yn.open();return null!==s&&s.document.write(o),s}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(yn)){var l='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(e.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+e.filename+'" width="500px" height="400px" /></body></html>',h=yn.open();if(null!==h){h.document.write(l);var u=this;h.document.documentElement.querySelector("#pdfViewer").onload=function(){h.document.title=e.filename,h.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(u.output("bloburl"))}}return h}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(yn))throw new Error("The option dataurlnewwindow just works in a browser-environment.");var f='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",e)+'"></iframe></body></html>',p=yn.open();if(null!==p&&(p.document.write(f),p.document.title=e.filename),p||"undefined"==typeof safari)return p;break;case"datauri":case"dataurl":return yn.document.location.href=this.output("datauristring",e);default:return null}},Bt.foo=function(){try{return Bt.apply(this,arguments)}catch(e){var t=e.stack||"";~t.indexOf(" at ")&&(t=t.split(" at ")[1]);var n="Error in function "+t.split("\n")[0].split("<")[0]+": "+e.message;if(!yn.console)throw new Error(n);yn.console.error(n,e),yn.alert&&alert(n)}},Bt.foo.bar=Bt,Bt.foo),ve=function(t){return!0===Array.isArray(Pt)&&Pt.indexOf(t)>-1};switch(r){case"pt":dt=1;break;case"mm":dt=72/25.4;break;case"cm":dt=72/2.54;break;case"in":dt=72;break;case"px":dt=1==ve("px_scaling")?.75:96/72;break;case"pc":case"em":dt=12;break;case"ex":dt=6;break;default:if("number"!=typeof r)throw new Error("Invalid unit: "+r);dt=r}var ye=null;T(),E();var we=d.__private__.getPageInfo=d.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:St[t].objId,pageNumber:t,pageContext:St[t]}},xe=d.__private__.getPageInfoByObjId=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var e in St)if(St[e].objId===t)break;return we(e)},_e=d.__private__.getCurrentPageInfo=d.getCurrentPageInfo=function(){return{objId:St[q].objId,pageNumber:q,pageContext:St[q]}};d.addPage=function(){return ae.apply(this,arguments),this},d.setPage=function(){return se.apply(this,arguments),$.call(this,Y[q]),this},d.insertPage=function(t){return this.addPage(),this.movePage(q,t),this},d.movePage=function(t,e){var n,r;if(t>e){n=Y[t],r=St[t];for(var i=t;i>e;i--)Y[i]=Y[i-1],St[i]=St[i-1];Y[e]=n,St[e]=r,this.setPage(e)}else if(t<e){n=Y[t],r=St[t];for(var a=t;a<e;a++)Y[a]=Y[a+1],St[a]=St[a+1];Y[e]=n,St[e]=r,this.setPage(e)}return this},d.deletePage=function(){return oe.apply(this,arguments),this},d.__private__.text=d.text=function(t,e,n,r,i){var a,o,s,l,h,c,u,d,p,g=(r=r||{}).scope||this;if("number"==typeof t&&"number"==typeof e&&("string"==typeof n||Array.isArray(n))){var m=n;n=e,e=t,t=m}if(arguments[3]instanceof jt==0?(s=arguments[4],l=arguments[5],"object"===y(u=arguments[3])&&null!==u||("string"==typeof s&&(l=s,s=null),"string"==typeof u&&(l=u,u=null),"number"==typeof u&&(s=u,u=null),r={flags:u,angle:s,align:l})):(S("The transform parameter of text() with a Matrix value"),p=i),isNaN(e)||isNaN(n)||null==t)throw new Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return g;var b,v="",_="number"==typeof r.lineHeightFactor?r.lineHeightFactor:Be,A=g.internal.scaleFactor;function L(t){return t=t.split("\t").join(Array(r.TabLen||9).join(" ")),re(t,u)}function k(t){for(var e,n=t.concat(),r=[],i=n.length;i--;)"string"==typeof(e=n.shift())?r.push(e):Array.isArray(t)&&(1===e.length||void 0===e[1]&&void 0===e[2])?r.push(e[0]):r.push([e[0],e[1],e[2]]);return r}function P(t,e){var n;if("string"==typeof t)n=e(t)[0];else if(Array.isArray(t)){for(var r,i,a=t.concat(),o=[],s=a.length;s--;)"string"==typeof(r=a.shift())?o.push(e(r)[0]):Array.isArray(r)&&"string"==typeof r[0]&&(i=e(r[0],r[1],r[2]),o.push([i[0],i[1],i[2]]));n=o}return n}var F=!1,I=!0;if("string"==typeof t)F=!0;else if(Array.isArray(t)){var j=t.concat();o=[];for(var O,D=j.length;D--;)("string"!=typeof(O=j.shift())||Array.isArray(O)&&"string"!=typeof O[0])&&(I=!1);F=I}if(!1===F)throw new Error('Type of text must be string or Array. "'+t+'" is not recognized.');"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var E=nt/g.internal.scaleFactor,B=E*(_-1);switch(r.baseline){case"bottom":n-=B;break;case"top":n+=E-B;break;case"hanging":n+=E-2*B;break;case"middle":n+=E/2-B}if((c=r.maxWidth||0)>0&&("string"==typeof t?t=g.splitTextToSize(t,c):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce(function(t,e){return t.concat(g.splitTextToSize(e,c))},[]))),a={text:t,x:e,y:n,options:r,mutex:{pdfEscape:re,activeFontKey:ft,fonts:bt,activeFontSize:nt}},kt.publish("preProcessText",a),t=a.text,s=(r=a.options).angle,p instanceof jt==0&&s&&"number"==typeof s){s*=Math.PI/180,0===r.rotationDirection&&(s=-s),x===w&&(s=-s);var M=Math.cos(s),T=Math.sin(s);p=new jt(M,T,-T,M,0,0)}else s&&s instanceof jt&&(p=s);x!==w||p||(p=Dt),void 0!==(h=r.charSpace||$e)&&(v+=N(C(h))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(d=r.horizontalScale)&&(v+=N(100*d)+" Tz\n"),r.lang;var R=-1,q=void 0!==r.renderingMode?r.renderingMode:r.stroke,U=g.internal.getCurrentPageInfo().pageContext;switch(q){case 0:case!1:case"fill":R=0;break;case 1:case!0:case"stroke":R=1;break;case 2:case"fillThenStroke":R=2;break;case 3:case"invisible":R=3;break;case 4:case"fillAndAddForClipping":R=4;break;case 5:case"strokeAndAddPathForClipping":R=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":R=6;break;case 7:case"addToPathForClipping":R=7}var z=void 0!==U.usedRenderingMode?U.usedRenderingMode:-1;-1!==R?v+=R+" Tr\n":-1!==z&&(v+="0 Tr\n"),-1!==R&&(U.usedRenderingMode=R),l=r.align||"left";var H,W=nt*_,G=g.internal.pageSize.getWidth(),V=bt[ft];h=r.charSpace||$e,c=r.maxWidth||0,u=Object.assign({autoencode:!0,noBOM:!0},r.flags);var K=[],Y=function(t){return g.getStringUnitWidth(t,{font:V,charSpace:h,fontSize:nt,doKerning:!1})*nt/A};if("[object Array]"===Object.prototype.toString.call(t)){var J;o=k(t),"left"!==l&&(H=o.map(Y));var X,$=0;if("right"===l){e-=H[0],t=[],D=o.length;for(var Q=0;Q<D;Q++)0===Q?(X=He(e),J=We(n)):(X=C($-H[Q]),J=-W),t.push([o[Q],X,J]),$=H[Q]}else if("center"===l){e-=H[0]/2,t=[],D=o.length;for(var tt=0;tt<D;tt++)0===tt?(X=He(e),J=We(n)):(X=C(($-H[tt])/2),J=-W),t.push([o[tt],X,J]),$=H[tt]}else if("left"===l){t=[],D=o.length;for(var et=0;et<D;et++)t.push(o[et])}else if("justify"===l&&"Identity-H"===V.encoding){t=[],D=o.length,c=0!==c?c:G;for(var rt=0,it=0;it<D;it++)if(J=0===it?We(n):-W,X=0===it?He(e):rt,it<D-1){var ot=C((c-H[it])/(o[it].split(" ").length-1)),st=o[it].split(" ");t.push([st[0]+" ",X,J]),rt=0;for(var lt=1;lt<st.length;lt++){var ht=(Y(st[lt-1]+" "+st[lt])-Y(st[lt]))*A+ot;lt==st.length-1?t.push([st[lt],ht,0]):t.push([st[lt]+" ",ht,0]),rt-=ht}}else t.push([o[it],X,J]);t.push(["",rt,0])}else{if("justify"!==l)throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(t=[],D=o.length,c=0!==c?c:G,it=0;it<D;it++)J=0===it?We(n):-W,X=0===it?He(e):0,it<D-1?K.push(N(C((c-H[it])/(o[it].split(" ").length-1)))):K.push(0),t.push([o[it],X,J])}}!0===("boolean"==typeof r.R2L?r.R2L:at)&&(t=P(t,function(t,e,n){return[t.split("").reverse().join(""),e,n]})),a={text:t,x:e,y:n,options:r,mutex:{pdfEscape:re,activeFontKey:ft,fonts:bt,activeFontSize:nt}},kt.publish("postProcessText",a),t=a.text,b=a.mutex.isHex||!1;var ct=bt[ft].encoding;"WinAnsiEncoding"!==ct&&"StandardEncoding"!==ct||(t=P(t,function(t,e,n){return[L(t),e,n]})),o=k(t),t=[];for(var ut,dt,pt,gt=Array.isArray(o[0])?1:0,mt="",vt=function(t,e,n){var i="";return n instanceof jt?(n="number"==typeof r.angle?Ot(n,new jt(1,0,0,1,t,e)):Ot(new jt(1,0,0,1,t,e),n),x===w&&(n=Ot(new jt(1,0,0,-1,0,0),n)),i=n.join(" ")+" Tm\n"):i=N(t)+" "+N(e)+" Td\n",i},yt=0;yt<o.length;yt++){switch(mt="",gt){case 1:pt=(b?"<":"(")+o[yt][0]+(b?">":")"),ut=parseFloat(o[yt][1]),dt=parseFloat(o[yt][2]);break;case 0:pt=(b?"<":"(")+o[yt]+(b?">":")"),ut=He(e),dt=We(n)}void 0!==K&&void 0!==K[yt]&&(mt=K[yt]+" Tw\n"),0===yt?t.push(mt+vt(ut,dt,p)+pt):0===gt?t.push(mt+pt):1===gt&&t.push(mt+vt(ut,dt,p)+pt)}t=0===gt?t.join(" Tj\nT* "):t.join(" Tj\n"),t+=" Tj\n";var wt="BT\n/";return wt+=ft+" "+nt+" Tf\n",wt+=N(nt*_)+" TL\n",wt+=Je+"\n",wt+=v,wt+=t,Z(wt+="ET"),f[ft]=!0,g};var Ae=d.__private__.clip=d.clip=function(t){return Z("evenodd"===t?"W*":"W"),this};d.clipEvenOdd=function(){return Ae("evenodd")},d.__private__.discardPath=d.discardPath=function(){return Z("n"),this};var Le=d.__private__.isValidStyle=function(t){var e=!1;return-1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(t)&&(e=!0),e};d.__private__.setDefaultPathOperation=d.setDefaultPathOperation=function(t){return Le(t)&&(h=t),this};var Ne=d.__private__.getStyle=d.getStyle=function(t){var e=h;switch(t){case"D":case"S":e="S";break;case"F":e="f";break;case"FD":case"DF":e="B";break;case"f":case"f*":case"B":case"B*":e=t}return e},Se=d.close=function(){return Z("h"),this};d.stroke=function(){return Z("S"),this},d.fill=function(t){return ke("f",t),this},d.fillEvenOdd=function(t){return ke("f*",t),this},d.fillStroke=function(t){return ke("B",t),this},d.fillStrokeEvenOdd=function(t){return ke("B*",t),this};var ke=function(t,e){"object"===y(e)?Ce(e,t):Z(t)},Pe=function(t){null===t||x===w&&void 0===t||(t=Ne(t),Z(t))};function Fe(t,e,n,r,i){var a=new $n(e||this.boundingBox,n||this.xStep,r||this.yStep,this.gState,i||this.matrix);a.stream=this.stream;var o=t+"$$"+this.cloneIndex+++"$$";return Et(o,a),a}var Ce=function(t,e){var n=xt[t.key],r=wt[n];if(r instanceof Xn)Z("q"),Z(Ie(e)),r.gState&&d.setGState(r.gState),Z(t.matrix.toString()+" cm"),Z("/"+n+" sh"),Z("Q");else if(r instanceof $n){var i=new jt(1,0,0,-1,0,cn());t.matrix&&(i=i.multiply(t.matrix||Dt),n=Fe.call(r,t.key,t.boundingBox,t.xStep,t.yStep,i).id),Z("q"),Z("/Pattern cs"),Z("/"+n+" scn"),r.gState&&d.setGState(r.gState),Z(e),Z("Q")}},Ie=function(t){switch(t){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},je=d.moveTo=function(t,e){return Z(N(C(t))+" "+N(I(e))+" m"),this},Oe=d.lineTo=function(t,e){return Z(N(C(t))+" "+N(I(e))+" l"),this},De=d.curveTo=function(t,e,n,r,i,a){return Z([N(C(t)),N(I(e)),N(C(n)),N(I(r)),N(C(i)),N(I(a)),"c"].join(" ")),this};d.__private__.line=d.line=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Le(i))throw new Error("Invalid arguments passed to jsPDF.line");return x===v?this.lines([[n-t,r-e]],t,e,[1,1],i||"S"):this.lines([[n-t,r-e]],t,e,[1,1]).stroke()},d.__private__.lines=d.lines=function(t,e,n,r,i,a){var o,s,l,h,c,u,f,d,p,g,m,b;if("number"==typeof t&&(b=n,n=e,e=t,t=b),r=r||[1,1],a=a||!1,isNaN(e)||isNaN(n)||!Array.isArray(t)||!Array.isArray(r)||!Le(i)||"boolean"!=typeof a)throw new Error("Invalid arguments passed to jsPDF.lines");for(je(e,n),o=r[0],s=r[1],h=t.length,g=e,m=n,l=0;l<h;l++)2===(c=t[l]).length?(g=c[0]*o+g,m=c[1]*s+m,Oe(g,m)):(u=c[0]*o+g,f=c[1]*s+m,d=c[2]*o+g,p=c[3]*s+m,g=c[4]*o+g,m=c[5]*s+m,De(u,f,d,p,g,m));return a&&Se(),Pe(i),this},d.path=function(t){for(var e=0;e<t.length;e++){var n=t[e],r=n.c;switch(n.op){case"m":je(r[0],r[1]);break;case"l":Oe(r[0],r[1]);break;case"c":De.apply(this,r);break;case"h":Se()}}return this},d.__private__.rect=d.rect=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Le(i))throw new Error("Invalid arguments passed to jsPDF.rect");return x===v&&(r=-r),Z([N(C(t)),N(I(e)),N(C(n)),N(C(r)),"re"].join(" ")),Pe(i),this},d.__private__.triangle=d.triangle=function(t,e,n,r,i,a,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(a)||!Le(o))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[n-t,r-e],[i-n,a-r],[t-i,e-a]],t,e,[1,1],o,!0),this},d.__private__.roundedRect=d.roundedRect=function(t,e,n,r,i,a,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(a)||!Le(o))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var s=4/3*(Math.SQRT2-1);return i=Math.min(i,.5*n),a=Math.min(a,.5*r),this.lines([[n-2*i,0],[i*s,0,i,a-a*s,i,a],[0,r-2*a],[0,a*s,-i*s,a,-i,a],[2*i-n,0],[-i*s,0,-i,-a*s,-i,-a],[0,2*a-r],[0,-a*s,i*s,-a,i,-a]],t+i,e,[1,1],o,!0),this},d.__private__.ellipse=d.ellipse=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Le(i))throw new Error("Invalid arguments passed to jsPDF.ellipse");var a=4/3*(Math.SQRT2-1)*n,o=4/3*(Math.SQRT2-1)*r;return je(t+n,e),De(t+n,e-o,t+a,e-r,t,e-r),De(t-a,e-r,t-n,e-o,t-n,e),De(t-n,e+o,t-a,e+r,t,e+r),De(t+a,e+r,t+n,e+o,t+n,e),Pe(i),this},d.__private__.circle=d.circle=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||!Le(r))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,n,n,r)},d.setFont=function(t,e,n){return n&&(e=L(e,n)),ft=he(t,e,{disableWarning:!1}),this};var Ee=d.__private__.getFont=d.getFont=function(){return bt[he.apply(d,arguments)]};d.__private__.getFontList=d.getFontList=function(){var t,e,n={};for(t in vt)if(vt.hasOwnProperty(t))for(e in n[t]=[],vt[t])vt[t].hasOwnProperty(e)&&n[t].push(e);return n},d.addFont=function(t,e,n,r,i){var a=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==a.indexOf(arguments[3])?i=arguments[3]:arguments[3]&&-1==a.indexOf(arguments[3])&&(n=L(n,r)),ne.call(this,t,e,n,i=i||"Identity-H")};var Be,Me=t.lineWidth||.200025,Te=d.__private__.getLineWidth=d.getLineWidth=function(){return Me},Re=d.__private__.setLineWidth=d.setLineWidth=function(t){return Me=t,Z(N(C(t))+" w"),this};d.__private__.setLineDash=Zn.API.setLineDash=Zn.API.setLineDashPattern=function(t,e){if(t=t||[],e=e||0,isNaN(e)||!Array.isArray(t))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return t=t.map(function(t){return N(C(t))}).join(" "),e=N(C(e)),Z("["+t+"] "+e+" d"),this};var qe=d.__private__.getLineHeight=d.getLineHeight=function(){return nt*Be};d.__private__.getLineHeight=d.getLineHeight=function(){return nt*Be};var Ue=d.__private__.setLineHeightFactor=d.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(Be=t),this},ze=d.__private__.getLineHeightFactor=d.getLineHeightFactor=function(){return Be};Ue(t.lineHeight);var He=d.__private__.getHorizontalCoordinate=function(t){return C(t)},We=d.__private__.getVerticalCoordinate=function(t){return x===w?t:St[q].mediaBox.topRightY-St[q].mediaBox.bottomLeftY-C(t)},Ge=d.__private__.getHorizontalCoordinateString=d.getHorizontalCoordinateString=function(t){return N(He(t))},Ve=d.__private__.getVerticalCoordinateString=d.getVerticalCoordinateString=function(t){return N(We(t))},Ke=t.strokeColor||"0 G";d.__private__.getStrokeColor=d.getDrawColor=function(){return Ht(Ke)},d.__private__.setStrokeColor=d.setDrawColor=function(t,e,n,r){return Ke=Wt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"draw",precision:2}),Z(Ke),this};var Ye=t.fillColor||"0 g";d.__private__.getFillColor=d.getFillColor=function(){return Ht(Ye)},d.__private__.setFillColor=d.setFillColor=function(t,e,n,r){return Ye=Wt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"fill",precision:2}),Z(Ye),this};var Je=t.textColor||"0 g",Xe=d.__private__.getTextColor=d.getTextColor=function(){return Ht(Je)};d.__private__.setTextColor=d.setTextColor=function(t,e,n,r){return Je=Wt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"text",precision:3}),this};var $e=t.charSpace,Ze=d.__private__.getCharSpace=d.getCharSpace=function(){return parseFloat($e||0)};d.__private__.setCharSpace=d.setCharSpace=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return $e=t,this};var Qe=0;d.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},d.__private__.setLineCap=d.setLineCap=function(t){var e=d.CapJoinStyles[t];if(void 0===e)throw new Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Qe=e,Z(e+" J"),this};var tn=0;d.__private__.setLineJoin=d.setLineJoin=function(t){var e=d.CapJoinStyles[t];if(void 0===e)throw new Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return tn=e,Z(e+" j"),this},d.__private__.setLineMiterLimit=d.__private__.setMiterLimit=d.setLineMiterLimit=d.setMiterLimit=function(t){if(t=t||0,isNaN(t))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return Z(N(C(t))+" M"),this},d.GState=Yn,d.setGState=function(t){(t="string"==typeof t?_t[At[t]]:en(null,t)).equals(Lt)||(Z("/"+t.id+" gs"),Lt=t)};var en=function(t,e){if(!t||!At[t]){var n=!1;for(var r in _t)if(_t.hasOwnProperty(r)&&_t[r].equals(e)){n=!0;break}if(n)e=_t[r];else{var i="GS"+(Object.keys(_t).length+1).toString(10);_t[i]=e,e.id=i}return t&&(At[t]=e.id),kt.publish("addGState",e),e}};d.addGState=function(t,e){return en(t,e),this},d.saveGraphicsState=function(){return Z("q"),yt.push({key:ft,size:nt,color:Je}),this},d.restoreGraphicsState=function(){Z("Q");var t=yt.pop();return ft=t.key,nt=t.size,Je=t.color,Lt=null,this},d.setCurrentTransformationMatrix=function(t){return Z(t.toString()+" cm"),this},d.comment=function(t){return Z("#"+t),this};var nn=function(t,e){var n=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var r=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var i="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return i},set:function(t){i=t.toString()}}),this},rn=function(t,e,n,r){nn.call(this,t,e),this.type="rect";var i=n||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}});var a=r||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return a},set:function(t){isNaN(t)||(a=parseFloat(t))}}),this},an=function(){this.page=Nt,this.currentPage=q,this.pages=Y.slice(0),this.pagesContext=St.slice(0),this.x=pt,this.y=gt,this.matrix=mt,this.width=ln(q),this.height=cn(q),this.outputDestination=X,this.id="",this.objectNumber=-1};an.prototype.restore=function(){Nt=this.page,q=this.currentPage,St=this.pagesContext,Y=this.pages,pt=this.x,gt=this.y,mt=this.matrix,hn(q,this.width),un(q,this.height),X=this.outputDestination};var on=function(t,e,n,r,i){It.push(new an),Nt=q=0,Y=[],pt=t,gt=e,mt=i,ie([n,r])};for(var sn in d.beginFormObject=function(t,e,n,r,i){return on(t,e,n,r,i),this},d.endFormObject=function(t){return function(t){if(Ct[t])It.pop().restore();else{var e=new an,n="Xo"+(Object.keys(Ft).length+1).toString(10);e.id=n,Ct[t]=n,Ft[n]=e,kt.publish("addFormObject",e),It.pop().restore()}}(t),this},d.doFormObject=function(t,e){var n=Ft[Ct[t]];return Z("q"),Z(e.toString()+" cm"),Z("/"+n.id+" Do"),Z("Q"),this},d.getFormObject=function(t){var e=Ft[Ct[t]];return{x:e.x,y:e.y,width:e.width,height:e.height,matrix:e.matrix}},d.save=function(t,e){return t=t||"generated.pdf",(e=e||{}).returnPromise=e.returnPromise||!1,!1===e.returnPromise?(Nn(me(ge()),t),"function"==typeof Nn.unload&&yn.setTimeout&&setTimeout(Nn.unload,911),this):new Promise(function(e,n){try{var i=Nn(me(ge()),t);"function"==typeof Nn.unload&&yn.setTimeout&&setTimeout(Nn.unload,911),e(i)}catch(r){n(r.message)}})},Zn.API)Zn.API.hasOwnProperty(sn)&&("events"===sn&&Zn.API.events.length?function(t,e){var n,r,i;for(i=e.length-1;-1!==i;i--)n=e[i][0],r=e[i][1],t.subscribe.apply(t,[n].concat("function"==typeof r?[r]:r))}(kt,Zn.API.events):d[sn]=Zn.API[sn]);var ln=d.getPageWidth=function(t){return(St[t=t||q].mediaBox.topRightX-St[t].mediaBox.bottomLeftX)/dt},hn=d.setPageWidth=function(t,e){St[t].mediaBox.topRightX=e*dt+St[t].mediaBox.bottomLeftX},cn=d.getPageHeight=function(t){return(St[t=t||q].mediaBox.topRightY-St[t].mediaBox.bottomLeftY)/dt},un=d.setPageHeight=function(t,e){St[t].mediaBox.topRightY=e*dt+St[t].mediaBox.bottomLeftY};return d.internal={pdfEscape:re,getStyle:Ne,getFont:Ee,getFontSize:it,getCharSpace:Ze,getTextColor:Xe,getLineHeight:qe,getLineHeightFactor:ze,getLineWidth:Te,write:Q,getHorizontalCoordinate:He,getVerticalCoordinate:We,getCoordinateString:Ge,getVerticalCoordinateString:Ve,collections:{},newObject:Mt,newAdditionalObject:qt,newObjectDeferred:Tt,newObjectDeferredBegin:Rt,getFilters:Gt,putStream:Vt,events:kt,scaleFactor:dt,pageSize:{getWidth:function(){return ln(q)},setWidth:function(t){hn(q,t)},getHeight:function(){return cn(q)},setHeight:function(t){un(q,t)}},encryptionOptions:c,encryption:ye,getEncryptor:function(t){return null!==c?ye.encryptor(t,0):function(t){return t}},output:be,getNumberOfPages:le,pages:Y,out:Z,f2:P,f3:F,getPageInfo:we,getPageInfoByObjId:xe,getCurrentPageInfo:_e,getPDFVersion:g,Point:nn,Rectangle:rn,Matrix:jt,hasHotfix:ve},Object.defineProperty(d.internal.pageSize,"width",{get:function(){return ln(q)},set:function(t){hn(q,t)},enumerable:!0,configurable:!0}),Object.defineProperty(d.internal.pageSize,"height",{get:function(){return cn(q)},set:function(t){un(q,t)},enumerable:!0,configurable:!0}),function(t){for(var e=0,n=et.length;e<n;e++){var r=ne.call(this,t[e][0],t[e][1],t[e][2],et[e][3],!0);!1===u&&(f[r]=!0);var i=t[e][0].split("-");ee({id:r,fontName:i[0],fontStyle:i[1]||""})}kt.publish("addFonts",{fonts:bt,dictionary:vt})}.call(d,et),ft="F1",ae(i,n),kt.publish("initialized"),d}Gn.prototype.lsbFirstWord=function(t){return String.fromCharCode(255&t,t>>8&255,t>>16&255,t>>24&255)},Gn.prototype.toHexString=function(t){return t.split("").map(function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)}).join("")},Gn.prototype.hexToBytes=function(t){for(var e=[],n=0;n<t.length;n+=2)e.push(String.fromCharCode(parseInt(t.substr(n,2),16)));return e.join("")},Gn.prototype.processOwnerPassword=function(t,e){return Hn(qn(e).substr(0,5),t)},Gn.prototype.encryptor=function(t,e){var n=qn(this.encryptionKey+String.fromCharCode(255&t,t>>8&255,t>>16&255,255&e,e>>8&255)).substr(0,10);return function(t){return Hn(n,t)}},Yn.prototype.equals=function(t){var e,n="id,objectNumber,equals";if(!t||y(t)!==y(this))return!1;var r=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e))return!1;if(this[e]!==t[e])return!1;r++}for(e in t)t.hasOwnProperty(e)&&n.indexOf(e)<0&&r--;return 0===r},Zn.API={events:[]},Zn.version="3.0.2";var Qn=Zn.API,tr=1,er=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},nr=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},rr=function(t){return t.toFixed(2)},ir=function(t){return t.toFixed(5)};Qn.__acroform__={};var ar=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},or=function(t){return t*tr},sr=function(t){var e=new Ar,n=Mr.internal.getHeight(t)||0,r=Mr.internal.getWidth(t)||0;return e.BBox=[0,0,Number(rr(r)),Number(rr(n))],e},lr=Qn.__acroform__.setBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|1<<e},hr=Qn.__acroform__.clearBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&~(1<<e)},cr=Qn.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return t&1<<e?1:0},ur=Qn.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return cr(t,e-1)},fr=Qn.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return lr(t,e-1)},dr=Qn.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return hr(t,e-1)},pr=Qn.__acroform__.calculateCoordinates=function(t,e){var n=e.internal.getHorizontalCoordinate,r=e.internal.getVerticalCoordinate,i=t[0],a=t[1],o=t[2],s=t[3],l={};return l.lowerLeft_X=n(i)||0,l.lowerLeft_Y=r(a+s)||0,l.upperRight_X=n(i+o)||0,l.upperRight_Y=r(a)||0,[Number(rr(l.lowerLeft_X)),Number(rr(l.lowerLeft_Y)),Number(rr(l.upperRight_X)),Number(rr(l.upperRight_Y))]},gr=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],n=t._V||t.DV,r=mr(t,n),i=t.scope.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(t.scope.__private__.encodeColorString(t.color)),e.push("/"+i+" "+rr(r.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(r.text),e.push("ET"),e.push("Q"),e.push("EMC");var a=sr(t);return a.scope=t.scope,a.stream=e.join("\n"),a}},mr=function(t,e){var n=0===t.fontSize?t.maxFontSize:t.fontSize,r={text:"",fontSize:""},i=(e=")"==(e="("==e.substr(0,1)?e.substr(1):e).substr(e.length-1)?e.substr(0,e.length-1):e).split(" ");i=t.multiline?i.map(function(t){return t.split("\n")}):i.map(function(t){return[t]});var a=n,o=Mr.internal.getHeight(t)||0;o=o<0?-o:o;var s=Mr.internal.getWidth(t)||0;s=s<0?-s:s;var l=function(e,n,r){if(e+1<i.length){var a=n+" "+i[e+1][0];return br(a,t,r).width<=s-4}return!1};a++;t:for(;a>0;){e="",a--;var h,c,u=br("3",t,a).height,f=t.multiline?o-a:(o-u)/2,d=f+=2,p=0,g=0,m=0;if(a<=0){e="(...) Tj\n",e+="% Width of Text: "+br(e,t,a=12).width+", FieldWidth:"+s+"\n";break}for(var b="",v=0,y=0;y<i.length;y++)if(i.hasOwnProperty(y)){var w=!1;if(1!==i[y].length&&m!==i[y].length-1){if((u+2)*(v+2)+2>o)continue t;b+=i[y][m],w=!0,g=y,y--}else{b=" "==(b+=i[y][m]+" ").substr(b.length-1)?b.substr(0,b.length-1):b;var x=parseInt(y),_=l(x,b,a),A=y>=i.length-1;if(_&&!A){b+=" ",m=0;continue}if(_||A){if(A)g=x;else if(t.multiline&&(u+2)*(v+2)+2>o)continue t}else{if(!t.multiline)continue t;if((u+2)*(v+2)+2>o)continue t;g=x}}for(var L="",N=p;N<=g;N++){var S=i[N];if(t.multiline){if(N===g){L+=S[m]+" ",m=(m+1)%S.length;continue}if(N===p){L+=S[S.length-1]+" ";continue}}L+=S[0]+" "}switch(L=" "==L.substr(L.length-1)?L.substr(0,L.length-1):L,c=br(L,t,a).width,t.textAlign){case"right":h=s-c-2;break;case"center":h=(s-c)/2;break;default:h=2}e+=rr(h)+" "+rr(d)+" Td\n",e+="("+er(L)+") Tj\n",e+=-rr(h)+" 0 Td\n",d=-(a+2),c=0,p=w?g:g+1,v++,b=""}break}return r.text=e,r.fontSize=a,r},br=function(t,e,n){var r=e.scope.internal.getFont(e.fontName,e.fontStyle),i=e.scope.getStringUnitWidth(t,{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:i}},vr={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},yr=function(t,e){var n={type:"reference",object:t};void 0===e.internal.getPageInfo(t.page).pageContext.annotations.find(function(t){return t.type===n.type&&t.object===n.object})&&e.internal.getPageInfo(t.page).pageContext.annotations.push(n)},wr=Qn.__acroform__.arrayToPdfArray=function(t,e,n){var r=function(t){return t};if(Array.isArray(t)){for(var i="[",a=0;a<t.length;a++)switch(0!==a&&(i+=" "),y(t[a])){case"boolean":case"number":case"object":i+=t[a].toString();break;case"string":"/"!==t[a].substr(0,1)?(void 0!==e&&n&&(r=n.internal.getEncryptor(e)),i+="("+er(r(t[a].toString()))+")"):i+=t[a].toString()}return i+"]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},xr=function(t,e,n){var r=function(t){return t};return void 0!==e&&n&&(r=n.internal.getEncryptor(e)),(t=t||"").toString(),"("+er(r(t))+")"},_r=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(t){this._objId=t}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};_r.prototype.toString=function(){return this.objId+" 0 R"},_r.prototype.putStream=function(){var t=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:t,objectId:this.objId}),this.scope.internal.out("endobj")},_r.prototype.getKeyValueListForStream=function(){var t=[],e=Object.getOwnPropertyNames(this).filter(function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"scope"!=t&&"objId"!=t&&"_"!=t.substring(0,1)});for(var n in e)if(!1===Object.getOwnPropertyDescriptor(this,e[n]).configurable){var r=e[n],i=this[r];i&&(Array.isArray(i)?t.push({key:r,value:wr(i,this.objId,this.scope)}):i instanceof _r?(i.scope=this.scope,t.push({key:r,value:i.objId+" 0 R"})):"function"!=typeof i&&t.push({key:r,value:i}))}return t};var Ar=function(){_r.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var t,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){t=e.trim()},get:function(){return t||null}})};ar(Ar,_r);var Lr=function(){_r.call(this);var t,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(t){var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+er(e(t))+")"}},set:function(e){t=e}})};ar(Lr,_r);var Nr=function t(){_r.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(e,3))},set:function(t){!0===Boolean(t)?this.F=fr(e,3):this.F=dr(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute Ff supplied.');n=t}});var r=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==r.length)return r},set:function(t){r=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[0])?0:r[0]},set:function(t){r[0]=t}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[1])?0:r[1]},set:function(t){r[1]=t}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[2])?0:r[2]},set:function(t){r[2]=t}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[3])?0:r[3]},set:function(t){r[3]=t}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=t;break;default:throw new Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var a=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!a||a.length<1){if(this instanceof Or)return;a="FieldObject"+t.FieldNum++}var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+er(e(a))+")"},set:function(t){a=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return a},set:function(t){a=t}});var o="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return o},set:function(t){o=t}});var s="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var l=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return l},set:function(t){l=t}});var h=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===h?50/tr:h},set:function(t){h=t}});var c="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return c},set:function(t){c=t}});var u="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!u||this instanceof Or||this instanceof Er))return xr(u,this.objId,this.scope)},set:function(t){t=t.toString(),u=t}});var f=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof Cr==0?xr(f,this.objId,this.scope):f},set:function(t){t=t.toString(),f=this instanceof Cr==0?"("===t.substr(0,1)?nr(t.substr(1,t.length-2)):nr(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Cr==1?nr(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof Cr==1?"/"+t:t}});var d=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(d)return d},set:function(t){this.V=t}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof Cr==0?xr(d,this.objId,this.scope):d},set:function(t){t=t.toString(),d=this instanceof Cr==0?"("===t.substr(0,1)?nr(t.substr(1,t.length-2)):nr(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Cr==1?nr(d.substr(1,d.length-1)):d},set:function(t){t=t.toString(),d=this instanceof Cr==1?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var p,g=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return g},set:function(t){t=Boolean(t),g=t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(p)return p},set:function(t){p=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,1))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,1):this.Ff=dr(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,2))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,2):this.Ff=dr(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,3))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,3):this.Ff=dr(this.Ff,3)}});var m=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==m)return m},set:function(t){if(-1===[0,1,2].indexOf(t))throw new Error('Invalid value "'+t+'" for attribute Q supplied.');m=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t;switch(m){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:m=2;break;case"center":case 1:m=1;break;default:m=0}}})};ar(Nr,_r);var Sr=function(){Nr.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var t=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){t=e}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return wr(e,this.objId,this.scope)},set:function(t){var n,r;r=[],"string"==typeof(n=t)&&(r=function(t,e,n){n||(n=1);for(var r,i=[];r=e.exec(t);)i.push(r[n]);return i}(n,/\((.*?)\)/g)),e=r}}),this.getOptions=function(){return e},this.setOptions=function(t){e=t,this.sort&&e.sort()},this.addOption=function(t){t=(t=t||"").toString(),e.push(t),this.sort&&e.sort()},this.removeOption=function(t,n){for(n=n||!1,t=(t=t||"").toString();-1!==e.indexOf(t)&&(e.splice(e.indexOf(t),1),!1!==n););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,18))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,18):this.Ff=dr(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,19))},set:function(t){!0===this.combo&&(!0===Boolean(t)?this.Ff=fr(this.Ff,19):this.Ff=dr(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,20))},set:function(t){!0===Boolean(t)?(this.Ff=fr(this.Ff,20),e.sort()):this.Ff=dr(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,22))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,22):this.Ff=dr(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,23):this.Ff=dr(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,27))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,27):this.Ff=dr(this.Ff,27)}}),this.hasAppearanceStream=!1};ar(Sr,Nr);var kr=function(){Sr.call(this),this.fontName="helvetica",this.combo=!1};ar(kr,Sr);var Pr=function(){kr.call(this),this.combo=!0};ar(Pr,kr);var Fr=function(){Pr.call(this),this.edit=!0};ar(Fr,Pr);var Cr=function(){Nr.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,15))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,15):this.Ff=dr(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,16))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,16):this.Ff=dr(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,17))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,17):this.Ff=dr(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,26):this.Ff=dr(this.Ff,26)}});var t,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(e).length){var n,r=[];for(n in r.push("<<"),e)r.push("/"+n+" ("+er(t(e[n]))+")");return r.push(">>"),r.join("\n")}},set:function(t){"object"===y(t)&&(e=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(t){"string"==typeof t&&(e.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(e){t="/"+e}})};ar(Cr,Nr);var Ir=function(){Cr.call(this),this.pushButton=!0};ar(Ir,Cr);var jr=function(){Cr.call(this),this.radio=!0,this.pushButton=!1;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=void 0!==e?e:[]}})};ar(jr,Cr);var Or=function(){var t,e;Nr.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(t){e=t}});var n,r={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};this.scope&&(t=this.scope.internal.getEncryptor(this.objId));var e,n=[];for(e in n.push("<<"),r)n.push("/"+e+" ("+er(t(r[e]))+")");return n.push(">>"),n.join("\n")},set:function(t){"object"===y(t)&&(r=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return r.CA||""},set:function(t){"string"==typeof t&&(r.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){n=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(t){n="/"+t}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Mr.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};ar(Or,Nr),jr.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t)||!("getCA"in t))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=t.createAppearanceStream(n.optionName),n.caption=t.getCA()}},jr.prototype.createOption=function(t){var e=new Or;return e.Parent=this,e.optionName=t,this.Kids.push(e),qr.call(this.scope,e),e};var Dr=function(){Cr.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Mr.CheckBox.createAppearanceStream()};ar(Dr,Cr);var Er=function(){Nr.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,13))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,13):this.Ff=dr(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,21))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,21):this.Ff=dr(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,23):this.Ff=dr(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,24))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,24):this.Ff=dr(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,25))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,25):this.Ff=dr(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,26):this.Ff=dr(this.Ff,26)}});var t=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){Number.isInteger(e)&&(t=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};ar(Er,Nr);var Br=function(){Er.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,14))},set:function(t){!0===Boolean(t)?this.Ff=fr(this.Ff,14):this.Ff=dr(this.Ff,14)}}),this.password=!0};ar(Br,Er);var Mr={CheckBox:{createAppearanceStream:function(){return{N:{On:Mr.CheckBox.YesNormal},D:{On:Mr.CheckBox.YesPushDown,Off:Mr.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=sr(t);e.scope=t.scope;var n=[],r=t.scope.internal.getFont(t.fontName,t.fontStyle).id,i=t.scope.__private__.encodeColorString(t.color),a=mr(t,t.caption);return n.push("0.749023 g"),n.push("0 0 "+rr(Mr.internal.getWidth(t))+" "+rr(Mr.internal.getHeight(t))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+r+" "+rr(a.fontSize)+" Tf "+i),n.push("BT"),n.push(a.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join("\n"),e},YesNormal:function(t){var e=sr(t);e.scope=t.scope;var n=t.scope.internal.getFont(t.fontName,t.fontStyle).id,r=t.scope.__private__.encodeColorString(t.color),i=[],a=Mr.internal.getHeight(t),o=Mr.internal.getWidth(t),s=mr(t,t.caption);return i.push("1 g"),i.push("0 0 "+rr(o)+" "+rr(a)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+rr(o-1)+" "+rr(a-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+n+" "+rr(s.fontSize)+" Tf "+r),i.push(s.text),i.push("ET"),i.push("Q"),e.stream=i.join("\n"),e},OffPushDown:function(t){var e=sr(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+rr(Mr.internal.getWidth(t))+" "+rr(Mr.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:Mr.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=Mr.RadioButton.Circle.YesNormal,e.D[t]=Mr.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=sr(t);e.scope=t.scope;var n=[],r=Mr.internal.getWidth(t)<=Mr.internal.getHeight(t)?Mr.internal.getWidth(t)/4:Mr.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Mr.internal.Bezier_C,a=Number((r*i).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+ir(Mr.internal.getWidth(t)/2)+" "+ir(Mr.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+a+" "+a+" "+r+" 0 "+r+" c"),n.push("-"+a+" "+r+" -"+r+" "+a+" -"+r+" 0 c"),n.push("-"+r+" -"+a+" -"+a+" -"+r+" 0 -"+r+" c"),n.push(a+" -"+r+" "+r+" -"+a+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=sr(t);e.scope=t.scope;var n=[],r=Mr.internal.getWidth(t)<=Mr.internal.getHeight(t)?Mr.internal.getWidth(t)/4:Mr.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),a=Number((i*Mr.internal.Bezier_C).toFixed(5)),o=Number((r*Mr.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+ir(Mr.internal.getWidth(t)/2)+" "+ir(Mr.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),n.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),n.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),n.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+ir(Mr.internal.getWidth(t)/2)+" "+ir(Mr.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+o+" "+o+" "+r+" 0 "+r+" c"),n.push("-"+o+" "+r+" -"+r+" "+o+" -"+r+" 0 c"),n.push("-"+r+" -"+o+" -"+o+" -"+r+" 0 -"+r+" c"),n.push(o+" -"+r+" "+r+" -"+o+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=sr(t);e.scope=t.scope;var n=[],r=Mr.internal.getWidth(t)<=Mr.internal.getHeight(t)?Mr.internal.getWidth(t)/4:Mr.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),a=Number((i*Mr.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+ir(Mr.internal.getWidth(t)/2)+" "+ir(Mr.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),n.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),n.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),n.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:Mr.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=Mr.RadioButton.Cross.YesNormal,e.D[t]=Mr.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=sr(t);e.scope=t.scope;var n=[],r=Mr.internal.calculateCross(t);return n.push("q"),n.push("1 1 "+rr(Mr.internal.getWidth(t)-2)+" "+rr(Mr.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(rr(r.x1.x)+" "+rr(r.x1.y)+" m"),n.push(rr(r.x2.x)+" "+rr(r.x2.y)+" l"),n.push(rr(r.x4.x)+" "+rr(r.x4.y)+" m"),n.push(rr(r.x3.x)+" "+rr(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=sr(t);e.scope=t.scope;var n=Mr.internal.calculateCross(t),r=[];return r.push("0.749023 g"),r.push("0 0 "+rr(Mr.internal.getWidth(t))+" "+rr(Mr.internal.getHeight(t))+" re"),r.push("f"),r.push("q"),r.push("1 1 "+rr(Mr.internal.getWidth(t)-2)+" "+rr(Mr.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(rr(n.x1.x)+" "+rr(n.x1.y)+" m"),r.push(rr(n.x2.x)+" "+rr(n.x2.y)+" l"),r.push(rr(n.x4.x)+" "+rr(n.x4.y)+" m"),r.push(rr(n.x3.x)+" "+rr(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=sr(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+rr(Mr.internal.getWidth(t))+" "+rr(Mr.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=t.scope.internal.getFont(t.fontName,t.fontStyle).id,n=t.scope.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+n}};Mr.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=Mr.internal.getWidth(t),n=Mr.internal.getHeight(t),r=Math.min(e,n);return{x1:{x:(e-r)/2,y:(n-r)/2+r},x2:{x:(e-r)/2+r,y:(n-r)/2},x3:{x:(e-r)/2,y:(n-r)/2},x4:{x:(e-r)/2+r,y:(n-r)/2+r}}}},Mr.internal.getWidth=function(t){var e=0;return"object"===y(t)&&(e=or(t.Rect[2])),e},Mr.internal.getHeight=function(t){var e=0;return"object"===y(t)&&(e=or(t.Rect[3])),e};var Tr,Rr,qr=Qn.addField=function(t){if(function(t,e){if(e.scope=t,void 0!==t.internal&&(void 0===t.internal.acroformPlugin||!1===t.internal.acroformPlugin.isInitialized)){if(Nr.FieldNum=0,t.internal.acroformPlugin=JSON.parse(JSON.stringify(vr)),t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");tr=t.internal.scaleFactor,t.internal.acroformPlugin.acroFormDictionaryRoot=new Lr,t.internal.acroformPlugin.acroFormDictionaryRoot.scope=t,t.internal.acroformPlugin.acroFormDictionaryRoot._eventID=t.internal.events.subscribe("postPutResources",function(){var e;(e=t).internal.events.unsubscribe(e.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete e.internal.acroformPlugin.acroFormDictionaryRoot._eventID,e.internal.acroformPlugin.printedOut=!0}),t.internal.events.subscribe("buildDocument",function(){!function(t){t.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var e=t.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];r.objId=void 0,r.hasAnnotation&&yr(r,t)}}(t)}),t.internal.events.subscribe("putCatalog",function(){!function(t){if(void 0===t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("putCatalogCallback: Root missing.");t.internal.write("/AcroForm "+t.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}(t)}),t.internal.events.subscribe("postPutPages",function(e){!function(t,e){var n=!t;for(var r in t||(e.internal.newObjectDeferredBegin(e.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),e.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),t=t||e.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(t.hasOwnProperty(r)){var i=t[r],a=[],o=i.Rect;if(i.Rect&&(i.Rect=pr(i.Rect,e)),e.internal.newObjectDeferredBegin(i.objId,!0),i.DA=Mr.createDefaultAppearanceStream(i),"object"===y(i)&&"function"==typeof i.getKeyValueListForStream&&(a=i.getKeyValueListForStream()),i.Rect=o,i.hasAppearanceStream&&!i.appearanceStreamContent){var s=gr(i);a.push({key:"AP",value:"<</N "+s+">>"}),e.internal.acroformPlugin.xForms.push(s)}if(i.appearanceStreamContent){var l="";for(var h in i.appearanceStreamContent)if(i.appearanceStreamContent.hasOwnProperty(h)){var c=i.appearanceStreamContent[h];if(l+="/"+h+" ",l+="<<",Object.keys(c).length>=1||Array.isArray(c)){for(var r in c)if(c.hasOwnProperty(r)){var u=c[r];"function"==typeof u&&(u=u.call(e,i)),l+="/"+r+" "+u+" ",e.internal.acroformPlugin.xForms.indexOf(u)>=0||e.internal.acroformPlugin.xForms.push(u)}}else"function"==typeof(u=c)&&(u=u.call(e,i)),l+="/"+r+" "+u,e.internal.acroformPlugin.xForms.indexOf(u)>=0||e.internal.acroformPlugin.xForms.push(u);l+=">>"}a.push({key:"AP",value:"<<\n"+l+">>"})}e.internal.putStream({additionalKeyValues:a,objectId:i.objId}),e.internal.out("endobj")}n&&function(t,e){for(var n in t)if(t.hasOwnProperty(n)){var r=n,i=t[n];e.internal.newObjectDeferredBegin(i.objId,!0),"object"===y(i)&&"function"==typeof i.putStream&&i.putStream(),delete t[r]}}(e.internal.acroformPlugin.xForms,e)}(e,t)}),t.internal.acroformPlugin.isInitialized=!0}}(this,t),!(t instanceof Nr))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=t).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),t.page=t.scope.internal.getCurrentPageInfo().pageNumber,this};function Ur(t){return t.reduce(function(t,e,n){return t[e]=n,t},{})}Qn.AcroFormChoiceField=Sr,Qn.AcroFormListBox=kr,Qn.AcroFormComboBox=Pr,Qn.AcroFormEditBox=Fr,Qn.AcroFormButton=Cr,Qn.AcroFormPushButton=Ir,Qn.AcroFormRadioButton=jr,Qn.AcroFormCheckBox=Dr,Qn.AcroFormTextField=Er,Qn.AcroFormPasswordField=Br,Qn.AcroFormAppearance=Mr,Qn.AcroForm={ChoiceField:Sr,ListBox:kr,ComboBox:Pr,EditBox:Fr,Button:Cr,PushButton:Ir,RadioButton:jr,CheckBox:Dr,TextField:Er,PasswordField:Br,Appearance:Mr},Zn.AcroForm={ChoiceField:Sr,ListBox:kr,ComboBox:Pr,EditBox:Fr,Button:Cr,PushButton:Ir,RadioButton:jr,CheckBox:Dr,TextField:Er,PasswordField:Br,Appearance:Mr},Zn.AcroForm,function(t){var e="addImage_";t.__addimage__={};var n="UNKNOWN",r={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},i=t.__addimage__.getImageFileTypeByImageData=function(t,e){var i,a,o,s,l,h=n;if("RGBA"===(e=e||n)||void 0!==t.data&&t.data instanceof Uint8ClampedArray&&"height"in t&&"width"in t)return"RGBA";if(A(t))for(l in r)for(o=r[l],i=0;i<o.length;i+=1){for(s=!0,a=0;a<o[i].length;a+=1)if(void 0!==o[i][a]&&o[i][a]!==t[a]){s=!1;break}if(!0===s){h=l;break}}else for(l in r)for(o=r[l],i=0;i<o.length;i+=1){for(s=!0,a=0;a<o[i].length;a+=1)if(void 0!==o[i][a]&&o[i][a]!==t.charCodeAt(a)){s=!1;break}if(!0===s){h=l;break}}return h===n&&e!==n&&(h=e),h},a=function t(e){for(var n=this.internal.write,r=this.internal.putStream,i=(0,this.internal.getFilters)();-1!==i.indexOf("FlateEncode");)i.splice(i.indexOf("FlateEncode"),1);e.objectId=this.internal.newObject();var a=[];if(a.push({key:"Type",value:"/XObject"}),a.push({key:"Subtype",value:"/Image"}),a.push({key:"Width",value:e.width}),a.push({key:"Height",value:e.height}),e.colorSpace===b.INDEXED?a.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(e.palette.length/3-1)+" "+("sMask"in e&&void 0!==e.sMask?e.objectId+2:e.objectId+1)+" 0 R]"}):(a.push({key:"ColorSpace",value:"/"+e.colorSpace}),e.colorSpace===b.DEVICE_CMYK&&a.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),a.push({key:"BitsPerComponent",value:e.bitsPerComponent}),"decodeParameters"in e&&void 0!==e.decodeParameters&&a.push({key:"DecodeParms",value:"<<"+e.decodeParameters+">>"}),"transparency"in e&&Array.isArray(e.transparency)){for(var o="",s=0,l=e.transparency.length;s<l;s++)o+=e.transparency[s]+" "+e.transparency[s]+" ";a.push({key:"Mask",value:"["+o+"]"})}void 0!==e.sMask&&a.push({key:"SMask",value:e.objectId+1+" 0 R"});var h=void 0!==e.filter?["/"+e.filter]:void 0;if(r({data:e.data,additionalKeyValues:a,alreadyAppliedFilters:h,objectId:e.objectId}),n("endobj"),"sMask"in e&&void 0!==e.sMask){var c=(null!=e.predictor?"/Predictor "+e.predictor:"")+" /Colors 1 /BitsPerComponent 8 /Columns "+e.width,u={width:e.width,height:e.height,colorSpace:"DeviceGray",bitsPerComponent:e.bitsPerComponent,decodeParameters:c,data:e.sMask};"filter"in e&&(u.filter=e.filter),t.call(this,u)}if(e.colorSpace===b.INDEXED){var f=this.internal.newObject();r({data:N(new Uint8Array(e.palette)),objectId:f}),n("endobj")}},o=function(){var t=this.internal.collections[e+"images"];for(var n in t)a.call(this,t[n])},s=function(){var t,n=this.internal.collections[e+"images"],r=this.internal.write;for(var i in n)r("/I"+(t=n[i]).index,t.objectId,"0","R")},l=function(){this.internal.collections[e+"images"]||(this.internal.collections[e+"images"]={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",s))},h=function(){var t=this.internal.collections[e+"images"];return l.call(this),t},c=function(){return Object.keys(this.internal.collections[e+"images"]).length},u=function(e){return"function"==typeof t["process"+e.toUpperCase()]},f=function(t){return"object"===y(t)&&1===t.nodeType},d=function(e,n){if("IMG"===e.nodeName&&e.hasAttribute("src")){var r=""+e.getAttribute("src");if(0===r.indexOf("data:image/"))return kn(unescape(r).split("base64,").pop());var i=t.loadFile(r,!0);if(void 0!==i)return i}if("CANVAS"===e.nodeName){if(0===e.width||0===e.height)throw new Error("Given canvas must have data. Canvas width: "+e.width+", height: "+e.height);var a;switch(n){case"PNG":a="image/png";break;case"WEBP":a="image/webp";break;default:a="image/jpeg"}return kn(e.toDataURL(a,1).split("base64,").pop())}},p=function(t){var n=this.internal.collections[e+"images"];if(n)for(var r in n)if(t===n[r].alias)return n[r]},g=function(t,e,n){return t||e||(t=-96,e=-96),t<0&&(t=-1*n.width*72/t/this.internal.scaleFactor),e<0&&(e=-1*n.height*72/e/this.internal.scaleFactor),0===t&&(t=e*n.width/n.height),0===e&&(e=t*n.height/n.width),[t,e]},m=function(t,e,n,r,i,a){var o=g.call(this,n,r,i),s=this.internal.getCoordinateString,l=this.internal.getVerticalCoordinateString,c=h.call(this);if(n=o[0],r=o[1],c[i.index]=i,a){a*=Math.PI/180;var u=Math.cos(a),f=Math.sin(a),d=function(t){return t.toFixed(4)},p=[d(u),d(f),d(-1*f),d(u),0,0,"cm"]}this.internal.write("q"),a?(this.internal.write([1,"0","0",1,s(t),l(e+r),"cm"].join(" ")),this.internal.write(p.join(" ")),this.internal.write([s(n),"0","0",s(r),"0","0","cm"].join(" "))):this.internal.write([s(n),"0","0",s(r),s(t),l(e+r),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+i.index+" Do"),this.internal.write("Q")},b=t.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};t.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var v=t.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},w=t.__addimage__.sHashCode=function(t){var e,n,r=0;if("string"==typeof t)for(n=t.length,e=0;e<n;e++)r=(r<<5)-r+t.charCodeAt(e),r|=0;else if(A(t))for(n=t.byteLength/2,e=0;e<n;e++)r=(r<<5)-r+t[e],r|=0;return r},x=t.__addimage__.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(t.substr(-2))&&(e=!1),e},_=t.__addimage__.extractImageFromDataUrl=function(t){if(null==t)return null;if(!(t=t.trim()).startsWith("data:"))return null;var e=t.indexOf(",");return e<0?null:t.substring(0,e).trim().endsWith("base64")?t.substring(e+1):null};t.__addimage__.isArrayBuffer=function(t){return t instanceof ArrayBuffer};var A=t.__addimage__.isArrayBufferView=function(t){return t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array},L=t.__addimage__.binaryStringToUint8Array=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n},N=t.__addimage__.arrayBufferToBinaryString=function(t){for(var e="",n=A(t)?t:new Uint8Array(t),r=0;r<n.length;r+=8192)e+=String.fromCharCode.apply(null,n.subarray(r,r+8192));return e};t.addImage=function(){var t,e,r,i,a,o,s,h,c;if("number"==typeof arguments[1]?(e=n,r=arguments[1],i=arguments[2],a=arguments[3],o=arguments[4],s=arguments[5],h=arguments[6],c=arguments[7]):(e=arguments[1],r=arguments[2],i=arguments[3],a=arguments[4],o=arguments[5],s=arguments[6],h=arguments[7],c=arguments[8]),"object"===y(t=arguments[0])&&!f(t)&&"imageData"in t){var u=t;t=u.imageData,e=u.format||e||n,r=u.x||r||0,i=u.y||i||0,a=u.w||u.width||a,o=u.h||u.height||o,s=u.alias||s,h=u.compression||h,c=u.rotation||u.angle||c}var d=this.internal.getFilters();if(void 0===h&&-1!==d.indexOf("FlateEncode")&&(h="SLOW"),isNaN(r)||isNaN(i))throw new Error("Invalid coordinates passed to jsPDF.addImage");l.call(this);var p=S.call(this,t,e,s,h);return m.call(this,r,i,a,o,p,c),this};var S=function(e,r,a,o){var s,l,h;if("string"==typeof e&&i(e)===n){e=unescape(e);var g=k(e,!1);(""!==g||void 0!==(g=t.loadFile(e,!0)))&&(e=g)}if(f(e)&&(e=d(e,r)),r=i(e,r),!u(r))throw new Error("addImage does not support files of type '"+r+"', please ensure that a plugin for '"+r+"' support is added.");if((null==(h=a)||0===h.length)&&(a=function(t){return"string"==typeof t||A(t)?w(t):A(t.data)?w(t.data):null}(e)),(s=p.call(this,a))||(e instanceof Uint8Array||"RGBA"===r||(l=e,e=L(e)),s=this["process"+r.toUpperCase()](e,c.call(this),a,function(e){return e&&"string"==typeof e&&(e=e.toUpperCase()),e in t.image_compression?e:v.NONE}(o),l)),!s)throw new Error("An unknown error occurred whilst processing the image.");return s},k=t.__addimage__.convertBase64ToBinaryString=function(t,e){e="boolean"!=typeof e||e;var n,r="";if("string"==typeof t){var a;n=null!==(a=_(t))&&void 0!==a?a:t;try{r=kn(n)}catch(i){if(e)throw x(n)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+i.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return r};t.getImageProperties=function(e){var r,a,o="";if(f(e)&&(e=d(e)),"string"==typeof e&&i(e)===n&&(""===(o=k(e,!1))&&(o=t.loadFile(e)||""),e=o),a=i(e),!u(a))throw new Error("addImage does not support files of type '"+a+"', please ensure that a plugin for '"+a+"' support is added.");if(e instanceof Uint8Array||(e=L(e)),!(r=this["process"+a.toUpperCase()](e)))throw new Error("An unknown error occurred whilst processing the image");return r.fileType=a,r}}(Zn.API),
/**
       * @license
       * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
Tr=Zn.API,Rr=function(t){if(void 0!==t&&""!=t)return!0},Zn.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),Tr.events.push(["putPage",function(t){for(var e,n,r,i=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,o=this.internal.getPageInfoByObjId(t.objId),s=t.pageContext.annotations,l=!1,h=0;h<s.length&&!l;h++)switch((e=s[h]).type){case"link":(Rr(e.options.url)||Rr(e.options.pageNumber))&&(l=!0);break;case"reference":case"text":case"freetext":l=!0}if(0!=l){this.internal.write("/Annots [");for(var c=0;c<s.length;c++){e=s[c];var u=this.internal.pdfEscape,f=this.internal.getEncryptor(t.objId);switch(e.type){case"reference":this.internal.write(" "+e.object.objId+" 0 R ");break;case"text":var d=this.internal.newAdditionalObject(),p=this.internal.newAdditionalObject(),g=this.internal.getEncryptor(d.objId),m=e.title||"Note";r="<</Type /Annot /Subtype /Text "+(n="/Rect ["+i(e.bounds.x)+" "+a(e.bounds.y+e.bounds.h)+" "+i(e.bounds.x+e.bounds.w)+" "+a(e.bounds.y)+"] ")+"/Contents ("+u(g(e.contents))+")",r+=" /Popup "+p.objId+" 0 R",r+=" /P "+o.objId+" 0 R",r+=" /T ("+u(g(m))+") >>",d.content=r;var b=d.objId+" 0 R";r="<</Type /Annot /Subtype /Popup "+(n="/Rect ["+i(e.bounds.x+30)+" "+a(e.bounds.y+e.bounds.h)+" "+i(e.bounds.x+e.bounds.w+30)+" "+a(e.bounds.y)+"] ")+" /Parent "+b,e.open&&(r+=" /Open true"),r+=" >>",p.content=r,this.internal.write(d.objId,"0 R",p.objId,"0 R");break;case"freetext":n="/Rect ["+i(e.bounds.x)+" "+a(e.bounds.y)+" "+i(e.bounds.x+e.bounds.w)+" "+a(e.bounds.y+e.bounds.h)+"] ";var v=e.color||"#000000";r="<</Type /Annot /Subtype /FreeText "+n+"/Contents ("+u(f(e.contents))+")",r+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+v+")",r+=" /Border [0 0 0]",r+=" >>",this.internal.write(r);break;case"link":if(e.options.name){var y=this.annotations._nameMap[e.options.name];e.options.pageNumber=y.page,e.options.top=y.y}else e.options.top||(e.options.top=0);if(n="/Rect ["+e.finalBounds.x+" "+e.finalBounds.y+" "+e.finalBounds.w+" "+e.finalBounds.h+"] ",r="",e.options.url)r="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /A <</S /URI /URI ("+u(f(e.options.url))+") >>";else if(e.options.pageNumber)switch(r="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(e.options.pageNumber).objId+" 0 R",e.options.magFactor=e.options.magFactor||"XYZ",e.options.magFactor){case"Fit":r+=" /Fit]";break;case"FitH":r+=" /FitH "+e.options.top+"]";break;case"FitV":e.options.left=e.options.left||0,r+=" /FitV "+e.options.left+"]";break;default:var w=a(e.options.top);e.options.left=e.options.left||0,void 0===e.options.zoom&&(e.options.zoom=0),r+=" /XYZ "+e.options.left+" "+w+" "+e.options.zoom+"]"}""!=r&&(r+=" >>",this.internal.write(r))}}this.internal.write("]")}}]),Tr.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},Tr.link=function(t,e,n,r,i){var a=this.internal.getCurrentPageInfo(),o=this.internal.getCoordinateString,s=this.internal.getVerticalCoordinateString;a.pageContext.annotations.push({finalBounds:{x:o(t),y:s(e),w:o(t+n),h:s(e+r)},options:i,type:"link"})},Tr.textWithLink=function(t,e,n,r){var i,a,o=this.getTextWidth(t),s=this.internal.getLineHeight()/this.internal.scaleFactor;if(void 0!==r.maxWidth){a=r.maxWidth;var l=this.splitTextToSize(t,a).length;i=Math.ceil(s*l)}else a=o,i=s;return this.text(t,e,n,r),n+=.2*s,"center"===r.align&&(e-=o/2),"right"===r.align&&(e-=o),this.link(e,n-s,a,i,r),o},Tr.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor},
/**
       * @license
       * Copyright (c) 2017 Aras Abbasi
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},r={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},i=[1570,1571,1573,1575];t.__arabicParser__={};var a=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==e[t.charCodeAt(0)]},o=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},s=t.__arabicParser__.isArabicEndLetter=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length<=2},l=t.__arabicParser__.isArabicAlfLetter=function(t){return o(t)&&i.indexOf(t.charCodeAt(0))>=0};t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length>=1};var h=t.__arabicParser__.arabicLetterHasFinalForm=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length>=2};t.__arabicParser__.arabicLetterHasInitialForm=function(t){return o(t)&&a(t)&&e[t.charCodeAt(0)].length>=3};var c=t.__arabicParser__.arabicLetterHasMedialForm=function(t){return o(t)&&a(t)&&4==e[t.charCodeAt(0)].length},u=t.__arabicParser__.resolveLigatures=function(t){var e=0,r=n,i="",a=0;for(e=0;e<t.length;e+=1)void 0!==r[t.charCodeAt(e)]?(a++,"number"==typeof(r=r[t.charCodeAt(e)])&&(i+=String.fromCharCode(r),r=n,a=0),e===t.length-1&&(r=n,i+=t.charAt(e-(a-1)),e-=a-1,a=0)):(r=n,i+=t.charAt(e-a),e-=a,a=0);return i};t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==r[t.charCodeAt(0)]};var f=t.__arabicParser__.getCorrectForm=function(t,e,n){return o(t)?!1===a(t)?-1:!h(t)||!o(e)&&!o(n)||!o(n)&&s(e)||s(t)&&!o(e)||s(t)&&l(e)||s(t)&&s(e)?0:c(t)&&o(e)&&!s(e)&&o(n)&&h(n)?3:s(t)||!o(n)?1:2:-1},d=function(t){var n=0,r=0,i=0,a="",s="",l="",h=(t=t||"").split("\\s+"),c=[];for(n=0;n<h.length;n+=1){for(c.push(""),r=0;r<h[n].length;r+=1)a=h[n][r],s=h[n][r-1],l=h[n][r+1],o(a)?(i=f(a,s,l),c[n]+=-1!==i?String.fromCharCode(e[a.charCodeAt(0)][i]):a):c[n]+=a;c[n]=u(c[n])}return c.join(" ")},p=t.__arabicParser__.processArabic=t.processArabic=function(){var t,e="string"==typeof arguments[0]?arguments[0]:arguments[0].text,n=[];if(Array.isArray(e)){var r=0;for(n=[],r=0;r<e.length;r+=1)Array.isArray(e[r])?n.push([d(e[r][0]),e[r][1],e[r][2]]):n.push([d(e[r])]);t=n}else t=d(e);return"string"==typeof arguments[0]?t:(arguments[0].text=t,arguments[0])};t.events.push(["preProcessText",p])}(Zn.API),Zn.API.autoPrint=function(t){var e;return(t=t||{}).variant=t.variant||"non-conform","javascript"===t.variant?this.addJS("print({});"):(this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})),this},
/**
       * @license
       * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e=function(){var t=void 0;Object.defineProperty(this,"pdf",{get:function(){return t},set:function(e){t=e}});var e=150;Object.defineProperty(this,"width",{get:function(){return e},set:function(t){e=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=e+1)}});var n=300;Object.defineProperty(this,"height",{get:function(){return n},set:function(t){n=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=n+1)}});var r=[];Object.defineProperty(this,"childNodes",{get:function(){return r},set:function(t){r=t}});var i={};Object.defineProperty(this,"style",{get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(t,e){var n;if("2d"!==(t=t||"2d"))return null;for(n in e)this.pdf.context2d.hasOwnProperty(n)&&(this.pdf.context2d[n]=e[n]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},t.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Zn.API),function(t){var e={left:0,top:0,bottom:0,right:0},n=!1,r=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),i.call(this))},i=function(){this.internal.__cell__.lastCell=new a,this.internal.__cell__.pages=1},a=function(){var t=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return t},set:function(e){t=e}});var e=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return e},set:function(t){e=t}});var n=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return n},set:function(t){n=t}});var r=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return r},set:function(t){r=t}});var i=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return i},set:function(t){i=t}});var a=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return a},set:function(t){a=t}});var o=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return o},set:function(t){o=t}}),this};a.prototype.clone=function(){return new a(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},a.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(t){return r.call(this),this.internal.__cell__.headerFunction="function"==typeof t?t:void 0,this},t.getTextDimensions=function(t,e){r.call(this);var n=(e=e||{}).fontSize||this.getFontSize(),i=e.font||this.getFont(),a=e.scaleFactor||this.internal.scaleFactor,o=0,s=0,l=0,h=this;if(!Array.isArray(t)&&"string"!=typeof t){if("number"!=typeof t)throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");t=String(t)}var c=e.maxWidth;c>0?"string"==typeof t?t=this.splitTextToSize(t,c):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce(function(t,e){return t.concat(h.splitTextToSize(e,c))},[])):t=Array.isArray(t)?t:[t];for(var u=0;u<t.length;u++)o<(l=this.getStringUnitWidth(t[u],{font:i})*n)&&(o=l);return 0!==o&&(s=t.length),{w:o/=a,h:Math.max((s*n*this.getLineHeightFactor()-n*(this.getLineHeightFactor()-1))/a,0)}},t.cellAddPage=function(){r.call(this),this.addPage();var t=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new a(t.left,t.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var o=t.cell=function(){var t;t=arguments[0]instanceof a?arguments[0]:new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),r.call(this);var i=this.internal.__cell__.lastCell,o=this.internal.__cell__.padding,s=this.internal.__cell__.margins||e,l=this.internal.__cell__.tableHeaderRow,h=this.internal.__cell__.printHeaders;return void 0!==i.lineNumber&&(i.lineNumber===t.lineNumber?(t.x=(i.x||0)+(i.width||0),t.y=i.y||0):i.y+i.height+t.height+s.bottom>this.getPageHeight()?(this.cellAddPage(),t.y=s.top,h&&l&&(this.printHeaderRow(t.lineNumber,!0),t.y+=l[0].height)):t.y=i.y+i.height||t.y),void 0!==t.text[0]&&(this.rect(t.x,t.y,t.width,t.height,!0===n?"FD":void 0),"right"===t.align?this.text(t.text,t.x+t.width-o,t.y+o,{align:"right",baseline:"top"}):"center"===t.align?this.text(t.text,t.x+t.width/2,t.y+o,{align:"center",baseline:"top",maxWidth:t.width-o-o}):this.text(t.text,t.x+o,t.y+o,{align:"left",baseline:"top",maxWidth:t.width-o-o})),this.internal.__cell__.lastCell=t,this};t.table=function(t,n,l,h,c){if(r.call(this),!l)throw new Error("No data for PDF table.");var u,f,d,p,g=[],m=[],b=[],v={},w={},x=[],_=[],A=(c=c||{}).autoSize||!1,L=!1!==c.printHeaders,N=c.css&&void 0!==c.css["font-size"]?16*c.css["font-size"]:c.fontSize||12,S=c.margins||Object.assign({width:this.getPageWidth()},e),k="number"==typeof c.padding?c.padding:3,P=c.headerBackgroundColor||"#c8c8c8",F=c.headerTextColor||"#000";if(i.call(this),this.internal.__cell__.printHeaders=L,this.internal.__cell__.margins=S,this.internal.__cell__.table_font_size=N,this.internal.__cell__.padding=k,this.internal.__cell__.headerBackgroundColor=P,this.internal.__cell__.headerTextColor=F,this.setFontSize(N),null==h)m=g=Object.keys(l[0]),b=g.map(function(){return"left"});else if(Array.isArray(h)&&"object"===y(h[0]))for(g=h.map(function(t){return t.name}),m=h.map(function(t){return t.prompt||t.name||""}),b=h.map(function(t){return t.align||"left"}),u=0;u<h.length;u+=1)w[h[u].name]=.7499990551181103*h[u].width;else Array.isArray(h)&&"string"==typeof h[0]&&(m=g=h,b=g.map(function(){return"left"}));if(A||Array.isArray(h)&&"string"==typeof h[0])for(u=0;u<g.length;u+=1){for(v[p=g[u]]=l.map(function(t){return t[p]}),this.setFont(void 0,"bold"),x.push(this.getTextDimensions(m[u],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),f=v[p],this.setFont(void 0,"normal"),d=0;d<f.length;d+=1)x.push(this.getTextDimensions(f[d],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);w[p]=Math.max.apply(null,x)+k+k,x=[]}if(L){var C={};for(u=0;u<g.length;u+=1)C[g[u]]={},C[g[u]].text=m[u],C[g[u]].align=b[u];var I=s.call(this,C,w);_=g.map(function(e){return new a(t,n,w[e],I,C[e].text,void 0,C[e].align)}),this.setTableHeaderRow(_),this.printHeaderRow(1,!1)}var j=h.reduce(function(t,e){return t[e.name]=e.align,t},{});for(u=0;u<l.length;u+=1){"rowStart"in c&&c.rowStart instanceof Function&&c.rowStart({row:u,data:l[u]},this);var O=s.call(this,l[u],w);for(d=0;d<g.length;d+=1){var D=l[u][g[d]];"cellStart"in c&&c.cellStart instanceof Function&&c.cellStart({row:u,col:d,data:D},this),o.call(this,new a(t,n,w[g[d]],O,D,u+2,j[g[d]]))}}return this.internal.__cell__.table_x=t,this.internal.__cell__.table_y=n,this};var s=function(t,e){var n=this.internal.__cell__.padding,r=this.internal.__cell__.table_font_size,i=this.internal.scaleFactor;return Object.keys(t).map(function(r){var i=t[r];return this.splitTextToSize(i.hasOwnProperty("text")?i.text:i,e[r]-n-n)},this).map(function(t){return this.getLineHeightFactor()*t.length*r/i+n+n},this).reduce(function(t,e){return Math.max(t,e)},0)};t.setTableHeaderRow=function(t){r.call(this),this.internal.__cell__.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(r.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var i;if(n=!0,"function"==typeof this.internal.__cell__.headerFunction){var s=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new a(s[0],s[1],s[2],s[3],void 0,-1)}this.setFont(void 0,"bold");for(var l=[],h=0;h<this.internal.__cell__.tableHeaderRow.length;h+=1){i=this.internal.__cell__.tableHeaderRow[h].clone(),e&&(i.y=this.internal.__cell__.margins.top||0,l.push(i)),i.lineNumber=t;var c=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),o.call(this,i),this.setTextColor(c)}l.length>0&&this.setTableHeaderRow(l),this.setFont(void 0,"normal"),n=!1}}(Zn.API);var zr={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Hr=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Wr=Ur(Hr),Gr=[100,200,300,400,500,600,700,800,900],Vr=Ur(Gr);function Kr(t){var e=t.family.replace(/"|'/g,"").toLowerCase(),n=function(t){return zr[t=t||"normal"]?t:"normal"}(t.style),r=function(t){return t?"number"==typeof t?t>=100&&t<=900&&t%100==0?t:400:/^\d00$/.test(t)?parseInt(t):"bold"===t?700:400:400}(t.weight),i=function(t){return"number"==typeof Wr[t=t||"normal"]?t:"normal"}(t.stretch);return{family:e,style:n,weight:r,stretch:i,src:t.src||[],ref:t.ref||{name:e,style:[i,n,r].join(" ")}}}function Yr(t,e,n,r){var i;for(i=n;i>=0&&i<e.length;i+=r)if(t[e[i]])return t[e[i]];for(i=n;i>=0&&i<e.length;i-=r)if(t[e[i]])return t[e[i]]}var Jr={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},Xr={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function $r(t){return[t.stretch,t.style,t.weight,t.family].join(" ")}function Zr(t){return t.trimLeft()}function Qr(t,e){for(var n=0;n<t.length;){if(t.charAt(n)===e)return[t.substring(0,n),t.substring(n+1)];n+=1}return null}function ti(t){var e=t.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===e?null:[e[0],t.substring(e[0].length)]}var ei,ni,ri,ii,ai,oi,si,li,hi=["times"];function ci(t,e,n,r){var i=4,a=di;switch(r){case Zn.API.image_compression.FAST:i=1,a=fi;break;case Zn.API.image_compression.MEDIUM:i=6,a=pi;break;case Zn.API.image_compression.SLOW:i=9,a=gi}t=function(t,e,n,r){for(var i,a=t.length/e,o=new Uint8Array(t.length+a),s=[ui,fi,di,pi,gi],l=0;l<a;l+=1){var h=l*e,c=t.subarray(h,h+e);if(r)o.set(r(c,n,i),h+l);else{for(var u=s.length,f=[],d=0;d<u;d+=1)f[d]=s[d](c,n,i);var p=bi(f.concat());o.set(f[p],h+l)}i=c}return o}(t,e,n,a);var o=Q(t,{level:i});return Zn.API.__addimage__.arrayBufferToBinaryString(o)}function ui(t){var e=Array.apply([],t);return e.unshift(0),e}function fi(t,e){var n=t.length,r=[];r[0]=1;for(var i=0;i<n;i+=1){var a=t[i-e]||0;r[i+1]=t[i]-a+256&255}return r}function di(t,e,n){var r=t.length,i=[];i[0]=2;for(var a=0;a<r;a+=1){var o=n&&n[a]||0;i[a+1]=t[a]-o+256&255}return i}function pi(t,e,n){var r=t.length,i=[];i[0]=3;for(var a=0;a<r;a+=1){var o=t[a-e]||0,s=n&&n[a]||0;i[a+1]=t[a]+256-(o+s>>>1)&255}return i}function gi(t,e,n){var r=t.length,i=[];i[0]=4;for(var a=0;a<r;a+=1){var o=mi(t[a-e]||0,n&&n[a]||0,n&&n[a-e]||0);i[a+1]=t[a]-o+256&255}return i}function mi(t,e,n){if(t===e&&e===n)return t;var r=Math.abs(e-n),i=Math.abs(t-n),a=Math.abs(t+e-n-n);return r<=i&&r<=a?t:i<=a?e:n}function bi(t){var e=t.map(function(t){return t.reduce(function(t,e){return t+Math.abs(e)},0)});return e.indexOf(Math.min.apply(null,e))}function vi(t,e,n){var r=e*n,i=Math.floor(r/8),a=16-(r-8*i+n),o=(1<<n)-1;return wi(t,i)>>a&o}function yi(t,e,n,r){var i=n*r,a=Math.floor(i/8),o=16-(i-8*a+r),s=(1<<r)-1,l=(e&s)<<o;!function(t,e,n){if(e+1<t.byteLength)t.setUint16(e,n,!1);else{var r=n>>8&255;t.setUint8(e,r)}}
/**
       * @license
       * (c) Dean McNamee <<EMAIL>>, 2013.
       *
       * https://github.com/deanm/omggif
       *
       * Permission is hereby granted, free of charge, to any person obtaining a copy
       * of this software and associated documentation files (the "Software"), to
       * deal in the Software without restriction, including without limitation the
       * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
       * sell copies of the Software, and to permit persons to whom the Software is
       * furnished to do so, subject to the following conditions:
       *
       * The above copyright notice and this permission notice shall be included in
       * all copies or substantial portions of the Software.
       *
       * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
       * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
       * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
       * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
       * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
       * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
       * IN THE SOFTWARE.
       *
       * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
       * including animation and compression.  It does not rely on any specific
       * underlying system, so should run in the browser, Node, or Plask.
       */(t,a,wi(t,a)&~(s<<o)&65535|l)}function wi(t,e){return e+1<t.byteLength?t.getUint16(e,!1):t.getUint8(e)<<8}function xi(t){var e=0;if(71!==t[e++]||73!==t[e++]||70!==t[e++]||56!==t[e++]||56!=(t[e++]+1&253)||97!==t[e++])throw new Error("Invalid GIF 87a/89a header.");var n=t[e++]|t[e++]<<8,r=t[e++]|t[e++]<<8,i=t[e++],a=i>>7,o=1<<1+(7&i);t[e++],t[e++];var s=null,l=null;a&&(s=e,l=o,e+=3*o);var h=!0,c=[],u=0,f=null,d=0,p=null;for(this.width=n,this.height=r;h&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(11!==t[e]||78==t[e+1]&&69==t[e+2]&&84==t[e+3]&&83==t[e+4]&&67==t[e+5]&&65==t[e+6]&&80==t[e+7]&&69==t[e+8]&&50==t[e+9]&&46==t[e+10]&&48==t[e+11]&&3==t[e+12]&&1==t[e+13]&&0==t[e+16])e+=14,p=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((k=t[e++])>=0))throw Error("Invalid block size");if(0===k)break;e+=k}break;case 249:if(4!==t[e++]||0!==t[e+4])throw new Error("Invalid graphics extension block.");var g=t[e++];u=t[e++]|t[e++]<<8,f=t[e++],1&g||(f=null),d=g>>2&7,e++;break;case 254:for(;;){if(!((k=t[e++])>=0))throw Error("Invalid block size");if(0===k)break;e+=k}break;default:throw new Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var m=t[e++]|t[e++]<<8,b=t[e++]|t[e++]<<8,v=t[e++]|t[e++]<<8,y=t[e++]|t[e++]<<8,w=t[e++],x=w>>6&1,_=1<<1+(7&w),A=s,L=l,N=!1;w>>7&&(N=!0,A=e,L=_,e+=3*_);var S=e;for(e++;;){var k;if(!((k=t[e++])>=0))throw Error("Invalid block size");if(0===k)break;e+=k}c.push({x:m,y:b,width:v,height:y,has_local_palette:N,palette_offset:A,palette_size:L,data_offset:S,data_length:e-S,transparent_index:f,interlaced:!!x,delay:u,disposal:d});break;case 59:h=!1;break;default:throw new Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return c.length},this.loopCount=function(){return p},this.frameInfo=function(t){if(t<0||t>=c.length)throw new Error("Frame index out of range.");return c[t]},this.decodeAndBlitFrameBGRA=function(e,r){var i=this.frameInfo(e),a=i.width*i.height,o=new Uint8Array(a);_i(t,i.data_offset,o,a);var s=i.palette_offset,l=i.transparent_index;null===l&&(l=256);var h=i.width,c=n-h,u=h,f=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),p=f,g=4*c;!0===i.interlaced&&(g+=4*n*7);for(var m=8,b=0,v=o.length;b<v;++b){var y=o[b];if(0===u&&(u=h,(p+=g)>=d&&(g=4*c+4*n*(m-1),p=f+(h+c)*(m<<1),m>>=1)),y===l)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],_=t[s+3*y+2];r[p++]=_,r[p++]=x,r[p++]=w,r[p++]=255}--u}},this.decodeAndBlitFrameRGBA=function(e,r){var i=this.frameInfo(e),a=i.width*i.height,o=new Uint8Array(a);_i(t,i.data_offset,o,a);var s=i.palette_offset,l=i.transparent_index;null===l&&(l=256);var h=i.width,c=n-h,u=h,f=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),p=f,g=4*c;!0===i.interlaced&&(g+=4*n*7);for(var m=8,b=0,v=o.length;b<v;++b){var y=o[b];if(0===u&&(u=h,(p+=g)>=d&&(g=4*c+4*n*(m-1),p=f+(h+c)*(m<<1),m>>=1)),y===l)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],_=t[s+3*y+2];r[p++]=w,r[p++]=x,r[p++]=_,r[p++]=255}--u}}}function _i(t,e,n,r){for(var i=t[e++],a=1<<i,o=a+1,s=o+1,l=i+1,h=(1<<l)-1,c=0,u=0,f=0,d=t[e++],p=new Int32Array(4096),g=null;;){for(;c<16&&0!==d;)u|=t[e++]<<c,c+=8,1===d?d=t[e++]:--d;if(c<l)break;var m=u&h;if(u>>=l,c-=l,m!==a){if(m===o)break;for(var b=m<s?m:g,v=0,y=b;y>a;)y=p[y]>>8,++v;var w=y;if(f+v+(b!==m?1:0)>r)return void xn.log("Warning, gif stream longer than expected.");n[f++]=w;var x=f+=v;for(b!==m&&(n[f++]=w),y=b;v--;)y=p[y],n[--x]=255&y,y>>=8;null!==g&&s<4096&&(p[s++]=g<<8|w,s>=h+1&&l<12&&(++l,h=h<<1|1)),g=m}else s=o+1,h=(1<<(l=i+1))-1,g=null}return f!==r&&xn.log("Warning, gif stream shorter than expected."),n
/**
       * @license
        Copyright (c) 2008, Adobe Systems Incorporated
        All rights reserved.

        Redistribution and use in source and binary forms, with or without 
        modification, are permitted provided that the following conditions are
        met:

        * Redistributions of source code must retain the above copyright notice, 
          this list of conditions and the following disclaimer.
        
        * Redistributions in binary form must reproduce the above copyright
          notice, this list of conditions and the following disclaimer in the 
          documentation and/or other materials provided with the distribution.
        
        * Neither the name of Adobe Systems Incorporated nor the names of its 
          contributors may be used to endorse or promote products derived from 
          this software without specific prior written permission.

        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
        IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
        THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
        PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
        CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
        EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
        PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
        PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
        LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
        NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
        SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      */}function Ai(t){var e,n,r,i,a,o=Math.floor,s=new Array(64),l=new Array(64),h=new Array(64),c=new Array(64),u=new Array(65535),f=new Array(65535),d=new Array(64),p=new Array(64),g=[],m=0,b=7,v=new Array(64),y=new Array(64),w=new Array(64),x=new Array(256),_=new Array(2048),A=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],L=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],N=[0,1,2,3,4,5,6,7,8,9,10,11],S=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],k=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],P=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],F=[0,1,2,3,4,5,6,7,8,9,10,11],C=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],I=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function j(t,e){for(var n=0,r=0,i=new Array,a=1;a<=16;a++){for(var o=1;o<=t[a];o++)i[e[r]]=[],i[e[r]][0]=n,i[e[r]][1]=a,r++,n++;n*=2}return i}function O(t){for(var e=t[0],n=t[1]-1;n>=0;)e&1<<n&&(m|=1<<b),n--,--b<0&&(255==m?(D(255),D(0)):D(m),b=7,m=0)}function D(t){g.push(t)}function E(t){D(t>>8&255),D(255&t)}function B(t,e,n,r,i){for(var a,o=i[0],s=i[240],l=function(t,e){var n,r,i,a,o,s,l,h,c,u,f=0;for(c=0;c<8;++c){n=t[f],r=t[f+1],i=t[f+2],a=t[f+3],o=t[f+4],s=t[f+5],l=t[f+6];var p=n+(h=t[f+7]),g=n-h,m=r+l,b=r-l,v=i+s,y=i-s,w=a+o,x=a-o,_=p+w,A=p-w,L=m+v,N=m-v;t[f]=_+L,t[f+4]=_-L;var S=.707106781*(N+A);t[f+2]=A+S,t[f+6]=A-S;var k=.382683433*((_=x+y)-(N=b+g)),P=.5411961*_+k,F=1.306562965*N+k,C=.707106781*(L=y+b),I=g+C,j=g-C;t[f+5]=j+P,t[f+3]=j-P,t[f+1]=I+F,t[f+7]=I-F,f+=8}for(f=0,c=0;c<8;++c){n=t[f],r=t[f+8],i=t[f+16],a=t[f+24],o=t[f+32],s=t[f+40],l=t[f+48];var O=n+(h=t[f+56]),D=n-h,E=r+l,B=r-l,M=i+s,T=i-s,R=a+o,q=a-o,U=O+R,z=O-R,H=E+M,W=E-M;t[f]=U+H,t[f+32]=U-H;var G=.707106781*(W+z);t[f+16]=z+G,t[f+48]=z-G;var V=.382683433*((U=q+T)-(W=B+D)),K=.5411961*U+V,Y=1.306562965*W+V,J=.707106781*(H=T+B),X=D+J,$=D-J;t[f+40]=$+K,t[f+24]=$-K,t[f+8]=X+Y,t[f+56]=X-Y,f++}for(c=0;c<64;++c)u=t[c]*e[c],d[c]=u>0?u+.5|0:u-.5|0;return d}(t,e),h=0;h<64;++h)p[A[h]]=l[h];var c=p[0]-n;n=p[0],0==c?O(r[0]):(O(r[f[a=32767+c]]),O(u[a]));for(var g=63;g>0&&0==p[g];)g--;if(0==g)return O(o),n;for(var m,b=1;b<=g;){for(var v=b;0==p[b]&&b<=g;)++b;var y=b-v;if(y>=16){m=y>>4;for(var w=1;w<=m;++w)O(s);y&=15}a=32767+p[b],O(i[(y<<4)+f[a]]),O(u[a]),b++}return 63!=g&&O(o),n}function M(t){t=Math.min(Math.max(t,1),100),a!=t&&(function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var r=o((e[n]*t+50)/100);r=Math.min(Math.max(r,1),255),s[A[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],a=0;a<64;a++){var u=o((i[a]*t+50)/100);u=Math.min(Math.max(u,1),255),l[A[a]]=u}for(var f=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],d=0,p=0;p<8;p++)for(var g=0;g<8;g++)h[d]=1/(s[A[d]]*f[p]*f[g]*8),c[d]=1/(l[A[d]]*f[p]*f[g]*8),d++}(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),a=t)}this.encode=function(t,a){a&&M(a),g=new Array,m=0,b=7,E(65496),E(65504),E(16),D(74),D(70),D(73),D(70),D(0),D(1),D(1),D(0),E(1),E(1),D(0),D(0),function(){E(65499),E(132),D(0);for(var t=0;t<64;t++)D(s[t]);D(1);for(var e=0;e<64;e++)D(l[e])}(),function(t,e){E(65472),E(17),D(8),E(e),E(t),D(3),D(1),D(17),D(0),D(2),D(17),D(1),D(3),D(17),D(1)}(t.width,t.height),function(){E(65476),E(418),D(0);for(var t=0;t<16;t++)D(L[t+1]);for(var e=0;e<=11;e++)D(N[e]);D(16);for(var n=0;n<16;n++)D(S[n+1]);for(var r=0;r<=161;r++)D(k[r]);D(1);for(var i=0;i<16;i++)D(P[i+1]);for(var a=0;a<=11;a++)D(F[a]);D(17);for(var o=0;o<16;o++)D(C[o+1]);for(var s=0;s<=161;s++)D(I[s])}(),E(65498),E(12),D(3),D(1),D(0),D(2),D(17),D(3),D(17),D(0),D(63),D(0);var o=0,u=0,f=0;m=0,b=7,this.encode.displayName="_encode_";for(var d,p,x,A,j,T,R,q,U,z=t.data,H=t.width,W=t.height,G=4*H,V=0;V<W;){for(d=0;d<G;){for(j=G*V+d,R=-1,q=0,U=0;U<64;U++)T=j+(q=U>>3)*G+(R=4*(7&U)),V+q>=W&&(T-=G*(V+1+q-W)),d+R>=G&&(T-=d+R-G+4),p=z[T++],x=z[T++],A=z[T++],v[U]=(_[p]+_[x+256|0]+_[A+512|0]>>16)-128,y[U]=(_[p+768|0]+_[x+1024|0]+_[A+1280|0]>>16)-128,w[U]=(_[p+1280|0]+_[x+1536|0]+_[A+1792|0]>>16)-128;o=B(v,h,o,e,r),u=B(y,c,u,n,i),f=B(w,c,f,n,i),d+=32}V+=8}if(b>=0){var K=[];K[1]=b+1,K[0]=(1<<b+1)-1,O(K)}return E(65497),new Uint8Array(g)},t=t||50,function(){for(var t=String.fromCharCode,e=0;e<256;e++)x[e]=t(e)}(),e=j(L,N),n=j(P,F),r=j(S,k),i=j(C,I),function(){for(var t=1,e=2,n=1;n<=15;n++){for(var r=t;r<e;r++)f[32767+r]=n,u[32767+r]=[],u[32767+r][1]=n,u[32767+r][0]=r;for(var i=-(e-1);i<=-t;i++)f[32767+i]=n,u[32767+i]=[],u[32767+i][1]=n,u[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)_[t]=19595*t,_[t+256|0]=38470*t,_[t+512|0]=7471*t+32768,_[t+768|0]=-11059*t,_[t+1024|0]=-21709*t,_[t+1280|0]=32768*t+8421375,_[t+1536|0]=-27439*t,_[t+1792|0]=-5329*t}(),M(t)}
/**
       * @license
       * Copyright (c) 2017 Aras Abbasi
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */function Li(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Ni(t){function e(t){if(!t)throw Error("assert :P")}function n(t,e,n){for(var r=0;4>r;r++)if(t[e+r]!=n.charCodeAt(r))return!0;return!1}function r(t,e,n,r,i){for(var a=0;a<i;a++)t[e+a]=n[r+a]}function i(t,e,n,r){for(var i=0;i<r;i++)t[e+i]=n}function a(t){return new Int32Array(t)}function o(t,e){for(var n=[],r=0;r<t;r++)n.push(new e);return n}function s(t,e){var n=[];return function t(n,r,i){for(var a=i[r],o=0;o<a&&(n.push(i.length>r+1?[]:new e),!(i.length<r+1));o++)t(n[o],r+1,i)}(n,0,t),n}var l=function(){var t=this;function l(t,e){for(var n=1<<e-1>>>0;t&n;)n>>>=1;return n?(t&n-1)+n:t}function h(t,n,r,i,a){e(!(i%r));do{t[n+(i-=r)]=a}while(0<i)}function c(t,n,r,i,o){if(e(2328>=o),512>=o)var s=a(512);else if(null==(s=a(o)))return 0;return function(t,n,r,i,o,s){var c,f,d=n,p=1<<r,g=a(16),m=a(16);for(e(0!=o),e(null!=i),e(null!=t),e(0<r),f=0;f<o;++f){if(15<i[f])return 0;++g[i[f]]}if(g[0]==o)return 0;for(m[1]=0,c=1;15>c;++c){if(g[c]>1<<c)return 0;m[c+1]=m[c]+g[c]}for(f=0;f<o;++f)c=i[f],0<i[f]&&(s[m[c]++]=f);if(1==m[15])return(i=new u).g=0,i.value=s[0],h(t,d,1,p,i),p;var b,v=-1,y=p-1,w=0,x=1,_=1,A=1<<r;for(f=0,c=1,o=2;c<=r;++c,o<<=1){if(x+=_<<=1,0>(_-=g[c]))return 0;for(;0<g[c];--g[c])(i=new u).g=c,i.value=s[f++],h(t,d+w,o,A,i),w=l(w,c)}for(c=r+1,o=2;15>=c;++c,o<<=1){if(x+=_<<=1,0>(_-=g[c]))return 0;for(;0<g[c];--g[c]){if(i=new u,(w&y)!=v){for(d+=A,b=1<<(v=c)-r;15>v&&!(0>=(b-=g[v]));)++v,b<<=1;p+=A=1<<(b=v-r),t[n+(v=w&y)].g=b+r,t[n+v].value=d-n-v}i.g=c-r,i.value=s[f++],h(t,d+(w>>r),o,A,i),w=l(w,c)}}return x!=2*m[15]-1?0:p}(t,n,r,i,o,s)}function u(){this.value=this.g=0}function f(){this.value=this.g=0}function d(){this.G=o(5,u),this.H=a(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=o(Tn,f)}function p(t,n,r,i){e(null!=t),e(null!=n),e(2147483648>i),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=n,t.pa=r,t.Jd=n,t.Yc=r+i,t.Zc=4<=i?r+i-4+1:r,S(t)}function g(t,e){for(var n=0;0<e--;)n|=P(t,128)<<e;return n}function m(t,e){var n=g(t,e);return k(t)?-n:n}function b(t,n,r,i){var a,o=0;for(e(null!=t),e(null!=n),e(4294967288>i),t.Sb=i,t.Ra=0,t.u=0,t.h=0,4<i&&(i=4),a=0;a<i;++a)o+=n[r+a]<<8*a;t.Ra=o,t.bb=i,t.oa=n,t.pa=r}function v(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<Un-8>>>0,++t.bb,t.u-=8;A(t)&&(t.h=1,t.u=0)}function y(t,n){if(e(0<=n),!t.h&&n<=qn){var r=_(t)&Rn[n];return t.u+=n,v(t),r}return t.h=1,t.u=0}function w(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function x(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function _(t){return t.Ra>>>(t.u&Un-1)>>>0}function A(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>Un}function L(t,e){t.u=e,t.h=A(t)}function N(t){t.u>=zn&&(e(t.u>=zn),v(t))}function S(t){e(null!=t&&null!=t.oa),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(null!=t&&null!=t.oa),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function k(t){return g(t,1)}function P(t,e){var n=t.Ca;0>t.b&&S(t);var r=t.b,i=n*e>>>8,a=(t.I>>>r>i)+0;for(a?(n-=i,t.I-=i+1<<r>>>0):n=i+1,r=n,i=0;256<=r;)i+=8,r>>=8;return r=7^i+Hn[r],t.b-=r,t.Ca=(n<<r)-1,a}function F(t,e,n){t[e+0]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=255&n}function C(t,e){return t[e+0]|t[e+1]<<8}function I(t,e){return C(t,e)|t[e+2]<<16}function j(t,e){return C(t,e)|C(t,e+2)<<16}function O(t,n){var r=1<<n;return e(null!=t),e(0<n),t.X=a(r),null==t.X?0:(t.Mb=32-n,t.Xa=n,1)}function D(t,n){e(null!=t),e(null!=n),e(t.Xa==n.Xa),r(n.X,0,t.X,0,1<<n.Xa)}function E(){this.X=[],this.Xa=this.Mb=0}function B(t,n,r,i){e(null!=r),e(null!=i);var a=r[0],o=i[0];return 0==a&&(a=(t*o+n/2)/n),0==o&&(o=(n*a+t/2)/t),0>=a||0>=o?0:(r[0]=a,i[0]=o,1)}function M(t,e){return t+(1<<e)-1>>>e}function T(t,e){return((4278255360&t)+(4278255360&e)>>>0&4278255360)+((16711935&t)+(16711935&e)>>>0&16711935)>>>0}function R(e,n){t[n]=function(n,r,i,a,o,s,l){var h;for(h=0;h<o;++h){var c=t[e](s[l+h-1],i,a+h);s[l+h]=T(n[r+h],c)}}}function q(){this.ud=this.hd=this.jd=0}function U(t,e){return((4278124286&(t^e))>>>1)+(t&e)>>>0}function z(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function H(t,e){return z(t+(t-e+.5>>1))}function W(t,e,n){return Math.abs(e-n)-Math.abs(t-n)}function G(t,e,n,r,i,a,o){for(r=a[o-1],n=0;n<i;++n)a[o+n]=r=T(t[e+n],r)}function V(t,e,n,r,i){var a;for(a=0;a<n;++a){var o=t[e+a],s=o>>8&255,l=16711935&(l=(l=16711935&o)+((s<<16)+s));r[i+a]=(4278255360&o)+l>>>0}}function K(t,e){e.jd=255&t,e.hd=t>>8&255,e.ud=t>>16&255}function Y(t,e,n,r,i,a){var o;for(o=0;o<r;++o){var s=e[n+o],l=s>>>8,h=s,c=255&(c=(c=s>>>16)+((t.jd<<24>>24)*(l<<24>>24)>>>5));h=255&(h=(h+=(t.hd<<24>>24)*(l<<24>>24)>>>5)+((t.ud<<24>>24)*(c<<24>>24)>>>5)),i[a+o]=(4278255360&s)+(c<<16)+h}}function J(e,n,r,i,a){t[n]=function(t,e,n,r,o,s,l,h,c){for(r=l;r<h;++r)for(l=0;l<c;++l)o[s++]=a(n[i(t[e++])])},t[e]=function(e,n,o,s,l,h,c){var u=8>>e.b,f=e.Ea,d=e.K[0],p=e.w;if(8>u)for(e=(1<<e.b)-1,p=(1<<u)-1;n<o;++n){var g,m=0;for(g=0;g<f;++g)g&e||(m=i(s[l++])),h[c++]=a(d[m&p]),m>>=u}else t["VP8LMapColor"+r](s,l,d,p,h,c,n,o,f)}}function X(t,e,n,r,i){for(n=e+n;e<n;){var a=t[e++];r[i++]=a>>16&255,r[i++]=a>>8&255,r[i++]=255&a}}function $(t,e,n,r,i){for(n=e+n;e<n;){var a=t[e++];r[i++]=a>>16&255,r[i++]=a>>8&255,r[i++]=255&a,r[i++]=a>>24&255}}function Z(t,e,n,r,i){for(n=e+n;e<n;){var a=(o=t[e++])>>16&240|o>>12&15,o=240&o|o>>28&15;r[i++]=a,r[i++]=o}}function Q(t,e,n,r,i){for(n=e+n;e<n;){var a=(o=t[e++])>>16&248|o>>13&7,o=o>>5&224|o>>3&31;r[i++]=a,r[i++]=o}}function tt(t,e,n,r,i){for(n=e+n;e<n;){var a=t[e++];r[i++]=255&a,r[i++]=a>>8&255,r[i++]=a>>16&255}}function et(t,e,n,i,a,o){if(0==o)for(n=e+n;e<n;)F(i,((o=t[e++])[0]>>24|o[1]>>8&65280|o[2]<<8&16711680|o[3]<<24)>>>0),a+=32;else r(i,a,t,e,n)}function nt(e,n){t[n][0]=t[e+"0"],t[n][1]=t[e+"1"],t[n][2]=t[e+"2"],t[n][3]=t[e+"3"],t[n][4]=t[e+"4"],t[n][5]=t[e+"5"],t[n][6]=t[e+"6"],t[n][7]=t[e+"7"],t[n][8]=t[e+"8"],t[n][9]=t[e+"9"],t[n][10]=t[e+"10"],t[n][11]=t[e+"11"],t[n][12]=t[e+"12"],t[n][13]=t[e+"13"],t[n][14]=t[e+"0"],t[n][15]=t[e+"0"]}function rt(t){return t==zr||t==Hr||t==Wr||t==Gr}function it(){this.eb=[],this.size=this.A=this.fb=0}function at(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function ot(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new it,this.f.kb=new at,this.sd=null}function st(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function lt(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function ht(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ct(t,e){var n=t.T,i=e.ba.f.RGBA,a=i.eb,o=i.fb+t.ka*i.A,s=mi[e.ba.S],l=t.y,h=t.O,c=t.f,u=t.N,f=t.ea,d=t.W,p=e.cc,g=e.dc,m=e.Mc,b=e.Nc,v=t.ka,y=t.ka+t.T,w=t.U,x=w+1>>1;for(0==v?s(l,h,null,null,c,u,f,d,c,u,f,d,a,o,null,null,w):(s(e.ec,e.fc,l,h,p,g,m,b,c,u,f,d,a,o-i.A,a,o,w),++n);v+2<y;v+=2)p=c,g=u,m=f,b=d,u+=t.Rc,d+=t.Rc,o+=2*i.A,s(l,(h+=2*t.fa)-t.fa,l,h,p,g,m,b,c,u,f,d,a,o-i.A,a,o,w);return h+=t.fa,t.j+y<t.o?(r(e.ec,e.fc,l,h,w),r(e.cc,e.dc,c,u,x),r(e.Mc,e.Nc,f,d,x),n--):1&y||s(l,h,null,null,c,u,f,d,c,u,f,d,a,o+i.A,null,null,w),n}function ut(t,n,r){var i=t.F,a=[t.J];if(null!=i){var o=t.U,s=n.ba.S,l=s==Rr||s==Wr;n=n.ba.f.RGBA;var h=[0],c=t.ka;h[0]=t.T,t.Kb&&(0==c?--h[0]:(--c,a[0]-=t.width),t.j+t.ka+t.T==t.o&&(h[0]=t.o-t.j-c));var u=n.eb;c=n.fb+c*n.A,t=Lr(i,a[0],t.width,o,h,u,c+(l?0:3),n.A),e(r==h),t&&rt(s)&&_r(u,c,l,o,h,n.A)}return 0}function ft(t){var e=t.ma,n=e.ba.S,r=11>n,i=n==Br||n==Tr||n==Rr||n==qr||12==n||rt(n);if(e.memory=null,e.Ib=null,e.Jb=null,e.Nd=null,!En(e.Oa,t,i?11:12))return 0;if(i&&rt(n)&&vn(),t.da)alert("todo:use_scaling");else{if(r){if(e.Ib=ht,t.Kb){if(n=t.U+1>>1,e.memory=a(t.U+2*n),null==e.memory)return 0;e.ec=e.memory,e.fc=0,e.cc=e.ec,e.dc=e.fc+t.U,e.Mc=e.cc,e.Nc=e.dc+n,e.Ib=ct,vn()}}else alert("todo:EmitYUV");i&&(e.Jb=ut,r&&mn())}if(r&&!Fi){for(t=0;256>t;++t)Ci[t]=89858*(t-128)+Ni>>Li,Oi[t]=-22014*(t-128)+Ni,ji[t]=-45773*(t-128),Ii[t]=113618*(t-128)+Ni>>Li;for(t=Si;t<ki;++t)e=76283*(t-16)+Ni>>Li,Di[t-Si]=Gt(e,255),Ei[t-Si]=Gt(e+8>>4,15);Fi=1}return 1}function dt(t){var n=t.ma,r=t.U,i=t.T;return e(!(1&t.ka)),0>=r||0>=i?0:(r=n.Ib(t,n),null!=n.Jb&&n.Jb(t,n,r),n.Dc+=r,1)}function pt(t){t.ma.memory=null}function gt(t,e,n,r){return 47!=y(t,8)?0:(e[0]=y(t,14)+1,n[0]=y(t,14)+1,r[0]=y(t,1),0!=y(t,3)?0:!t.h)}function mt(t,e){if(4>t)return t+1;var n=t-2>>1;return(2+(1&t)<<n)+y(e,n)+1}function bt(t,e){return 120<e?e-120:1<=(n=((n=$r[e-1])>>4)*t+(8-(15&n)))?n:1;var n}function vt(t,e,n){var r=_(n),i=t[e+=255&r].g-8;return 0<i&&(L(n,n.u+8),r=_(n),e+=t[e].value,e+=r&(1<<i)-1),L(n,n.u+t[e].g),t[e].value}function yt(t,n,r){return r.g+=t.g,r.value+=t.value<<n>>>0,e(8>=r.g),t.g}function wt(t,n,r){var i=t.xc;return e((n=0==i?0:t.vc[t.md*(r>>i)+(n>>i)])<t.Wb),t.Ya[n]}function xt(t,n,i,a){var o=t.ab,s=t.c*n,l=t.C;n=l+n;var h=i,c=a;for(a=t.Ta,i=t.Ua;0<o--;){var u=t.gc[o],f=l,d=n,p=h,g=c,m=(c=a,h=i,u.Ea);switch(e(f<d),e(d<=u.nc),u.hc){case 2:Vn(p,g,(d-f)*m,c,h);break;case 0:var b=f,v=d,y=c,w=h,x=(S=u).Ea;0==b&&(Wn(p,g,null,null,1,y,w),G(p,g+1,0,0,x-1,y,w+1),g+=x,w+=x,++b);for(var _=1<<S.b,A=_-1,L=M(x,S.b),N=S.K,S=S.w+(b>>S.b)*L;b<v;){var k=N,P=S,F=1;for(Gn(p,g,y,w-x,1,y,w);F<x;){var C=(F&~A)+_;C>x&&(C=x),(0,$n[k[P++]>>8&15])(p,g+ +F,y,w+F-x,C-F,y,w+F),F=C}g+=x,w+=x,++b&A||(S+=L)}d!=u.nc&&r(c,h-m,c,h+(d-f-1)*m,m);break;case 1:for(m=p,v=g,x=(p=u.Ea)-(w=p&~(y=(g=1<<u.b)-1)),b=M(p,u.b),_=u.K,u=u.w+(f>>u.b)*b;f<d;){for(A=_,L=u,N=new q,S=v+w,k=v+p;v<S;)K(A[L++],N),Zn(N,m,v,g,c,h),v+=g,h+=g;v<k&&(K(A[L++],N),Zn(N,m,v,x,c,h),v+=x,h+=x),++f&y||(u+=b)}break;case 3:if(p==c&&g==h&&0<u.b){for(v=c,p=m=h+(d-f)*m-(w=(d-f)*M(u.Ea,u.b)),g=c,y=h,b=[],w=(x=w)-1;0<=w;--w)b[w]=g[y+w];for(w=x-1;0<=w;--w)v[p+w]=b[w];Kn(u,f,d,c,m,c,h)}else Kn(u,f,d,p,g,c,h)}h=a,c=i}c!=i&&r(a,i,h,c,s)}function _t(t,n){var r=t.V,i=t.Ba+t.c*t.C,a=n-t.C;if(e(n<=t.l.o),e(16>=a),0<a){var o=t.l,s=t.Ta,l=t.Ua,h=o.width;if(xt(t,a,r,i),a=l=[l],e((r=t.C)<(i=n)),e(o.v<o.va),i>o.o&&(i=o.o),r<o.j){var c=o.j-r;r=o.j,a[0]+=c*h}if(r>=i?r=0:(a[0]+=4*o.v,o.ka=r-o.j,o.U=o.va-o.v,o.T=i-r,r=1),r){if(l=l[0],11>(r=t.ca).S){var u=r.f.RGBA,f=(i=r.S,a=o.U,o=o.T,c=u.eb,u.A),d=o;for(u=u.fb+t.Ma*u.A;0<d--;){var p=s,g=l,m=a,b=c,v=u;switch(i){case Er:Qn(p,g,m,b,v);break;case Br:tr(p,g,m,b,v);break;case zr:tr(p,g,m,b,v),_r(b,v,0,m,1,0);break;case Mr:rr(p,g,m,b,v);break;case Tr:et(p,g,m,b,v,1);break;case Hr:et(p,g,m,b,v,1),_r(b,v,0,m,1,0);break;case Rr:et(p,g,m,b,v,0);break;case Wr:et(p,g,m,b,v,0),_r(b,v,1,m,1,0);break;case qr:er(p,g,m,b,v);break;case Gr:er(p,g,m,b,v),Ar(b,v,m,1,0);break;case Ur:nr(p,g,m,b,v);break;default:e(0)}l+=h,u+=f}t.Ma+=o}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=r.height)}}t.C=n,e(t.C<=t.i)}function At(t){var e;if(0<t.ua)return 0;for(e=0;e<t.Wb;++e){var n=t.Ya[e].G,r=t.Ya[e].H;if(0<n[1][r[1]+0].g||0<n[2][r[2]+0].g||0<n[3][r[3]+0].g)return 0}return 1}function Lt(t,n,r,i,a,o){if(0!=t.Z){var s=t.qd,l=t.rd;for(e(null!=gi[t.Z]);n<r;++n)gi[t.Z](s,l,i,a,i,a,o),s=i,l=a,a+=o;t.qd=s,t.rd=l}}function Nt(t,n){var r=t.l.ma,i=0==r.Z||1==r.Z?t.l.j:t.C;if(i=t.C<i?i:t.C,e(n<=t.l.o),n>i){var a=t.l.width,o=r.ca,s=r.tb+a*i,l=t.V,h=t.Ba+t.c*i,c=t.gc;e(1==t.ab),e(3==c[0].hc),Jn(c[0],i,n,l,h,o,s),Lt(r,i,n,o,s,a)}t.C=t.Ma=n}function St(t,n,r,i,a,o,s){var l=t.$/i,h=t.$%i,c=t.m,u=t.s,f=r+t.$,d=f;a=r+i*a;var p=r+i*o,g=280+u.ua,m=t.Pb?l:16777216,b=0<u.ua?u.Wa:null,v=u.wc,y=f<p?wt(u,h,l):null;e(t.C<o),e(p<=a);var w=!1;t:for(;;){for(;w||f<p;){var x=0;if(l>=m){var S=f-r;e((m=t).Pb),m.wd=m.m,m.xd=S,0<m.s.ua&&D(m.s.Wa,m.s.vb),m=l+Qr}if(h&v||(y=wt(u,h,l)),e(null!=y),y.Qb&&(n[f]=y.qb,w=!0),!w)if(N(c),y.jc){x=c,S=n;var k=f,P=y.pd[_(x)&Tn-1];e(y.jc),256>P.g?(L(x,x.u+P.g),S[k]=P.value,x=0):(L(x,x.u+P.g-256),e(256<=P.value),x=P.value),0==x&&(w=!0)}else x=vt(y.G[0],y.H[0],c);if(c.h)break;if(w||256>x){if(!w)if(y.nd)n[f]=(y.qb|x<<8)>>>0;else{if(N(c),w=vt(y.G[1],y.H[1],c),N(c),S=vt(y.G[2],y.H[2],c),k=vt(y.G[3],y.H[3],c),c.h)break;n[f]=(k<<24|w<<16|x<<8|S)>>>0}if(w=!1,++f,++h>=i&&(h=0,++l,null!=s&&l<=o&&!(l%16)&&s(t,l),null!=b))for(;d<f;)x=n[d++],b.X[(506832829*x&**********)>>>b.Mb]=x}else if(280>x){if(x=mt(x-256,c),S=vt(y.G[4],y.H[4],c),N(c),S=bt(i,S=mt(S,c)),c.h)break;if(f-r<S||a-f<x)break t;for(k=0;k<x;++k)n[f+k]=n[f+k-S];for(f+=x,h+=x;h>=i;)h-=i,++l,null!=s&&l<=o&&!(l%16)&&s(t,l);if(e(f<=a),h&v&&(y=wt(u,h,l)),null!=b)for(;d<f;)x=n[d++],b.X[(506832829*x&**********)>>>b.Mb]=x}else{if(!(x<g))break t;for(w=x-280,e(null!=b);d<f;)x=n[d++],b.X[(506832829*x&**********)>>>b.Mb]=x;x=f,e(!(w>>>(S=b).Xa)),n[x]=S.X[w],w=!0}w||e(c.h==A(c))}if(t.Pb&&c.h&&f<a)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&D(t.s.vb,t.s.Wa);else{if(c.h)break t;null!=s&&s(t,l>o?o:l),t.a=0,t.$=f-r}return 1}return t.a=3,0}function kt(t){e(null!=t),t.vc=null,t.yc=null,t.Ya=null;var n=t.Wa;null!=n&&(n.X=null),t.vb=null,e(null!=t)}function Pt(){var e=new on;return null==e?null:(e.a=0,e.xb=pi,nt("Predictor","VP8LPredictors"),nt("Predictor","VP8LPredictors_C"),nt("PredictorAdd","VP8LPredictorsAdd"),nt("PredictorAdd","VP8LPredictorsAdd_C"),Vn=V,Zn=Y,Qn=X,tr=$,er=Z,nr=Q,rr=tt,t.VP8LMapColor32b=Yn,t.VP8LMapColor8b=Xn,e)}function Ft(t,n,r,s,l){var h=1,f=[t],p=[n],g=s.m,m=s.s,b=null,v=0;t:for(;;){if(r)for(;h&&y(g,1);){var w=f,x=p,A=s,S=1,k=A.m,P=A.gc[A.ab],F=y(k,2);if(A.Oc&1<<F)h=0;else{switch(A.Oc|=1<<F,P.hc=F,P.Ea=w[0],P.nc=x[0],P.K=[null],++A.ab,e(4>=A.ab),F){case 0:case 1:P.b=y(k,3)+2,S=Ft(M(P.Ea,P.b),M(P.nc,P.b),0,A,P.K),P.K=P.K[0];break;case 3:var C,I=y(k,8)+1,j=16<I?0:4<I?1:2<I?2:3;if(w[0]=M(P.Ea,j),P.b=j,C=S=Ft(I,1,0,A,P.K)){var D,E=I,B=P,R=1<<(8>>B.b),q=a(R);if(null==q)C=0;else{var U=B.K[0],z=B.w;for(q[0]=B.K[0][0],D=1;D<1*E;++D)q[D]=T(U[z+D],q[D-1]);for(;D<4*R;++D)q[D]=0;B.K[0]=null,B.K[0]=q,C=1}}S=C;break;case 2:break;default:e(0)}h=S}}if(f=f[0],p=p[0],h&&y(g,1)&&!(h=1<=(v=y(g,4))&&11>=v)){s.a=3;break t}var H;if(H=h)e:{var W,G,V,K=s,Y=f,J=p,X=v,$=r,Z=K.m,Q=K.s,tt=[null],et=1,nt=0,rt=Zr[X];n:for(;;){if($&&y(Z,1)){var it=y(Z,3)+2,at=M(Y,it),ot=M(J,it),st=at*ot;if(!Ft(at,ot,0,K,tt))break n;for(tt=tt[0],Q.xc=it,W=0;W<st;++W){var lt=tt[W]>>8&65535;tt[W]=lt,lt>=et&&(et=lt+1)}}if(Z.h)break n;for(G=0;5>G;++G){var ht=Yr[G];!G&&0<X&&(ht+=1<<X),nt<ht&&(nt=ht)}var ct=o(et*rt,u),ut=et,ft=o(ut,d);if(null==ft)var dt=null;else e(65536>=ut),dt=ft;var pt=a(nt);if(null==dt||null==pt||null==ct){K.a=1;break n}var gt=ct;for(W=V=0;W<et;++W){var mt=dt[W],bt=mt.G,vt=mt.H,wt=0,xt=1,_t=0;for(G=0;5>G;++G){ht=Yr[G],bt[G]=gt,vt[G]=V,!G&&0<X&&(ht+=1<<X);r:{var At,Lt=ht,Nt=K,Pt=pt,Ct=gt,It=V,jt=0,Ot=Nt.m,Dt=y(Ot,1);if(i(Pt,0,0,Lt),Dt){var Et=y(Ot,1)+1,Bt=y(Ot,1),Mt=y(Ot,0==Bt?1:8);Pt[Mt]=1,2==Et&&(Pt[Mt=y(Ot,8)]=1);var Tt=1}else{var Rt=a(19),qt=y(Ot,4)+4;if(19<qt){Nt.a=3;var Ut=0;break r}for(At=0;At<qt;++At)Rt[Xr[At]]=y(Ot,3);var zt=void 0,Ht=void 0,Wt=Nt,Gt=Rt,Vt=Lt,Kt=Pt,Yt=0,Jt=Wt.m,Xt=8,$t=o(128,u);i:for(;c($t,0,7,Gt,19);){if(y(Jt,1)){var Zt=2+2*y(Jt,3);if((zt=2+y(Jt,Zt))>Vt)break i}else zt=Vt;for(Ht=0;Ht<Vt&&zt--;){N(Jt);var Qt=$t[0+(127&_(Jt))];L(Jt,Jt.u+Qt.g);var te=Qt.value;if(16>te)Kt[Ht++]=te,0!=te&&(Xt=te);else{var ee=16==te,ne=te-16,re=Kr[ne],ie=y(Jt,Vr[ne])+re;if(Ht+ie>Vt)break i;for(var ae=ee?Xt:0;0<ie--;)Kt[Ht++]=ae}}Yt=1;break i}Yt||(Wt.a=3),Tt=Yt}(Tt=Tt&&!Ot.h)&&(jt=c(Ct,It,8,Pt,Lt)),Tt&&0!=jt?Ut=jt:(Nt.a=3,Ut=0)}if(0==Ut)break n;if(xt&&1==Jr[G]&&(xt=0==gt[V].g),wt+=gt[V].g,V+=Ut,3>=G){var oe,se=pt[0];for(oe=1;oe<ht;++oe)pt[oe]>se&&(se=pt[oe]);_t+=se}}if(mt.nd=xt,mt.Qb=0,xt&&(mt.qb=(bt[3][vt[3]+0].value<<24|bt[1][vt[1]+0].value<<16|bt[2][vt[2]+0].value)>>>0,0==wt&&256>bt[0][vt[0]+0].value&&(mt.Qb=1,mt.qb+=bt[0][vt[0]+0].value<<8)),mt.jc=!mt.Qb&&6>_t,mt.jc){var le,he=mt;for(le=0;le<Tn;++le){var ce=le,ue=he.pd[ce],fe=he.G[0][he.H[0]+ce];256<=fe.value?(ue.g=fe.g+256,ue.value=fe.value):(ue.g=0,ue.value=0,ce>>=yt(fe,8,ue),ce>>=yt(he.G[1][he.H[1]+ce],16,ue),ce>>=yt(he.G[2][he.H[2]+ce],0,ue),yt(he.G[3][he.H[3]+ce],24,ue))}}}Q.vc=tt,Q.Wb=et,Q.Ya=dt,Q.yc=ct,H=1;break e}H=0}if(!(h=H)){s.a=3;break t}if(0<v){if(m.ua=1<<v,!O(m.Wa,v)){s.a=1,h=0;break t}}else m.ua=0;var de=s,pe=f,ge=p,me=de.s,be=me.xc;if(de.c=pe,de.i=ge,me.md=M(pe,be),me.wc=0==be?-1:(1<<be)-1,r){s.xb=di;break t}if(null==(b=a(f*p))){s.a=1,h=0;break t}h=(h=St(s,b,0,f,p,p,null))&&!g.h;break t}return h?(null!=l?l[0]=b:(e(null==b),e(r)),s.$=0,r||kt(m)):kt(m),h}function Ct(t,n){var r=t.c*t.i,i=r+n+16*n;return e(t.c<=n),t.V=a(i),null==t.V?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+r+n,1)}function It(t,n){var r=t.C,i=n-r,a=t.V,o=t.Ba+t.c*r;for(e(n<=t.l.o);0<i;){var s=16<i?16:i,l=t.l.ma,h=t.l.width,c=h*s,u=l.ca,f=l.tb+h*r,d=t.Ta,p=t.Ua;xt(t,s,a,o),Nr(d,p,u,f,c),Lt(l,r,r+s,u,f,h),i-=s,a+=s*t.c,r+=s}e(r==n),t.C=t.Ma=n}function jt(){this.ub=this.yd=this.td=this.Rb=0}function Ot(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Dt(){this.Fb=this.Bb=this.Cb=0,this.Zb=a(4),this.Lb=a(4)}function Et(){this.Yb=function(){var t=[];return function t(e,n,r){for(var i=r[n],a=0;a<i&&(e.push(r.length>n+1?[]:0),!(r.length<n+1));a++)t(e[a],n+1,r)}(t,0,[3,11]),t}()}function Bt(){this.jb=a(3),this.Wc=s([4,8],Et),this.Xc=s([4,17],Et)}function Mt(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new a(4),this.od=new a(4)}function Tt(){this.ld=this.La=this.dd=this.tc=0}function Rt(){this.Na=this.la=0}function qt(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Ut(){this.ad=a(384),this.Za=0,this.Ob=a(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function zt(){this.uc=this.M=this.Nb=0,this.wa=Array(new Tt),this.Y=0,this.ya=Array(new Ut),this.aa=0,this.l=new Vt}function Ht(){this.y=a(16),this.f=a(8),this.ea=a(8)}function Wt(){this.cb=this.a=0,this.sc="",this.m=new w,this.Od=new jt,this.Kc=new Ot,this.ed=new Mt,this.Qa=new Dt,this.Ic=this.$c=this.Aa=0,this.D=new zt,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=o(8,w),this.ia=0,this.pb=o(4,qt),this.Pa=new Bt,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Ht),this.Hd=0,this.rb=Array(new Rt),this.sb=0,this.wa=Array(new Tt),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Ut),this.L=this.aa=0,this.gd=s([4,2],Tt),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Gt(t,e){return 0>t?0:t>e?e:t}function Vt(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Kt(){var t=new Wt;return null!=t&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ni||(ni=$t)),t}function Yt(t,e,n){return 0==t.a&&(t.a=e,t.sc=n,t.cb=0),0}function Jt(t,e,n){return 3<=n&&157==t[e+0]&&1==t[e+1]&&42==t[e+2]}function Xt(t,n){if(null==t)return 0;if(t.a=0,t.sc="OK",null==n)return Yt(t,2,"null VP8Io passed to VP8GetHeaders()");var r=n.data,a=n.w,o=n.ha;if(4>o)return Yt(t,7,"Truncated header.");var s=r[a+0]|r[a+1]<<8|r[a+2]<<16,l=t.Od;if(l.Rb=!(1&s),l.td=s>>1&7,l.yd=s>>4&1,l.ub=s>>5,3<l.td)return Yt(t,3,"Incorrect keyframe parameters.");if(!l.yd)return Yt(t,4,"Frame not displayable.");a+=3,o-=3;var h=t.Kc;if(l.Rb){if(7>o)return Yt(t,7,"cannot parse picture header");if(!Jt(r,a,o))return Yt(t,3,"Bad code word");h.c=16383&(r[a+4]<<8|r[a+3]),h.Td=r[a+4]>>6,h.i=16383&(r[a+6]<<8|r[a+5]),h.Ud=r[a+6]>>6,a+=7,o-=7,t.za=h.c+15>>4,t.Ub=h.i+15>>4,n.width=h.c,n.height=h.i,n.Da=0,n.j=0,n.v=0,n.va=n.width,n.o=n.height,n.da=0,n.ib=n.width,n.hb=n.height,n.U=n.width,n.T=n.height,i((s=t.Pa).jb,0,255,s.jb.length),e(null!=(s=t.Qa)),s.Cb=0,s.Bb=0,s.Fb=1,i(s.Zb,0,0,s.Zb.length),i(s.Lb,0,0,s.Lb)}if(l.ub>o)return Yt(t,7,"bad partition length");p(s=t.m,r,a,l.ub),a+=l.ub,o-=l.ub,l.Rb&&(h.Ld=k(s),h.Kd=k(s)),h=t.Qa;var c,u=t.Pa;if(e(null!=s),e(null!=h),h.Cb=k(s),h.Cb){if(h.Bb=k(s),k(s)){for(h.Fb=k(s),c=0;4>c;++c)h.Zb[c]=k(s)?m(s,7):0;for(c=0;4>c;++c)h.Lb[c]=k(s)?m(s,6):0}if(h.Bb)for(c=0;3>c;++c)u.jb[c]=k(s)?g(s,8):255}else h.Bb=0;if(s.Ka)return Yt(t,3,"cannot parse segment header");if((h=t.ed).zd=k(s),h.Tb=g(s,6),h.wb=g(s,3),h.Pc=k(s),h.Pc&&k(s)){for(u=0;4>u;++u)k(s)&&(h.vd[u]=m(s,6));for(u=0;4>u;++u)k(s)&&(h.od[u]=m(s,6))}if(t.L=0==h.Tb?0:h.zd?1:2,s.Ka)return Yt(t,3,"cannot parse filter header");var f=o;if(o=c=a,a=c+f,h=f,t.Xb=(1<<g(t.m,2))-1,f<3*(u=t.Xb))r=7;else{for(c+=3*u,h-=3*u,f=0;f<u;++f){var d=r[o+0]|r[o+1]<<8|r[o+2]<<16;d>h&&(d=h),p(t.Jc[+f],r,c,d),c+=d,h-=d,o+=3}p(t.Jc[+u],r,c,h),r=c<a?0:5}if(0!=r)return Yt(t,r,"cannot parse partitions");for(r=g(c=t.m,7),o=k(c)?m(c,4):0,a=k(c)?m(c,4):0,h=k(c)?m(c,4):0,u=k(c)?m(c,4):0,c=k(c)?m(c,4):0,f=t.Qa,d=0;4>d;++d){if(f.Cb){var b=f.Zb[d];f.Fb||(b+=r)}else{if(0<d){t.pb[d]=t.pb[0];continue}b=r}var v=t.pb[d];v.Sc[0]=ti[Gt(b+o,127)],v.Sc[1]=ei[Gt(b+0,127)],v.Eb[0]=2*ti[Gt(b+a,127)],v.Eb[1]=101581*ei[Gt(b+h,127)]>>16,8>v.Eb[1]&&(v.Eb[1]=8),v.Qc[0]=ti[Gt(b+u,117)],v.Qc[1]=ei[Gt(b+c,127)],v.lc=b+c}if(!l.Rb)return Yt(t,4,"Not a key frame.");for(k(s),l=t.Pa,r=0;4>r;++r){for(o=0;8>o;++o)for(a=0;3>a;++a)for(h=0;11>h;++h)u=P(s,li[r][o][a][h])?g(s,8):oi[r][o][a][h],l.Wc[r][o].Yb[a][h]=u;for(o=0;17>o;++o)l.Xc[r][o]=l.Wc[r][hi[o]]}return t.kc=k(s),t.kc&&(t.Bd=g(s,8)),t.cb=1}function $t(t,e,n,r,i,a,o){var s=e[i].Yb[n];for(n=0;16>i;++i){if(!P(t,s[n+0]))return i;for(;!P(t,s[n+1]);)if(s=e[++i].Yb[0],n=0,16==i)return 16;var l=e[i+1].Yb;if(P(t,s[n+2])){var h=t,c=0;if(P(h,(f=s)[(u=n)+3]))if(P(h,f[u+6])){for(s=0,u=2*(c=P(h,f[u+8]))+(f=P(h,f[u+9+c])),c=0,f=ri[u];f[s];++s)c+=c+P(h,f[s]);c+=3+(8<<u)}else P(h,f[u+7])?(c=7+2*P(h,165),c+=P(h,145)):c=5+P(h,159);else c=P(h,f[u+4])?3+P(h,f[u+5]):2;s=l[2]}else c=1,s=l[1];l=o+ii[i],0>(h=t).b&&S(h);var u,f=h.b,d=(u=h.Ca>>1)-(h.I>>f)>>31;--h.b,h.Ca+=d,h.Ca|=1,h.I-=(u+1&d)<<f,a[l]=((c^d)-d)*r[(0<i)+0]}return 16}function Zt(t){var e=t.rb[t.sb-1];e.la=0,e.Na=0,i(t.zc,0,0,t.zc.length),t.ja=0}function Qt(t,e,n,r,i){i=t[e+n+32*r]+(i>>3),t[e+n+32*r]=-256&i?0>i?0:255:i}function te(t,e,n,r,i,a){Qt(t,e,0,n,r+i),Qt(t,e,1,n,r+a),Qt(t,e,2,n,r-a),Qt(t,e,3,n,r-i)}function ee(t){return(20091*t>>16)+t}function ne(t,e,n,r){var i,o=0,s=a(16);for(i=0;4>i;++i){var l=t[e+0]+t[e+8],h=t[e+0]-t[e+8],c=(35468*t[e+4]>>16)-ee(t[e+12]),u=ee(t[e+4])+(35468*t[e+12]>>16);s[o+0]=l+u,s[o+1]=h+c,s[o+2]=h-c,s[o+3]=l-u,o+=4,e++}for(i=o=0;4>i;++i)l=(t=s[o+0]+4)+s[o+8],h=t-s[o+8],c=(35468*s[o+4]>>16)-ee(s[o+12]),Qt(n,r,0,0,l+(u=ee(s[o+4])+(35468*s[o+12]>>16))),Qt(n,r,1,0,h+c),Qt(n,r,2,0,h-c),Qt(n,r,3,0,l-u),o++,r+=32}function re(t,e,n,r){var i=t[e+0]+4,a=35468*t[e+4]>>16,o=ee(t[e+4]),s=35468*t[e+1]>>16;te(n,r,0,i+o,t=ee(t[e+1]),s),te(n,r,1,i+a,t,s),te(n,r,2,i-a,t,s),te(n,r,3,i-o,t,s)}function ie(t,e,n,r,i){ne(t,e,n,r),i&&ne(t,e+16,n,r+4)}function ae(t,e,n,r){ar(t,e+0,n,r,1),ar(t,e+32,n,r+128,1)}function oe(t,e,n,r){var i;for(t=t[e+0]+4,i=0;4>i;++i)for(e=0;4>e;++e)Qt(n,r,e,i,t)}function se(t,e,n,r){t[e+0]&&lr(t,e+0,n,r),t[e+16]&&lr(t,e+16,n,r+4),t[e+32]&&lr(t,e+32,n,r+128),t[e+48]&&lr(t,e+48,n,r+128+4)}function le(t,e,n,r){var i,o=a(16);for(i=0;4>i;++i){var s=t[e+0+i]+t[e+12+i],l=t[e+4+i]+t[e+8+i],h=t[e+4+i]-t[e+8+i],c=t[e+0+i]-t[e+12+i];o[0+i]=s+l,o[8+i]=s-l,o[4+i]=c+h,o[12+i]=c-h}for(i=0;4>i;++i)s=(t=o[0+4*i]+3)+o[3+4*i],l=o[1+4*i]+o[2+4*i],h=o[1+4*i]-o[2+4*i],c=t-o[3+4*i],n[r+0]=s+l>>3,n[r+16]=c+h>>3,n[r+32]=s-l>>3,n[r+48]=c-h>>3,r+=64}function he(t,e,n){var r,i=e-32,a=Or,o=255-t[i-1];for(r=0;r<n;++r){var s,l=a,h=o+t[e-1];for(s=0;s<n;++s)t[e+s]=l[h+t[i+s]];e+=32}}function ce(t,e){he(t,e,4)}function ue(t,e){he(t,e,8)}function fe(t,e){he(t,e,16)}function de(t,e){var n;for(n=0;16>n;++n)r(t,e+32*n,t,e-32,16)}function pe(t,e){var n;for(n=16;0<n;--n)i(t,e,t[e-1],16),e+=32}function ge(t,e,n){var r;for(r=0;16>r;++r)i(e,n+32*r,t,16)}function me(t,e){var n,r=16;for(n=0;16>n;++n)r+=t[e-1+32*n]+t[e+n-32];ge(r>>5,t,e)}function be(t,e){var n,r=8;for(n=0;16>n;++n)r+=t[e-1+32*n];ge(r>>4,t,e)}function ve(t,e){var n,r=8;for(n=0;16>n;++n)r+=t[e+n-32];ge(r>>4,t,e)}function ye(t,e){ge(128,t,e)}function we(t,e,n){return t+2*e+n+2>>2}function xe(t,e){var n,i=e-32;for(i=new Uint8Array([we(t[i-1],t[i+0],t[i+1]),we(t[i+0],t[i+1],t[i+2]),we(t[i+1],t[i+2],t[i+3]),we(t[i+2],t[i+3],t[i+4])]),n=0;4>n;++n)r(t,e+32*n,i,0,i.length)}function _e(t,e){var n=t[e-1],r=t[e-1+32],i=t[e-1+64],a=t[e-1+96];F(t,e+0,16843009*we(t[e-1-32],n,r)),F(t,e+32,16843009*we(n,r,i)),F(t,e+64,16843009*we(r,i,a)),F(t,e+96,16843009*we(i,a,a))}function Ae(t,e){var n,r=4;for(n=0;4>n;++n)r+=t[e+n-32]+t[e-1+32*n];for(r>>=3,n=0;4>n;++n)i(t,e+32*n,r,4)}function Le(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1-32],o=t[e+0-32],s=t[e+1-32],l=t[e+2-32],h=t[e+3-32];t[e+0+96]=we(r,i,t[e-1+96]),t[e+1+96]=t[e+0+64]=we(n,r,i),t[e+2+96]=t[e+1+64]=t[e+0+32]=we(a,n,r),t[e+3+96]=t[e+2+64]=t[e+1+32]=t[e+0+0]=we(o,a,n),t[e+3+64]=t[e+2+32]=t[e+1+0]=we(s,o,a),t[e+3+32]=t[e+2+0]=we(l,s,o),t[e+3+0]=we(h,l,s)}function Ne(t,e){var n=t[e+1-32],r=t[e+2-32],i=t[e+3-32],a=t[e+4-32],o=t[e+5-32],s=t[e+6-32],l=t[e+7-32];t[e+0+0]=we(t[e+0-32],n,r),t[e+1+0]=t[e+0+32]=we(n,r,i),t[e+2+0]=t[e+1+32]=t[e+0+64]=we(r,i,a),t[e+3+0]=t[e+2+32]=t[e+1+64]=t[e+0+96]=we(i,a,o),t[e+3+32]=t[e+2+64]=t[e+1+96]=we(a,o,s),t[e+3+64]=t[e+2+96]=we(o,s,l),t[e+3+96]=we(s,l,l)}function Se(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1-32],o=t[e+0-32],s=t[e+1-32],l=t[e+2-32],h=t[e+3-32];t[e+0+0]=t[e+1+64]=a+o+1>>1,t[e+1+0]=t[e+2+64]=o+s+1>>1,t[e+2+0]=t[e+3+64]=s+l+1>>1,t[e+3+0]=l+h+1>>1,t[e+0+96]=we(i,r,n),t[e+0+64]=we(r,n,a),t[e+0+32]=t[e+1+96]=we(n,a,o),t[e+1+32]=t[e+2+96]=we(a,o,s),t[e+2+32]=t[e+3+96]=we(o,s,l),t[e+3+32]=we(s,l,h)}function ke(t,e){var n=t[e+0-32],r=t[e+1-32],i=t[e+2-32],a=t[e+3-32],o=t[e+4-32],s=t[e+5-32],l=t[e+6-32],h=t[e+7-32];t[e+0+0]=n+r+1>>1,t[e+1+0]=t[e+0+64]=r+i+1>>1,t[e+2+0]=t[e+1+64]=i+a+1>>1,t[e+3+0]=t[e+2+64]=a+o+1>>1,t[e+0+32]=we(n,r,i),t[e+1+32]=t[e+0+96]=we(r,i,a),t[e+2+32]=t[e+1+96]=we(i,a,o),t[e+3+32]=t[e+2+96]=we(a,o,s),t[e+3+64]=we(o,s,l),t[e+3+96]=we(s,l,h)}function Pe(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1+96];t[e+0+0]=n+r+1>>1,t[e+2+0]=t[e+0+32]=r+i+1>>1,t[e+2+32]=t[e+0+64]=i+a+1>>1,t[e+1+0]=we(n,r,i),t[e+3+0]=t[e+1+32]=we(r,i,a),t[e+3+32]=t[e+1+64]=we(i,a,a),t[e+3+64]=t[e+2+64]=t[e+0+96]=t[e+1+96]=t[e+2+96]=t[e+3+96]=a}function Fe(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],a=t[e-1+96],o=t[e-1-32],s=t[e+0-32],l=t[e+1-32],h=t[e+2-32];t[e+0+0]=t[e+2+32]=n+o+1>>1,t[e+0+32]=t[e+2+64]=r+n+1>>1,t[e+0+64]=t[e+2+96]=i+r+1>>1,t[e+0+96]=a+i+1>>1,t[e+3+0]=we(s,l,h),t[e+2+0]=we(o,s,l),t[e+1+0]=t[e+3+32]=we(n,o,s),t[e+1+32]=t[e+3+64]=we(r,n,o),t[e+1+64]=t[e+3+96]=we(i,r,n),t[e+1+96]=we(a,i,r)}function Ce(t,e){var n;for(n=0;8>n;++n)r(t,e+32*n,t,e-32,8)}function Ie(t,e){var n;for(n=0;8>n;++n)i(t,e,t[e-1],8),e+=32}function je(t,e,n){var r;for(r=0;8>r;++r)i(e,n+32*r,t,8)}function Oe(t,e){var n,r=8;for(n=0;8>n;++n)r+=t[e+n-32]+t[e-1+32*n];je(r>>4,t,e)}function De(t,e){var n,r=4;for(n=0;8>n;++n)r+=t[e+n-32];je(r>>3,t,e)}function Ee(t,e){var n,r=4;for(n=0;8>n;++n)r+=t[e-1+32*n];je(r>>3,t,e)}function Be(t,e){je(128,t,e)}function Me(t,e,n){var r=t[e-n],i=t[e+0],a=3*(i-r)+Ir[1020+t[e-2*n]-t[e+n]],o=jr[112+(a+4>>3)];t[e-n]=Or[255+r+jr[112+(a+3>>3)]],t[e+0]=Or[255+i-o]}function Te(t,e,n,r){var i=t[e+0],a=t[e+n];return Dr[255+t[e-2*n]-t[e-n]]>r||Dr[255+a-i]>r}function Re(t,e,n,r){return 4*Dr[255+t[e-n]-t[e+0]]+Dr[255+t[e-2*n]-t[e+n]]<=r}function qe(t,e,n,r,i){var a=t[e-3*n],o=t[e-2*n],s=t[e-n],l=t[e+0],h=t[e+n],c=t[e+2*n],u=t[e+3*n];return 4*Dr[255+s-l]+Dr[255+o-h]>r?0:Dr[255+t[e-4*n]-a]<=i&&Dr[255+a-o]<=i&&Dr[255+o-s]<=i&&Dr[255+u-c]<=i&&Dr[255+c-h]<=i&&Dr[255+h-l]<=i}function Ue(t,e,n,r){var i=2*r+1;for(r=0;16>r;++r)Re(t,e+r,n,i)&&Me(t,e+r,n)}function ze(t,e,n,r){var i=2*r+1;for(r=0;16>r;++r)Re(t,e+r*n,1,i)&&Me(t,e+r*n,1)}function He(t,e,n,r){var i;for(i=3;0<i;--i)Ue(t,e+=4*n,n,r)}function We(t,e,n,r){var i;for(i=3;0<i;--i)ze(t,e+=4,n,r)}function Ge(t,e,n,r,i,a,o,s){for(a=2*a+1;0<i--;){if(qe(t,e,n,a,o))if(Te(t,e,n,s))Me(t,e,n);else{var l=t,h=e,c=n,u=l[h-2*c],f=l[h-c],d=l[h+0],p=l[h+c],g=l[h+2*c],m=27*(v=Ir[1020+3*(d-f)+Ir[1020+u-p]])+63>>7,b=18*v+63>>7,v=9*v+63>>7;l[h-3*c]=Or[255+l[h-3*c]+v],l[h-2*c]=Or[255+u+b],l[h-c]=Or[255+f+m],l[h+0]=Or[255+d-m],l[h+c]=Or[255+p-b],l[h+2*c]=Or[255+g-v]}e+=r}}function Ve(t,e,n,r,i,a,o,s){for(a=2*a+1;0<i--;){if(qe(t,e,n,a,o))if(Te(t,e,n,s))Me(t,e,n);else{var l=t,h=e,c=n,u=l[h-c],f=l[h+0],d=l[h+c],p=jr[112+(4+(g=3*(f-u))>>3)],g=jr[112+(g+3>>3)],m=p+1>>1;l[h-2*c]=Or[255+l[h-2*c]+m],l[h-c]=Or[255+u+g],l[h+0]=Or[255+f-p],l[h+c]=Or[255+d-m]}e+=r}}function Ke(t,e,n,r,i,a){Ge(t,e,n,1,16,r,i,a)}function Ye(t,e,n,r,i,a){Ge(t,e,1,n,16,r,i,a)}function Je(t,e,n,r,i,a){var o;for(o=3;0<o;--o)Ve(t,e+=4*n,n,1,16,r,i,a)}function Xe(t,e,n,r,i,a){var o;for(o=3;0<o;--o)Ve(t,e+=4,1,n,16,r,i,a)}function $e(t,e,n,r,i,a,o,s){Ge(t,e,i,1,8,a,o,s),Ge(n,r,i,1,8,a,o,s)}function Ze(t,e,n,r,i,a,o,s){Ge(t,e,1,i,8,a,o,s),Ge(n,r,1,i,8,a,o,s)}function Qe(t,e,n,r,i,a,o,s){Ve(t,e+4*i,i,1,8,a,o,s),Ve(n,r+4*i,i,1,8,a,o,s)}function tn(t,e,n,r,i,a,o,s){Ve(t,e+4,1,i,8,a,o,s),Ve(n,r+4,1,i,8,a,o,s)}function en(){this.ba=new ot,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new lt,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function nn(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function rn(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function an(){this.ua=0,this.Wa=new E,this.vb=new E,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new d,this.yc=new u}function on(){this.xb=this.a=0,this.l=new Vt,this.ca=new ot,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new x,this.Pb=0,this.wd=new x,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new an,this.ab=0,this.gc=o(4,rn),this.Oc=0}function sn(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Vt,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function ln(t,e,n,r,i,a,o){for(t=null==t?0:t[e+0],e=0;e<o;++e)i[a+e]=t+n[r+e]&255,t=i[a+e]}function hn(t,e,n,r,i,a,o){var s;if(null==t)ln(null,null,n,r,i,a,o);else for(s=0;s<o;++s)i[a+s]=t[e+s]+n[r+s]&255}function cn(t,e,n,r,i,a,o){if(null==t)ln(null,null,n,r,i,a,o);else{var s,l=t[e+0],h=l,c=l;for(s=0;s<o;++s)h=c+(l=t[e+s])-h,c=n[r+s]+(-256&h?0>h?0:255:h)&255,h=l,i[a+s]=c}}function un(t,n,i,o){var s=n.width,l=n.o;if(e(null!=t&&null!=n),0>i||0>=o||i+o>l)return null;if(!t.Cc){if(null==t.ga){var h;if(t.ga=new sn,(h=null==t.ga)||(h=n.width*n.o,e(0==t.Gb.length),t.Gb=a(h),t.Uc=0,null==t.Gb?h=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,h=1),h=!h),!h){h=t.ga;var c=t.Fa,u=t.P,f=t.qc,d=t.mb,p=t.nb,g=u+1,m=f-1,v=h.l;if(e(null!=c&&null!=d&&null!=n),gi[0]=null,gi[1]=ln,gi[2]=hn,gi[3]=cn,h.ca=d,h.tb=p,h.c=n.width,h.i=n.height,e(0<h.c&&0<h.i),1>=f)n=0;else if(h.$a=3&c[u+0],h.Z=c[u+0]>>2&3,h.Lc=c[u+0]>>4&3,u=c[u+0]>>6&3,0>h.$a||1<h.$a||4<=h.Z||1<h.Lc||u)n=0;else if(v.put=dt,v.ac=ft,v.bc=pt,v.ma=h,v.width=n.width,v.height=n.height,v.Da=n.Da,v.v=n.v,v.va=n.va,v.j=n.j,v.o=n.o,h.$a)t:{e(1==h.$a),n=Pt();e:for(;;){if(null==n){n=0;break t}if(e(null!=h),h.mc=n,n.c=h.c,n.i=h.i,n.l=h.l,n.l.ma=h,n.l.width=h.c,n.l.height=h.i,n.a=0,b(n.m,c,g,m),!Ft(h.c,h.i,1,n,null))break e;if(1==n.ab&&3==n.gc[0].hc&&At(n.s)?(h.ic=1,c=n.c*n.i,n.Ta=null,n.Ua=0,n.V=a(c),n.Ba=0,null==n.V?(n.a=1,n=0):n=1):(h.ic=0,n=Ct(n,h.c)),!n)break e;n=1;break t}h.mc=null,n=0}else n=m>=h.c*h.i;h=!n}if(h)return null;1!=t.ga.Lc?t.Ga=0:o=l-i}e(null!=t.ga),e(i+o<=l);t:{if(n=(c=t.ga).c,l=c.l.o,0==c.$a){if(g=t.rc,m=t.Vc,v=t.Fa,u=t.P+1+i*n,f=t.mb,d=t.nb+i*n,e(u<=t.P+t.qc),0!=c.Z)for(e(null!=gi[c.Z]),h=0;h<o;++h)gi[c.Z](g,m,v,u,f,d,n),g=f,m=d,d+=n,u+=n;else for(h=0;h<o;++h)r(f,d,v,u,n),g=f,m=d,d+=n,u+=n;t.rc=g,t.Vc=m}else{if(e(null!=c.mc),n=i+o,e(null!=(h=c.mc)),e(n<=h.i),h.C>=n)n=1;else if(c.ic||mn(),c.ic){c=h.V,g=h.Ba,m=h.c;var y=h.i,w=(v=1,u=h.$/m,f=h.$%m,d=h.m,p=h.s,h.$),x=m*y,_=m*n,L=p.wc,S=w<_?wt(p,f,u):null;e(w<=x),e(n<=y),e(At(p));e:for(;;){for(;!d.h&&w<_;){if(f&L||(S=wt(p,f,u)),e(null!=S),N(d),256>(y=vt(S.G[0],S.H[0],d)))c[g+w]=y,++w,++f>=m&&(f=0,++u<=n&&!(u%16)&&Nt(h,u));else{if(!(280>y)){v=0;break e}y=mt(y-256,d);var k,P=vt(S.G[4],S.H[4],d);if(N(d),!(w>=(P=bt(m,P=mt(P,d)))&&x-w>=y)){v=0;break e}for(k=0;k<y;++k)c[g+w+k]=c[g+w+k-P];for(w+=y,f+=y;f>=m;)f-=m,++u<=n&&!(u%16)&&Nt(h,u);w<_&&f&L&&(S=wt(p,f,u))}e(d.h==A(d))}Nt(h,u>n?n:u);break e}!v||d.h&&w<x?(v=0,h.a=d.h?5:3):h.$=w,n=v}else n=St(h,h.V,h.Ba,h.c,h.i,n,It);if(!n){o=0;break t}}i+o>=l&&(t.Cc=1),o=1}if(!o)return null;if(t.Cc&&(null!=(o=t.ga)&&(o.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+i*s}function fn(t,e,n,r,i,a){for(;0<i--;){var o,s=t,l=e+(n?1:0),h=t,c=e+(n?0:3);for(o=0;o<r;++o){var u=h[c+4*o];255!=u&&(u*=32897,s[l+4*o+0]=s[l+4*o+0]*u>>23,s[l+4*o+1]=s[l+4*o+1]*u>>23,s[l+4*o+2]=s[l+4*o+2]*u>>23)}e+=a}}function dn(t,e,n,r,i){for(;0<r--;){var a;for(a=0;a<n;++a){var o=t[e+2*a+0],s=15&(h=t[e+2*a+1]),l=4369*s,h=(240&h|h>>4)*l>>16;t[e+2*a+0]=(240&o|o>>4)*l>>16&240|(15&o|o<<4)*l>>16>>4&15,t[e+2*a+1]=240&h|s}e+=i}}function pn(t,e,n,r,i,a,o,s){var l,h,c=255;for(h=0;h<i;++h){for(l=0;l<r;++l){var u=t[e+l];a[o+4*l]=u,c&=u}e+=n,o+=s}return 255!=c}function gn(t,e,n,r,i){var a;for(a=0;a<i;++a)n[r+a]=t[e+a]>>8}function mn(){_r=fn,Ar=dn,Lr=pn,Nr=gn}function bn(n,r,i){t[n]=function(t,n,a,o,s,l,h,c,u,f,d,p,g,m,b,v,y){var w,x=y-1>>1,_=s[l+0]|h[c+0]<<16,A=u[f+0]|d[p+0]<<16;e(null!=t);var L=3*_+A+131074>>2;for(r(t[n+0],255&L,L>>16,g,m),null!=a&&(L=3*A+_+131074>>2,r(a[o+0],255&L,L>>16,b,v)),w=1;w<=x;++w){var N=s[l+w]|h[c+w]<<16,S=u[f+w]|d[p+w]<<16,k=_+N+A+S+524296,P=k+2*(N+A)>>3;L=P+_>>1,_=(k=k+2*(_+S)>>3)+N>>1,r(t[n+2*w-1],255&L,L>>16,g,m+(2*w-1)*i),r(t[n+2*w-0],255&_,_>>16,g,m+(2*w-0)*i),null!=a&&(L=k+A>>1,_=P+S>>1,r(a[o+2*w-1],255&L,L>>16,b,v+(2*w-1)*i),r(a[o+2*w+0],255&_,_>>16,b,v+(2*w+0)*i)),_=N,A=S}1&y||(L=3*_+A+131074>>2,r(t[n+y-1],255&L,L>>16,g,m+(y-1)*i),null!=a&&(L=3*A+_+131074>>2,r(a[o+y-1],255&L,L>>16,b,v+(y-1)*i)))}}function vn(){mi[Er]=bi,mi[Br]=yi,mi[Mr]=vi,mi[Tr]=wi,mi[Rr]=xi,mi[qr]=_i,mi[Ur]=Ai,mi[zr]=yi,mi[Hr]=wi,mi[Wr]=xi,mi[Gr]=_i}function yn(t){return-16384&t?0>t?0:255:t>>Pi}function wn(t,e){return yn((19077*t>>8)+(26149*e>>8)-14234)}function xn(t,e,n){return yn((19077*t>>8)-(6419*e>>8)-(13320*n>>8)+8708)}function _n(t,e){return yn((19077*t>>8)+(33050*e>>8)-17685)}function An(t,e,n,r,i){r[i+0]=wn(t,n),r[i+1]=xn(t,e,n),r[i+2]=_n(t,e)}function Ln(t,e,n,r,i){r[i+0]=_n(t,e),r[i+1]=xn(t,e,n),r[i+2]=wn(t,n)}function Nn(t,e,n,r,i){var a=xn(t,e,n);e=a<<3&224|_n(t,e)>>3,r[i+0]=248&wn(t,n)|a>>5,r[i+1]=e}function Sn(t,e,n,r,i){var a=240&_n(t,e)|15;r[i+0]=240&wn(t,n)|xn(t,e,n)>>4,r[i+1]=a}function kn(t,e,n,r,i){r[i+0]=255,An(t,e,n,r,i+1)}function Pn(t,e,n,r,i){Ln(t,e,n,r,i),r[i+3]=255}function Fn(t,e,n,r,i){An(t,e,n,r,i),r[i+3]=255}function Gt(t,e){return 0>t?0:t>e?e:t}function Cn(e,n,r){t[e]=function(t,e,i,a,o,s,l,h,c){for(var u=h+(-2&c)*r;h!=u;)n(t[e+0],i[a+0],o[s+0],l,h),n(t[e+1],i[a+0],o[s+0],l,h+r),e+=2,++a,++s,h+=2*r;1&c&&n(t[e+0],i[a+0],o[s+0],l,h)}}function In(t,e,n){return 0==n?0==t?0==e?6:5:0==e?4:0:n}function jn(t,e,n,r,i){switch(t>>>30){case 3:ar(e,n,r,i,0);break;case 2:or(e,n,r,i);break;case 1:lr(e,n,r,i)}}function On(t,e){var n,a,o=e.M,s=e.Nb,l=t.oc,h=t.pc+40,c=t.oc,u=t.pc+584,f=t.oc,d=t.pc+600;for(n=0;16>n;++n)l[h+32*n-1]=129;for(n=0;8>n;++n)c[u+32*n-1]=129,f[d+32*n-1]=129;for(0<o?l[h-1-32]=c[u-1-32]=f[d-1-32]=129:(i(l,h-32-1,127,21),i(c,u-32-1,127,9),i(f,d-32-1,127,9)),a=0;a<t.za;++a){var p=e.ya[e.aa+a];if(0<a){for(n=-1;16>n;++n)r(l,h+32*n-4,l,h+32*n+12,4);for(n=-1;8>n;++n)r(c,u+32*n-4,c,u+32*n+4,4),r(f,d+32*n-4,f,d+32*n+4,4)}var g=t.Gd,m=t.Hd+a,b=p.ad,v=p.Hc;if(0<o&&(r(l,h-32,g[m].y,0,16),r(c,u-32,g[m].f,0,8),r(f,d-32,g[m].ea,0,8)),p.Za){var y=l,w=h-32+16;for(0<o&&(a>=t.za-1?i(y,w,g[m].y[15],4):r(y,w,g[m+1].y,0,4)),n=0;4>n;n++)y[w+128+n]=y[w+256+n]=y[w+384+n]=y[w+0+n];for(n=0;16>n;++n,v<<=2)y=l,w=h+Bi[n],ui[p.Ob[n]](y,w),jn(v,b,16*+n,y,w)}else if(y=In(a,o,p.Ob[0]),ci[y](l,h),0!=v)for(n=0;16>n;++n,v<<=2)jn(v,b,16*+n,l,h+Bi[n]);for(n=p.Gc,y=In(a,o,p.Dd),fi[y](c,u),fi[y](f,d),v=b,y=c,w=u,255&(p=0|n)&&(170&p?sr(v,256,y,w):hr(v,256,y,w)),p=f,v=d,255&(n>>=8)&&(170&n?sr(b,320,p,v):hr(b,320,p,v)),o<t.Ub-1&&(r(g[m].y,0,l,h+480,16),r(g[m].f,0,c,u+224,8),r(g[m].ea,0,f,d+224,8)),n=8*s*t.B,g=t.sa,m=t.ta+16*a+16*s*t.R,b=t.qa,p=t.ra+8*a+n,v=t.Ha,y=t.Ia+8*a+n,n=0;16>n;++n)r(g,m+n*t.R,l,h+32*n,16);for(n=0;8>n;++n)r(b,p+n*t.B,c,u+32*n,8),r(v,y+n*t.B,f,d+32*n,8)}}function Dn(t,r,i,a,o,s,l,h,c){var u=[0],f=[0],d=0,p=null!=c?c.kd:0,g=null!=c?c:new nn;if(null==t||12>i)return 7;g.data=t,g.w=r,g.ha=i,r=[r],i=[i],g.gb=[g.gb];t:{var m=r,v=i,y=g.gb;if(e(null!=t),e(null!=v),e(null!=y),y[0]=0,12<=v[0]&&!n(t,m[0],"RIFF")){if(n(t,m[0]+8,"WEBP")){y=3;break t}var w=j(t,m[0]+4);if(12>w||4294967286<w){y=3;break t}if(p&&w>v[0]-8){y=7;break t}y[0]=w,m[0]+=12,v[0]-=12}y=0}if(0!=y)return y;for(w=0<g.gb[0],i=i[0];;){t:{var _=t;v=r,y=i;var A=u,L=f,N=m=[0];if((P=d=[d])[0]=0,8>y[0])y=7;else{if(!n(_,v[0],"VP8X")){if(10!=j(_,v[0]+4)){y=3;break t}if(18>y[0]){y=7;break t}var S=j(_,v[0]+8),k=1+I(_,v[0]+12);if(2147483648<=k*(_=1+I(_,v[0]+15))){y=3;break t}null!=N&&(N[0]=S),null!=A&&(A[0]=k),null!=L&&(L[0]=_),v[0]+=18,y[0]-=18,P[0]=1}y=0}}if(d=d[0],m=m[0],0!=y)return y;if(v=!!(2&m),!w&&d)return 3;if(null!=s&&(s[0]=!!(16&m)),null!=l&&(l[0]=v),null!=h&&(h[0]=0),l=u[0],m=f[0],d&&v&&null==c){y=0;break}if(4>i){y=7;break}if(w&&d||!w&&!d&&!n(t,r[0],"ALPH")){i=[i],g.na=[g.na],g.P=[g.P],g.Sa=[g.Sa];t:{S=t,y=r,w=i;var P=g.gb;A=g.na,L=g.P,N=g.Sa,k=22,e(null!=S),e(null!=w),_=y[0];var F=w[0];for(e(null!=A),e(null!=N),A[0]=null,L[0]=null,N[0]=0;;){if(y[0]=_,w[0]=F,8>F){y=7;break t}var C=j(S,_+4);if(4294967286<C){y=3;break t}var O=8+C+1&-2;if(k+=O,0<P&&k>P){y=3;break t}if(!n(S,_,"VP8 ")||!n(S,_,"VP8L")){y=0;break t}if(F[0]<O){y=7;break t}n(S,_,"ALPH")||(A[0]=S,L[0]=_+8,N[0]=C),_+=O,F-=O}}if(i=i[0],g.na=g.na[0],g.P=g.P[0],g.Sa=g.Sa[0],0!=y)break}i=[i],g.Ja=[g.Ja],g.xa=[g.xa];t:if(P=t,y=r,w=i,A=g.gb[0],L=g.Ja,N=g.xa,S=y[0],_=!n(P,S,"VP8 "),k=!n(P,S,"VP8L"),e(null!=P),e(null!=w),e(null!=L),e(null!=N),8>w[0])y=7;else{if(_||k){if(P=j(P,S+4),12<=A&&P>A-12){y=3;break t}if(p&&P>w[0]-8){y=7;break t}L[0]=P,y[0]+=8,w[0]-=8,N[0]=k}else N[0]=5<=w[0]&&47==P[S+0]&&!(P[S+4]>>5),L[0]=w[0];y=0}if(i=i[0],g.Ja=g.Ja[0],g.xa=g.xa[0],r=r[0],0!=y)break;if(4294967286<g.Ja)return 3;if(null==h||v||(h[0]=g.xa?2:1),l=[l],m=[m],g.xa){if(5>i){y=7;break}h=l,p=m,v=s,null==t||5>i?t=0:5<=i&&47==t[r+0]&&!(t[r+4]>>5)?(w=[0],P=[0],A=[0],b(L=new x,t,r,i),gt(L,w,P,A)?(null!=h&&(h[0]=w[0]),null!=p&&(p[0]=P[0]),null!=v&&(v[0]=A[0]),t=1):t=0):t=0}else{if(10>i){y=7;break}h=m,null==t||10>i||!Jt(t,r+3,i-3)?t=0:(p=t[r+0]|t[r+1]<<8|t[r+2]<<16,v=16383&(t[r+7]<<8|t[r+6]),t=16383&(t[r+9]<<8|t[r+8]),1&p||3<(p>>1&7)||!(p>>4&1)||p>>5>=g.Ja||!v||!t?t=0:(l&&(l[0]=v),h&&(h[0]=t),t=1))}if(!t)return 3;if(l=l[0],m=m[0],d&&(u[0]!=l||f[0]!=m))return 3;null!=c&&(c[0]=g,c.offset=r-c.w,e(4294967286>r-c.w),e(c.offset==c.ha-i));break}return 0==y||7==y&&d&&null==c?(null!=s&&(s[0]|=null!=g.na&&0<g.na.length),null!=a&&(a[0]=l),null!=o&&(o[0]=m),0):y}function En(t,e,n){var r=e.width,i=e.height,a=0,o=0,s=r,l=i;if(e.Da=null!=t&&0<t.Da,e.Da&&(s=t.cd,l=t.bd,a=t.v,o=t.j,11>n||(a&=-2,o&=-2),0>a||0>o||0>=s||0>=l||a+s>r||o+l>i))return 0;if(e.v=a,e.j=o,e.va=a+s,e.o=o+l,e.U=s,e.T=l,e.da=null!=t&&0<t.da,e.da){if(!B(s,l,n=[t.ib],a=[t.hb]))return 0;e.ib=n[0],e.hb=a[0]}return e.ob=null!=t&&t.ob,e.Kb=null==t||!t.Sd,e.da&&(e.ob=e.ib<3*r/4&&e.hb<3*i/4,e.Kb=0),1}function Bn(t){if(null==t)return 2;if(11>t.S){var e=t.f.RGBA;e.fb+=(t.height-1)*e.A,e.A=-e.A}else e=t.f.kb,t=t.height,e.O+=(t-1)*e.fa,e.fa=-e.fa,e.N+=(t-1>>1)*e.Ab,e.Ab=-e.Ab,e.W+=(t-1>>1)*e.Db,e.Db=-e.Db,null!=e.F&&(e.J+=(t-1)*e.lb,e.lb=-e.lb);return 0}function Mn(t,e,n,r){if(null==r||0>=t||0>=e)return 2;if(null!=n){if(n.Da){var i=n.cd,o=n.bd,s=-2&n.v,l=-2&n.j;if(0>s||0>l||0>=i||0>=o||s+i>t||l+o>e)return 2;t=i,e=o}if(n.da){if(!B(t,e,i=[n.ib],o=[n.hb]))return 2;t=i[0],e=o[0]}}r.width=t,r.height=e;t:{var h=r.width,c=r.height;if(t=r.S,0>=h||0>=c||!(t>=Er&&13>t))t=2;else{if(0>=r.Rd&&null==r.sd){s=o=i=e=0;var u=(l=h*Ri[t])*c;if(11>t||(o=(c+1)/2*(e=(h+1)/2),12==t&&(s=(i=h)*c)),null==(c=a(u+2*o+s))){t=1;break t}r.sd=c,11>t?((h=r.f.RGBA).eb=c,h.fb=0,h.A=l,h.size=u):((h=r.f.kb).y=c,h.O=0,h.fa=l,h.Fd=u,h.f=c,h.N=0+u,h.Ab=e,h.Cd=o,h.ea=c,h.W=0+u+o,h.Db=e,h.Ed=o,12==t&&(h.F=c,h.J=0+u+2*o),h.Tc=s,h.lb=i)}if(e=1,i=r.S,o=r.width,s=r.height,i>=Er&&13>i)if(11>i)t=r.f.RGBA,e&=(l=Math.abs(t.A))*(s-1)+o<=t.size,e&=l>=o*Ri[i],e&=null!=t.eb;else{t=r.f.kb,l=(o+1)/2,u=(s+1)/2,h=Math.abs(t.fa),c=Math.abs(t.Ab);var f=Math.abs(t.Db),d=Math.abs(t.lb),p=d*(s-1)+o;e&=h*(s-1)+o<=t.Fd,e&=c*(u-1)+l<=t.Cd,e=(e&=f*(u-1)+l<=t.Ed)&h>=o&c>=l&f>=l,e&=null!=t.y,e&=null!=t.f,e&=null!=t.ea,12==i&&(e&=d>=o,e&=p<=t.Tc,e&=null!=t.F)}else e=0;t=e?0:2}}return 0!=t||null!=n&&n.fd&&(t=Bn(r)),t}var Tn=64,Rn=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],qn=24,Un=32,zn=8,Hn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];R("Predictor0","PredictorAdd0"),t.Predictor0=function(){return **********},t.Predictor1=function(t){return t},t.Predictor2=function(t,e,n){return e[n+0]},t.Predictor3=function(t,e,n){return e[n+1]},t.Predictor4=function(t,e,n){return e[n-1]},t.Predictor5=function(t,e,n){return U(U(t,e[n+1]),e[n+0])},t.Predictor6=function(t,e,n){return U(t,e[n-1])},t.Predictor7=function(t,e,n){return U(t,e[n+0])},t.Predictor8=function(t,e,n){return U(e[n-1],e[n+0])},t.Predictor9=function(t,e,n){return U(e[n+0],e[n+1])},t.Predictor10=function(t,e,n){return U(U(t,e[n-1]),U(e[n+0],e[n+1]))},t.Predictor11=function(t,e,n){var r=e[n+0];return 0>=W(r>>24&255,t>>24&255,(e=e[n-1])>>24&255)+W(r>>16&255,t>>16&255,e>>16&255)+W(r>>8&255,t>>8&255,e>>8&255)+W(255&r,255&t,255&e)?r:t},t.Predictor12=function(t,e,n){var r=e[n+0];return(z((t>>24&255)+(r>>24&255)-((e=e[n-1])>>24&255))<<24|z((t>>16&255)+(r>>16&255)-(e>>16&255))<<16|z((t>>8&255)+(r>>8&255)-(e>>8&255))<<8|z((255&t)+(255&r)-(255&e)))>>>0},t.Predictor13=function(t,e,n){var r=e[n-1];return(H((t=U(t,e[n+0]))>>24&255,r>>24&255)<<24|H(t>>16&255,r>>16&255)<<16|H(t>>8&255,r>>8&255)<<8|H(255&t,255&r))>>>0};var Wn=t.PredictorAdd0;t.PredictorAdd1=G,R("Predictor2","PredictorAdd2"),R("Predictor3","PredictorAdd3"),R("Predictor4","PredictorAdd4"),R("Predictor5","PredictorAdd5"),R("Predictor6","PredictorAdd6"),R("Predictor7","PredictorAdd7"),R("Predictor8","PredictorAdd8"),R("Predictor9","PredictorAdd9"),R("Predictor10","PredictorAdd10"),R("Predictor11","PredictorAdd11"),R("Predictor12","PredictorAdd12"),R("Predictor13","PredictorAdd13");var Gn=t.PredictorAdd2;J("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),J("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Vn,Kn=t.ColorIndexInverseTransform,Yn=t.MapARGB,Jn=t.VP8LColorIndexInverseTransformAlpha,Xn=t.MapAlpha,$n=t.VP8LPredictorsAdd=[];$n.length=16,(t.VP8LPredictors=[]).length=16,(t.VP8LPredictorsAdd_C=[]).length=16,(t.VP8LPredictors_C=[]).length=16;var Zn,Qn,tr,er,nr,rr,ir,ar,or,sr,lr,hr,cr,ur,fr,dr,pr,gr,mr,br,vr,yr,wr,xr,_r,Ar,Lr,Nr,Sr=a(511),kr=a(2041),Pr=a(225),Fr=a(767),Cr=0,Ir=kr,jr=Pr,Or=Fr,Dr=Sr,Er=0,Br=1,Mr=2,Tr=3,Rr=4,qr=5,Ur=6,zr=7,Hr=8,Wr=9,Gr=10,Vr=[2,3,7],Kr=[3,3,11],Yr=[280,256,256,256,40],Jr=[0,1,1,1,0],Xr=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],$r=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Zr=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Qr=8,ti=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],ei=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ni=null,ri=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],ii=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],ai=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],oi=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],si=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],li=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],hi=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],ci=[],ui=[],fi=[],di=1,pi=2,gi=[],mi=[];bn("UpsampleRgbLinePair",An,3),bn("UpsampleBgrLinePair",Ln,3),bn("UpsampleRgbaLinePair",Fn,4),bn("UpsampleBgraLinePair",Pn,4),bn("UpsampleArgbLinePair",kn,4),bn("UpsampleRgba4444LinePair",Sn,2),bn("UpsampleRgb565LinePair",Nn,2);var bi=t.UpsampleRgbLinePair,vi=t.UpsampleBgrLinePair,yi=t.UpsampleRgbaLinePair,wi=t.UpsampleBgraLinePair,xi=t.UpsampleArgbLinePair,_i=t.UpsampleRgba4444LinePair,Ai=t.UpsampleRgb565LinePair,Li=16,Ni=1<<Li-1,Si=-227,ki=482,Pi=6,Fi=0,Ci=a(256),Ii=a(256),ji=a(256),Oi=a(256),Di=a(ki-Si),Ei=a(ki-Si);Cn("YuvToRgbRow",An,3),Cn("YuvToBgrRow",Ln,3),Cn("YuvToRgbaRow",Fn,4),Cn("YuvToBgraRow",Pn,4),Cn("YuvToArgbRow",kn,4),Cn("YuvToRgba4444Row",Sn,2),Cn("YuvToRgb565Row",Nn,2);var Bi=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Mi=[0,2,8],Ti=[8,7,6,4,4,2,2,2,1,1,1,1];this.WebPDecodeRGBA=function(t,n,s,l,h){var c=Br,u=new en,f=new ot;u.ba=f,f.S=c,f.width=[f.width],f.height=[f.height];var d=f.width,p=f.height,g=new st;if(null==g||null==t)var m=2;else e(null!=g),m=Dn(t,n,s,g.width,g.height,g.Pd,g.Qd,g.format,null);if(0!=m?d=0:(null!=d&&(d[0]=g.width[0]),null!=p&&(p[0]=g.height[0]),d=1),d){f.width=f.width[0],f.height=f.height[0],null!=l&&(l[0]=f.width),null!=h&&(h[0]=f.height);t:{if(l=new Vt,(h=new nn).data=t,h.w=n,h.ha=s,h.kd=1,n=[0],e(null!=h),(0==(t=Dn(h.data,h.w,h.ha,null,null,null,n,null,h))||7==t)&&n[0]&&(t=4),0==(n=t)){if(e(null!=u),l.data=h.data,l.w=h.w+h.offset,l.ha=h.ha-h.offset,l.put=dt,l.ac=ft,l.bc=pt,l.ma=u,h.xa){if(null==(t=Pt())){u=1;break t}if(function(t,n){var r=[0],i=[0],a=[0];e:for(;;){if(null==t)return 0;if(null==n)return t.a=2,0;if(t.l=n,t.a=0,b(t.m,n.data,n.w,n.ha),!gt(t.m,r,i,a)){t.a=3;break e}if(t.xb=pi,n.width=r[0],n.height=i[0],!Ft(r[0],i[0],1,t,null))break e;return 1}return e(0!=t.a),0}(t,l)){if(l=0==(n=Mn(l.width,l.height,u.Oa,u.ba))){e:{l=t;n:for(;;){if(null==l){l=0;break e}if(e(null!=l.s.yc),e(null!=l.s.Ya),e(0<l.s.Wb),e(null!=(s=l.l)),e(null!=(h=s.ma)),0!=l.xb){if(l.ca=h.ba,l.tb=h.tb,e(null!=l.ca),!En(h.Oa,s,Tr)){l.a=2;break n}if(!Ct(l,s.width))break n;if(s.da)break n;if((s.da||rt(l.ca.S))&&mn(),11>l.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=l.ca.f.kb.F&&mn()),l.Pb&&0<l.s.ua&&null==l.s.vb.X&&!O(l.s.vb,l.s.Wa.Xa)){l.a=1;break n}l.xb=0}if(!St(l,l.V,l.Ba,l.c,l.i,s.o,_t))break n;h.Dc=l.Ma,l=1;break e}e(0!=l.a),l=0}l=!l}l&&(n=t.a)}else n=t.a}else{if(null==(t=new Kt)){u=1;break t}if(t.Fa=h.na,t.P=h.P,t.qc=h.Sa,Xt(t,l)){if(0==(n=Mn(l.width,l.height,u.Oa,u.ba))){if(t.Aa=0,s=u.Oa,e(null!=(h=t)),null!=s){if(0<(d=0>(d=s.Md)?0:100<d?255:255*d/100)){for(p=g=0;4>p;++p)12>(m=h.pb[p]).lc&&(m.ia=d*Ti[0>m.lc?0:m.lc]>>3),g|=m.ia;g&&(alert("todo:VP8InitRandom"),h.ia=1)}h.Ga=s.Id,100<h.Ga?h.Ga=100:0>h.Ga&&(h.Ga=0)}(function(t,n){if(null==t)return 0;if(null==n)return Yt(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Xt(t,n))return 0;if(e(t.cb),null==n.ac||n.ac(n)){n.ob&&(t.L=0);var s=Mi[t.L];if(2==t.L?(t.yb=0,t.zb=0):(t.yb=n.v-s>>4,t.zb=n.j-s>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=n.o+15+s>>4,t.Hb=n.va+15+s>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var l=t.ed;for(s=0;4>s;++s){var h;if(t.Qa.Cb){var c=t.Qa.Lb[s];t.Qa.Fb||(c+=l.Tb)}else c=l.Tb;for(h=0;1>=h;++h){var u=t.gd[s][h],f=c;if(l.Pc&&(f+=l.vd[0],h&&(f+=l.od[0])),0<(f=0>f?0:63<f?63:f)){var d=f;0<l.wb&&(d=4<l.wb?d>>2:d>>1)>9-l.wb&&(d=9-l.wb),1>d&&(d=1),u.dd=d,u.tc=2*f+d,u.ld=40<=f?2:15<=f?1:0}else u.tc=0;u.La=h}}}s=0}else Yt(t,6,"Frame setup failed"),s=t.a;if(s=0==s){if(s){t.$c=0,0<t.Aa||(t.Ic=1);e:{s=t.Ic,l=4*(d=t.za);var p=32*d,g=d+1,m=0<t.L?d*(0<t.Aa?2:1):0,b=(2==t.Aa?2:1)*d;if((u=l+832+(h=3*(16*s+Mi[t.L])/2*p)+(c=null!=t.Fa&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=u)s=0;else{if(u>t.Vb){if(t.Vb=0,t.Ec=a(u),t.Fc=0,null==t.Ec){s=Yt(t,1,"no memory during frame initialization.");break e}t.Vb=u}u=t.Ec,f=t.Fc,t.Ac=u,t.Bc=f,f+=l,t.Gd=o(p,Ht),t.Hd=0,t.rb=o(g+1,Rt),t.sb=1,t.wa=m?o(m,Tt):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=d),e(!0),t.oc=u,t.pc=f,f+=832,t.ya=o(b,Ut),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,2==t.Aa&&(t.D.aa+=d),t.R=16*d,t.B=8*d,d=(p=Mi[t.L])*t.R,p=p/2*t.B,t.sa=u,t.ta=f+d,t.qa=t.sa,t.ra=t.ta+16*s*t.R+p,t.Ha=t.qa,t.Ia=t.ra+8*s*t.B+p,t.$c=0,f+=h,t.mb=c?u:null,t.nb=c?f:null,e(f+c<=t.Fc+t.Vb),Zt(t),i(t.Ac,t.Bc,0,l),s=1}}if(s){if(n.ka=0,n.y=t.sa,n.O=t.ta,n.f=t.qa,n.N=t.ra,n.ea=t.Ha,n.Vd=t.Ia,n.fa=t.R,n.Rc=t.B,n.F=null,n.J=0,!Cr){for(s=-255;255>=s;++s)Sr[255+s]=0>s?-s:s;for(s=-1020;1020>=s;++s)kr[1020+s]=-128>s?-128:127<s?127:s;for(s=-112;112>=s;++s)Pr[112+s]=-16>s?-16:15<s?15:s;for(s=-255;510>=s;++s)Fr[255+s]=0>s?0:255<s?255:s;Cr=1}ir=le,ar=ie,sr=ae,lr=oe,hr=se,or=re,cr=Ke,ur=Ye,fr=$e,dr=Ze,pr=Je,gr=Xe,mr=Qe,br=tn,vr=Ue,yr=ze,wr=He,xr=We,ui[0]=Ae,ui[1]=ce,ui[2]=xe,ui[3]=_e,ui[4]=Le,ui[5]=Se,ui[6]=Ne,ui[7]=ke,ui[8]=Fe,ui[9]=Pe,ci[0]=me,ci[1]=fe,ci[2]=de,ci[3]=pe,ci[4]=be,ci[5]=ve,ci[6]=ye,fi[0]=Oe,fi[1]=ue,fi[2]=Ce,fi[3]=Ie,fi[4]=Ee,fi[5]=De,fi[6]=Be,s=1}else s=0}s&&(s=function(t,n){for(t.M=0;t.M<t.Va;++t.M){var o,s=t.Jc[t.M&t.Xb],l=t.m,h=t;for(o=0;o<h.za;++o){var c=l,u=h,f=u.Ac,d=u.Bc+4*o,p=u.zc,g=u.ya[u.aa+o];if(u.Qa.Bb?g.$b=P(c,u.Pa.jb[0])?2+P(c,u.Pa.jb[2]):P(c,u.Pa.jb[1]):g.$b=0,u.kc&&(g.Ad=P(c,u.Bd)),g.Za=!P(c,145)+0,g.Za){var m=g.Ob,b=0;for(u=0;4>u;++u){var v,y=p[0+u];for(v=0;4>v;++v){y=si[f[d+v]][y];for(var w=ai[P(c,y[0])];0<w;)w=ai[2*w+P(c,y[w])];y=-w,f[d+v]=y}r(m,b,f,d,4),b+=4,p[0+u]=y}}else y=P(c,156)?P(c,128)?1:3:P(c,163)?2:0,g.Ob[0]=y,i(f,d,y,4),i(p,0,y,4);g.Dd=P(c,142)?P(c,114)?P(c,183)?1:3:2:0}if(h.m.Ka)return Yt(t,7,"Premature end-of-partition0 encountered.");for(;t.ja<t.za;++t.ja){if(h=s,c=(l=t).rb[l.sb-1],f=l.rb[l.sb+l.ja],o=l.ya[l.aa+l.ja],d=l.kc?o.Ad:0)c.la=f.la=0,o.Za||(c.Na=f.Na=0),o.Hc=0,o.Gc=0,o.ia=0;else{var x,_;if(c=f,f=h,d=l.Pa.Xc,p=l.ya[l.aa+l.ja],g=l.pb[p.$b],u=p.ad,m=0,b=l.rb[l.sb-1],y=v=0,i(u,m,0,384),p.Za)var A=0,L=d[3];else{w=a(16);var N=c.Na+b.Na;if(N=ni(f,d[1],N,g.Eb,0,w,0),c.Na=b.Na=(0<N)+0,1<N)ir(w,0,u,m);else{var S=w[0]+3>>3;for(w=0;256>w;w+=16)u[m+w]=S}A=1,L=d[0]}var k=15&c.la,F=15&b.la;for(w=0;4>w;++w){var C=1&F;for(S=_=0;4>S;++S)k=k>>1|(C=(N=ni(f,L,N=C+(1&k),g.Sc,A,u,m))>A)<<7,_=_<<2|(3<N?3:1<N?2:0!=u[m+0]),m+=16;k>>=4,F=F>>1|C<<7,v=(v<<8|_)>>>0}for(L=k,A=F>>4,x=0;4>x;x+=2){for(_=0,k=c.la>>4+x,F=b.la>>4+x,w=0;2>w;++w){for(C=1&F,S=0;2>S;++S)N=C+(1&k),k=k>>1|(C=0<(N=ni(f,d[2],N,g.Qc,0,u,m)))<<3,_=_<<2|(3<N?3:1<N?2:0!=u[m+0]),m+=16;k>>=2,F=F>>1|C<<5}y|=_<<4*x,L|=k<<4<<x,A|=(240&F)<<x}c.la=L,b.la=A,p.Hc=v,p.Gc=y,p.ia=43690&y?0:g.ia,d=!(v|y)}if(0<l.L&&(l.wa[l.Y+l.ja]=l.gd[o.$b][o.Za],l.wa[l.Y+l.ja].La|=!d),h.Ka)return Yt(t,7,"Premature end-of-file encountered.")}if(Zt(t),l=n,h=1,o=(s=t).D,c=0<s.L&&s.M>=s.zb&&s.M<=s.Va,0==s.Aa)e:{if(o.M=s.M,o.uc=c,On(s,o),h=1,o=(_=s.D).Nb,c=(y=Mi[s.L])*s.R,f=y/2*s.B,w=16*o*s.R,S=8*o*s.B,d=s.sa,p=s.ta-c+w,g=s.qa,u=s.ra-f+S,m=s.Ha,b=s.Ia-f+S,F=0==(k=_.M),v=k>=s.Va-1,2==s.Aa&&On(s,_),_.uc)for(C=(N=s).D.M,e(N.D.uc),_=N.yb;_<N.Hb;++_){A=_,L=C;var I=(j=(U=N).D).Nb;x=U.R;var j=j.wa[j.Y+A],O=U.sa,D=U.ta+16*I*x+16*A,E=j.dd,B=j.tc;if(0!=B)if(e(3<=B),1==U.L)0<A&&yr(O,D,x,B+4),j.La&&xr(O,D,x,B),0<L&&vr(O,D,x,B+4),j.La&&wr(O,D,x,B);else{var M=U.B,T=U.qa,R=U.ra+8*I*M+8*A,q=U.Ha,U=U.Ia+8*I*M+8*A;I=j.ld,0<A&&(ur(O,D,x,B+4,E,I),dr(T,R,q,U,M,B+4,E,I)),j.La&&(gr(O,D,x,B,E,I),br(T,R,q,U,M,B,E,I)),0<L&&(cr(O,D,x,B+4,E,I),fr(T,R,q,U,M,B+4,E,I)),j.La&&(pr(O,D,x,B,E,I),mr(T,R,q,U,M,B,E,I))}}if(s.ia&&alert("todo:DitherRow"),null!=l.put){if(_=16*k,k=16*(k+1),F?(l.y=s.sa,l.O=s.ta+w,l.f=s.qa,l.N=s.ra+S,l.ea=s.Ha,l.W=s.Ia+S):(_-=y,l.y=d,l.O=p,l.f=g,l.N=u,l.ea=m,l.W=b),v||(k-=y),k>l.o&&(k=l.o),l.F=null,l.J=null,null!=s.Fa&&0<s.Fa.length&&_<k&&(l.J=un(s,l,_,k-_),l.F=s.mb,null==l.F&&0==l.F.length)){h=Yt(s,3,"Could not decode alpha data.");break e}_<l.j&&(y=l.j-_,_=l.j,e(!(1&y)),l.O+=s.R*y,l.N+=s.B*(y>>1),l.W+=s.B*(y>>1),null!=l.F&&(l.J+=l.width*y)),_<k&&(l.O+=l.v,l.N+=l.v>>1,l.W+=l.v>>1,null!=l.F&&(l.J+=l.v),l.ka=_-l.j,l.U=l.va-l.v,l.T=k-_,h=l.put(l))}o+1!=s.Ic||v||(r(s.sa,s.ta-c,d,p+16*s.R,c),r(s.qa,s.ra-f,g,u+8*s.B,f),r(s.Ha,s.Ia-f,m,b+8*s.B,f))}if(!h)return Yt(t,6,"Output aborted.")}return 1}(t,n)),null!=n.bc&&n.bc(n),s&=1}return s?(t.cb=0,s):0})(t,l)||(n=t.a)}}else n=t.a}0==n&&null!=u.Oa&&u.Oa.fd&&(n=Bn(u.ba))}u=n}c=0!=u?null:11>c?f.f.RGBA.eb:f.f.kb.y}else c=null;return c};var Ri=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function h(t,e){for(var n="",r=0;r<4;r++)n+=String.fromCharCode(t[e++]);return n}function c(t,e){return t[e+0]|t[e+1]<<8}function u(t,e){return(t[e+0]|t[e+1]<<8|t[e+2]<<16)>>>0}function f(t,e){return(t[e+0]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}new l;var d=[0],p=[0],g=[],m=new l,b=t,v=function(t,e){var n={},r=0,i=!1,a=0,o=0;if(n.frames=[],!
/** @license
         * Copyright (c) 2017 Dominik Homberger
        Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
        The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
        THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
        https://webpjs.appspot.com
        WebPRiffParser <EMAIL>
        */
function(t,e){for(var n=0;n<4;n++)if(t[e+n]!="RIFF".charCodeAt(n))return!0;return!1}(t,e)){for(f(t,e+=4),e+=8;e<t.length;){var s=h(t,e),l=f(t,e+=4);e+=4;var d=l+(1&l);switch(s){case"VP8 ":case"VP8L":void 0===n.frames[r]&&(n.frames[r]={}),(m=n.frames[r]).src_off=i?o:e-8,m.src_size=a+l+8,r++,i&&(i=!1,a=0,o=0);break;case"VP8X":(m=n.header={}).feature_flags=t[e];var p=e+4;m.canvas_width=1+u(t,p),p+=3,m.canvas_height=1+u(t,p),p+=3;break;case"ALPH":i=!0,a=d+8,o=e-8;break;case"ANIM":(m=n.header).bgcolor=f(t,e),p=e+4,m.loop_count=c(t,p),p+=2;break;case"ANMF":var g,m;(m=n.frames[r]={}).offset_x=2*u(t,e),e+=3,m.offset_y=2*u(t,e),e+=3,m.width=1+u(t,e),e+=3,m.height=1+u(t,e),e+=3,m.duration=u(t,e),e+=3,g=t[e++],m.dispose=1&g,m.blend=g>>1&1}"ANMF"!=s&&(e+=d)}return n}}(b,0);v.response=b,v.rgbaoutput=!0,v.dataurl=!1;var y=v.header?v.header:null,w=v.frames?v.frames:null;if(y){y.loop_counter=y.loop_count,d=[y.canvas_height],p=[y.canvas_width];for(var x=0;x<w.length&&0!=w[x].blend;x++);}var _=w[0],A=m.WebPDecodeRGBA(b,_.src_off,_.src_size,p,d);_.rgba=A,_.imgwidth=p[0],_.imgheight=d[0];for(var L=0;L<p[0]*d[0]*4;L++)g[L]=A[L];return this.width=p,this.height=d,this.data=g,this}!function(t){var e,n,r,i,a,o,s,l,h,c=function(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new l,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new o,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new o,this.lineDashOffset=t.lineDashOffset||0,this.lineDash=t.lineDash||[],this.margin=t.margin||[0,0,0,0],this.prevPageLastElemOffset=t.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new u(this),e=this.internal.f2,n=this.internal.getCoordinateString,r=this.internal.getVerticalCoordinateString,i=this.internal.getHorizontalCoordinate,a=this.internal.getVerticalCoordinate,o=this.internal.Point,s=this.internal.Rectangle,l=this.internal.Matrix,h=new c}]);var u=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var e=t;Object.defineProperty(this,"pdf",{get:function(){return e}});var n=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return n},set:function(t){n=Boolean(t)}});var r=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return r},set:function(t){r=Boolean(t)}});var i=0;Object.defineProperty(this,"posX",{get:function(){return i},set:function(t){isNaN(t)||(i=t)}});var a=0;Object.defineProperty(this,"posY",{get:function(){return a},set:function(t){isNaN(t)||(a=t)}}),Object.defineProperty(this,"margin",{get:function(){return h.margin},set:function(t){var e;"number"==typeof t?e=[t,t,t,t]:((e=new Array(4))[0]=t[0],e[1]=t.length>=2?t[1]:e[0],e[2]=t.length>=3?t[2]:e[0],e[3]=t.length>=4?t[3]:e[1]),h.margin=e}});var o=!1;Object.defineProperty(this,"autoPaging",{get:function(){return o},set:function(t){o=t}});var s=0;Object.defineProperty(this,"lastBreak",{get:function(){return s},set:function(t){s=t}});var l=[];Object.defineProperty(this,"pageBreaks",{get:function(){return l},set:function(t){l=t}}),Object.defineProperty(this,"ctx",{get:function(){return h},set:function(t){t instanceof c&&(h=t)}}),Object.defineProperty(this,"path",{get:function(){return h.path},set:function(t){h.path=t}});var u=[];Object.defineProperty(this,"ctxStack",{get:function(){return u},set:function(t){u=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=f(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=f(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}});var d=null,p=null;Object.defineProperty(this,"fontFaces",{get:function(){return p},set:function(t){d=null,p=t}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var n=e[1];e[2];var r=e[3],i=e[4];e[5];var a=e[6],o=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(i)[2];i="px"===o?Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor):"em"===o?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(i);var s=function(t){var e,n,r=[],i=t.trim();if(""===i)return hi;if(i in Xr)return[Xr[i]];for(;""!==i;){switch(n=null,e=(i=Zr(i)).charAt(0)){case'"':case"'":n=Qr(i.substring(1),e);break;default:n=ti(i)}if(null===n)return hi;if(r.push(n[0]),""!==(i=Zr(n[1]))&&","!==i.charAt(0))return hi;i=i.replace(/^,/,"")}return r}(a);if(this.fontFaces){var l=function(t,e){if(null===d){var n=function(t){var e=[];return Object.keys(t).forEach(function(n){t[n].forEach(function(t){var r=null;switch(t){case"bold":r={family:n,weight:"bold"};break;case"italic":r={family:n,style:"italic"};break;case"bolditalic":r={family:n,weight:"bold",style:"italic"};break;case"":case"normal":r={family:n}}null!==r&&(r.ref={name:n,style:t},e.push(r))})}),e}(t.getFontList());d=function(t){for(var e={},n=0;n<t.length;++n){var r=Kr(t[n]),i=r.family,a=r.stretch,o=r.style,s=r.weight;e[i]=e[i]||{},e[i][a]=e[i][a]||{},e[i][a][o]=e[i][a][o]||{},e[i][a][o][s]=r}return e}(n.concat(e))}return d}(this.pdf,this.fontFaces),h=s.map(function(t){return{family:t,stretch:"normal",weight:r,style:n}}),c=function(t,e,n){for(var r=(n=n||{}).defaultFontFamily||"times",i=Object.assign({},Jr,n.genericFontFamilies||{}),a=null,o=null,s=0;s<e.length;++s)if(i[(a=Kr(e[s])).family]&&(a.family=i[a.family]),t.hasOwnProperty(a.family)){o=t[a.family];break}if(!(o=o||t[r]))throw new Error("Could not find a font-family for the rule '"+$r(a)+"' and default family '"+r+"'.");if(o=function(t,e){if(e[t])return e[t];var n=Wr[t],r=n<=Wr.normal?-1:1,i=Yr(e,Hr,n,r);if(!i)throw new Error("Could not find a matching font-stretch value for "+t);return i}(a.stretch,o),o=function(t,e){if(e[t])return e[t];for(var n=zr[t],r=0;r<n.length;++r)if(e[n[r]])return e[n[r]];throw new Error("Could not find a matching font-style for "+t)}(a.style,o),!(o=function(t,e){if(e[t])return e[t];if(400===t&&e[500])return e[500];if(500===t&&e[400])return e[400];var n=Vr[t],r=Yr(e,Gr,n,t<400?-1:1);if(!r)throw new Error("Could not find a matching font-weight for value "+t);return r}(a.weight,o)))throw new Error("Failed to resolve a font for the rule '"+$r(a)+"'.");return o}(l,h);this.pdf.setFont(c.ref.name,c.ref.style)}else{var u="";("bold"===r||parseInt(r,10)>=700||"bold"===n)&&(u="bold"),"italic"===n&&(u+="italic"),0===u.length&&(u="normal");for(var f="",p={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},g=0;g<s.length;g++){if(void 0!==this.pdf.internal.getFont(s[g],u,{noFallback:!0,disableWarning:!0})){f=s[g];break}if("bolditalic"===u&&void 0!==this.pdf.internal.getFont(s[g],"bold",{noFallback:!0,disableWarning:!0}))f=s[g],u="bold";else if(void 0!==this.pdf.internal.getFont(s[g],"normal",{noFallback:!0,disableWarning:!0})){f=s[g],u="normal";break}}if(""===f)for(var m=0;m<s.length;m++)if(p[s[m]]){f=p[s[m]];break}f=""===f?"Times":f,this.pdf.setFont(f,u)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(t){this.ctx.lineDashOffset=t,T.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(t){this.ctx.lineDash=t,T.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=Boolean(t)}})};u.prototype.setLineDash=function(t){this.lineDash=t},u.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},u.prototype.fill=function(){x.call(this,"fill",!1)},u.prototype.stroke=function(){x.call(this,"stroke",!1)},u.prototype.beginPath=function(){this.path=[{type:"begin"}]},u.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw xn.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var n=this.ctx.transform.applyToPoint(new o(t,e));this.path.push({type:"mt",x:n.x,y:n.y}),this.ctx.lastPoint=new o(t,e)},u.prototype.closePath=function(){var t=new o(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===y(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new o(this.path[e+1].x,this.path[e+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new o(t.x,t.y)},u.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw xn.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var n=this.ctx.transform.applyToPoint(new o(t,e));this.path.push({type:"lt",x:n.x,y:n.y}),this.ctx.lastPoint=new o(n.x,n.y)},u.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),x.call(this,null,!0)},u.prototype.quadraticCurveTo=function(t,e,n,r){if(isNaN(n)||isNaN(r)||isNaN(t)||isNaN(e))throw xn.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var i=this.ctx.transform.applyToPoint(new o(n,r)),a=this.ctx.transform.applyToPoint(new o(t,e));this.path.push({type:"qct",x1:a.x,y1:a.y,x:i.x,y:i.y}),this.ctx.lastPoint=new o(i.x,i.y)},u.prototype.bezierCurveTo=function(t,e,n,r,i,a){if(isNaN(i)||isNaN(a)||isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw xn.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var s=this.ctx.transform.applyToPoint(new o(i,a)),l=this.ctx.transform.applyToPoint(new o(t,e)),h=this.ctx.transform.applyToPoint(new o(n,r));this.path.push({type:"bct",x1:l.x,y1:l.y,x2:h.x,y2:h.y,x:s.x,y:s.y}),this.ctx.lastPoint=new o(s.x,s.y)},u.prototype.arc=function(t,e,n,r,i,a){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i))throw xn.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(a=Boolean(a),!this.ctx.transform.isIdentity){var s=this.ctx.transform.applyToPoint(new o(t,e));t=s.x,e=s.y;var l=this.ctx.transform.applyToPoint(new o(0,n)),h=this.ctx.transform.applyToPoint(new o(0,0));n=Math.sqrt(Math.pow(l.x-h.x,2)+Math.pow(l.y-h.y,2))}Math.abs(i-r)>=2*Math.PI&&(r=0,i=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:n,startAngle:r,endAngle:i,counterclockwise:a})},u.prototype.arcTo=function(t,e,n,r,i){throw new Error("arcTo not implemented.")},u.prototype.rect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw xn.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+n,e),this.lineTo(t+n,e+r),this.lineTo(t,e+r),this.lineTo(t,e),this.lineTo(t+n,e),this.lineTo(t,e)},u.prototype.fillRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw xn.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!d.call(this)){var i={};"butt"!==this.lineCap&&(i.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(i.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,n,r),this.fill(),i.hasOwnProperty("lineCap")&&(this.lineCap=i.lineCap),i.hasOwnProperty("lineJoin")&&(this.lineJoin=i.lineJoin)}},u.prototype.strokeRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw xn.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");p.call(this)||(this.beginPath(),this.rect(t,e,n,r),this.stroke())},u.prototype.clearRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw xn.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,n,r))},u.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var r=new c(this.ctx);this.ctxStack.push(this.ctx),this.ctx=r}},u.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},u.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var f=function(t){var e,n,r,i;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))e=0,n=0,r=0,i=0;else{var a=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==a)e=parseInt(a[1]),n=parseInt(a[2]),r=parseInt(a[3]),i=1;else if(null!==(a=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(t)))e=parseInt(a[1]),n=parseInt(a[2]),r=parseInt(a[3]),i=parseFloat(a[4]);else{if(i=1,"string"==typeof t&&"#"!==t.charAt(0)){var o=new Sn(t);t=o.ok?o.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,n=t.substring(2,3),n+=n,r=t.substring(3,4),r+=r):(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7)),e=parseInt(e,16),n=parseInt(n,16),r=parseInt(r,16)}}return{r:e,g:n,b:r,a:i,style:t}},d=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},p=function(){return Boolean(this.ctx.isStrokeTransparent||0==this.globalAlpha)};u.prototype.fillText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw xn.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(r=isNaN(r)?void 0:r,!d.call(this)){var i=E(this.ctx.transform.rotation),a=this.ctx.transform.scaleX;F.call(this,{text:t,x:e,y:n,scale:a,angle:i,align:this.textAlign,maxWidth:r})}},u.prototype.strokeText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw xn.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!p.call(this)){r=isNaN(r)?void 0:r;var i=E(this.ctx.transform.rotation),a=this.ctx.transform.scaleX;F.call(this,{text:t,x:e,y:n,scale:a,renderingMode:"stroke",angle:i,align:this.textAlign,maxWidth:r})}},u.prototype.measureText=function(t){if("string"!=typeof t)throw xn.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,n=this.pdf.internal.scaleFactor,r=e.internal.getFontSize(),i=e.getStringUnitWidth(t)*r/e.internal.scaleFactor;return new function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this}({width:i*=Math.round(96*n/72*1e4)/1e4})},u.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw xn.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var n=new l(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(n)},u.prototype.rotate=function(t){if(isNaN(t))throw xn.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new l(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},u.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw xn.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var n=new l(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(n)},u.prototype.transform=function(t,e,n,r,i,a){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(a))throw xn.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var o=new l(t,e,n,r,i,a);this.ctx.transform=this.ctx.transform.multiply(o)},u.prototype.setTransform=function(t,e,n,r,i,a){t=isNaN(t)?1:t,e=isNaN(e)?0:e,n=isNaN(n)?0:n,r=isNaN(r)?1:r,i=isNaN(i)?0:i,a=isNaN(a)?0:a,this.ctx.transform=new l(t,e,n,r,i,a)};var g=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};u.prototype.drawImage=function(t,e,n,r,i,a,o,h,c){var u=this.pdf.getImageProperties(t),f=1,d=1,p=1,b=1;void 0!==r&&void 0!==h&&(p=h/r,b=c/i,f=u.width/r*h/r,d=u.height/i*c/i),void 0===a&&(a=e,o=n,e=0,n=0),void 0!==r&&void 0===h&&(h=r,c=i),void 0===r&&void 0===h&&(h=u.width,c=u.height);for(var y,x=this.ctx.transform.decompose(),A=E(x.rotate.shx),L=new l,N=(L=(L=(L=L.multiply(x.translate)).multiply(x.skew)).multiply(x.scale)).applyToRectangle(new s(a-e*p,o-n*b,r*f,i*d)),S=m.call(this,N),k=[],P=0;P<S.length;P+=1)-1===k.indexOf(S[P])&&k.push(S[P]);if(w(k),this.autoPaging)for(var F=k[0],C=k[k.length-1],I=F;I<C+1;I++){this.pdf.setPage(I);var j=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],O=1===I?this.posY+this.margin[0]:this.margin[0],D=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],B=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],M=1===I?0:D+(I-2)*B;if(0!==this.ctx.clip_path.length){var T=this.path;y=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=v(y,this.posX+this.margin[3],-M+O+this.ctx.prevPageLastElemOffset),_.call(this,"fill",!0),this.path=T}var R=JSON.parse(JSON.stringify(N));R=v([R],this.posX+this.margin[3],-M+O+this.ctx.prevPageLastElemOffset)[0];var q=(I>F||I<C)&&g.call(this);q&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],j,B,null).clip().discardPath()),this.pdf.addImage(t,"JPEG",R.x,R.y,R.w,R.h,null,null,A),q&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(t,"JPEG",N.x,N.y,N.w,N.h,null,null,A)};var m=function(t,e,n){var r=[];e=e||this.pdf.internal.pageSize.width,n=n||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var i=this.posY+this.ctx.prevPageLastElemOffset;switch(t.type){default:case"mt":case"lt":r.push(Math.floor((t.y+i)/n)+1);break;case"arc":r.push(Math.floor((t.y+i-t.radius)/n)+1),r.push(Math.floor((t.y+i+t.radius)/n)+1);break;case"qct":var a=B(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);r.push(Math.floor((a.y+i)/n)+1),r.push(Math.floor((a.y+a.h+i)/n)+1);break;case"bct":var o=M(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);r.push(Math.floor((o.y+i)/n)+1),r.push(Math.floor((o.y+o.h+i)/n)+1);break;case"rect":r.push(Math.floor((t.y+i)/n)+1),r.push(Math.floor((t.y+t.h+i)/n)+1)}for(var s=0;s<r.length;s+=1)for(;this.pdf.internal.getNumberOfPages()<r[s];)b.call(this);return r},b=function(){var t=this.fillStyle,e=this.strokeStyle,n=this.font,r=this.lineCap,i=this.lineWidth,a=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=n,this.lineCap=r,this.lineWidth=i,this.lineJoin=a},v=function(t,e,n){for(var r=0;r<t.length;r++)switch(t[r].type){case"bct":t[r].x2+=e,t[r].y2+=n;case"qct":t[r].x1+=e,t[r].y1+=n;default:t[r].x+=e,t[r].y+=n}return t},w=function(t){return t.sort(function(t,e){return t-e})},x=function(t,e){for(var n,r,i=this.fillStyle,a=this.strokeStyle,o=this.lineCap,s=this.lineWidth,l=Math.abs(s*this.ctx.transform.scaleX),h=this.lineJoin,c=JSON.parse(JSON.stringify(this.path)),u=JSON.parse(JSON.stringify(this.path)),f=[],d=0;d<u.length;d++)if(void 0!==u[d].x)for(var p=m.call(this,u[d]),y=0;y<p.length;y+=1)-1===f.indexOf(p[y])&&f.push(p[y]);for(var x=0;x<f.length;x++)for(;this.pdf.internal.getNumberOfPages()<f[x];)b.call(this);if(w(f),this.autoPaging)for(var A=f[0],L=f[f.length-1],N=A;N<L+1;N++){this.pdf.setPage(N),this.fillStyle=i,this.strokeStyle=a,this.lineCap=o,this.lineWidth=l,this.lineJoin=h;var S=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],k=1===N?this.posY+this.margin[0]:this.margin[0],P=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],F=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],C=1===N?0:P+(N-2)*F;if(0!==this.ctx.clip_path.length){var I=this.path;n=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=v(n,this.posX+this.margin[3],-C+k+this.ctx.prevPageLastElemOffset),_.call(this,t,!0),this.path=I}if(r=JSON.parse(JSON.stringify(c)),this.path=v(r,this.posX+this.margin[3],-C+k+this.ctx.prevPageLastElemOffset),!1===e||0===N){var j=(N>A||N<L)&&g.call(this);j&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],S,F,null).clip().discardPath()),_.call(this,t,e),j&&this.pdf.restoreGraphicsState()}this.lineWidth=s}else this.lineWidth=l,_.call(this,t,e),this.lineWidth=s;this.path=c},_=function(t,e){if(("stroke"!==t||e||!p.call(this))&&("stroke"===t||e||!d.call(this))){for(var n,r,i=[],a=this.path,o=0;o<a.length;o++){var s=a[o];switch(s.type){case"begin":i.push({begin:!0});break;case"close":i.push({close:!0});break;case"mt":i.push({start:s,deltas:[],abs:[]});break;case"lt":var l=i.length;if(a[o-1]&&!isNaN(a[o-1].x)&&(n=[s.x-a[o-1].x,s.y-a[o-1].y],l>0))for(;l>=0;l--)if(!0!==i[l-1].close&&!0!==i[l-1].begin){i[l-1].deltas.push(n),i[l-1].abs.push(s);break}break;case"bct":n=[s.x1-a[o-1].x,s.y1-a[o-1].y,s.x2-a[o-1].x,s.y2-a[o-1].y,s.x-a[o-1].x,s.y-a[o-1].y],i[i.length-1].deltas.push(n);break;case"qct":var h=a[o-1].x+2/3*(s.x1-a[o-1].x),c=a[o-1].y+2/3*(s.y1-a[o-1].y),u=s.x+2/3*(s.x1-s.x),f=s.y+2/3*(s.y1-s.y),g=s.x,m=s.y;n=[h-a[o-1].x,c-a[o-1].y,u-a[o-1].x,f-a[o-1].y,g-a[o-1].x,m-a[o-1].y],i[i.length-1].deltas.push(n);break;case"arc":i.push({deltas:[],abs:[],arc:!0}),Array.isArray(i[i.length-1].abs)&&i[i.length-1].abs.push(s)}}r=e?null:"stroke"===t?"stroke":"fill";for(var b=!1,v=0;v<i.length;v++)if(i[v].arc)for(var y=i[v].abs,w=0;w<y.length;w++){var x=y[w];"arc"===x.type?N.call(this,x.x,x.y,x.radius,x.startAngle,x.endAngle,x.counterclockwise,void 0,e,!b):C.call(this,x.x,x.y),b=!0}else if(!0===i[v].close)this.pdf.internal.out("h"),b=!1;else if(!0!==i[v].begin){var _=i[v].start.x,A=i[v].start.y;I.call(this,i[v].deltas,_,A),b=!0}r&&S.call(this,r),e&&k.call(this)}},A=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,n=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-n;case"top":return t+e-n;case"hanging":return t+e-2*n;case"middle":return t+e/2-n;default:return t}},L=function(t){return t+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};u.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},u.prototype.createPattern=function(){return this.createLinearGradient()},u.prototype.createRadialGradient=function(){return this.createLinearGradient()};var N=function(t,e,n,r,i,a,o,s,l){for(var h=O.call(this,n,r,i,a),c=0;c<h.length;c++){var u=h[c];0===c&&(l?P.call(this,u.x1+t,u.y1+e):C.call(this,u.x1+t,u.y1+e)),j.call(this,t,e,u.x2,u.y2,u.x3,u.y3,u.x4,u.y4)}s?k.call(this):S.call(this,o)},S=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},k=function(){this.pdf.clip(),this.pdf.discardPath()},P=function(t,e){this.pdf.internal.out(n(t)+" "+r(e)+" m")},F=function(t){var e;switch(t.align){case"right":case"end":e="right";break;case"center":e="center";break;default:e="left"}var n=this.pdf.getTextDimensions(t.text),r=A.call(this,t.y),i=L.call(this,r)-n.h,a=this.ctx.transform.applyToPoint(new o(t.x,r)),h=this.ctx.transform.decompose(),c=new l;c=(c=(c=c.multiply(h.translate)).multiply(h.skew)).multiply(h.scale);for(var u,f,d,p=this.ctx.transform.applyToRectangle(new s(t.x,r,n.w,n.h)),b=c.applyToRectangle(new s(t.x,i,n.w,n.h)),y=m.call(this,b),x=[],N=0;N<y.length;N+=1)-1===x.indexOf(y[N])&&x.push(y[N]);if(w(x),this.autoPaging)for(var S=x[0],k=x[x.length-1],P=S;P<k+1;P++){this.pdf.setPage(P);var F=1===P?this.posY+this.margin[0]:this.margin[0],C=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],I=this.pdf.internal.pageSize.height-this.margin[2],j=I-this.margin[0],O=this.pdf.internal.pageSize.width-this.margin[1],D=O-this.margin[3],E=1===P?0:C+(P-2)*j;if(0!==this.ctx.clip_path.length){var B=this.path;u=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=v(u,this.posX+this.margin[3],-1*E+F),_.call(this,"fill",!0),this.path=B}var M=v([JSON.parse(JSON.stringify(b))],this.posX+this.margin[3],-E+F+this.ctx.prevPageLastElemOffset)[0];t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale);var T="text"!==this.autoPaging;if(T||M.y+M.h<=I){if(T||M.y>=F&&M.x<=O){var R=T?t.text:this.pdf.splitTextToSize(t.text,t.maxWidth||O-M.x)[0],q=v([JSON.parse(JSON.stringify(p))],this.posX+this.margin[3],-E+F+this.ctx.prevPageLastElemOffset)[0],U=T&&(P>S||P<k)&&g.call(this);U&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],D,j,null).clip().discardPath()),this.pdf.text(R,q.x,q.y,{angle:t.angle,align:e,renderingMode:t.renderingMode}),U&&this.pdf.restoreGraphicsState()}}else M.y<I&&(this.ctx.prevPageLastElemOffset+=I-M.y);t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)}else t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale),this.pdf.text(t.text,a.x+this.posX,a.y+this.posY,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)},C=function(t,e,i,a){i=i||0,a=a||0,this.pdf.internal.out(n(t+i)+" "+r(e+a)+" l")},I=function(t,e,n){return this.pdf.lines(t,e,n,null,null)},j=function(t,n,r,o,s,l,h,c){this.pdf.internal.out([e(i(r+t)),e(a(o+n)),e(i(s+t)),e(a(l+n)),e(i(h+t)),e(a(c+n)),"c"].join(" "))},O=function(t,e,n,r){for(var i=2*Math.PI,a=Math.PI/2;e>n;)e-=i;var o=Math.abs(n-e);o<i&&r&&(o=i-o);for(var s=[],l=r?-1:1,h=e;o>1e-5;){var c=h+l*Math.min(o,a);s.push(D.call(this,t,h,c)),o-=Math.abs(c-h),h=c}return s},D=function(t,e,n){var r=(n-e)/2,i=t*Math.cos(r),a=t*Math.sin(r),o=i,s=-a,l=o*o+s*s,h=l+o*i+s*a,c=4/3*(Math.sqrt(2*l*h)-h)/(o*a-s*i),u=o-c*s,f=s+c*o,d=u,p=-f,g=r+e,m=Math.cos(g),b=Math.sin(g);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:u*m-f*b,y2:u*b+f*m,x3:d*m-p*b,y3:d*b+p*m,x4:t*Math.cos(n),y4:t*Math.sin(n)}},E=function(t){return 180*t/Math.PI},B=function(t,e,n,r,i,a){var o=t+.5*(n-t),l=e+.5*(r-e),h=i+.5*(n-i),c=a+.5*(r-a),u=Math.min(t,i,o,h),f=Math.max(t,i,o,h),d=Math.min(e,a,l,c),p=Math.max(e,a,l,c);return new s(u,d,f-u,p-d)},M=function(t,e,n,r,i,a,o,l){var h,c,u,f,d,p,g,m,b,v,y,w,x,_,A=n-t,L=r-e,N=i-n,S=a-r,k=o-i,P=l-a;for(c=0;c<41;c++)b=(g=(u=t+(h=c/40)*A)+h*((d=n+h*N)-u))+h*(d+h*(i+h*k-d)-g),v=(m=(f=e+h*L)+h*((p=r+h*S)-f))+h*(p+h*(a+h*P-p)-m),0==c?(y=b,w=v,x=b,_=v):(y=Math.min(y,b),w=Math.min(w,v),x=Math.max(x,b),_=Math.max(_,v));return new s(Math.round(y),Math.round(w),Math.round(x-y),Math.round(_-w))},T=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var t,e,n=(t=this.ctx.lineDash,e=this.ctx.lineDashOffset,JSON.stringify({lineDash:t,lineDashOffset:e}));this.prevLineDash!==n&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=n)}}}(Zn.API),
/**
       * @license
       * jsPDF filters PlugIn
       * Copyright (c) 2014 Aras Abbasi
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e=function(t){var e,n,r,i,a,o,s,l,h,c;for(n=[],r=0,i=(t+=e="\0\0\0\0".slice(t.length%4||4)).length;i>r;r+=4)0!==(a=(t.charCodeAt(r)<<24)+(t.charCodeAt(r+1)<<16)+(t.charCodeAt(r+2)<<8)+t.charCodeAt(r+3))?(o=(a=((a=((a=((a=(a-(c=a%85))/85)-(h=a%85))/85)-(l=a%85))/85)-(s=a%85))/85)%85,n.push(o+33,s+33,l+33,h+33,c+33)):n.push(122);return function(t,e){for(var n=e;n>0;n--)t.pop()}(n,e.length),String.fromCharCode.apply(String,n)+"~>"},n=function(t){var e,n,r,i,a,o=String,s="length",l=255,h="charCodeAt",c="slice",u="replace";for(t[c](-2),t=t[c](0,-2)[u](/\s/g,"")[u]("z","!!!!!"),r=[],i=0,a=(t+=e="uuuuu"[c](t[s]%5||5))[s];a>i;i+=5)n=52200625*(t[h](i)-33)+614125*(t[h](i+1)-33)+7225*(t[h](i+2)-33)+85*(t[h](i+3)-33)+(t[h](i+4)-33),r.push(l&n>>24,l&n>>16,l&n>>8,l&n);return function(t,e){for(var n=e;n>0;n--)t.pop()}(r,e[s]),o.fromCharCode.apply(o,r)},r=function(t){return t.split("").map(function(t){return("0"+t.charCodeAt().toString(16)).slice(-2)}).join("")+">"},i=function(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var n="",r=0;r<t.length;r+=2)n+=String.fromCharCode("0x"+(t[r]+t[r+1]));return n},a=function(t){for(var e=new Uint8Array(t.length),n=t.length;n--;)e[n]=t.charCodeAt(n);return(e=Q(e)).reduce(function(t,e){return t+String.fromCharCode(e)},"")};t.processDataByFilters=function(t,o){var s=0,l=t||"",h=[];for("string"==typeof(o=o||[])&&(o=[o]),s=0;s<o.length;s+=1)switch(o[s]){case"ASCII85Decode":case"/ASCII85Decode":l=n(l),h.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":l=e(l),h.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":l=i(l),h.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":l=r(l),h.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":l=a(l),h.push("/FlateDecode");break;default:throw new Error('The filter: "'+o[s]+'" is not implemented')}return{data:l,reverseChain:h.reverse().join(" ")}}}(Zn.API),
/**
       * @license
       * jsPDF fileloading PlugIn
       * Copyright (c) 2018 Aras Abbasi (<EMAIL>)
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){t.loadFile=function(t,e,n){return function(t,e,n){e=!1!==e,n="function"==typeof n?n:function(){};var r=void 0;try{r=function(t,e,n){var r=new XMLHttpRequest,i=0,a=function(t){var e=t.length,n=[],r=String.fromCharCode;for(i=0;i<e;i+=1)n.push(r(255&t.charCodeAt(i)));return n.join("")};if(r.open("GET",t,!e),r.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(r.onload=function(){200===r.status?n(a(this.responseText)):n(void 0)}),r.send(null),e&&200===r.status)return a(r.responseText)}(t,e,n)}catch(yn){}return r}(t,e,n)},t.loadImageFile=t.loadFile}(Zn.API),function(t){function r(){return(yn.html2canvas?Promise.resolve(yn.html2canvas):n(()=>e.import("./html2canvas.esm-legacy-DIGTxsNk.js"),void 0)).catch(function(t){return Promise.reject(new Error("Could not load html2canvas: "+t))}).then(function(t){return t.default?t.default:t})}function i(){return(yn.DOMPurify?Promise.resolve(yn.DOMPurify):n(()=>e.import("./purify.es-legacy-oIg4zOcz.js"),void 0)).catch(function(t){return Promise.reject(new Error("Could not load dompurify: "+t))}).then(function(t){return t.default?t.default:t})}var a=function(t){var e=y(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},o=function(t,e){var n=document.createElement(t);for(var r in e.className&&(n.className=e.className),e.innerHTML&&e.dompurify&&(n.innerHTML=e.dompurify.sanitize(e.innerHTML)),e.style)n.style[r]=e.style[r];return n},s=function t(e,n){for(var r=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==n&&1===i.nodeType&&"SCRIPT"===i.nodeName||r.appendChild(t(i,n));return 1===e.nodeType&&("CANVAS"===e.nodeName?(r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(r.value=e.value),r.addEventListener("load",function(){r.scrollTop=e.scrollTop,r.scrollLeft=e.scrollLeft},!0)),r},l=function t(e){var n=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),r=t.convert(Promise.resolve(),n);return(r=r.setProgress(1,t,1,[t])).set(e)};(l.prototype=Object.create(Promise.prototype)).constructor=l,l.convert=function(t,e){return t.__proto__=e||l.prototype,t},l.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},l.prototype.from=function(t,e){return this.then(function(){switch(e=e||function(t){switch(a(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(t),e){case"string":return this.then(i).then(function(e){return this.set({src:o("div",{innerHTML:t,dompurify:e})})});case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}})},l.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},l.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var t={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},e=s(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=o("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=o("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(o("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},l.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(r).then(function(t){var e=Object.assign({},this.opt.html2canvas);return delete e.onrendered,t(this.prop.container,e)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},l.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(r).then(function(t){var e=this.opt.jsPDF,n=this.opt.fontFaces,r="number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,i=Object.assign({async:!0,allowTaint:!0,scale:r,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete i.onrendered,e.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,e.context2d.posX=this.opt.x,e.context2d.posY=this.opt.y,e.context2d.margin=this.opt.margin,e.context2d.fontFaces=n,n)for(var a=0;a<n.length;++a){var o=n[a],s=o.src.find(function(t){return"truetype"===t.format});s&&e.addFont(s.url,o.ref.name,o.ref.style)}return i.windowHeight=i.windowHeight||0,i.windowHeight=0==i.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):i.windowHeight,e.context2d.save(!0),t(this.prop.container,i)}).then(function(t){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},l.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t})},l.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},l.prototype.output=function(t,e,n){return"img"===(n=n||"pdf").toLowerCase()||"image"===n.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},l.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(t,e)})},l.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}})},l.prototype.save=function(t){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},l.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},l.prototype.set=function(t){if("object"!==a(t))return this;var e=Object.keys(t||{}).map(function(e){if(e in l.template.prop)return function(){this.prop[e]=t[e]};switch(e){case"margin":return this.setMargin.bind(this,t.margin);case"jsPDF":return function(){return this.opt.jsPDF=t.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,t.pageSize);default:return function(){this.opt[e]=t[e]}}},this);return this.then(function(){return this.thenList(e)})},l.prototype.get=function(t,e){return this.then(function(){var n=t in l.template.prop?this.prop[t]:this.opt[t];return e?e(n):n})},l.prototype.setMargin=function(t){return this.then(function(){switch(a(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t}).then(this.setPageSize)},l.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then(function(){(t=t||Zn.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t})},l.prototype.setProgress=function(t,e,n,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=n&&(this.progress.n=n),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},l.prototype.updateProgress=function(t,e,n,r){return this.setProgress(t?this.progress.val+t:null,e||null,n?this.progress.n+n:null,r?this.progress.stack.concat(r):null)},l.prototype.then=function(t,e){var n=this;return this.thenCore(t,e,function(t,e){return n.updateProgress(null,null,1,[t]),Promise.prototype.then.call(this,function(e){return n.updateProgress(null,t),e}).then(t,e).then(function(t){return n.updateProgress(1),t})})},l.prototype.thenCore=function(t,e,n){n=n||Promise.prototype.then;var r=this;t&&(t=t.bind(r)),e&&(e=e.bind(r));var i=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?r:l.convert(Object.assign({},r),Promise.prototype),a=n.call(i,t,e);return l.convert(a,r.__proto__)},l.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},l.prototype.thenList=function(t){var e=this;return t.forEach(function(t){e=e.thenCore(t)}),e},l.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return l.convert(e,this)},l.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},l.prototype.error=function(t){return this.then(function(){throw new Error(t)})},l.prototype.using=l.prototype.set,l.prototype.saveAs=l.prototype.save,l.prototype.export=l.prototype.output,l.prototype.run=l.prototype.then,Zn.getPageSize=function(t,e,n){if("object"===y(t)){var r=t;t=r.orientation,e=r.unit||e,n=r.format||n}e=e||"mm",n=n||"a4",t=(""+(t||"P")).toLowerCase();var i,a=(""+n).toLowerCase(),o={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":i=1;break;case"mm":i=72/25.4;break;case"cm":i=72/2.54;break;case"in":i=72;break;case"px":i=.75;break;case"pc":case"em":i=12;break;case"ex":i=6;break;default:throw"Invalid unit: "+e}var s,l=0,h=0;if(o.hasOwnProperty(a))l=o[a][1]/i,h=o[a][0]/i;else try{l=n[1],h=n[0]}catch(Sn){throw new Error("Invalid format: "+n)}if("p"===t||"portrait"===t)t="p",h>l&&(s=h,h=l,l=s);else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",l>h&&(s=h,h=l,l=s)}return{width:h,height:l,unit:e,k:i,orientation:t}},t.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this,e.fontFaces=e.fontFaces?e.fontFaces.map(Kr):null;var n=new l(e);return e.worker?n:n.from(t).doCallback()}}(Zn.API),Zn.API.addJS=function(t){return ri=t,this.internal.events.subscribe("postPutResources",function(){ei=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ei+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),ni=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+ri+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==ei&&void 0!==ni&&this.internal.out("/Names <</JavaScript "+ei+" 0 R>>")}),this},
/**
       * @license
       * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e;t.events.push(["postPutResources",function(){var t=this,n=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var r=t.outline.render().split(/\r\n/),i=0;i<r.length;i++){var a=r[i],o=n.exec(a);if(null!=o){var s=o[1];t.internal.newObjectDeferredBegin(s,!1)}t.internal.write(a)}if(this.outline.createNamedDestinations){var l=this.internal.pages.length,h=[];for(i=0;i<l;i++){var c=t.internal.newObject();h.push(c);var u=t.internal.getPageInfo(i+1);t.internal.write("<< /D["+u.objId+" 0 R /XYZ null null null]>> endobj")}var f=t.internal.newObject();for(t.internal.write("<< /Names [ "),i=0;i<h.length;i++)t.internal.write("(page_"+(i+1)+")"+h[i]+" 0 R");t.internal.write(" ] >>","endobj"),e=t.internal.newObject(),t.internal.write("<< /Dests "+f+" 0 R"),t.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){var t=this;t.outline.root.children.length>0&&(t.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&t.internal.write("/Names "+e+" 0 R"))}]),t.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(t,e,n){var r={title:e,options:n,children:[]};return null==t&&(t=this.root),t.children.push(r),r},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(e){e.id=t.internal.newObjectDeferred();for(var n=0;n<e.children.length;n++)this.genIds_r(e.children[n])},t.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),t.children.length>0&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},t.outline.renderItems=function(e){for(var n=this.ctx.pdf.internal.getVerticalCoordinateString,r=0;r<e.children.length;r++){var i=e.children[r];this.objStart(i),this.line("/Title "+this.makeString(i.title)),this.line("/Parent "+this.makeRef(e)),r>0&&this.line("/Prev "+this.makeRef(e.children[r-1])),r<e.children.length-1&&this.line("/Next "+this.makeRef(e.children[r+1])),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1])));var a=this.count=this.count_r({count:0},i);if(a>0&&this.line("/Count "+a),i.options&&i.options.pageNumber){var o=t.internal.getPageInfo(i.options.pageNumber);this.line("/Dest ["+o.objId+" 0 R /XYZ 0 "+n(0)+" 0]")}this.objEnd()}for(var s=0;s<e.children.length;s++)this.renderItems(e.children[s])},t.outline.line=function(t){this.ctx.val+=t+"\r\n"},t.outline.makeRef=function(t){return t.id+" 0 R"},t.outline.makeString=function(e){return"("+t.internal.pdfEscape(e)+")"},t.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},t.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},t.outline.count_r=function(t,e){for(var n=0;n<e.children.length;n++)t.count++,this.count_r(t,e.children[n]);return t.count}}])}(Zn.API),
/**
       * @license
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e=[192,193,194,195,196,197,198,199];t.processJPEG=function(t,n,r,i,a,o){var s,l=this.decode.DCT_DECODE,h=null;if("string"==typeof t||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=a||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,s=function(t){for(var n,r=256*t.charCodeAt(4)+t.charCodeAt(5),i=t.length,a={width:0,height:0,numcomponents:1},o=4;o<i;o+=2){if(o+=r,-1!==e.indexOf(t.charCodeAt(o+1))){n=256*t.charCodeAt(o+5)+t.charCodeAt(o+6),a={width:256*t.charCodeAt(o+7)+t.charCodeAt(o+8),height:n,numcomponents:t.charCodeAt(o+9)};break}r=256*t.charCodeAt(o+2)+t.charCodeAt(o+3)}return a}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t),s.numcomponents){case 1:o=this.color_spaces.DEVICE_GRAY;break;case 4:o=this.color_spaces.DEVICE_CMYK;break;case 3:o=this.color_spaces.DEVICE_RGB}h={data:t,width:s.width,height:s.height,colorSpace:o,bitsPerComponent:8,filter:l,index:n,alias:r}}return h}}(Zn.API),Zn.API.processPNG=function(t,e,n,r){if(this.__addimage__.isArrayBuffer(t)&&(t=new Uint8Array(t)),this.__addimage__.isArrayBufferView(t)){var i,a=new bn(t,{checkCrc:!0}).decode(),o=a.width,s=a.height,l=a.channels,h=a.palette,c=a.depth;i=h&&1===l?function(t){for(var e=t.width,n=t.height,r=t.data,i=t.palette,a=t.depth,o=!1,s=[],l=[],h=void 0,c=!1,u=0,f=0;f<i.length;f++){var d=nt(i[f],4),p=d[0],g=d[1],m=d[2],b=d[3];s.push(p,g,m),null!=b&&(0===b?(u++,l.length<1&&l.push(f)):b<255&&(c=!0))}if(c||u>1){o=!0,l=void 0;var v=e*n;h=new Uint8Array(v);for(var y=new DataView(r.buffer),w=0;w<v;w++){var x=nt(i[vi(y,w,a)],4)[3];h[w]=x}}return{colorSpace:"Indexed",colorsPerPixel:1,colorBytes:r,alphaBytes:h,needSMask:o,palette:s,mask:l}}(a):2===l||4===l?function(t){for(var e=t.data,n=t.width,r=t.height,i=t.channels,a=t.depth,o=2===i?"DeviceGray":"DeviceRGB",s=i-1,l=n*r,h=s,c=l*h,u=1*l,f=Math.ceil(c*a/8),d=Math.ceil(u*a/8),p=new Uint8Array(f),g=new Uint8Array(d),m=new DataView(e.buffer),b=new DataView(p.buffer),v=new DataView(g.buffer),y=!1,w=0;w<l;w++){for(var x=w*i,_=0;_<h;_++)yi(b,vi(m,x+_,a),w*h+_,a);var A=vi(m,x+h,a);A<(1<<a)-1&&(y=!0),yi(v,A,1*w,a)}return{colorSpace:o,colorsPerPixel:s,colorBytes:p,alphaBytes:g,needSMask:y}}(a):function(t){var e=t.data,n=1===t.channels?"DeviceGray":"DeviceRGB";return{colorSpace:n,colorsPerPixel:"DeviceGray"===n?1:3,colorBytes:e instanceof Uint8Array?e:new Uint8Array(e.buffer),needSMask:!1}}(a);var u,f,d,p=i,g=p.colorSpace,m=p.colorsPerPixel,b=p.colorBytes,v=p.alphaBytes,y=p.needSMask,w=p.palette,x=p.mask,_=null;return r!==Zn.API.image_compression.NONE?(_=function(t){var e;switch(t){case Zn.API.image_compression.FAST:e=11;break;case Zn.API.image_compression.MEDIUM:e=13;break;case Zn.API.image_compression.SLOW:e=14;break;default:e=12}return e}(r),u=this.decode.FLATE_DECODE,f="/Predictor ".concat(_," "),t=ci(b,o*m,m,r),y&&(d=ci(v,o,1,r))):(u=void 0,f="",t=b,y&&(d=v)),f+="/Colors ".concat(m," /BitsPerComponent ").concat(c," /Columns ").concat(o),(this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t))&&(t=this.__addimage__.arrayBufferToBinaryString(t)),(d&&this.__addimage__.isArrayBuffer(d)||this.__addimage__.isArrayBufferView(d))&&(d=this.__addimage__.arrayBufferToBinaryString(d)),{alias:n,data:t,index:e,filter:u,decodeParameters:f,transparency:x,palette:w,sMask:d,predictor:_,width:o,height:s,bitsPerComponent:c,colorSpace:g}}},function(t){t.processGIF89A=function(e,n,r,i){var a=new xi(e),o=a.width,s=a.height,l=[];a.decodeAndBlitFrameRGBA(0,l);var h={data:l,width:o,height:s},c=new Ai(100).encode(h,100);return t.processJPEG.call(this,c,n,r,i)},t.processGIF87A=t.processGIF89A}(Zn.API),Li.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var n=this.datav.getUint8(this.pos++,!0),r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:i,green:r,blue:n,quad:a}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Li.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(n){xn.log("bit decode error:"+n)}},Li.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),n=e%4;for(t=this.height-1;t>=0;t--){for(var r=this.bottom_up?t:this.height-1-t,i=0;i<e;i++)for(var a=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var l=this.palette[a>>7-s&1];this.data[o+4*s]=l.blue,this.data[o+4*s+1]=l.green,this.data[o+4*s+2]=l.red,this.data[o+4*s+3]=255}0!==n&&(this.pos+=4-n)}},Li.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<t;i++){var a=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+2*i*4,s=a>>4,l=15&a,h=this.palette[s];if(this.data[o]=h.blue,this.data[o+1]=h.green,this.data[o+2]=h.red,this.data[o+3]=255,2*i+1>=this.width)break;h=this.palette[l],this.data[o+4]=h.blue,this.data[o+4+1]=h.green,this.data[o+4+2]=h.red,this.data[o+4+3]=255}0!==e&&(this.pos+=4-e)}},Li.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,r=0;r<this.width;r++){var i=this.datav.getUint8(this.pos++,!0),a=n*this.width*4+4*r;if(i<this.palette.length){var o=this.palette[i];this.data[a]=o.red,this.data[a+1]=o.green,this.data[a+2]=o.blue,this.data[a+3]=255}else this.data[a]=255,this.data[a+1]=255,this.data[a+2]=255,this.data[a+3]=255}0!==t&&(this.pos+=4-t)}},Li.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<this.width;i++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var o=(a&e)/e*255|0,s=(a>>5&e)/e*255|0,l=(a>>10&e)/e*255|0,h=a>>15?255:0,c=r*this.width*4+4*i;this.data[c]=l,this.data[c+1]=s,this.data[c+2]=o,this.data[c+3]=h}this.pos+=t}},Li.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,a=0;a<this.width;a++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(o&e)/e*255|0,l=(o>>5&n)/n*255|0,h=(o>>11)/e*255|0,c=i*this.width*4+4*a;this.data[c]=h,this.data[c+1]=l,this.data[c+2]=s,this.data[c+3]=255}this.pos+=t}},Li.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=e*this.width*4+4*n;this.data[o]=a,this.data[o+1]=i,this.data[o+2]=r,this.data[o+3]=255}this.pos+=this.width%4}},Li.prototype.bit32=function(){for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*n;this.data[s]=a,this.data[s+1]=i,this.data[s+2]=r,this.data[s+3]=o}},Li.prototype.getData=function(){return this.data},
/**
       * @license
       * Copyright (c) 2018 Aras Abbasi
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){t.processBMP=function(e,n,r,i){var a=new Li(e,!1),o=a.width,s=a.height,l={data:a.getData(),width:o,height:s},h=new Ai(100).encode(l,100);return t.processJPEG.call(this,h,n,r,i)}}(Zn.API),Ni.prototype.getData=function(){return this.data},
/**
       * @license
       * Copyright (c) 2019 Aras Abbasi
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){t.processWEBP=function(e,n,r,i){var a=new Ni(e),o=a.width,s=a.height,l={data:a.getData(),width:o,height:s},h=new Ai(100).encode(l,100);return t.processJPEG.call(this,h,n,r,i)}}(Zn.API),Zn.API.processRGBA=function(t,e,n){for(var r=t.data,i=r.length,a=new Uint8Array(i/4*3),o=new Uint8Array(i/4),s=0,l=0,h=0;h<i;h+=4){var c=r[h],u=r[h+1],f=r[h+2],d=r[h+3];a[s++]=c,a[s++]=u,a[s++]=f,o[l++]=d}var p=this.__addimage__.arrayBufferToBinaryString(a);return{alpha:this.__addimage__.arrayBufferToBinaryString(o),data:p,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:t.width,height:t.height}},Zn.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!=={af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},ii=Zn.API,ai=ii.getCharWidthsArray=function(t,e){var n,r,i=(e=e||{}).font||this.internal.getFont(),a=e.fontSize||this.internal.getFontSize(),o=e.charSpace||this.internal.getCharSpace(),s=e.widths?e.widths:i.metadata.Unicode.widths,l=s.fof?s.fof:1,h=e.kerning?e.kerning:i.metadata.Unicode.kerning,c=h.fof?h.fof:1,u=!1!==e.doKerning,f=0,d=t.length,p=0,g=s[0]||l,m=[];for(n=0;n<d;n++)r=t.charCodeAt(n),"function"==typeof i.metadata.widthOfString?m.push((i.metadata.widthOfGlyph(i.metadata.characterToGlyph(r))+o*(1e3/a)||0)/1e3):(f=u&&"object"===y(h[r])&&!isNaN(parseInt(h[r][p],10))?h[r][p]/c:0,m.push((s[r]||g)/l+f)),p=r;return m},oi=ii.getStringUnitWidth=function(t,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),r=e.font||this.internal.getFont(),i=e.charSpace||this.internal.getCharSpace();return ii.processArabic&&(t=ii.processArabic(t)),"function"==typeof r.metadata.widthOfString?r.metadata.widthOfString(t,n,i)/n:ai.apply(this,arguments).reduce(function(t,e){return t+e},0)},si=function(t,e,n,r){for(var i=[],a=0,o=t.length,s=0;a!==o&&s+e[a]<n;)s+=e[a],a++;i.push(t.slice(0,a));var l=a;for(s=0;a!==o;)s+e[a]>r&&(i.push(t.slice(l,a)),s=0,l=a),s+=e[a],a++;return l!==a&&i.push(t.slice(l,a)),i},li=function(t,e,n){n||(n={});var r,i,a,o,s,l,h,c=[],u=[c],f=n.textIndent||0,d=0,p=0,g=t.split(" "),m=ai.apply(this,[" ",n])[0];if(l=-1===n.lineIndent?g[0].length+2:n.lineIndent||0){var b=Array(l).join(" "),v=[];g.map(function(t){(t=t.split(/\s*\n/)).length>1?v=v.concat(t.map(function(t,e){return(e&&t.length?"\n":"")+t})):v.push(t[0])}),g=v,l=oi.apply(this,[b,n])}for(a=0,o=g.length;a<o;a++){var y=0;if(r=g[a],l&&"\n"==r[0]&&(r=r.substr(1),y=1),f+d+(p=(i=ai.apply(this,[r,n])).reduce(function(t,e){return t+e},0))>e||y){if(p>e){for(s=si.apply(this,[r,i,e-(f+d),e]),c.push(s.shift()),c=[s.pop()];s.length;)u.push([s.shift()]);p=i.slice(r.length-(c[0]?c[0].length:0)).reduce(function(t,e){return t+e},0)}else c=[r];u.push(c),f=p+l,d=m}else c.push(r),f+=d+p,d=m}return h=l?function(t,e){return(e?b:"")+t.join(" ")}:function(t){return t.join(" ")},u.map(h)},ii.splitTextToSize=function(t,e,n){var r,i=(n=n||{}).fontSize||this.internal.getFontSize(),a=function(t){if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var e=this.internal.getFont(t.fontName,t.fontStyle),n="Unicode";return e.metadata[n]?{widths:e.metadata[n].widths||{0:1},kerning:e.metadata[n].kerning||{}}:{font:e.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}.call(this,n);r=Array.isArray(t)?t:String(t).split(/\r?\n/);var o=1*this.internal.scaleFactor*e/i;a.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/i:0,a.lineIndent=n.lineIndent;var s,l,h=[];for(s=0,l=r.length;s<l;s++)h=h.concat(li.apply(this,[r[s],o,a]));return h},function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="0123456789abcdef",n="klmnopqrstuvwxyz",r={},i={},a=0;a<16;a++)r[n[a]]=e[a],i[e[a]]=n[a];var o=function(t){return"0x"+parseInt(t,10).toString(16)},s=t.__fontmetrics__.compress=function(t){var e,n,r,a,l=["{"];for(var h in t){if(e=t[h],isNaN(parseInt(h,10))?n="'"+h+"'":(h=parseInt(h,10),n=(n=o(h).slice(2)).slice(0,-1)+i[n.slice(-1)]),"number"==typeof e)e<0?(r=o(e).slice(3),a="-"):(r=o(e).slice(2),a=""),r=a+r.slice(0,-1)+i[r.slice(-1)];else{if("object"!==y(e))throw new Error("Don't know what to do with value type "+y(e)+".");r=s(e)}l.push(n+r)}return l.push("}"),l.join("")},l=t.__fontmetrics__.uncompress=function(t){if("string"!=typeof t)throw new Error("Invalid argument passed to uncompress.");for(var e,n,i,a,o={},s=1,l=o,h=[],c="",u="",f=t.length-1,d=1;d<f;d+=1)"'"==(a=t[d])?e?(i=e.join(""),e=void 0):e=[]:e?e.push(a):"{"==a?(h.push([l,i]),l={},i=void 0):"}"==a?((n=h.pop())[0][n[1]]=l,i=void 0,l=n[0]):"-"==a?s=-1:void 0===i?r.hasOwnProperty(a)?(c+=r[a],i=parseInt(c,16)*s,s=1,c=""):c+=a:r.hasOwnProperty(a)?(u+=r[a],l[i]=parseInt(u,16)*s,s=1,i=void 0,u=""):u+=a;return o},h={codePages:["WinAnsiEncoding"],WinAnsiEncoding:l("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},c={Unicode:{Courier:h,"Courier-Bold":h,"Courier-BoldOblique":h,"Courier-Oblique":h,Helvetica:h,"Helvetica-Bold":h,"Helvetica-BoldOblique":h,"Helvetica-Oblique":h,"Times-Roman":h,"Times-Bold":h,"Times-BoldItalic":h,"Times-Italic":h}},u={Unicode:{"Courier-Oblique":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":l("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":l("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":l("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:l("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:l("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":l("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:l("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":l("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":l("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":l("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":l("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e=t.font,n=u.Unicode[e.postScriptName];n&&(e.metadata.Unicode={},e.metadata.Unicode.widths=n.widths,e.metadata.Unicode.kerning=n.kerning);var r=c.Unicode[e.postScriptName];r&&(e.metadata.Unicode.encoding=r,e.encoding=r.codePages[0])}])}(Zn.API),
/**
       * @license
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n};t.API.events.push(["addFont",function(n){var r=void 0,i=n.font,a=n.instance;if(!i.isStandardFont){if(void 0===a)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+i.postScriptName+"').");if("string"!=typeof(r=!1===a.existsFileInVFS(i.postScriptName)?a.loadFile(i.postScriptName):a.getFileFromVFS(i.postScriptName)))throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+i.postScriptName+"').");!function(n,r){r=/^\x00\x01\x00\x00/.test(r)?e(r):e(kn(r)),n.metadata=t.API.TTFFont.open(r),n.metadata.Unicode=n.metadata.Unicode||{encoding:{},kerning:{},widths:[]},n.metadata.glyIdsUsed=[0]}(i,r)}}])}(Zn),Zn.API.addSvgAsImage=function(t,r,i,a,o,s,l,h){if(isNaN(r)||isNaN(i))throw xn.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(a)||isNaN(o))throw xn.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var c=document.createElement("canvas");c.width=a,c.height=o;var u=c.getContext("2d");u.fillStyle="#fff",u.fillRect(0,0,c.width,c.height);var f={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},d=this;return(yn.canvg?Promise.resolve(yn.canvg):n(()=>e.import("./index.es-legacy-C_ZbPwrJ.js"),void 0)).catch(function(t){return Promise.reject(new Error("Could not load canvg: "+t))}).then(function(t){return t.default?t.default:t}).then(function(e){return e.fromString(u,t,f)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(t){return t.render(f)}).then(function(){d.addImage(c.toDataURL("image/jpeg",1),r,i,a,o,l,h)})},Zn.API.putTotalPages=function(t){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(t,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var r=1;r<=this.internal.getNumberOfPages();r++)for(var i=0;i<this.internal.pages[r].length;i++)this.internal.pages[r][i]=this.internal.pages[r][i].replace(e,n);return this},Zn.API.viewerPreferences=function(t,e){var n;t=t||{},e=e||!1;var r,i,a,o={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},s=Object.keys(o),l=[],h=0,c=0,u=0;function f(t,e){var n,r=!1;for(n=0;n<t.length;n+=1)t[n]===e&&(r=!0);return r}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(o)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var d=s.length;for(u=0;u<d;u+=1)n[s[u]].value=n[s[u]].defaultValue,n[s[u]].explicitSet=!1}if("object"===y(t))for(i in t)if(a=t[i],f(s,i)&&void 0!==a){if("boolean"===n[i].type&&"boolean"==typeof a)n[i].value=a;else if("name"===n[i].type&&f(n[i].valueSet,a))n[i].value=a;else if("integer"===n[i].type&&Number.isInteger(a))n[i].value=a;else if("array"===n[i].type){for(h=0;h<a.length;h+=1)if(r=!0,1===a[h].length&&"number"==typeof a[h][0])l.push(String(a[h]-1));else if(a[h].length>1){for(c=0;c<a[h].length;c+=1)"number"!=typeof a[h][c]&&(r=!1);!0===r&&l.push([a[h][0]-1,a[h][1]-1].join(" "))}n[i].value="["+l.join(" ")+"]"}else n[i].value=n[i].defaultValue;n[i].explicitSet=!0}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var t,e=[];for(t in n)!0===n[t].explicitSet&&("name"===n[t].type?e.push("/"+t+" /"+n[t].value):e.push("/"+t+" "+n[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},
/** ====================================================================
       * @license
       * jsPDF XMP metadata plugin
       * Copyright (c) 2016 Jussi Utunen, <EMAIL>
       *
       * Permission is hereby granted, free of charge, to any person obtaining
       * a copy of this software and associated documentation files (the
       * "Software"), to deal in the Software without restriction, including
       * without limitation the rights to use, copy, modify, merge, publish,
       * distribute, sublicense, and/or sell copies of the Software, and to
       * permit persons to whom the Software is furnished to do so, subject to
       * the following conditions:
       *
       * The above copyright notice and this permission notice shall be
       * included in all copies or substantial portions of the Software.
       *
       * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
       * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
       * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
       * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
       * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
       * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
       * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
       * ====================================================================
       */
function(t){var e=function(){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),n=unescape(encodeURIComponent(t)),r=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),i=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),a=unescape(encodeURIComponent("</x:xmpmeta>")),o=n.length+r.length+i.length+e.length+a.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+o+" >>"),this.internal.write("stream"),this.internal.write(e+n+r+i+a),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};t.addMetadata=function(t,r){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:t,namespaceuri:r||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(Zn.API),function(t){var e=t.API,n=e.pdfEscape16=function(t,e){for(var n,r=e.metadata.Unicode.widths,i=["","0","00","000","0000"],a=[""],o=0,s=t.length;o<s;++o){if(n=e.metadata.characterToGlyph(t.charCodeAt(o)),e.metadata.glyIdsUsed.push(n),e.metadata.toUnicode[n]=t.charCodeAt(o),-1==r.indexOf(n)&&(r.push(n),r.push([parseInt(e.metadata.widthOfGlyph(n),10)])),"0"==n)return a.join("");n=n.toString(16),a.push(i[4-n.length],n)}return a.join("")},r=function(t){var e,n,r,i,a,o,s;for(a="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",r=[],o=0,s=(n=Object.keys(t).sort(function(t,e){return t-e})).length;o<s;o++)e=n[o],r.length>=100&&(a+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar",r=[]),void 0!==t[e]&&null!==t[e]&&"function"==typeof t[e].toString&&(i=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),r.push("<"+e+"><"+i+">"));return r.length&&(a+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar\n"),a+"endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};e.events.push(["putFont",function(e){!function(e){var n=e.font,i=e.out,a=e.newObject,o=e.putStream;if(n.metadata instanceof t.API.TTFFont&&"Identity-H"===n.encoding){for(var s=n.metadata.Unicode.widths,l=n.metadata.subset.encode(n.metadata.glyIdsUsed,1),h="",c=0;c<l.length;c++)h+=String.fromCharCode(l[c]);var u=a();o({data:h,addLength1:!0,objectId:u}),i("endobj");var f=a();o({data:r(n.metadata.toUnicode),addLength1:!0,objectId:f}),i("endobj");var d=a();i("<<"),i("/Type /FontDescriptor"),i("/FontName /"+Vn(n.fontName)),i("/FontFile2 "+u+" 0 R"),i("/FontBBox "+t.API.PDFObject.convert(n.metadata.bbox)),i("/Flags "+n.metadata.flags),i("/StemV "+n.metadata.stemV),i("/ItalicAngle "+n.metadata.italicAngle),i("/Ascent "+n.metadata.ascender),i("/Descent "+n.metadata.decender),i("/CapHeight "+n.metadata.capHeight),i(">>"),i("endobj");var p=a();i("<<"),i("/Type /Font"),i("/BaseFont /"+Vn(n.fontName)),i("/FontDescriptor "+d+" 0 R"),i("/W "+t.API.PDFObject.convert(s)),i("/CIDToGIDMap /Identity"),i("/DW 1000"),i("/Subtype /CIDFontType2"),i("/CIDSystemInfo"),i("<<"),i("/Supplement 0"),i("/Registry (Adobe)"),i("/Ordering ("+n.encoding+")"),i(">>"),i(">>"),i("endobj"),n.objectNumber=a(),i("<<"),i("/Type /Font"),i("/Subtype /Type0"),i("/ToUnicode "+f+" 0 R"),i("/BaseFont /"+Vn(n.fontName)),i("/Encoding /"+n.encoding),i("/DescendantFonts ["+p+" 0 R]"),i(">>"),i("endobj"),n.isAlreadyPutted=!0}}(e)}]),e.events.push(["putFont",function(e){!function(e){var n=e.font,i=e.out,a=e.newObject,o=e.putStream;if(n.metadata instanceof t.API.TTFFont&&"WinAnsiEncoding"===n.encoding){for(var s=n.metadata.rawData,l="",h=0;h<s.length;h++)l+=String.fromCharCode(s[h]);var c=a();o({data:l,addLength1:!0,objectId:c}),i("endobj");var u=a();o({data:r(n.metadata.toUnicode),addLength1:!0,objectId:u}),i("endobj");var f=a();i("<<"),i("/Descent "+n.metadata.decender),i("/CapHeight "+n.metadata.capHeight),i("/StemV "+n.metadata.stemV),i("/Type /FontDescriptor"),i("/FontFile2 "+c+" 0 R"),i("/Flags 96"),i("/FontBBox "+t.API.PDFObject.convert(n.metadata.bbox)),i("/FontName /"+Vn(n.fontName)),i("/ItalicAngle "+n.metadata.italicAngle),i("/Ascent "+n.metadata.ascender),i(">>"),i("endobj"),n.objectNumber=a();for(var d=0;d<n.metadata.hmtx.widths.length;d++)n.metadata.hmtx.widths[d]=parseInt(n.metadata.hmtx.widths[d]*(1e3/n.metadata.head.unitsPerEm));i("<</Subtype/TrueType/Type/Font/ToUnicode "+u+" 0 R/BaseFont/"+Vn(n.fontName)+"/FontDescriptor "+f+" 0 R/Encoding/"+n.encoding+" /FirstChar 29 /LastChar 255 /Widths "+t.API.PDFObject.convert(n.metadata.hmtx.widths)+">>"),i("endobj"),n.isAlreadyPutted=!0}}(e)}]);var i=function(t){var e,r=t.text||"",i=t.x,a=t.y,o=t.options||{},s=t.mutex||{},l=s.pdfEscape,h=s.activeFontKey,c=s.fonts,u=h,f="",d=0,p="",g=c[u].encoding;if("Identity-H"!==c[u].encoding)return{text:r,x:i,y:a,options:o,mutex:s};for(p=r,u=h,Array.isArray(r)&&(p=r[0]),d=0;d<p.length;d+=1)c[u].metadata.hasOwnProperty("cmap")&&(e=c[u].metadata.cmap.unicode.codeMap[p[d].charCodeAt(0)]),e||p[d].charCodeAt(0)<256&&c[u].metadata.hasOwnProperty("Unicode")?f+=p[d]:f+="";var m="";return parseInt(u.slice(1))<14||"WinAnsiEncoding"===g?m=l(f,u).split("").map(function(t){return t.charCodeAt(0).toString(16)}).join(""):"Identity-H"===g&&(m=n(f,c[u])),s.isHex=!0,{text:m,x:i,y:a,options:o,mutex:s}};e.events.push(["postProcessText",function(t){var e=t.text||"",n=[],r={text:e,x:t.x,y:t.y,options:t.options,mutex:t.mutex};if(Array.isArray(e)){var a=0;for(a=0;a<e.length;a+=1)Array.isArray(e[a])&&3===e[a].length?n.push([i(Object.assign({},r,{text:e[a][0]})).text,e[a][1],e[a][2]]):n.push(i(Object.assign({},r,{text:e[a]})).text);t.text=n}else t.text=i(Object.assign({},r,{text:e})).text}])}(Zn),
/**
       * @license
       * jsPDF virtual FileSystem functionality
       *
       * Licensed under the MIT License.
       * http://opensource.org/licenses/mit-license
       */
function(t){var e=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0};t.existsFileInVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]},t.addFileToVFS=function(t,n){return e.call(this),this.internal.vFS[t]=n,this},t.getFileFromVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null}}(Zn.API),
/**
       * @license
       * Unicode Bidi Engine based on the work of Alex Shensis (@asthensis)
       * MIT License
       */
function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var n,r,i,a,o,s,l,h=e,c=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],u=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],f={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},d={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},p=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],g=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),m=!1,b=0;this.__bidiEngine__={};var v=function(t){var e=t.charCodeAt(),n=e>>8,r=d[n];return void 0!==r?h[256*r+(255&e)]:252===n||253===n?"AL":g.test(n)?"L":8===n?"R":"N"},y=function(t){for(var e,n=0;n<t.length;n++){if("L"===(e=v(t.charAt(n))))return!1;if("R"===e)return!0}return!1},w=function(t,e,o,s){var l,h,c,u,f=e[s];switch(f){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":m=!1;break;case"N":case"AN":break;case"EN":m&&(f="AN");break;case"AL":m=!0,f="R";break;case"WS":case"BN":f="N";break;case"CS":s<1||s+1>=e.length||"EN"!==(l=o[s-1])&&"AN"!==l||"EN"!==(h=e[s+1])&&"AN"!==h?f="N":m&&(h="AN"),f=h===l?h:"N";break;case"ES":f="EN"===(l=s>0?o[s-1]:"B")&&s+1<e.length&&"EN"===e[s+1]?"EN":"N";break;case"ET":if(s>0&&"EN"===o[s-1]){f="EN";break}if(m){f="N";break}for(c=s+1,u=e.length;c<u&&"ET"===e[c];)c++;f=c<u&&"EN"===e[c]?"EN":"N";break;case"NSM":if(i&&!a){for(u=e.length,c=s+1;c<u&&"NSM"===e[c];)c++;if(c<u){var d=t[s],p=d>=1425&&d<=2303||64286===d;if(l=e[c],p&&("R"===l||"AL"===l)){f="R";break}}}f=s<1||"B"===(l=e[s-1])?"N":o[s-1];break;case"B":m=!1,n=!0,f=b;break;case"S":r=!0,f="N"}return f},x=function(t,e,n){var r=t.split("");return n&&_(r,n,{hiLevel:b}),r.reverse(),e&&e.reverse(),r.join("")},_=function(t,e,i){var a,o,s,l,h,d=-1,p=t.length,g=0,y=[],x=b?u:c,_=[];for(m=!1,n=!1,r=!1,o=0;o<p;o++)_[o]=v(t[o]);for(s=0;s<p;s++){if(h=g,y[s]=w(t,_,y,s),a=240&(g=x[h][f[y[s]]]),g&=15,e[s]=l=x[g][5],a>0)if(16===a){for(o=d;o<s;o++)e[o]=1;d=-1}else d=-1;if(x[g][6])-1===d&&(d=s);else if(d>-1){for(o=d;o<s;o++)e[o]=l;d=-1}"B"===_[s]&&(e[s]=0),i.hiLevel|=l}r&&function(t,e,n){for(var r=0;r<n;r++)if("S"===t[r]){e[r]=b;for(var i=r-1;i>=0&&"WS"===t[i];i--)e[i]=b}}(_,e,p)},A=function(t,e,r,i,a){if(!(a.hiLevel<t)){if(1===t&&1===b&&!n)return e.reverse(),void(r&&r.reverse());for(var o,s,l,h,c=e.length,u=0;u<c;){if(i[u]>=t){for(l=u+1;l<c&&i[l]>=t;)l++;for(h=u,s=l-1;h<s;h++,s--)o=e[h],e[h]=e[s],e[s]=o,r&&(o=r[h],r[h]=r[s],r[s]=o);u=l}u++}}},L=function(t,e,n){var r=t.split(""),i={hiLevel:b};return n||(n=[]),_(r,n,i),function(t,e,n){if(0!==n.hiLevel&&l)for(var r,i=0;i<t.length;i++)1===e[i]&&(r=p.indexOf(t[i]))>=0&&(t[i]=p[r+1])}(r,n,i),A(2,r,e,n,i),A(1,r,e,n,i),r.join("")};return this.__bidiEngine__.doBidiReorder=function(t,e,n){if(function(t,e){if(e)for(var n=0;n<t.length;n++)e[n]=n;void 0===a&&(a=y(t)),void 0===s&&(s=y(t))}(t,e),i||!o||s)if(i&&o&&a^s)b=a?1:0,t=x(t,e,n);else if(!i&&o&&s)b=a?1:0,t=L(t,e,n),t=x(t,e);else if(!i||a||o||s){if(i&&!o&&a^s)t=x(t,e),a?(b=0,t=L(t,e,n)):(b=1,t=L(t,e,n),t=x(t,e));else if(i&&a&&!o&&s)b=1,t=L(t,e,n),t=x(t,e);else if(!i&&!o&&a^s){var r=l;a?(b=1,t=L(t,e,n),b=0,l=!1,t=L(t,e,n),l=r):(b=0,t=L(t,e,n),t=x(t,e),b=1,l=!1,t=L(t,e,n),l=r,t=x(t,e))}}else b=0,t=L(t,e,n);else b=a?1:0,t=L(t,e,n);return t},this.__bidiEngine__.setOptions=function(t){t&&(i=t.isInputVisual,o=t.isOutputVisual,a=t.isInputRtl,s=t.isOutputRtl,l=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text;t.x,t.y;var r=t.options||{};t.mutex,r.lang;var i=[];if(r.isInputVisual="boolean"!=typeof r.isInputVisual||r.isInputVisual,n.setOptions(r),"[object Array]"===Object.prototype.toString.call(e)){var a=0;for(i=[],a=0;a<e.length;a+=1)"[object Array]"===Object.prototype.toString.call(e[a])?i.push([n.doBidiReorder(e[a][0]),e[a][1],e[a][2]]):i.push([n.doBidiReorder(e[a])]);t.text=i}else t.text=n.doBidiReorder(e);n.setOptions({isInputVisual:!0})}])}(Zn),Zn.API.TTFFont=function(){function t(t){var e;if(this.rawData=t,e=this.contents=new ki(t),this.contents.pos=4,"ttcf"===e.readString(4))throw new Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new Vi(this),this.registerTTF()}return t.open=function(e){return new t(e)},t.prototype.parse=function(){return this.directory=new Pi(this.contents),this.head=new Ii(this),this.name=new Ti(this),this.cmap=new Oi(this),this.toUnicode={},this.hhea=new Di(this),this.maxp=new Ri(this),this.hmtx=new qi(this),this.post=new Bi(this),this.os2=new Ei(this),this.loca=new Gi(this),this.glyf=new zi(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var t,e,n,r,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=function(){var e,n,r,i;for(i=[],e=0,n=(r=this.bbox).length;e<n;e++)t=r[e],i.push(Math.round(t*this.scaleFactor));return i}.call(this),this.stemV=0,this.post.exists?(n=255&(r=this.post.italic_angle),32768&(e=r>>16)&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+n)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(i=this.familyClass)||2===i||3===i||4===i||5===i||7===i,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},t.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},t.prototype.widthOfString=function(t,e,n){var r,i,a,o;for(a=0,i=0,o=(t=""+t).length;0<=o?i<o:i>o;i=0<=o?++i:--i)r=t.charCodeAt(i),a+=this.widthOfGlyph(this.characterToGlyph(r))+n*(1e3/e)||0;return a*(e/1e3)},t.prototype.lineHeight=function(t,e){var n;return null==e&&(e=!1),n=e?this.lineGap:0,(this.ascender+n-this.decender)/1e3*t},t}();var Si,ki=function(){function t(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t},t.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=2147483648?t-4294967296:t},t.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)n[e]=String.fromCharCode(this.readByte());return n.join("")},t.prototype.writeString=function(t){var e,n,r;for(r=[],e=0,n=t.length;0<=n?e<n:e>n;e=0<=n?++e:--e)r.push(this.writeByte(t.charCodeAt(e)));return r},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,n,r,i,a,o,s;return t=this.readByte(),e=this.readByte(),n=this.readByte(),r=this.readByte(),i=this.readByte(),a=this.readByte(),o=this.readByte(),s=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^n)+4294967296*(255^r)+16777216*(255^i)+65536*(255^a)+256*(255^o)+(255^s)+1):72057594037927940*t+281474976710656*e+1099511627776*n+4294967296*r+16777216*i+65536*a+256*o+s},t.prototype.writeLongLong=function(t){var e,n;return e=Math.floor(t/4294967296),n=**********&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.read=function(t){var e,n;for(e=[],n=0;0<=t?n<t:n>t;n=0<=t?++n:--n)e.push(this.readByte());return e},t.prototype.write=function(t){var e,n,r,i;for(i=[],n=0,r=t.length;n<r;n++)e=t[n],i.push(this.writeByte(e));return i},t}(),Pi=function(){var t;function e(t){var e,n,r;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},n=0,r=this.tableCount;0<=r?n<r:n>r;n=0<=r?++n:--n)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}return e.prototype.encode=function(e){var n,r,i,a,o,s,l,h,c,u,f,d,p;for(p in f=Object.keys(e).length,s=Math.log(2),c=16*Math.floor(Math.log(f)/s),a=Math.floor(c/s),h=16*f-c,(r=new ki).writeInt(this.scalarType),r.writeShort(f),r.writeShort(c),r.writeShort(a),r.writeShort(h),i=16*f,l=r.pos+i,o=null,d=[],e)for(u=e[p],r.writeString(p),r.writeInt(t(u)),r.writeInt(l),r.writeInt(u.length),d=d.concat(u),"head"===p&&(o=l),l+=u.length;l%4;)d.push(0),l++;return r.write(d),n=2981146554-t(r.data),r.pos=o+8,r.writeUInt32(n),r.data},t=function(t){var e,n,r,i;for(t=Ui.call(t);t.length%4;)t.push(0);for(r=new ki(t),n=0,e=0,i=t.length;e<i;e=e+=4)n+=r.readUInt32();return **********&n},e}(),Fi={}.hasOwnProperty,Ci=function(t,e){for(var n in e)Fi.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t};Si=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var Ii=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="head",t.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},t.prototype.encode=function(t){var e;return(e=new ki).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},t}(),ji=function(){function t(t,e){var n,r,i,a,o,s,l,h,c,u,f,d,p,g,m,b,v;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),c=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(s=0;s<256;++s)this.codeMap[s]=t.readByte();break;case 4:for(f=t.readUInt16(),u=f/2,t.pos+=6,i=function(){var e,n;for(n=[],s=e=0;0<=u?e<u:e>u;s=0<=u?++e:--e)n.push(t.readUInt16());return n}(),t.pos+=2,p=function(){var e,n;for(n=[],s=e=0;0<=u?e<u:e>u;s=0<=u?++e:--e)n.push(t.readUInt16());return n}(),l=function(){var e,n;for(n=[],s=e=0;0<=u?e<u:e>u;s=0<=u?++e:--e)n.push(t.readUInt16());return n}(),h=function(){var e,n;for(n=[],s=e=0;0<=u?e<u:e>u;s=0<=u?++e:--e)n.push(t.readUInt16());return n}(),r=(this.length-t.pos+this.offset)/2,o=function(){var e,n;for(n=[],s=e=0;0<=r?e<r:e>r;s=0<=r?++e:--e)n.push(t.readUInt16());return n}(),s=m=0,v=i.length;m<v;s=++m)for(g=i[s],n=b=d=p[s];d<=g?b<=g:b>=g;n=d<=g?++b:--b)0===h[s]?a=n+l[s]:0!==(a=o[h[s]/2+(n-d)-(u-s)]||0)&&(a+=l[s]),this.codeMap[n]=65535&a}t.pos=c}return t.encode=function(t,e){var n,r,i,a,o,s,l,h,c,u,f,d,p,g,m,b,v,y,w,x,_,A,L,N,S,k,P,F,C,I,j,O,D,E,B,M,T,R,q,U,z,H,W,G,V,K;switch(F=new ki,a=Object.keys(t).sort(function(t,e){return t-e}),e){case"macroman":for(p=0,g=function(){var t=[];for(d=0;d<256;++d)t.push(0);return t}(),b={0:0},i={},C=0,D=a.length;C<D;C++)null==b[W=t[r=a[C]]]&&(b[W]=++p),i[r]={old:t[r],new:b[t[r]]},g[r]=b[t[r]];return F.writeUInt16(1),F.writeUInt16(0),F.writeUInt32(12),F.writeUInt16(0),F.writeUInt16(262),F.writeUInt16(0),F.write(g),{charMap:i,subtable:F.data,maxGlyphID:p+1};case"unicode":for(k=[],c=[],v=0,b={},n={},m=l=null,I=0,E=a.length;I<E;I++)null==b[w=t[r=a[I]]]&&(b[w]=++v),n[r]={old:w,new:b[w]},o=b[w]-r,null!=m&&o===l||(m&&c.push(m),k.push(r),l=o),m=r;for(m&&c.push(m),c.push(65535),k.push(65535),N=2*(L=k.length),A=2*Math.pow(Math.log(L)/Math.LN2,2),u=Math.log(A/2)/Math.LN2,_=2*L-A,s=[],x=[],f=[],d=j=0,B=k.length;j<B;d=++j){if(S=k[d],h=c[d],65535===S){s.push(0),x.push(0);break}if(S-(P=n[S].new)>=32768)for(s.push(0),x.push(2*(f.length+L-d)),r=O=S;S<=h?O<=h:O>=h;r=S<=h?++O:--O)f.push(n[r].new);else s.push(P-S),x.push(0)}for(F.writeUInt16(3),F.writeUInt16(1),F.writeUInt32(12),F.writeUInt16(4),F.writeUInt16(16+8*L+2*f.length),F.writeUInt16(0),F.writeUInt16(N),F.writeUInt16(A),F.writeUInt16(u),F.writeUInt16(_),z=0,M=c.length;z<M;z++)r=c[z],F.writeUInt16(r);for(F.writeUInt16(0),H=0,T=k.length;H<T;H++)r=k[H],F.writeUInt16(r);for(G=0,R=s.length;G<R;G++)o=s[G],F.writeUInt16(o);for(V=0,q=x.length;V<q;V++)y=x[V],F.writeUInt16(y);for(K=0,U=f.length;K<U;K++)p=f[K],F.writeUInt16(p);return{charMap:n,subtable:F.data,maxGlyphID:v+1}}},t}(),Oi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="cmap",t.prototype.parse=function(t){var e,n,r;for(t.pos=this.offset,this.version=t.readUInt16(),r=t.readUInt16(),this.tables=[],this.unicode=null,n=0;0<=r?n<r:n>r;n=0<=r?++n:--n)e=new ji(t,this.offset),this.tables.push(e),e.isUnicode&&null==this.unicode&&(this.unicode=e);return!0},t.encode=function(t,e){var n,r;return null==e&&(e="macroman"),n=ji.encode(t,e),(r=new ki).writeUInt16(0),r.writeUInt16(1),n.table=r.data.concat(n.subtable),n},t}(),Di=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="hhea",t.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},t}(),Ei=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="OS/2",t.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var e,n;for(n=[],e=0;e<10;++e)n.push(t.readByte());return n}(),this.charRange=function(){var e,n;for(n=[],e=0;e<4;++e)n.push(t.readInt());return n}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var e,n;for(n=[],e=0;e<2;e=++e)n.push(t.readInt());return n}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},t}(),Bi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="post",t.prototype.parse=function(t){var e,n,r;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:case 196608:break;case 131072:var i;for(n=t.readUInt16(),this.glyphNameIndex=[],i=0;0<=n?i<n:i>n;i=0<=n?++i:--i)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],r=[];t.pos<this.offset+this.length;)e=t.readByte(),r.push(this.names.push(t.readString(e)));return r;case 151552:return n=t.readUInt16(),this.offsets=t.read(n);case 262144:return this.map=function(){var e,n,r;for(r=[],i=e=0,n=this.file.maxp.numGlyphs;0<=n?e<n:e>n;i=0<=n?++e:--e)r.push(t.readUInt32());return r}.call(this)}},t}(),Mi=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},Ti=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="name",t.prototype.parse=function(t){var e,n,r,i,a,o,s,l,h,c,u;for(t.pos=this.offset,t.readShort(),e=t.readShort(),o=t.readShort(),n=[],i=0;0<=e?i<e:i>e;i=0<=e?++i:--i)n.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+o+t.readShort()});for(s={},i=h=0,c=n.length;h<c;i=++h)r=n[i],t.pos=r.offset,l=t.readString(r.length),a=new Mi(l,r),null==s[u=r.nameID]&&(s[u]=[]),s[r.nameID].push(a);this.strings=s,this.copyright=s[0],this.fontFamily=s[1],this.fontSubfamily=s[2],this.uniqueSubfamily=s[3],this.fontName=s[4],this.version=s[5];try{this.postscriptName=s[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(kn){this.postscriptName=s[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=s[7],this.manufacturer=s[8],this.designer=s[9],this.description=s[10],this.vendorUrl=s[11],this.designerUrl=s[12],this.license=s[13],this.licenseUrl=s[14],this.preferredFamily=s[15],this.preferredSubfamily=s[17],this.compatibleFull=s[18],this.sampleText=s[19]},t}(),Ri=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="maxp",t.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},t}(),qi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="hmtx",t.prototype.parse=function(t){var e,n,r,i,a,o,s;for(t.pos=this.offset,this.metrics=[],e=0,o=this.file.hhea.numberOfMetrics;0<=o?e<o:e>o;e=0<=o?++e:--e)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(r=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var n,i;for(i=[],e=n=0;0<=r?n<r:n>r;e=0<=r?++n:--n)i.push(t.readInt16());return i}(),this.widths=function(){var t,e,n,r;for(r=[],t=0,e=(n=this.metrics).length;t<e;t++)i=n[t],r.push(i.advance);return r}.call(this),n=this.widths[this.widths.length-1],s=[],e=a=0;0<=r?a<r:a>r;e=0<=r?++a:--a)s.push(this.widths.push(n));return s},t.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},t}(),Ui=[].slice,zi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="glyf",t.prototype.parse=function(){return this.cache={}},t.prototype.glyphFor=function(t){var e,n,r,i,a,o,s,l,h,c;return t in this.cache?this.cache[t]:(i=this.file.loca,e=this.file.contents,n=i.indexOf(t),0===(r=i.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+n,a=(o=new ki(e.read(r))).readShort(),l=o.readShort(),c=o.readShort(),s=o.readShort(),h=o.readShort(),this.cache[t]=-1===a?new Wi(o,l,c,s,h):new Hi(o,a,l,c,s,h),this.cache[t]))},t.prototype.encode=function(t,e,n){var r,i,a,o,s;for(a=[],i=[],o=0,s=e.length;o<s;o++)r=t[e[o]],i.push(a.length),r&&(a=a.concat(r.encode(n)));return i.push(a.length),{table:a,offsets:i}},t}(),Hi=function(){function t(t,e,n,r,i,a){this.raw=t,this.numberOfContours=e,this.xMin=n,this.yMin=r,this.xMax=i,this.yMax=a,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),Wi=function(){function t(t,e,n,r,i){var a,o;for(this.raw=t,this.xMin=e,this.yMin=n,this.xMax=r,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],a=this.raw;o=a.readShort(),this.glyphOffsets.push(a.pos),this.glyphIDs.push(a.readUInt16()),32&o;)a.pos+=1&o?4:2,128&o?a.pos+=8:64&o?a.pos+=4:8&o&&(a.pos+=2)}return t.prototype.encode=function(){var t,e,n;for(e=new ki(Ui.call(this.raw.data)),t=0,n=this.glyphIDs.length;t<n;++t)e.pos=this.glyphOffsets[t];return e.data},t}(),Gi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return Ci(t,Si),t.prototype.tag="loca",t.prototype.parse=function(t){var e,n;return t.pos=this.offset,e=this.file.head.indexToLocFormat,this.offsets=0===e?function(){var e,r;for(r=[],n=0,e=this.length;n<e;n+=2)r.push(2*t.readUInt16());return r}.call(this):function(){var e,r;for(r=[],n=0,e=this.length;n<e;n+=4)r.push(t.readUInt32());return r}.call(this)},t.prototype.indexOf=function(t){return this.offsets[t]},t.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},t.prototype.encode=function(t,e){for(var n=new Uint32Array(this.offsets.length),r=0,i=0,a=0;a<n.length;++a)if(n[a]=r,i<e.length&&e[i]==a){++i,n[a]=r;var o=this.offsets[a],s=this.offsets[a+1]-o;s>0&&(r+=s)}for(var l=new Array(4*n.length),h=0;h<n.length;++h)l[4*h+3]=255&n[h],l[4*h+2]=(65280&n[h])>>8,l[4*h+1]=(16711680&n[h])>>16,l[4*h]=(**********&n[h])>>24;return l},t}(),Vi=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var t,e,n,r,i;for(e in r=this.font.cmap.tables[0].codeMap,t={},i=this.subset)n=i[e],t[e]=r[n];return t},t.prototype.glyphsFor=function(t){var e,n,r,i,a,o,s;for(r={},a=0,o=t.length;a<o;a++)r[i=t[a]]=this.font.glyf.glyphFor(i);for(i in e=[],r)(null!=(n=r[i])?n.compound:void 0)&&e.push.apply(e,n.glyphIDs);if(e.length>0)for(i in s=this.glyphsFor(e))n=s[i],r[i]=n;return r},t.prototype.encode=function(t,e){var n,r,i,a,o,s,l,h,c,u,f,d,p,g,m;for(r in n=Oi.encode(this.generateCmap(),"unicode"),a=this.glyphsFor(t),f={0:0},m=n.charMap)f[(s=m[r]).old]=s.new;for(d in u=n.maxGlyphID,a)d in f||(f[d]=u++);return h=function(t){var e,n;for(e in n={},t)n[t[e]]=e;return n}(f),c=Object.keys(h).sort(function(t,e){return t-e}),p=function(){var t,e,n;for(n=[],t=0,e=c.length;t<e;t++)o=c[t],n.push(h[o]);return n}(),i=this.font.glyf.encode(a,p,f),l=this.font.loca.encode(i.offsets,p),g={cmap:this.font.cmap.raw(),glyf:i.table,loca:l,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(g["OS/2"]=this.font.os2.raw()),this.font.directory.encode(g)},t}();function Ki(t,e,n,r,i){r=r||{};var a=i.internal.scaleFactor,o=i.internal.getFontSize()/a,s=o*(i.getLineHeightFactor?i.getLineHeightFactor():1.15),l="",h=1;if("middle"!==r.valign&&"bottom"!==r.valign&&"center"!==r.halign&&"right"!==r.halign||(h=(l="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1),n+=o*(2-1.15),"middle"===r.valign?n-=h/2*s:"bottom"===r.valign&&(n-=h*s),"center"===r.halign||"right"===r.halign){var c=o;if("center"===r.halign&&(c*=.5),l&&h>=1){for(var u=0;u<l.length;u++)i.text(l[u],e-i.getStringUnitWidth(l[u])*c,n),n+=s;return i}e-=i.getStringUnitWidth(t)*c}return"justify"===r.halign?i.text(t,e,n,{maxWidth:r.maxWidth||100,align:"justify"}):i.text(t,e,n),i}Zn.API.PDFObject=function(){var t;function e(){}return t=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},e.convert=function(n){var r,i,a,o;if(Array.isArray(n))return"["+function(){var t,i,a;for(a=[],t=0,i=n.length;t<i;t++)r=n[t],a.push(e.convert(r));return a}().join(" ")+"]";if("string"==typeof n)return"/"+n;if(null!=n?n.isString:void 0)return"("+n+")";if(n instanceof Date)return"(D:"+t(n.getUTCFullYear(),4)+t(n.getUTCMonth(),2)+t(n.getUTCDate(),2)+t(n.getUTCHours(),2)+t(n.getUTCMinutes(),2)+t(n.getUTCSeconds(),2)+"Z)";if("[object Object]"==={}.toString.call(n)){for(i in a=["<<"],n)o=n[i],a.push("/"+i+" "+e.convert(o));return a.push(">>"),a.join("\n")}return""+n},e}();var Yi={},Ji=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:Yi=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){var r,i,a;void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var o=this.jsPDFDocument.internal.getFont(),s=o.fontStyle,l=o.fontName;if(e.font&&(l=e.font),e.fontStyle){s=e.fontStyle;var h=this.getFontList()[l];h&&-1===h.indexOf(s)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(h[0]),s=h[0])}if(this.jsPDFDocument.setFont(l,s),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var c=t.unifyColor(e.fillColor);c&&(r=this.jsPDFDocument).setFillColor.apply(r,c),(c=t.unifyColor(e.textColor))&&(i=this.jsPDFDocument).setTextColor.apply(i,c),(c=t.unifyColor(e.lineColor))&&(a=this.jsPDFDocument).setDrawColor.apply(a,c),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,r,i){return this.jsPDFDocument.rect(t,e,n,r,i)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return Yi||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}(),Xi=function(t,e){return Xi=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},Xi(t,e)};function $i(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}Xi(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}"function"==typeof SuppressedError&&SuppressedError;var Zi=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return $i(e,t),e}(Array);function Qi(t,e,n){n.applyStyles(e,!0);var r=(Array.isArray(t)?t:[t]).map(function(t){return n.getTextWidth(t)}).reduce(function(t,e){return Math.max(t,e)},0);return r}function ta(t,e,n,r){var i=e.settings.tableLineWidth,a=e.settings.tableLineColor;t.applyStyles({lineWidth:i,lineColor:a});var o=ea(i,!1);o&&t.rect(n.x,n.y,e.getWidth(t.pageSize().width),r.y-n.y,o)}function ea(t,e){var n=t>0,r=e||0===e;return n&&r?"DF":n?"S":r?"F":null}function na(t,e){var n,r,i,a;if(t=t||e,Array.isArray(t)){if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!==(n=t.left)&&void 0!==n?n:e,top:null!==(r=t.top)&&void 0!==r?r:e,right:null!==(i=t.right)&&void 0!==i?i:e,bottom:null!==(a=t.bottom)&&void 0!==a?a:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}function ra(t,e){var n=na(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}function ia(t,e,n,r,i){var a={},o=96/72,s=aa(e,function(t){return i.getComputedStyle(t).backgroundColor});null!=s&&(a.fillColor=s);var l=aa(e,function(t){return i.getComputedStyle(t).color});null!=l&&(a.textColor=l);var h=function(t,e){var n=[t.paddingTop,t.paddingRight,t.paddingBottom,t.paddingLeft],r=96/(72/e),i=(parseInt(t.lineHeight)-parseInt(t.fontSize))/e/2,a=n.map(function(t){return parseInt(t||"0")/r}),o=na(a,0);return i>o.top&&(o.top=i),i>o.bottom&&(o.bottom=i),o}(r,n);h&&(a.cellPadding=h);var c="borderTopColor",u=o*n,f=r.borderTopWidth;if(r.borderBottomWidth===f&&r.borderRightWidth===f&&r.borderLeftWidth===f){var d=(parseFloat(f)||0)/u;d&&(a.lineWidth=d)}else a.lineWidth={top:(parseFloat(r.borderTopWidth)||0)/u,right:(parseFloat(r.borderRightWidth)||0)/u,bottom:(parseFloat(r.borderBottomWidth)||0)/u,left:(parseFloat(r.borderLeftWidth)||0)/u},a.lineWidth.top||(a.lineWidth.right?c="borderRightColor":a.lineWidth.bottom?c="borderBottomColor":a.lineWidth.left&&(c="borderLeftColor"));var p=aa(e,function(t){return i.getComputedStyle(t)[c]});null!=p&&(a.lineColor=p);var g=["left","right","center","justify"];-1!==g.indexOf(r.textAlign)&&(a.halign=r.textAlign),-1!==(g=["middle","bottom","top"]).indexOf(r.verticalAlign)&&(a.valign=r.verticalAlign);var m=parseInt(r.fontSize||"");isNaN(m)||(a.fontSize=m/o);var b=function(t){var e="";return("bold"===t.fontWeight||"bolder"===t.fontWeight||parseInt(t.fontWeight)>=700)&&(e="bold"),"italic"!==t.fontStyle&&"oblique"!==t.fontStyle||(e+="italic"),e}(r);b&&(a.fontStyle=b);var v=(r.fontFamily||"").toLowerCase();return-1!==t.indexOf(v)&&(a.font=v),a}function aa(t,e){var n=oa(t,e);if(!n)return null;var r=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!r||!Array.isArray(r))return null;var i=[parseInt(r[1]),parseInt(r[2]),parseInt(r[3])];return 0===parseInt(r[4])||isNaN(i[0])||isNaN(i[1])||isNaN(i[2])?null:i}function oa(t,e){var n=e(t);return"rgba(0, 0, 0, 0)"===n||"transparent"===n||"initial"===n||"inherit"===n?null==t.parentElement?null:oa(t.parentElement,e):n}function sa(t,e,n,r,i){var a,o,s;void 0===r&&(r=!1),void 0===i&&(i=!1),s="string"==typeof e?n.document.querySelector(e):e;var l=Object.keys(t.getFontList()),h=t.scaleFactor(),c=[],u=[],f=[];if(!s)return console.error("Html table could not be found with input: ",e),{head:c,body:u,foot:f};for(var d=0;d<s.rows.length;d++){var p=s.rows[d],g=null===(o=null===(a=null==p?void 0:p.parentElement)||void 0===a?void 0:a.tagName)||void 0===o?void 0:o.toLowerCase(),m=la(l,h,n,p,r,i);m&&("thead"===g?c.push(m):"tfoot"===g?f.push(m):u.push(m))}return{head:c,body:u,foot:f}}function la(t,e,n,r,i,a){for(var o=new Zi(r),s=0;s<r.cells.length;s++){var l=r.cells[s],h=n.getComputedStyle(l);if(i||"none"!==h.display){var c=void 0;a&&(c=ia(t,l,e,h,n)),o.push({rowSpan:l.rowSpan,colSpan:l.colSpan,styles:c,_element:l,content:ha(l)})}}var u=n.getComputedStyle(r);if(o.length>0&&(i||"none"!==u.display))return o}function ha(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map(function(t){return t.trim()}).join("\n"),e.innerText||e.textContent||""}function ca(t,e,n,r,i){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(t),o=1;o<arguments.length;o++){var s=arguments[o];if(null!=s)for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(a[l]=s[l])}return a}function ua(t,e){var n=new Ji(t),r=n.getDocumentOptions(),i=n.getGlobalOptions();!function(t,e,n){for(var r=0,i=[t,e,n];r<i.length;r++){var a=i[r];a&&"object"!=typeof a&&console.error("The options parameter should be of type object, is: "+typeof a),a.startY&&"number"!=typeof a.startY&&(console.error("Invalid value for startY option",a.startY),delete a.startY)}}(i,r,e);var a,o=ca({},i,r,e);"undefined"!=typeof window&&(a=window);var s=function(t,e,n){for(var r={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},i=function(i){if("columnStyles"===i){var a=t[i],o=e[i],s=n[i];r.columnStyles=ca({},a,o,s)}else{var l=[t,e,n].map(function(t){return t[i]||{}});r[i]=ca({},l[0],l[1],l[2])}},a=0,o=Object.keys(r);a<o.length;a++)i(o[a]);return r}(i,r,e),l=function(t,e,n){for(var r={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},i=0,a=[t,e,n];i<a.length;i++){var o=a[i];o.didParseCell&&r.didParseCell.push(o.didParseCell),o.willDrawCell&&r.willDrawCell.push(o.willDrawCell),o.didDrawCell&&r.didDrawCell.push(o.didDrawCell),o.willDrawPage&&r.willDrawPage.push(o.willDrawPage),o.didDrawPage&&r.didDrawPage.push(o.didDrawPage)}return r}(i,r,e),h=function(t,e){var n,r,i,a,o,s,l,h,c,u,f,d,p,g,m=na(e.margin,40/t.scaleFactor()),b=null!==(n=function(t,e){var n=t.getLastAutoTable(),r=t.scaleFactor(),i=t.pageNumber(),a=!1;return n&&n.startPageNumber&&(a=n.startPageNumber+n.pageNumber-1===i),"number"==typeof e?e:null!=e&&!1!==e||!a||null==(null==n?void 0:n.finalY)?null:n.finalY+20/r}(t,e.startY))&&void 0!==n?n:m.top;p=!0===e.showFoot?"everyPage":!1===e.showFoot?"never":null!==(r=e.showFoot)&&void 0!==r?r:"everyPage",g=!0===e.showHead?"everyPage":!1===e.showHead?"never":null!==(i=e.showHead)&&void 0!==i?i:"everyPage";var v=null!==(a=e.useCss)&&void 0!==a&&a,y=e.theme||(v?"plain":"striped"),w=!!e.horizontalPageBreak,x=null!==(o=e.horizontalPageBreakRepeat)&&void 0!==o?o:null;return{includeHiddenHtml:null!==(s=e.includeHiddenHtml)&&void 0!==s&&s,useCss:v,theme:y,startY:b,margin:m,pageBreak:null!==(l=e.pageBreak)&&void 0!==l?l:"auto",rowPageBreak:null!==(h=e.rowPageBreak)&&void 0!==h?h:"auto",tableWidth:null!==(c=e.tableWidth)&&void 0!==c?c:"auto",showHead:g,showFoot:p,tableLineWidth:null!==(u=e.tableLineWidth)&&void 0!==u?u:0,tableLineColor:null!==(f=e.tableLineColor)&&void 0!==f?f:200,horizontalPageBreak:w,horizontalPageBreakRepeat:x,horizontalPageBreakBehaviour:null!==(d=e.horizontalPageBreakBehaviour)&&void 0!==d?d:"afterAllRows"}}(n,o),c=function(t,e,n){var r=e.head||[],i=e.body||[],a=e.foot||[];if(e.html){var o=e.includeHiddenHtml;if(n){var s=sa(t,e.html,n,o,e.useCss)||{};r=s.head||r,i=s.body||r,a=s.foot||r}else console.error("Cannot parse html in non browser environment")}var l=e.columns||function(t,e,n){var r=t[0]||e[0]||n[0]||[],i=[];return Object.keys(r).filter(function(t){return"_element"!==t}).forEach(function(t){var e,n=1;"object"!=typeof(e=Array.isArray(r)?r[parseInt(t)]:r[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var a=0;a<n;a++){var o={dataKey:Array.isArray(r)?i.length:t+(a>0?"_".concat(a):"")};i.push(o)}}),i}(r,i,a);return{columns:l,head:r,body:i,foot:a}}(n,o,a);return{id:e.tableId,content:c,hooks:l,styles:s,settings:h}}var fa,da=function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()},pa=function(t){function e(e,n,r,i,a,o){var s=t.call(this,e,n,o)||this;return s.cell=r,s.row=i,s.column=a,s.section=i.section,s}return $i(e,t),e}(da),ga=function(){function t(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,r,i,a){for(var o=0,s=e;o<s.length;o++){var l=!1===(0,s[o])(new pa(t,this,n,r,i,a));if(n.text=Array.isArray(n.text)?n.text:[n.text],l)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,r=this.hooks.didDrawPage;n<r.length;n++)(0,r[n])(new da(t,this,e))},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,r=this.hooks.willDrawPage;n<r.length;n++)(0,r[n])(new da(t,this,e))},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce(function(t,e){return t+e.wrappedWidth},0);var e=this.settings.margin;return t-e.left-e.right},t}(),ma=function(){function t(t,e,n,r,i){void 0===i&&(i=!1),this.height=0,this.raw=t,t instanceof Zi&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=r,this.spansMultiplePages=i}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(t,n){var r;return Math.max(t,(null===(r=e.cells[n.index])||void 0===r?void 0:r.height)||0)},0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1}).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce(function(t,r){var i=n.cells[r.index];if(!i)return 0;var a=e.getLineHeight(i.styles.fontSize),o=i.padding("vertical")+a;return o>t?o:t},0)},t}(),ba=function(){function t(t,e,n){var r;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var i=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,i=null!==(r=t.content)&&void 0!==r?r:t,t._element&&(this.raw=t._element));var a=null!=i?""+i:"";this.text=a.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){var t,e;if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var r=this.width-this.padding("horizontal");e=this.x+r/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){void 0===e&&(e=1.15);var n=(Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical");return Math.max(n,this.styles.minCellHeight)},t.prototype.padding=function(t){var e=na(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}(),va=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,r=t.allRows();n<r.length;n++){var i=r[n].cells[this.index];i&&"number"==typeof i.styles.cellWidth&&(e=Math.max(e,i.styles.cellWidth))}return e},t}();function ya(t,e){!function(t,e){var n=t.scaleFactor(),r=e.settings.horizontalPageBreak,i=ra(t,e);e.allRows().forEach(function(a){for(var o=0,s=e.columns;o<s.length;o++){var l=s[o],h=a.cells[l.index];if(h){var c=e.hooks.didParseCell;e.callCellHooks(t,c,h,a,l,null);var u=h.padding("horizontal");h.contentWidth=Qi(h.text,h.styles,t)+u;var f=Qi(h.text.join(" ").split(/[^\S\u00A0]+/),h.styles,t);if(h.minReadableWidth=f+h.padding("horizontal"),"number"==typeof h.styles.cellWidth)h.minWidth=h.styles.cellWidth,h.wrappedWidth=h.styles.cellWidth;else if("wrap"===h.styles.cellWidth||!0===r)h.contentWidth>i?(h.minWidth=i,h.wrappedWidth=i):(h.minWidth=h.contentWidth,h.wrappedWidth=h.contentWidth);else{var d=10/n;h.minWidth=h.styles.minCellWidth||d,h.wrappedWidth=h.contentWidth,h.minWidth>h.wrappedWidth&&(h.wrappedWidth=h.minWidth)}}}}),e.allRows().forEach(function(t){for(var n=0,r=e.columns;n<r.length;n++){var i=r[n],a=t.cells[i.index];if(a&&1===a.colSpan)i.wrappedWidth=Math.max(i.wrappedWidth,a.wrappedWidth),i.minWidth=Math.max(i.minWidth,a.minWidth),i.minReadableWidth=Math.max(i.minReadableWidth,a.minReadableWidth);else{var o=e.styles.columnStyles[i.dataKey]||e.styles.columnStyles[i.index]||{},s=o.cellWidth||o.minCellWidth;s&&"number"==typeof s&&(i.minWidth=s,i.wrappedWidth=s)}a&&(a.colSpan>1&&!i.minWidth&&(i.minWidth=a.minWidth),a.colSpan>1&&!i.wrappedWidth&&(i.wrappedWidth=a.minWidth))}})}(t,e);var n=[],r=0;e.columns.forEach(function(t){var i=t.getMaxCustomCellWidth(e);i?t.width=i:(t.width=t.wrappedWidth,n.push(t)),r+=t.width});var i=e.getWidth(t.pageSize().width)-r;i&&(i=wa(n,i,function(t){return Math.max(t.minReadableWidth,t.minWidth)})),i&&(i=wa(n,i,function(t){return t.minWidth})),i=Math.abs(i),!e.settings.horizontalPageBreak&&i>.1/t.scaleFactor()&&(i=i<1?i:Math.round(i),console.warn("Of the table content, ".concat(i," units width could not fit page"))),function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var r=e[n],i=null,a=0,o=0,s=0;s<t.columns.length;s++){var l=t.columns[s];if((o-=1)>1&&t.columns[s+1])a+=l.width,delete r.cells[l.index];else if(i){var h=i;delete r.cells[l.index],i=null,h.width=l.width+a}else{if(!(h=r.cells[l.index]))continue;if(o=h.colSpan,a=0,h.colSpan>1){i=h,a+=l.width;continue}h.width=l.width+a}}}(e),function(t,e){for(var n={count:0,height:0},r=0,i=t.allRows();r<i.length;r++){for(var a=i[r],o=0,s=t.columns;o<s.length;o++){var l=s[o],h=a.cells[l.index];if(h){e.applyStyles(h.styles,!0);var c=h.width-h.padding("horizontal");if("linebreak"===h.styles.overflow)h.text=e.splitTextToSize(h.text,c+1/e.scaleFactor(),{fontSize:h.styles.fontSize});else if("ellipsize"===h.styles.overflow)h.text=xa(h.text,c,h.styles,e,"...");else if("hidden"===h.styles.overflow)h.text=xa(h.text,c,h.styles,e,"");else if("function"==typeof h.styles.overflow){var u=h.styles.overflow(h.text,c);h.text="string"==typeof u?[u]:u}h.contentHeight=h.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var f=h.contentHeight/h.rowSpan;h.rowSpan>1&&n.count*n.height<f*h.rowSpan?n={height:f,count:h.rowSpan}:n&&n.count>0&&n.height>f&&(f=n.height),f>a.height&&(a.height=f)}}n.count--}}(e,t),function(t){for(var e={},n=1,r=t.allRows(),i=0;i<r.length;i++)for(var a=r[i],o=0,s=t.columns;o<s.length;o++){var l=s[o],h=e[l.index];if(n>1)n--,delete a.cells[l.index];else if(h)h.cell.height+=a.height,n=h.cell.colSpan,delete a.cells[l.index],h.left--,h.left<=1&&delete e[l.index];else{var c=a.cells[l.index];if(!c)continue;if(c.height=a.height,c.rowSpan>1){var u=r.length-i,f=c.rowSpan>u?u:c.rowSpan;e[l.index]={cell:c,left:f,row:a}}}}}(e)}function wa(t,e,n){for(var r=e,i=t.reduce(function(t,e){return t+e.wrappedWidth},0),a=0;a<t.length;a++){var o=t[a],s=r*(o.wrappedWidth/i),l=o.width+s,h=n(o),c=l<h?h:l;e-=c-o.width,o.width=c}if(e=Math.round(1e10*e)/1e10){var u=t.filter(function(t){return!(e<0)||t.width>n(t)});u.length&&(e=wa(u,e,n))}return e}function xa(t,e,n,r,i){return t.map(function(t){return function(t,e,n,r,i){var a=1e4*r.scaleFactor();if((e=Math.ceil(e*a)/a)>=Qi(t,n,r))return t;for(;e<Qi(t+i,n,r)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+i}(t,e,n,r,i)})}function _a(t,e){var n=new Ji(t),r=function(t,e){var n,r=t.content,i=function(t){return t.map(function(t,e){var n,r;return r="object"==typeof t&&null!==(n=t.dataKey)&&void 0!==n?n:e,new va(r,t,e)})}(r.columns);(0===r.head.length&&(n=La(i,"head"))&&r.head.push(n),0===r.foot.length)&&((n=La(i,"foot"))&&r.foot.push(n));var a=t.settings.theme,o=t.styles;return{columns:i,head:Aa("head",r.head,i,o,a,e),body:Aa("body",r.body,i,o,a,e),foot:Aa("foot",r.foot,i,o,a,e)}}(e,n.scaleFactor()),i=new ga(e,r);return ya(n,i),n.applyStyles(n.userStyles),i}function Aa(t,e,n,r,i,a){var o={};return e.map(function(e,s){for(var l=0,h={},c=0,u=0,f=0,d=n;f<d.length;f++){var p=d[f];if(null==o[p.index]||0===o[p.index].left)if(0===u){var g=void 0,m={};"object"!=typeof(g=Array.isArray(e)?e[p.index-c-l]:e[p.dataKey])||Array.isArray(g)||(m=(null==g?void 0:g.styles)||{});var b=Na(t,p,s,i,r,a,m),v=new ba(g,b,t);h[p.dataKey]=v,h[p.index]=v,u=v.colSpan-1,o[p.index]={left:v.rowSpan-1,times:u}}else u--,c++;else o[p.index].left--,u=o[p.index].times,l++}return new ma(e,s,t,h)})}function La(t,e){var n={};return t.forEach(function(t){if(null!=t.raw){var r=function(t,e){if("head"===t){if("object"==typeof e)return e.header||null;if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=r&&(n[t.dataKey]=r)}}),Object.keys(n).length>0?n:null}function Na(t,e,n,r,i,a,o){var s,l={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}}[r];"head"===t?s=i.headStyles:"body"===t?s=i.bodyStyles:"foot"===t&&(s=i.footStyles);var h=ca({},l.table,l[t],i.styles,s),c=i.columnStyles[e.dataKey]||i.columnStyles[e.index]||{},u="body"===t?c:{},f="body"===t&&n%2==0?ca({},l.alternateRow,i.alternateRowStyles):{},d=function(t){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/t,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}(a),p=ca({},d,h,f,u);return ca(p,o)}function Sa(t,e,n){var r;void 0===n&&(n={});var i=ra(t,e),a=new Map,o=[],s=[],l=[];Array.isArray(e.settings.horizontalPageBreakRepeat)?l=e.settings.horizontalPageBreakRepeat:"string"!=typeof e.settings.horizontalPageBreakRepeat&&"number"!=typeof e.settings.horizontalPageBreakRepeat||(l=[e.settings.horizontalPageBreakRepeat]),l.forEach(function(t){var n=e.columns.find(function(e){return e.dataKey===t||e.index===t});n&&!a.has(n.index)&&(a.set(n.index,!0),o.push(n.index),s.push(e.columns[n.index]),i-=n.wrappedWidth)});for(var h=!0,c=null!==(r=null==n?void 0:n.start)&&void 0!==r?r:0;c<e.columns.length;)if(a.has(c))c++;else{var u=e.columns[c].wrappedWidth;if(!(h||i>=u))break;h=!1,o.push(c),s.push(e.columns[c]),i-=u,c++}return{colIndexes:o,columns:s,lastIndex:c-1}}function ka(t,e){var n=e.settings,r=n.startY,i=n.margin,a={x:i.left,y:r},o=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),s=r+i.bottom+o;"avoid"===n.pageBreak&&(s+=e.body.reduce(function(t,e){return t+e.height},0));var l=new Ji(t);("always"===n.pageBreak||null!=n.startY&&s>l.pageSize().height)&&(Ma(l),a.y=i.top),e.callWillDrawPageHooks(l,a);var h=ca({},a);e.startPageNumber=l.pageNumber(),n.horizontalPageBreak?function(t,e,n,r){var i=function(t,e){for(var n=[],r=0;r<e.columns.length;r++){var i=Sa(t,e,{start:r});i.columns.length&&(n.push(i),r=i.lastIndex)}return n}(t,e),a=e.settings;if("afterAllRows"===a.horizontalPageBreakBehaviour)i.forEach(function(i,a){t.applyStyles(t.userStyles),a>0?Ba(t,e,n,r,i.columns,!0):Pa(t,e,r,i.columns),function(t,e,n,r,i){t.applyStyles(t.userStyles),e.body.forEach(function(a,o){var s=o===e.body.length-1;ja(t,e,a,s,n,r,i)})}(t,e,n,r,i.columns),Ca(t,e,r,i.columns)});else for(var o=-1,s=i[0],l=function(){var a=o;if(s){t.applyStyles(t.userStyles);var l=s.columns;o>=0?Ba(t,e,n,r,l,!0):Pa(t,e,r,l),a=Fa(t,e,o+1,r,l),Ca(t,e,r,l)}var h=a-o;i.slice(1).forEach(function(i){t.applyStyles(t.userStyles),Ba(t,e,n,r,i.columns,!0),Fa(t,e,o+1,r,i.columns,h),Ca(t,e,r,i.columns)}),o=a};o<e.body.length-1;)l()}(l,e,h,a):(l.applyStyles(l.userStyles),"firstPage"!==n.showHead&&"everyPage"!==n.showHead||e.head.forEach(function(t){return Oa(l,e,t,a,e.columns)}),l.applyStyles(l.userStyles),e.body.forEach(function(t,n){var r=n===e.body.length-1;ja(l,e,t,r,h,a,e.columns)}),l.applyStyles(l.userStyles),"lastPage"!==n.showFoot&&"everyPage"!==n.showFoot||e.foot.forEach(function(t){return Oa(l,e,t,a,e.columns)})),ta(l,e,h,a),e.callEndPageHooks(l,a),e.finalY=a.y,t.lastAutoTable=e,l.applyStyles(l.userStyles)}function Pa(t,e,n,r){var i=e.settings;t.applyStyles(t.userStyles),"firstPage"!==i.showHead&&"everyPage"!==i.showHead||e.head.forEach(function(i){return Oa(t,e,i,n,r)})}function Fa(t,e,n,r,i,a){t.applyStyles(t.userStyles),a=null!=a?a:e.body.length;var o=Math.min(n+a,e.body.length),s=-1;return e.body.slice(n,o).forEach(function(a,o){var l=n+o===e.body.length-1,h=Ea(t,e,l,r);a.canEntireRowFit(h,i)&&(Oa(t,e,a,r,i),s=n+o)}),s}function Ca(t,e,n,r){var i=e.settings;t.applyStyles(t.userStyles),"lastPage"!==i.showFoot&&"everyPage"!==i.showFoot||e.foot.forEach(function(i){return Oa(t,e,i,n,r)})}function Ia(t,e,n){var r=n.getLineHeight(t.styles.fontSize),i=t.padding("vertical"),a=Math.floor((e-i)/r);return Math.max(0,a)}function ja(t,e,n,r,i,a,o){var s=Ea(t,e,r,a);if(n.canEntireRowFit(s,o))Oa(t,e,n,a,o);else if(function(t,e,n,r){var i=t.pageSize().height,a=r.settings.margin,o=i-(a.top+a.bottom);"body"===e.section&&(o-=r.getHeadHeight(r.columns)+r.getFootHeight(r.columns));var s=e.getMinimumRowHeight(r.columns,t),l=s<n;if(s>o)return console.error("Will not be able to print row ".concat(e.index," correctly since it's minimum height is larger than page height")),!0;if(!l)return!1;var h=e.hasRowSpan(r.columns);return e.getMaxCellHeight(r.columns)>o?(h&&console.error("The content of row ".concat(e.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!h&&"avoid"!==r.settings.rowPageBreak}(t,n,s,e)){var l=function(t,e,n,r){var i={};t.spansMultiplePages=!0,t.height=0;for(var a=0,o=0,s=n.columns;o<s.length;o++){var l=s[o];if(m=t.cells[l.index]){Array.isArray(m.text)||(m.text=[m.text]),(g=ca(g=new ba(m.raw,m.styles,m.section),m)).text=[];var h=Ia(m,e,r);m.text.length>h&&(g.text=m.text.splice(h,m.text.length));var c=r.scaleFactor(),u=r.getLineHeightFactor();m.contentHeight=m.getContentHeight(c,u),m.contentHeight>=e&&(m.contentHeight=e,g.styles.minCellHeight-=e),m.contentHeight>t.height&&(t.height=m.contentHeight),g.contentHeight=g.getContentHeight(c,u),g.contentHeight>a&&(a=g.contentHeight),i[l.index]=g}}var f=new ma(t.raw,-1,t.section,i,!0);f.height=a;for(var d=0,p=n.columns;d<p.length;d++){var g,m;l=p[d],(g=f.cells[l.index])&&(g.height=f.height),(m=t.cells[l.index])&&(m.height=t.height)}return f}(n,s,e,t);Oa(t,e,n,a,o),Ba(t,e,i,a,o),ja(t,e,l,r,i,a,o)}else Ba(t,e,i,a,o),ja(t,e,n,r,i,a,o)}function Oa(t,e,n,r,i){r.x=e.settings.margin.left;for(var a=0,o=i;a<o.length;a++){var s=o[a],l=n.cells[s.index];if(l)if(t.applyStyles(l.styles),l.x=r.x,l.y=r.y,!1!==e.callCellHooks(t,e.hooks.willDrawCell,l,n,s,r)){Da(t,l,r);var h=l.getTextPos();Ki(l.text,h.x,h.y,{halign:l.styles.halign,valign:l.styles.valign,maxWidth:Math.ceil(l.width-l.padding("left")-l.padding("right"))},t.getDocument()),e.callCellHooks(t,e.hooks.didDrawCell,l,n,s,r),r.x+=s.width}else r.x+=s.width;else r.x+=s.width}r.y+=n.height}function Da(t,e,n){var r=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof r.lineWidth){var i=ea(r.lineWidth,r.fillColor);i&&t.rect(e.x,n.y,e.width,e.height,i)}else"object"==typeof r.lineWidth&&(r.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,r){var i,a,o,s;function l(e,n,r,i,a){t.getDocument().setLineWidth(e),t.getDocument().line(n,r,i,a,"S")}r.top&&(i=n.x,a=n.y,o=n.x+e.width,s=n.y,r.right&&(o+=.5*r.right),r.left&&(i-=.5*r.left),l(r.top,i,a,o,s)),r.bottom&&(i=n.x,a=n.y+e.height,o=n.x+e.width,s=n.y+e.height,r.right&&(o+=.5*r.right),r.left&&(i-=.5*r.left),l(r.bottom,i,a,o,s)),r.left&&(i=n.x,a=n.y,o=n.x,s=n.y+e.height,r.top&&(a-=.5*r.top),r.bottom&&(s+=.5*r.bottom),l(r.left,i,a,o,s)),r.right&&(i=n.x+e.width,a=n.y,o=n.x+e.width,s=n.y+e.height,r.top&&(a-=.5*r.top),r.bottom&&(s+=.5*r.bottom),l(r.right,i,a,o,s))}(t,e,n,r.lineWidth))}function Ea(t,e,n,r){var i=e.settings.margin.bottom,a=e.settings.showFoot;return("everyPage"===a||"lastPage"===a&&n)&&(i+=e.getFootHeight(e.columns)),t.pageSize().height-r.y-i}function Ba(t,e,n,r,i,a){void 0===i&&(i=[]),void 0===a&&(a=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||a||e.foot.forEach(function(n){return Oa(t,e,n,r,i)}),e.callEndPageHooks(t,r);var o=e.settings.margin;ta(t,e,n,r),Ma(t),e.pageNumber++,r.x=o.left,r.y=o.top,n.y=o.top,e.callWillDrawPageHooks(t,r),"everyPage"===e.settings.showHead&&(e.head.forEach(function(n){return Oa(t,e,n,r,i)}),t.applyStyles(t.userStyles))}function Ma(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}try{if("undefined"!=typeof window&&window){var Ta=window,Ra=Ta.jsPDF||(null===(fa=Ta.jspdf)||void 0===fa?void 0:fa.jsPDF);Ra&&function(t){t.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return ka(this,_a(this,ua(this,t[0]))),this},t.API.lastAutoTable=!1,t.API.autoTableText=function(t,e,n,r){Ki(t,e,n,r,this)},t.API.autoTableSetDefaults=function(t){return Ji.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){Ji.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){var n;if(void 0===e&&(e=!1),"undefined"==typeof window)return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var r=sa(new Ji(this),t,window,e,!1),i=r.head,a=r.body,o=(null===(n=i[0])||void 0===n?void 0:n.map(function(t){return t.content}))||[];return{columns:o,rows:a,data:a}}}(Ra)}}catch(jo){console.error("Could not apply autoTable plugin",jo)}const qa={class:"filter-item"},Ua=["value"],za=["value"],Ha=r({name:"FilterSection",props:{filters:{type:Object,required:!0},uniqueDesa:{type:Array,required:!0},uniqueKelompok:{type:Array,required:!0}},emits:["filter-change"],data(){return{localFilters:{...this.filters}}},watch:{filters:{handler(t){this.localFilters={...t}},deep:!0}},methods:{emitFilterChange(){this.$emit("filter-change",{...this.localFilters})}}},[["render",function(t,e,n,r,d,p){return u(),i("section",null,[a("div",qa,[a("div",null,[e[7]||(e[7]=a("label",{for:"desaFilter"},"Filter Desa:",-1)),o(a("select",{"onUpdate:modelValue":e[0]||(e[0]=t=>d.localFilters.sambung_desa=t),onChange:e[1]||(e[1]=(...t)=>p.emitFilterChange&&p.emitFilterChange(...t)),id:"desaFilter"},[e[6]||(e[6]=a("option",{value:""},"Semua",-1)),(u(!0),i(l,null,h(n.uniqueDesa,t=>(u(),i("option",{key:t,value:t},f(t),9,Ua))),128))],544),[[s,d.localFilters.sambung_desa]])]),a("div",null,[e[9]||(e[9]=a("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),o(a("select",{"onUpdate:modelValue":e[2]||(e[2]=t=>d.localFilters.sambung_kelompok=t),onChange:e[3]||(e[3]=(...t)=>p.emitFilterChange&&p.emitFilterChange(...t)),id:"kelompokFilter"},[e[8]||(e[8]=a("option",{value:""},"Semua",-1)),(u(!0),i(l,null,h(n.uniqueKelompok,t=>(u(),i("option",{key:t,value:t},f(t),9,za))),128))],544),[[s,d.localFilters.sambung_kelompok]])]),a("div",null,[e[10]||(e[10]=a("label",{for:"namaFilter"},"Filter Nama:",-1)),o(a("input",{type:"text","onUpdate:modelValue":e[4]||(e[4]=t=>d.localFilters.nama=t),onInput:e[5]||(e[5]=(...t)=>p.emitFilterChange&&p.emitFilterChange(...t)),placeholder:"Cari nama...",id:"namaFilter"},null,544),[[c,d.localFilters.nama]])])])])}],["__scopeId","data-v-b60a221c"]]),Wa={class:"statistics-section"},Ga={class:"stats-container"},Va={class:"stat-card"},Ka={class:"stat-number"},Ya={class:"stat-card"},Ja={class:"stat-number"},Xa={class:"stat-card"},$a={class:"stat-number"},Za={class:"stat-card"},Qa={class:"stat-number"},to={class:"stat-card"},eo={class:"stat-number"},no=r({name:"StatisticsSection",props:{stats:{type:Object,required:!0,default:()=>({total_count:0,male_count:0,female_count:0,with_photo_count:0,without_photo_count:0})}}},[["render",function(t,e,n,r,o,s){return u(),i("section",Wa,[a("div",Ga,[a("div",Va,[e[0]||(e[0]=a("h3",null,"Total",-1)),a("p",Ka,f(n.stats.total_count),1)]),a("div",Ya,[e[1]||(e[1]=a("h3",null,"Laki-laki",-1)),a("p",Ja,f(n.stats.male_count),1)]),a("div",Xa,[e[2]||(e[2]=a("h3",null,"Perempuan",-1)),a("p",$a,f(n.stats.female_count),1)]),a("div",Za,[e[3]||(e[3]=a("h3",null,"Dengan Foto",-1)),a("p",Qa,f(n.stats.with_photo_count),1)]),a("div",to,[e[4]||(e[4]=a("h3",null,"Tanpa Foto",-1)),a("p",eo,f(n.stats.without_photo_count),1)])])])}],["__scopeId","data-v-2f07d2d7"]]),ro={class:"table-container"},io={id:"biodataTable"},ao={key:0},oo={key:0},so={key:0},lo={key:0},ho={key:0},co=["onClick"],uo=r({name:"BiodataTable",props:{data:{type:Array,required:!0},sortKey:{type:String,default:""},sortOrder:{type:String,default:"asc"}},emits:["sort","row-click"]},[["render",function(t,e,n,r,o,s){return u(),i("div",ro,[a("table",io,[a("thead",null,[a("tr",null,[e[10]||(e[10]=a("th",null,"No.",-1)),a("th",{onClick:e[0]||(e[0]=e=>t.$emit("sort","nama_lengkap"))},[e[5]||(e[5]=d(" Nama Lengkap ",-1)),"nama_lengkap"===n.sortKey?(u(),i("span",ao,f("asc"===n.sortOrder?"↑":"↓"),1)):p("",!0)]),a("th",{onClick:e[1]||(e[1]=e=>t.$emit("sort","nama_panggilan"))},[e[6]||(e[6]=d(" Nama Panggilan ",-1)),"nama_panggilan"===n.sortKey?(u(),i("span",oo,f("asc"===n.sortOrder?"↑":"↓"),1)):p("",!0)]),a("th",{onClick:e[2]||(e[2]=e=>t.$emit("sort","sambung_desa"))},[e[7]||(e[7]=d(" Desa ",-1)),"sambung_desa"===n.sortKey?(u(),i("span",so,f("asc"===n.sortOrder?"↑":"↓"),1)):p("",!0)]),a("th",{onClick:e[3]||(e[3]=e=>t.$emit("sort","sambung_kelompok"))},[e[8]||(e[8]=d(" Kelompok ",-1)),"sambung_kelompok"===n.sortKey?(u(),i("span",lo,f("asc"===n.sortOrder?"↑":"↓"),1)):p("",!0)]),a("th",{onClick:e[4]||(e[4]=e=>t.$emit("sort","jenis_kelamin"))},[e[9]||(e[9]=d(" Jenis Kelamin ",-1)),"jenis_kelamin"===n.sortKey?(u(),i("span",ho,f("asc"===n.sortOrder?"↑":"↓"),1)):p("",!0)])])]),a("tbody",null,[(u(!0),i(l,null,h(n.data,(e,n)=>(u(),i("tr",{key:n,onClick:n=>t.$emit("row-click",e.biodata_id),class:"clickable-row"},[a("td",null,f(n+1),1),a("td",null,f(e.nama_lengkap),1),a("td",null,f(e.nama_panggilan),1),a("td",null,f(e.sambung_desa),1),a("td",null,f(e.sambung_kelompok),1),a("td",null,f(e.jenis_kelamin),1)],8,co))),128))])])])}],["__scopeId","data-v-78faf7ea"]]),fo={name:"BiodataDetailModal",props:{show:{type:Boolean,default:!1},data:{type:Object,default:null},apiKey:{type:String,required:!0}},emits:["close"],data:()=>({photoUrl:null,loadingPhoto:!1}),watch:{data:{handler(t){t&&t.foto_filename?this.loadPhoto(t.foto_filename):(this.photoUrl=null,this.loadingPhoto=!1)},immediate:!0},show(t){t||this.photoUrl&&(URL.revokeObjectURL(this.photoUrl),this.photoUrl=null)}},methods:{async loadPhoto(t){this.loadingPhoto=!0,this.photoUrl=null;const e=`/api/biodata/generus/foto/${t}`;console.log("🔍 [PHOTO DEBUG] Starting photo load process"),console.log("📁 [PHOTO DEBUG] Filename:",t),console.log("🌐 [PHOTO DEBUG] Full URL:",e),console.log("🔑 [PHOTO DEBUG] API Key (first 10 chars):",this.apiKey?this.apiKey.substring(0,10)+"...":"NOT PROVIDED"),console.log("📋 [PHOTO DEBUG] Request headers:",{Authorization:`ApiKey ${this.apiKey}`});try{console.log("🚀 [PHOTO DEBUG] Making fetch request...");const t=await fetch(e,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(console.log("📡 [PHOTO DEBUG] Response received"),console.log("📊 [PHOTO DEBUG] Response status:",t.status),console.log("📊 [PHOTO DEBUG] Response status text:",t.statusText),console.log("📊 [PHOTO DEBUG] Response headers:",Object.fromEntries(t.headers.entries())),console.log("📊 [PHOTO DEBUG] Response URL:",t.url),console.log("📊 [PHOTO DEBUG] Response type:",t.type),!t.ok){console.error("❌ [PHOTO DEBUG] Response not OK"),console.error("❌ [PHOTO DEBUG] Status:",t.status),console.error("❌ [PHOTO DEBUG] Status text:",t.statusText);try{const e=await t.text();console.error("❌ [PHOTO DEBUG] Error response body:",e)}catch(n){console.error("❌ [PHOTO DEBUG] Could not read error response body:",n)}throw new Error(`HTTP error! status: ${t.status} - ${t.statusText}`)}console.log("✅ [PHOTO DEBUG] Response OK, converting to blob...");const r=await t.blob();console.log("📦 [PHOTO DEBUG] Blob created, size:",r.size,"bytes"),console.log("📦 [PHOTO DEBUG] Blob type:",r.type),this.photoUrl=URL.createObjectURL(r),console.log("🖼️ [PHOTO DEBUG] Photo URL created successfully:",this.photoUrl)}catch(jo){console.error("💥 [PHOTO DEBUG] Error in loadPhoto method:"),console.error("💥 [PHOTO DEBUG] Error type:",jo.constructor.name),console.error("💥 [PHOTO DEBUG] Error message:",jo.message),console.error("💥 [PHOTO DEBUG] Full error object:",jo),console.error("💥 [PHOTO DEBUG] Stack trace:",jo.stack),this.photoUrl=null}finally{this.loadingPhoto=!1,console.log("🏁 [PHOTO DEBUG] Photo loading process completed")}},formatDate:t=>t?new Date(t).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"}):"-",formatHobi:t=>t?"object"==typeof t?Object.values(t).join(", ")||"-":t:"-",handleImageError(t){console.error("Image failed to load"),this.photoUrl=null}}},po={class:"modal-header"},go={class:"modal-body"},mo={key:0,class:"detail-grid"},bo={class:"detail-section"},vo={class:"detail-section"},yo={class:"detail-section"},wo={key:0,class:"detail-section photo-section"},xo=["src","alt"],_o={key:1,class:"loading-photo"},Ao={key:2,class:"no-photo"},Lo={key:1,class:"detail-section"},No={key:1,class:"loading-content"},So={components:{FilterSection:Ha,StatisticsSection:no,BiodataTable:uo,BiodataDetailModal:r(fo,[["render",function(t,e,n,r,o,s){return n.show?(u(),i("div",{key:0,class:"modal-overlay",onClick:e[3]||(e[3]=e=>t.$emit("close"))},[a("div",{class:"modal-content",onClick:e[2]||(e[2]=g(()=>{},["stop"]))},[a("div",po,[e[4]||(e[4]=a("h3",null,"Detail Biodata",-1)),a("button",{class:"close-button",onClick:e[0]||(e[0]=e=>t.$emit("close"))},"×")]),a("div",go,[n.data?(u(),i("div",mo,[a("div",bo,[e[14]||(e[14]=a("h4",null,"Informasi Pribadi",-1)),a("p",null,[e[5]||(e[5]=a("strong",null,"Nama Lengkap:",-1)),d(" "+f(n.data.nama_lengkap),1)]),a("p",null,[e[6]||(e[6]=a("strong",null,"Nama Panggilan:",-1)),d(" "+f(n.data.nama_panggilan),1)]),a("p",null,[e[7]||(e[7]=a("strong",null,"Tempat Lahir:",-1)),d(" "+f(n.data.kelahiran_tempat),1)]),a("p",null,[e[8]||(e[8]=a("strong",null,"Tanggal Lahir:",-1)),d(" "+f(s.formatDate(n.data.kelahiran_tanggal)),1)]),a("p",null,[e[9]||(e[9]=a("strong",null,"Alamat:",-1)),d(" "+f(n.data.alamat_tinggal),1)]),a("p",null,[e[10]||(e[10]=a("strong",null,"Sekolah/Kelas:",-1)),d(" "+f(n.data.sekolah_kelas),1)]),a("p",null,[e[11]||(e[11]=a("strong",null,"No. HP:",-1)),d(" "+f(n.data.nomor_hape||"-"),1)]),a("p",null,[e[12]||(e[12]=a("strong",null,"Daerah:",-1)),d(" "+f(n.data.daerah),1)]),a("p",null,[e[13]||(e[13]=a("strong",null,"Jenis Kelamin:",-1)),d(" "+f(n.data.jenis_kelamin),1)])]),a("div",vo,[e[21]||(e[21]=a("h4",null,"Informasi Keluarga",-1)),a("p",null,[e[15]||(e[15]=a("strong",null,"Nama Ayah:",-1)),d(" "+f(n.data.nama_ayah),1)]),a("p",null,[e[16]||(e[16]=a("strong",null,"Status Ayah:",-1)),d(" "+f(n.data.status_ayah),1)]),a("p",null,[e[17]||(e[17]=a("strong",null,"No. HP Ayah:",-1)),d(" "+f(n.data.nomor_hape_ayah||"-"),1)]),a("p",null,[e[18]||(e[18]=a("strong",null,"Nama Ibu:",-1)),d(" "+f(n.data.nama_ibu),1)]),a("p",null,[e[19]||(e[19]=a("strong",null,"Status Ibu:",-1)),d(" "+f(n.data.status_ibu),1)]),a("p",null,[e[20]||(e[20]=a("strong",null,"No. HP Ibu:",-1)),d(" "+f(n.data.nomor_hape_ibu||"-"),1)])]),a("div",yo,[e[26]||(e[26]=a("h4",null,"Lainnya",-1)),a("p",null,[e[22]||(e[22]=a("strong",null,"Sambung Desa:",-1)),d(" "+f(n.data.sambung_desa),1)]),a("p",null,[e[23]||(e[23]=a("strong",null,"Sambung Kelompok:",-1)),d(" "+f(n.data.sambung_kelompok),1)]),a("p",null,[e[24]||(e[24]=a("strong",null,"Hobi:",-1)),d(" "+f(s.formatHobi(n.data.hobi)),1)]),a("p",null,[e[25]||(e[25]=a("strong",null,"Tanggal Pendataan:",-1)),d(" "+f(s.formatDate(n.data.pendataan_tanggal)),1)])]),n.data.foto_filename?(u(),i("div",wo,[e[28]||(e[28]=a("h4",null,"Foto",-1)),o.photoUrl?(u(),i("img",{key:0,src:o.photoUrl,alt:`Foto ${n.data.nama_lengkap}`,class:"biodata-photo",onError:e[1]||(e[1]=(...t)=>s.handleImageError&&s.handleImageError(...t))},null,40,xo)):o.loadingPhoto?(u(),i("div",_o,[...e[27]||(e[27]=[a("p",null,"Memuat foto...",-1)])])):(u(),i("p",Ao,"Foto tidak tersedia"))])):(u(),i("div",Lo,[...e[29]||(e[29]=[a("h4",null,"Foto",-1),a("p",{class:"no-photo"},"Foto tidak tersedia",-1)])]))])):(u(),i("div",No,[...e[30]||(e[30]=[a("p",null,"Memuat detail...",-1)])]))])])])):p("",!0)}],["__scopeId","data-v-30322fc9"]])},data:()=>({biodataData:[],detailedData:{},showModal:!1,selectedDetailData:null,filters:{sambung_desa:"",sambung_kelompok:"",nama:""},apiKey:"",sortKey:"",sortOrder:"asc"}),computed:{uniqueDesa(){return[...new Set(this.biodataData.map(t=>t.sambung_desa))].sort()},uniqueKelompok(){return[...new Set(this.biodataData.map(t=>t.sambung_kelompok))].sort()},filteredData(){const t=this.biodataData.filter(t=>{const e=!this.filters.sambung_desa||t.sambung_desa===this.filters.sambung_desa,n=!this.filters.sambung_kelompok||t.sambung_kelompok===this.filters.sambung_kelompok,r=!this.filters.nama||t.nama_lengkap.toLowerCase().includes(this.filters.nama.toLowerCase())||t.nama_panggilan.toLowerCase().includes(this.filters.nama.toLowerCase());return e&&n&&r});return this.sortKey&&t.sort((t,e)=>{let n="index"===this.sortKey?1:t[this.sortKey],r="index"===this.sortKey?1:e[this.sortKey];return"string"==typeof n&&(n=n.toLowerCase()),"string"==typeof r&&(r=r.toLowerCase()),n<r?"asc"===this.sortOrder?-1:1:n>r?"asc"===this.sortOrder?1:-1:0}),t},filteredStats(){const t=this.filteredData,e=new Set,n=new Set;Object.keys(this.detailedData).forEach(t=>{this.detailedData[t]?.foto_filename?e.add(t):n.add(t)});const r=t.filter(t=>e.has(t.biodata_id)).length,i=t.filter(t=>n.has(t.biodata_id)).length,a=t.length-r-i;return{total_count:t.length,male_count:t.filter(t=>"laki-laki"===t.jenis_kelamin.toLowerCase()).length,female_count:t.filter(t=>"perempuan"===t.jenis_kelamin.toLowerCase()).length,with_photo_count:r,without_photo_count:i+a}}},methods:{handleFilterChange(t){this.filters={...t}},async fetchBiodataGenerus(){try{const t=await fetch("/api/biodata/generus/",{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(!t.ok)throw new Error(`HTTP error! status: ${t.status}`);this.biodataData=await t.json()}catch(jo){console.error("Error fetching data:",jo)}},async fetchDetailedData(t){if(console.log("🔍 [FETCH DEBUG] Fetching detailed data for biodata ID:",t),this.detailedData[t])return console.log("📋 [FETCH DEBUG] Data already cached, returning cached data"),this.detailedData[t];const e=`/api/biodata/generus/${t}`;console.log("🌐 [FETCH DEBUG] API URL:",e),console.log("🔑 [FETCH DEBUG] API Key (first 10 chars):",this.apiKey?this.apiKey.substring(0,10)+"...":"NOT PROVIDED");try{console.log("🚀 [FETCH DEBUG] Making fetch request...");const n=await fetch(e,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(console.log("📡 [FETCH DEBUG] Response received, status:",n.status),!n.ok)throw console.error("❌ [FETCH DEBUG] Response not OK, status:",n.status),new Error(`HTTP error! status: ${n.status}`);const r=await n.json();return console.log("📋 [FETCH DEBUG] Data parsed successfully:",r),console.log("📸 [FETCH DEBUG] Photo filename in response:",r.foto_filename),this.detailedData[t]=r,r}catch(jo){return console.error("💥 [FETCH DEBUG] Error fetching detailed data:",jo),null}},async openDetailModal(t){console.log("🔍 [MODAL DEBUG] Opening detail modal for biodata ID:",t),this.showModal=!0,this.selectedDetailData=null;const e=await this.fetchDetailedData(t);e?(console.log("📋 [MODAL DEBUG] Detail data received:",e),console.log("📸 [MODAL DEBUG] Photo filename:",e.foto_filename),console.log("🔑 [MODAL DEBUG] API Key available:",!!this.apiKey),this.selectedDetailData=e):console.error("❌ [MODAL DEBUG] No data received for biodata ID:",t)},closeModal(){this.showModal=!1,this.selectedDetailData=null},downloadPDF(){const t=new Zn({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}});t.setFont("times","normal"),t.setFontSize(20),t.text("Laporan Biodata Generus",t.internal.pageSize.getWidth()/2,2,{align:"center"}),t.setFontSize(16),t.text(`${this.filters.sambung_desa||"Semua Desa"}`,t.internal.pageSize.getWidth()/2,2.8,{align:"center"}),t.autoTable({head:[["No.","Nama Lengkap","Nama Panggilan","Desa","Kelompok","Jenis Kelamin"]],body:this.filteredData.map((t,e)=>[e+1,t.nama_lengkap,t.nama_panggilan,t.sambung_desa,t.sambung_kelompok,t.jenis_kelamin]),startY:4,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},didDrawPage:e=>{const n=e.pageNumber,r=t.internal.pageSize.height,i=t.internal.pageSize.width;t.setFontSize(10);const a=r-1.5;t.setDrawColor(200,200,200),t.setLineWidth(.02),t.line(1,a,i-1,a);const o=`BIODATA GENERUS - ${this.filters.sambung_desa||"Semua Desa"} - Halaman ${n}`;t.text(o,i-1,a+.5,{align:"right"})}});const e=`Laporan-Biodata-Generus-${this.filters.sambung_desa||"semua"}.pdf`;t.save(e)},sortTable(t){this.sortKey===t?this.sortOrder="asc"===this.sortOrder?"desc":"asc":(this.sortKey=t,this.sortOrder="asc")}},mounted(){const t=new URLSearchParams(window.location.search);this.apiKey=t.get("key"),this.fetchBiodataGenerus(),document.title="Pantauan Biodata Generus"}},ko={id:"app"},Po={class:"button-container"},Fo=r(So,[["render",function(t,e,n,r,o,s){const l=b("FilterSection"),h=b("StatisticsSection"),c=b("BiodataTable"),f=b("BiodataDetailModal");return u(),i("div",ko,[e[1]||(e[1]=a("h1",null,"Pantauan Biodata Generus",-1)),m(l,{filters:o.filters,"unique-desa":s.uniqueDesa,"unique-kelompok":s.uniqueKelompok,onFilterChange:s.handleFilterChange},null,8,["filters","unique-desa","unique-kelompok","onFilterChange"]),m(h,{stats:s.filteredStats},null,8,["stats"]),m(c,{data:s.filteredData,"sort-key":o.sortKey,"sort-order":o.sortOrder,onSort:s.sortTable,onRowClick:s.openDetailModal},null,8,["data","sort-key","sort-order","onSort","onRowClick"]),a("div",Po,[a("button",{onClick:e[0]||(e[0]=(...t)=>s.downloadPDF&&s.downloadPDF(...t))},"Download as PDF")]),m(f,{show:o.showModal,data:o.selectedDetailData,"api-key":o.apiKey,onClose:s.closeModal},null,8,["show","data","api-key","onClose"])])}]]),Co=Object.freeze(Object.defineProperty({__proto__:null,default:Fo},Symbol.toStringTag,{value:"Module"}));t("p",Co)}}});
