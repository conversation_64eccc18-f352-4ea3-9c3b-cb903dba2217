const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/biodata-generus-DXxqHPQF.js","assets/vendor-eS0gtEdu.js","assets/biodata-generus-C0FcPbOu.css","assets/pantau-biodata-generus-CXBcWYtK.js","assets/pantau-biodata-generus-fDY6ZHwN.css"])))=>i.map(i=>d[i]);
import{d as L,c as m,o as f,a as P,b as E,w,r as b,e as B,f as O,g as A,h as C,i as $,j as S}from"./vendor-eS0gtEdu.js";function H(){import.meta.url,import("_").catch(()=>1),(async function*(){})().next()}(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))u(e);new MutationObserver(e=>{for(const o of e)if(o.type==="childList")for(const n of o.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&u(n)}).observe(document,{childList:!0,subtree:!0});function a(e){const o={};return e.integrity&&(o.integrity=e.integrity),e.referrerPolicy&&(o.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?o.credentials="include":e.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function u(e){if(e.ep)return;e.ep=!0;const o=a(e);fetch(e.href,o)}})();const k=(t,s)=>{const a=t.__vccOpts||t;for(const[u,e]of s)a[u]=e;return a},G=L({data(){return{isLoading:!1}},computed:{currentRoute(){return this.$route}},methods:{setLoading(t){this.isLoading=t}}}),R={class:"app"},q={class:"main",role:"main"},x={key:1,class:"loading"};function N(t,s,a,u,e,o){const n=b("router-view");return f(),m("div",R,[P("main",q,[E(n,null,{default:w(({Component:r,route:p})=>[(f(),m("div",{class:"view-wrapper",key:p.path},[t.isLoading?(f(),m("div",x,"Loading...")):(f(),B(O(r),{key:0,onLoading:t.setLoading},null,40,["onLoading"]))]))]),_:1})])])}const V=k(G,[["render",N]]),j="modulepreload",D=function(t){return"/"+t},_={},g=function(s,a,u){let e=Promise.resolve();if(a&&a.length>0){let n=function(i){return Promise.all(i.map(l=>Promise.resolve(l).then(d=>({status:"fulfilled",value:d}),d=>({status:"rejected",reason:d}))))};document.getElementsByTagName("link");const r=document.querySelector("meta[property=csp-nonce]"),p=(r==null?void 0:r.nonce)||(r==null?void 0:r.getAttribute("nonce"));e=n(a.map(i=>{if(i=D(i),i in _)return;_[i]=!0;const l=i.endsWith(".css"),d=l?'[rel="stylesheet"]':"";if(document.querySelector('link[href="'.concat(i,'"]').concat(d)))return;const c=document.createElement("link");if(c.rel=l?"stylesheet":j,l||(c.as="script"),c.crossOrigin="",c.href=i,p&&c.setAttribute("nonce",p),document.head.appendChild(c),l)return new Promise((y,v)=>{c.addEventListener("load",y),c.addEventListener("error",()=>v(new Error("Unable to preload CSS for ".concat(i))))})}))}function o(n){const r=new Event("vite:preloadError",{cancelable:!0});if(r.payload=n,window.dispatchEvent(r),!r.defaultPrevented)throw n}return e.then(n=>{for(const r of n||[])r.status==="rejected"&&o(r.reason);return s().catch(o)})},I={BiodataGenerus:()=>g(()=>import("./biodata-generus-DXxqHPQF.js"),__vite__mapDeps([0,1,2])),PantauBiodataGenerus:()=>g(()=>import("./pantau-biodata-generus-CXBcWYtK.js").then(t=>t.p),__vite__mapDeps([3,1,4]))},T=[{path:"/biodata-generus",name:"BiodataGenerus",title:"Biodata Generus"},{path:"/pantau-biodata-generus",name:"PantauBiodataGenerus",title:"Pantauan Biodata Generus"}],U=T.map(t=>{const{name:s,path:a}=t;return{name:s,path:a,component:I[s],meta:{title:t.title,requiresAuth:!1}}}),W=A({history:C(),routes:U}),h=$(V),F=S();h.use(F);h.use(W);h.mount("#app");export{k as _,H as __vite_legacy_guard,g as a};
