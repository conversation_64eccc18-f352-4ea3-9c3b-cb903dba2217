System.register(["./index-legacy-HSZdlha3.js","./vendor-legacy-wqB_kNia.js"],function(e,a){"use strict";var t,o,i,n,s,r,l,d,c,h,u,m,p,g,f,b,k,v;return{setters:[e=>{t=e._},e=>{o=e.c,i=e.k,n=e.o,s=e.a,r=e.l,l=e.m,d=e.v,c=e.F,h=e.n,u=e.t,m=e.p,p=e.b,g=e.r,f=e.q,b=e.s,k=e.e,v=e.w}],execute:function(){var a=document.createElement("style");a.textContent=".form-container[data-v-a5516b31]{width:500px;border-radius:20px;background-color:#f9f9f9;box-shadow:0 10px 25px rgba(44,74,62,.2),0 6px 12px rgba(44,74,62,.15);text-align:center;margin:10px auto;padding:20px}@media screen and (max-width: 500px){.form-container[data-v-a5516b31]{width:90%;max-width:500px}}[data-v-b6cce062] .form-row label,[data-v-b6cce062] .form-row input,[data-v-b6cce062] .form-row .suggestions-container input,[data-v-b6cce062] .form-row select{all:unset;box-sizing:border-box}[data-v-b6cce062] .form-row{display:flex;align-items:center;margin:0 0 8px}[data-v-b6cce062] .form-row label{min-width:90px;margin-right:6px;text-align:left;font-weight:700}[data-v-b6cce062] .form-row input,[data-v-b6cce062] .form-row .suggestions-container input,[data-v-b6cce062] .form-row select{flex:1;width:100%;padding:8px;border:1px solid #ccc;border-radius:4px}[data-v-b6cce062] .suggestions-container{position:relative;flex:1;width:100%}[data-v-b6cce062] .suggestions{position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #ccc;border-top:none;z-index:1000;max-height:200px;overflow-y:auto}[data-v-b6cce062] .suggestion-item{padding:8px;cursor:pointer}[data-v-b6cce062] .suggestion-item:hover{background-color:#f0f0f0}[data-v-b6cce062] .loading-indicator{position:absolute;top:100%;left:0;right:0;background:#fff;border:1px solid #ccc;border-top:none;z-index:1000;padding:8px;text-align:center}.alamat-tinggal-box[data-v-8aa95022]{border:1px solid #ccc;border-radius:8px;padding:6px;margin-top:8px;background-color:#f9f9f9}#alamat_tinggal[data-v-8aa95022]{width:100%;margin:0;padding:0}.photo-preview[data-v-7761e3a6]{margin-top:10px}.review-photo[data-v-7761e3a6]{max-width:150px;max-height:150px;border-radius:8px;border:2px solid #ddd;object-fit:cover}.photo-upload-container[data-v-6280c6fb]{margin:20px 0}.section-title[data-v-6280c6fb]{font-weight:700;margin-bottom:15px;color:#2c4a3e}.photo-preview[data-v-6280c6fb]{position:relative;display:inline-block;margin-bottom:15px}.preview-image[data-v-6280c6fb]{max-width:200px;max-height:200px;border-radius:8px;border:2px solid #ddd;object-fit:cover}.remove-photo-btn[data-v-6280c6fb]{position:absolute;top:-10px;right:-10px;background:#f44;color:#fff;border:none;border-radius:50%;width:30px;height:30px;cursor:pointer;font-size:16px;display:flex;align-items:center;justify-content:center}.upload-options[data-v-6280c6fb]{display:flex;flex-direction:column;gap:15px}.camera-section[data-v-6280c6fb],.file-section[data-v-6280c6fb]{display:flex;flex-direction:column;align-items:flex-start}.camera-btn[data-v-6280c6fb],.file-upload-label[data-v-6280c6fb]{background-color:#2e5a35;color:#fff;border:none;padding:12px;border-radius:20px;cursor:pointer;font-size:16px;font-weight:600;transition:background-color .3s ease;box-sizing:border-box;width:100%;text-align:center;display:inline-block}.camera-btn[data-v-6280c6fb]:hover,.file-upload-label[data-v-6280c6fb]:hover{background-color:#3d7a47}.camera-btn[data-v-6280c6fb]:disabled{background:#ccc;cursor:not-allowed}.file-input[data-v-6280c6fb]{display:none}.camera-warning[data-v-6280c6fb]{color:#666;font-size:14px;margin-top:5px}.camera-modal[data-v-6280c6fb]{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.9);display:flex;align-items:center;justify-content:center;z-index:1000;backdrop-filter:blur(5px)}.camera-content[data-v-6280c6fb]{background:#fff;border-radius:15px;padding:25px;max-width:95vw;max-height:95vh;overflow:auto;box-shadow:0 10px 30px rgba(0,0,0,.5);border:2px solid #2e5a35}.camera-header[data-v-6280c6fb]{display:flex;justify-content:space-between;align-items:center;margin-bottom:15px;padding-bottom:10px;border-bottom:1px solid #eee}.camera-controls-header[data-v-6280c6fb]{display:flex;gap:10px;align-items:center}.close-btn[data-v-6280c6fb]{background:#f44;color:#fff;border:none;font-size:18px;cursor:pointer;width:35px;height:35px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-weight:700;transition:all .2s}.close-btn[data-v-6280c6fb]:hover{background:#f66;transform:scale(1.1)}.switch-camera-btn[data-v-6280c6fb]{background:#2e5a35;color:#fff;border:none;padding:8px 12px;border-radius:20px;cursor:pointer;font-size:14px;transition:background-color .2s}.switch-camera-btn[data-v-6280c6fb]:hover{background:#3d7a47}.camera-viewport[data-v-6280c6fb]{margin-bottom:15px}.camera-video[data-v-6280c6fb]{width:100%;max-width:640px;height:auto;border-radius:8px}.camera-controls[data-v-6280c6fb]{display:flex;gap:10px;justify-content:center}.capture-btn[data-v-6280c6fb]{background:#2c4a3e;color:#fff;border:none;padding:12px 20px;border-radius:8px;cursor:pointer;font-size:16px}.cancel-btn[data-v-6280c6fb]{background:#666;color:#fff;border:none;padding:12px 20px;border-radius:8px;cursor:pointer;font-size:16px}.error-message[data-v-6280c6fb]{color:#f44;font-size:14px;margin-top:10px;padding:8px;background:#ffe6e6;border-radius:4px;border:1px solid #ffcccc}.loading-photo[data-v-6280c6fb]{text-align:center;padding:20px;color:#666;font-style:italic}.existing-photo-label[data-v-6280c6fb]{position:absolute;bottom:-25px;left:0;right:0;text-align:center;font-size:12px;color:#666;font-style:italic}body{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline;line-height:1;box-sizing:border-box;background-color:#fff;color:#2c4a3e;width:100%}.form-container{width:500px;border-radius:20px;background-color:#f9f9f9;box-shadow:0 10px 25px rgba(44,74,62,.2),0 6px 12px rgba(44,74,62,.15);box-sizing:border-box;text-align:center;margin:10px auto;transition:opacity .3s ease-in-out;padding:20px}.hidden{opacity:0;pointer-events:none;position:absolute;left:-9999px;visibility:hidden;display:none;z-index:-1}.form-title{font-size:22px;font-weight:700;color:#2e5a35;margin-bottom:10px}.form-subtitle{font-size:20px;color:#2e5a35;font-weight:700;margin-bottom:20px}.section-title{font-size:21px;font-weight:700;color:#2e5a35;margin:30px 20% 25px;padding-bottom:10px;border-bottom:2px solid rgba(46,90,53,.3);text-align:center;align-items:center}form{text-align:left}.form-group{margin-bottom:15px}input,select,textarea{width:90%;padding:10px;margin-bottom:15px;margin-left:8%;margin-right:5%;border:none;border-bottom:2px solid #2e5a35;border-radius:8px;font-size:16px;transition:all .3s ease;box-sizing:border-box;-webkit-appearance:none;appearance:none;background-color:rgba(46,90,53,0);color:#2c4a3e}textarea{height:100px;resize:vertical;border-left:none;border-right:none;border-top:none;border-bottom:2px solid #2e5a35;border-radius:8px}label{display:block;margin-bottom:8px;color:#2c4a3e;font-weight:650;font-size:16px;text-align:left;padding-top:10px}input::placeholder,textarea::placeholder{color:#2e5a35;opacity:.7}input:focus,select:focus,textarea:focus{outline:none;border-bottom:2px solid #3d7a47;box-shadow:none;background-color:rgba(46,90,53,.05)}select{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e5a35' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E\");background-repeat:no-repeat;background-position:right 10px center;background-size:16px;padding-right:40px}button{width:100%;padding:12px;border:none;border-radius:20px;background-color:#2e5a35;color:#fff;font-size:16px;cursor:pointer;transition:background-color .3s ease;box-sizing:border-box;margin-top:20px;font-weight:600}button:hover{background-color:#3d7a47}button.secondary{background-color:#fff;color:#2e5a35;border:2px solid #2e5a35;margin-top:10px}button.secondary:hover{background-color:#f0f0f0}.confirmation-message{font-size:20px;font-weight:700;color:#2e5a35;margin:20px 0;line-height:1.5}.review-data{text-align:left;padding:10px;background-color:#fff;border-radius:15px;margin:20px 0}.review-item{padding:10px 15px;border-bottom:1px solid rgba(46,90,53,.1);font-size:16px;line-height:1.5}.review-item:last-child{border-bottom:none}.suggestions-container{position:relative;flex:1}.suggestions{position:absolute;width:100%;max-height:200px;overflow-y:auto;background:#fff;border:1px solid #2e5a35;border-radius:10px;z-index:10;margin-top:-10px;box-shadow:0 4px 8px rgba(0,0,0,.1);text-align:left}.suggestion-item{padding:10px 15px;cursor:pointer;border-bottom:1px solid rgba(46,90,53,.1)}.suggestion-item:hover{background-color:rgba(46,90,53,.1)}.review-actions{display:flex;flex-direction:column;gap:10px}@media (max-width: 520px){.form-container{width:95%;padding:15px}}.selected-hobi-container{display:flex;flex-wrap:wrap;gap:8px;margin-bottom:10px}.hobi-tag{background-color:#e8f5e9;border:1px solid #2e5a35;border-radius:16px;padding:6px 12px;font-size:14px;color:#2c4a3e;display:flex;align-items:center}.remove-hobi{margin-left:6px;font-size:18px;font-weight:700;cursor:pointer;color:#2e5a35;line-height:1}.remove-hobi:hover{color:#d32f2f}.review-hobi-item{margin:5px 0;padding-left:10px;border-left:2px solid rgba(46,90,53,.3)}.spaci{margin:20px 0 30px;padding:0 5%;border-bottom:2px solid rgba(46,90,53,.1)}\n/*$vite$:1*/",document.head.appendChild(a);const w={class:"form-container"},y=t({},[["render",function(e,a){return n(),o("div",w,[i(e.$slots,"default",{},void 0)])}],["__scopeId","data-v-a5516b31"]]),K={props:{placeholder:{type:String,default:"Ketik untuk mencari..."}},data:()=>({inputValue:"",showSuggestions:!1,isComposing:!1}),methods:{handleInput(){this.showSuggestions=!0,this.$emit("input-change",this.inputValue)},handleFocus(){this.inputValue&&(this.showSuggestions=!0)},handleBlur(){setTimeout(()=>{this.showSuggestions=!1},150)},handleKeyup(e){const a=this.$refs.inputEl;a&&this.inputValue!==a.value&&(this.inputValue=a.value,this.handleInput())},handleCompositionEnd(e){this.isComposing=!1,this.inputValue=e.target.value,this.handleInput()},handleCompositionStart(){this.isComposing=!0}}},x={mixins:[K],props:{value:{type:String,required:!0}},data:()=>({jalan:"",nomor:"",provinsiList:[],kabupatenList:[],kecamatanList:[],kelurahanList:[],selectedProvinsi:null,selectedKabupaten:null,selectedKecamatan:null,selectedKelurahan:null,kabupatenInput:"",kecamatanInput:"",kelurahanInput:"",showKabupatenSuggestions:!1,showKecamatanSuggestions:!1,showKelurahanSuggestions:!1,isLoadingProvinsi:!1,isLoadingKabupaten:!1,isLoadingKecamatan:!1,isLoadingKelurahan:!1,isUpdating:!1,updateTimer:null,wilayahCache:new Map,inputDebounceTimers:{kabupaten:null,kecamatan:null,kelurahan:null},provinsiSelected:!1,kabupatenSelected:!1,kecamatanSelected:!1,kelurahanSelected:!1,originalAddressState:null,lastEmittedAddress:"",isUserEditing:!1,userEditingTimer:null,persistedJalan:"",persistedNomor:""}),computed:{filteredProvinsiList(){const e=this.inputValue.toLowerCase();return e?this.provinsiList.filter(a=>a.name.toLowerCase().includes(e)):[]},filteredKabupatenList(){const e=this.kabupatenInput.toLowerCase();return e?this.kabupatenList.filter(a=>a.name.toLowerCase().includes(e)):[]},filteredKecamatanList(){const e=this.kecamatanInput.toLowerCase();return e?this.kecamatanList.filter(a=>a.name.toLowerCase().includes(e)):[]},filteredKelurahanList(){const e=this.kelurahanInput.toLowerCase();return e?this.kelurahanList.filter(a=>a.name.toLowerCase().includes(e)):[]}},watch:{jalan:"updateAlamat",nomor:"updateAlamat",selectedProvinsi:"updateAlamat",selectedKabupaten:"updateAlamat",selectedKecamatan:"updateAlamat",selectedKelurahan:"updateAlamat",value:{immediate:!0,handler(e){console.log("AlamatTinggalForm value changed:",e),!this.isUpdating&&e&&"string"==typeof e&&""!==e.trim()&&this.parseExistingAddress(e)}},"$parent.isActive":function(e){e||this.forceAddressUpdate()}},methods:{handleJalanKeydown(e){e.stopPropagation()," "!==e.key&&32!==e.keyCode||e.stopPropagation()},handleNomorKeydown(e){e.stopPropagation()," "!==e.key&&32!==e.keyCode||e.stopPropagation()},handleJalanInput(e){e.stopPropagation();const a=e.target.selectionStart,t=e.target.scrollTop,o=document.activeElement;this.jalan=e.target.value,this.persistedJalan=e.target.value;try{localStorage.setItem("alamat_jalan_temp",e.target.value)}catch(i){console.warn("Could not save temp jalan",i)}this.originalAddressState&&(this.originalAddressState.jalan=e.target.value),this.isUserEditing=!0,clearTimeout(this.userEditingTimer),this.$nextTick(()=>{o===this.$refs.jalanInputEl&&(this.$refs.jalanInputEl.focus(),this.$refs.jalanInputEl.setSelectionRange(a,a),this.$refs.jalanInputEl.scrollTop=t),this.userEditingTimer=setTimeout(()=>{this.isUserEditing=!1},1e3)})},handleNomorInput(e){e.stopPropagation();const a=e.target.selectionStart,t=e.target.scrollTop,o=document.activeElement,i=e.target.value;if(this.nomor=i,this.persistedNomor=i,!1!==e.isTrusted)try{localStorage.setItem("alamat_nomor",i),localStorage.setItem("alamat_nomor_priority","true"),sessionStorage.setItem("alamat_nomor_current",i)}catch(n){console.warn("Could not save temp nomor",n)}try{localStorage.setItem("alamat_nomor_temp",e.target.value)}catch(n){console.warn("Could not save temp nomor",n)}this.originalAddressState&&(this.originalAddressState.nomor=e.target.value),this.isUserEditing=!0,clearTimeout(this.userEditingTimer),this.$nextTick(()=>{o===this.$refs.nomorInputEl&&(this.$refs.nomorInputEl.focus(),this.$refs.nomorInputEl.setSelectionRange(a,a),this.$refs.nomorInputEl.scrollTop=t),this.userEditingTimer=setTimeout(()=>{this.isUserEditing=!1},1e3)})},persistJalanChange(e){this.jalan=e.target.value,this.persistedJalan=e.target.value;try{localStorage.setItem("alamat_jalan",e.target.value)}catch(a){console.warn("Could not save jalan to localStorage",a)}this.isUserEditing=!1,this.forceAddressUpdate()},persistNomorChange(e){const a=e.target.value;console.log("Persisting house number change:",a),this.nomor=a,this.persistedNomor=a;try{localStorage.setItem("alamat_nomor",a),localStorage.setItem("alamat_nomor_priority","true")}catch(t){console.warn("Could not save nomor to localStorage",t)}this.isUserEditing=!1,this.isUpdating=!1,this.forceAddressUpdateWithNumber(a)},forceAddressUpdateWithNumber(e){console.log("Force updating address with specific number:",e),clearTimeout(this.updateTimer);const a=this.persistedJalan||this.jalan||"",t=[];if(""!==a.trim()&&t.push(`Jl. ${a.trim()},`),e&&""!==e.trim()&&t.push(`No. ${e.trim()},`),this.selectedKelurahan&&this.kelurahanSelected){const e=this.selectedKelurahan.name.trim();t.push(`Desa/Kel. ${e},`)}if(this.selectedKecamatan&&this.kecamatanSelected&&t.push(`Kec. ${this.selectedKecamatan.name},`),this.selectedKabupaten?.name&&t.push(`${this.selectedKabupaten.name},`),this.selectedProvinsi?.name&&t.push(`${this.selectedProvinsi.name}`),t.length>0){const a=t.join(" ");console.log("Emitting updated alamat with forced number:",e),this.lastEmittedAddress=a,this.$emit("input",a)}},handleInput(e){e&&e.stopPropagation(),this.selectedProvinsi&&this.inputValue!==this.selectedProvinsi.name&&(this.provinsiSelected=!1,this.selectedProvinsi=null),this.provinsiSelected||(this.showSuggestions=!0),this.$emit("input-change")},handleFocus(){!this.provinsiSelected&&this.inputValue&&(this.showSuggestions=!0)},handleBlur(){setTimeout(()=>{this.showSuggestions=!1,this.selectedProvinsi||this.provinsiSelected||(this.inputValue="")},150)},getCacheKey:(e,a=null)=>a?`${e}_${a}`:e,getCachedData(e,a=null){const t=this.getCacheKey(e,a),o=this.wilayahCache.get(t);if(o){if(Date.now()-o.timestamp<3e5)return o.data;this.wilayahCache.delete(t)}return null},setCachedData(e,a,t=null){const o=this.getCacheKey(e,t);if(this.wilayahCache.set(o,{data:a,timestamp:Date.now()}),this.wilayahCache.size>100){const e=Array.from(this.wilayahCache.entries());e.sort((e,a)=>e[1].timestamp-a[1].timestamp);for(let a=0;a<20;a++)this.wilayahCache.delete(e[a][0])}},async prefetchPopularRegions(){const e=["31","32","33","35"];for(const t of e)if(!this.getCachedData("regencies",t))try{const e=await fetch(`/api/wilayah/regencies/${t}`);if(e.ok){const a=await e.json();a.data&&this.setCachedData("regencies",a.data,t)}}catch(a){console.debug("Prefetch failed for province",t,a)}},cleanupTimers(){clearTimeout(this.updateTimer),Object.values(this.inputDebounceTimers).forEach(e=>{e&&clearTimeout(e)})},async fetchProvinsi(e=0){const a=this.getCachedData("provinces");if(a)this.provinsiList=a;else try{this.isLoadingProvinsi=!0;const a=new AbortController,t=setTimeout(()=>a.abort(),1e4),o=await fetch("/api/wilayah/provinces",{signal:a.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(t),!o.ok){if(502===o.status&&e<3)return console.warn(`502 error fetching provinces, retrying... (attempt ${e+1})`),await new Promise(a=>setTimeout(a,1e3*(e+1))),this.fetchProvinsi(e+1);throw new Error(`Failed to fetch provinces: ${o.status} ${o.statusText}`)}const i=await o.json();if(!i.data||!Array.isArray(i.data))throw console.error("Invalid provinces data format:",i),new Error("Invalid data format received");this.provinsiList=i.data,this.setCachedData("provinces",i.data)}catch(t){if(console.error("Error fetching provinces:",t),("AbortError"===t.name||t.message.includes("fetch"))&&e<2)return console.warn(`Network error fetching provinces, retrying... (attempt ${e+1})`),await new Promise(e=>setTimeout(e,2e3)),this.fetchProvinsi(e+1);this.provinsiList=[]}finally{this.isLoadingProvinsi=!1}},selectProvinsi(e){if(this.selectedProvinsi&&this.selectedProvinsi.code===e.code)return this.showSuggestions=!1,void(this.provinsiSelected=!0);console.log("Selected province:",e.name),this.isUpdating=!0,this.selectedProvinsi=e,this.inputValue=e.name,this.showSuggestions=!1,this.provinsiSelected=!0;const a=this.jalan,t=this.nomor;this.selectedKabupaten=null,this.kabupatenInput="",this.selectedKecamatan=null,this.kecamatanInput="",this.selectedKelurahan=null,this.kelurahanInput="",this.kabupatenSelected=!1,this.kecamatanSelected=!1,this.kelurahanSelected=!1,this.jalan=a,this.nomor=t,this.fetchKabupaten().then(()=>{setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)})},debouncedKabupatenInput(e){clearTimeout(this.inputDebounceTimers.kabupaten),this.inputDebounceTimers.kabupaten=setTimeout(()=>{this.processKabupatenInput(e)},150)},debouncedKecamatanInput(e){clearTimeout(this.inputDebounceTimers.kecamatan),this.inputDebounceTimers.kecamatan=setTimeout(()=>{this.processKecamatanInput(e)},150)},debouncedKelurahanInput(e){clearTimeout(this.inputDebounceTimers.kelurahan),this.inputDebounceTimers.kelurahan=setTimeout(()=>{this.processKelurahanInput(e)},150)},processKabupatenInput(e){this.isUpdating||(this.selectedKabupaten&&e!==this.selectedKabupaten.name&&(this.kabupatenSelected=!1,this.selectedKabupaten=null,this.selectedKecamatan=null,this.kecamatanInput="",this.selectedKelurahan=null,this.kelurahanInput="",this.kecamatanSelected=!1,this.kelurahanSelected=!1,this.kecamatanList=[],this.kelurahanList=[]),this.showKabupatenSuggestions=e.length>0)},processKecamatanInput(e){this.selectedKecamatan&&e!==this.selectedKecamatan.name&&(this.kecamatanSelected=!1,this.selectedKecamatan=null,this.selectedKelurahan=null,this.kelurahanInput="",this.kelurahanSelected=!1,this.kelurahanList=[]),this.showKecamatanSuggestions=e.length>0},processKelurahanInput(e){this.selectedKelurahan&&e!==this.selectedKelurahan.name&&(this.kelurahanSelected=!1,this.selectedKelurahan=null),this.showKelurahanSuggestions=e.length>0},handleKabupatenInput(e){e&&e.stopPropagation(),this.isUpdating||(this.debouncedKabupatenInput(this.kabupatenInput),this.isComposing||this.$emit("kabupaten-change"))},handleKabupatenFocus(){!this.kabupatenSelected&&this.kabupatenInput&&(this.showKabupatenSuggestions=!0)},handleKabupatenBlur(){setTimeout(()=>{this.showKabupatenSuggestions=!1,this.selectedKabupaten||this.kabupatenSelected||(this.kabupatenInput="")},150)},handleKabupatenKeyup(e){const a=this.$refs.kabupatenInputEl;a&&this.kabupatenInput!==a.value&&(this.kabupatenInput=a.value,this.handleKabupatenInput()),"Enter"===e.key&&this.filteredKabupatenList.length>0&&(e.preventDefault(),this.selectKabupaten(this.filteredKabupatenList[0]))},handleKabupatenCompositionEnd(e){this.isComposing=!1,this.kabupatenInput=e.target.value,this.handleKabupatenInput()},selectKabupaten(e){if(this.selectedKabupaten&&this.selectedKabupaten.code===e.code)return this.showKabupatenSuggestions=!1,void(this.kabupatenSelected=!0);console.log("Selecting kabupaten:",e.name),this.isUpdating=!0;const a=this.jalan,t=this.nomor;this.selectedKabupaten=e,this.kabupatenInput=e.name,this.showKabupatenSuggestions=!1,this.kabupatenSelected=!0,this.selectedKecamatan=null,this.kecamatanInput="",this.selectedKelurahan=null,this.kelurahanInput="",this.kecamatanSelected=!1,this.kelurahanSelected=!1,this.kecamatanList=[],this.kelurahanList=[],this.jalan=a,this.nomor=t,this.fetchKecamatan().then(()=>{setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)})},handleKecamatanInput(e){e.stopPropagation(),this.debouncedKecamatanInput(this.kecamatanInput),this.isComposing||this.$emit("kecamatan-change")},handleKecamatanFocus(){!this.kecamatanSelected&&this.kecamatanInput&&(this.showKecamatanSuggestions=!0)},handleKecamatanBlur(){setTimeout(()=>{this.showKecamatanSuggestions=!1,this.selectedKecamatan||this.kecamatanSelected||(this.kecamatanInput="")},150)},handleKecamatanKeyup(e){const a=this.$refs.kecamatanInputEl;a&&this.kecamatanInput!==a.value&&(this.kecamatanInput=a.value,this.handleKecamatanInput()),"Enter"===e.key&&this.filteredKecamatanList.length>0&&(e.preventDefault(),this.selectKecamatan(this.filteredKecamatanList[0]))},handleKecamatanCompositionEnd(e){this.isComposing=!1,this.kecamatanInput=e.target.value,this.handleKecamatanInput()},selectKecamatan(e){if(this.selectedKecamatan&&this.selectedKecamatan.code===e.code)return this.showKecamatanSuggestions=!1,void(this.kecamatanSelected=!0);console.log("Selecting kecamatan:",e.name),this.isUpdating=!0,this.selectedKecamatan=e,this.kecamatanInput=e.name,this.showKecamatanSuggestions=!1,this.kecamatanSelected=!0,this.selectedKelurahan=null,this.kelurahanInput="",this.kelurahanSelected=!1,this.kelurahanList=[],this.fetchKelurahan().then(()=>{setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)})},handleKelurahanInput(e){e.stopPropagation(),this.debouncedKelurahanInput(this.kelurahanInput),this.isComposing||this.$emit("kelurahan-change")},handleKelurahanFocus(){!this.kelurahanSelected&&this.kelurahanInput&&(this.showKelurahanSuggestions=!0)},handleKelurahanBlur(){setTimeout(()=>{this.showKelurahanSuggestions=!1,this.selectedKelurahan||this.kelurahanSelected||(this.kelurahanInput="")},150)},handleKelurahanKeyup(e){const a=this.$refs.kelurahanInputEl;a&&this.kelurahanInput!==a.value&&(this.kelurahanInput=a.value,this.handleKelurahanInput()),"Enter"===e.key&&this.filteredKelurahanList.length>0&&(e.preventDefault(),this.selectKelurahan(this.filteredKelurahanList[0]))},handleKelurahanCompositionEnd(e){this.isComposing=!1,this.kelurahanInput=e.target.value,this.handleKelurahanInput()},selectKelurahan(e){if(this.selectedKelurahan&&this.selectedKelurahan.code===e.code)return this.showKelurahanSuggestions=!1,void(this.kelurahanSelected=!0);console.log("Selecting kelurahan:",e.name),this.isUpdating=!0,this.selectedKelurahan={...e,name:e.name.trim()},this.kelurahanInput=e.name.trim(),this.showKelurahanSuggestions=!1,this.kelurahanSelected=!0,setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)},async fetchKabupaten(e=0){if(!this.selectedProvinsi)return void console.log("Cannot fetch kabupaten: No province selected");const a=this.getCachedData("regencies",this.selectedProvinsi.code);if(a)return this.kabupatenList=a,this.$nextTick(()=>{this.$refs.kabupatenInputEl&&!this.kabupatenSelected&&this.$refs.kabupatenInputEl.focus()}),a;console.log("Fetching kabupaten for province:",this.selectedProvinsi.name,"code:",this.selectedProvinsi.code,e>0?`(retry ${e})`:"");try{this.isLoadingKabupaten=!0,this.kabupatenList=[];const a=new AbortController,t=setTimeout(()=>a.abort(),1e4),o=await fetch(`/api/wilayah/regencies/${this.selectedProvinsi.code}`,{signal:a.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(t),!o.ok){if(502===o.status&&e<3)return console.warn(`502 error fetching kabupaten, retrying... (attempt ${e+1})`),await new Promise(a=>setTimeout(a,1e3*(e+1))),this.fetchKabupaten(e+1);throw new Error(`Failed to fetch kabupaten: ${o.status} ${o.statusText}`)}const i=await o.json();if(!i.data||!Array.isArray(i.data))throw console.error("Invalid kabupaten data format:",i),new Error("Invalid data format received");return this.kabupatenList=i.data,this.setCachedData("regencies",i.data,this.selectedProvinsi.code),console.log(`Loaded ${this.kabupatenList.length} kabupaten for ${this.selectedProvinsi.name}`),i.data}catch(t){return console.error("Error fetching kabupaten:",t),("AbortError"===t.name||t.message.includes("fetch"))&&e<2?(console.warn(`Network error fetching kabupaten, retrying... (attempt ${e+1})`),await new Promise(e=>setTimeout(e,2e3)),this.fetchKabupaten(e+1)):(this.kabupatenList=[],t.message.includes("502")&&console.warn("Server temporarily unavailable. Please try selecting the province again."),[])}finally{this.isLoadingKabupaten=!1,this.$nextTick(()=>{this.$refs.kabupatenInputEl&&!this.kabupatenSelected&&this.$refs.kabupatenInputEl.focus()})}},async fetchKecamatan(e=0){if(!this.selectedKabupaten)return console.log("Cannot fetch kecamatan: No kabupaten selected"),[];const a=this.getCachedData("districts",this.selectedKabupaten.code);if(a)return this.kecamatanList=a,this.$nextTick(()=>{this.$refs.kecamatanInputEl&&!this.kecamatanSelected&&this.$refs.kecamatanInputEl.focus()}),a;console.log("Fetching kecamatan for kabupaten:",this.selectedKabupaten.name,"code:",this.selectedKabupaten.code,e>0?`(retry ${e})`:"");try{this.isLoadingKecamatan=!0,this.kecamatanList=[];const a=new AbortController,t=setTimeout(()=>a.abort(),1e4),o=await fetch(`/api/wilayah/districts/${this.selectedKabupaten.code}`,{signal:a.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(t),!o.ok){if(502===o.status&&e<3)return console.warn(`502 error fetching kecamatan, retrying... (attempt ${e+1})`),await new Promise(a=>setTimeout(a,1e3*(e+1))),this.fetchKecamatan(e+1);throw new Error(`Failed to fetch kecamatan: ${o.status} ${o.statusText}`)}const i=await o.json();if(!i.data||!Array.isArray(i.data))throw console.error("Invalid kecamatan data format:",i),new Error("Invalid data format received");return this.kecamatanList=i.data,this.setCachedData("districts",i.data,this.selectedKabupaten.code),console.log(`Loaded ${this.kecamatanList.length} kecamatan for ${this.selectedKabupaten.name}`),i.data}catch(t){return console.error("Error fetching kecamatan:",t),("AbortError"===t.name||t.message.includes("fetch"))&&e<2?(console.warn(`Network error fetching kecamatan, retrying... (attempt ${e+1})`),await new Promise(e=>setTimeout(e,2e3)),this.fetchKecamatan(e+1)):(this.kecamatanList=[],[])}finally{this.isLoadingKecamatan=!1,this.$nextTick(()=>{this.$refs.kecamatanInputEl&&!this.kecamatanSelected&&this.$refs.kecamatanInputEl.focus()})}},async fetchKelurahan(e=0){if(!this.selectedKecamatan)return void console.log("Cannot fetch kelurahan: No kecamatan selected");const a=this.getCachedData("villages",this.selectedKecamatan.code);if(a)return this.kelurahanList=a,this.$nextTick(()=>{this.$refs.kelurahanInputEl&&this.$refs.kelurahanInputEl.focus()}),a;console.log("Fetching kelurahan for kecamatan:",this.selectedKecamatan.name,"code:",this.selectedKecamatan.code,e>0?`(retry ${e})`:"");try{this.isLoadingKelurahan=!0,this.kelurahanList=[];const a=new AbortController,t=setTimeout(()=>a.abort(),1e4),o=await fetch(`/api/wilayah/villages/${this.selectedKecamatan.code}`,{signal:a.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(t),!o.ok){if(502===o.status&&e<3)return console.warn(`502 error fetching kelurahan, retrying... (attempt ${e+1})`),await new Promise(a=>setTimeout(a,1e3*(e+1))),this.fetchKelurahan(e+1);throw new Error(`Failed to fetch kelurahan: ${o.status} ${o.statusText}`)}const i=await o.json();if(!i.data||!Array.isArray(i.data))throw console.error("Invalid kelurahan data format:",i),new Error("Invalid data format received");return this.kelurahanList=i.data,this.setCachedData("villages",i.data,this.selectedKecamatan.code),console.log(`Loaded ${this.kelurahanList.length} kelurahan for ${this.selectedKecamatan.name}`),this.$nextTick(()=>{this.$refs.kelurahanInputEl&&this.$refs.kelurahanInputEl.focus()}),i.data}catch(t){return console.error("Error fetching kelurahan:",t),("AbortError"===t.name||t.message.includes("fetch"))&&e<2?(console.warn(`Network error fetching kelurahan, retrying... (attempt ${e+1})`),await new Promise(e=>setTimeout(e,2e3)),this.fetchKelurahan(e+1)):(this.kelurahanList=[],[])}finally{this.isLoadingKelurahan=!1}},updateAlamat(){if(this.isUpdating)return;if(this.isUserEditing)return;this.isUpdating=!0,clearTimeout(this.updateTimer);let e=!1;try{e="true"===localStorage.getItem("alamat_nomor_priority")}catch(o){console.warn("Could not read nomor priority",o)}let a="";if(e)try{const e=localStorage.getItem("alamat_nomor");e&&(a=e,console.log("Using priority stored nomor:",a))}catch(o){console.warn("Could not read stored nomor",o)}a||(a=this.persistedNomor||this.nomor||"");const t=this.persistedJalan||this.jalan||"";this.updateTimer=setTimeout(()=>{const e=[];if(""!==t.trim()&&e.push(`Jl. ${t.trim()},`),""!==a.trim()&&e.push(`No. ${a.trim()},`),this.selectedKelurahan&&this.kelurahanSelected){const a=this.selectedKelurahan.name.trim();e.push(`Desa/Kel. ${a},`)}if(this.selectedKecamatan&&this.kecamatanSelected&&e.push(`Kec. ${this.selectedKecamatan.name},`),this.selectedKabupaten?.name&&e.push(`${this.selectedKabupaten.name},`),this.selectedProvinsi?.name&&e.push(`${this.selectedProvinsi.name}`),e.length>0){const o=e.join(" ");if(a&&!o.includes(`No. ${a}`))return console.warn("Number missing from address, forcing rebuild with:",a),void this.forceAddressUpdateWithNumber(a);console.log("Emitting updated alamat with jalan:",t,"and nomor:",a),this.originalAddressState={...this.originalAddressState,jalan:t,nomor:a,provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},this.$emit("input",o),this.lastEmittedAddress=o}this.isUpdating=!1},300)},parseExistingAddress(e){if(this.isUpdating||this.isUserEditing||!e||"string"!=typeof e)return void console.log("Skipping address parsing:",this.isUpdating?"update in progress":this.isUserEditing?"user is currently editing":"invalid address string");console.log("Parsing address:",e);const a=this.jalan,t=this.nomor,o=a&&""!==a.trim(),i=t&&""!==t.trim();this.isUpdating=!0;try{const s=e.split(",").map(e=>e.trim());let r="",l="";try{r=localStorage.getItem("alamat_jalan")||"",l=localStorage.getItem("alamat_nomor")||""}catch(n){console.warn("Could not read persisted address",n)}if(r?(this.jalan=r,this.persistedJalan=r,console.log("Using persisted jalan value:",r)):o?console.log("Preserving user-set jalan:",a):s[0]?.toLowerCase().startsWith("jl.")&&(this.jalan=s[0].substring(3).trim(),console.log("Found jalan in address:",this.jalan)),l)this.nomor=l,this.persistedNomor=l,console.log("Using persisted nomor value:",l);else if(i)console.log("Preserving user-set nomor:",t);else{const e=s.find(e=>e?.toLowerCase().includes("no."));e&&(this.nomor=e.substring(e.toLowerCase().indexOf("no.")+3).trim(),console.log("Found nomor in address:",this.nomor))}const d=/kec\.|kecamatan/i,c=s.find(e=>e&&d.test(e));let h="";c&&(h=c.replace(d,"").trim(),console.log("Found kecamatan in address:",h));const u=/desa\/kel\.|desa|kel\.|kelurahan/i,m=s.find(e=>e&&u.test(e));let p="";m&&(p=m.replace(u,"").trim(),console.log("Found kelurahan in address:",p));const g=/kab\.|kabupaten|kota/i,f=s.find(e=>e&&g.test(e));let b="";f&&(b=f.replace(g,"").trim(),console.log("Found kabupaten in address:",b)),this.fetchProvinsi().then(async()=>{const e=s[s.length-1]?.trim()||"";if(e&&this.provinsiList.length>0){const a=this.provinsiList.find(a=>a.name.toLowerCase()===e.toLowerCase());if(a&&(console.log("Found matching province:",a.name),this.selectedProvinsi=a,this.inputValue=a.name,this.provinsiSelected=!0,b))return this.fetchKabupaten().then(async()=>{if(this.kabupatenList.length>0){console.log("Looking for kabupaten:",b);const e=this.kabupatenList.find(e=>b.toLowerCase().includes(e.name.toLowerCase())||e.name.toLowerCase().includes(b.toLowerCase()));if(e&&(console.log("Found matching kabupaten:",e.name),this.selectedKabupaten=e,this.kabupatenInput=e.name,this.kabupatenSelected=!0,h))return this.fetchKecamatan().then(async()=>{if(this.kecamatanList.length>0){console.log("Looking for kecamatan:",h);const e=this.kecamatanList.find(e=>h.toLowerCase().includes(e.name.toLowerCase())||e.name.toLowerCase().includes(h.toLowerCase()));if(e&&(console.log("Found matching kecamatan:",e.name),this.selectedKecamatan=e,this.kecamatanInput=e.name,this.kecamatanSelected=!0,p))return this.fetchKelurahan().then(()=>{if(this.kelurahanList.length>0){console.log("Looking for kelurahan:",p);let e=this.kelurahanList.find(e=>e.name.trim().toLowerCase()===p.toLowerCase());e||(e=this.kelurahanList.find(e=>p.toLowerCase().includes(e.name.trim().toLowerCase())||e.name.trim().toLowerCase().includes(p.toLowerCase()))),e&&(console.log("Found matching kelurahan:",e.name),this.selectedKelurahan={...e,name:e.name.trim()},this.kelurahanInput=e.name.trim(),this.kelurahanSelected=!0)}})}})}})}}).catch(e=>{console.error("Error in address parsing chain:",e)}).finally(()=>{this.originalAddressState={jalan:this.jalan,nomor:this.nomor,provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},setTimeout(()=>{this.isUpdating=!1},1e3)})}catch(s){console.error("Error parsing address:",s),this.isUpdating=!1}},forceAddressUpdate(){console.log("Force updating address before component destroyed");let e="";try{"true"===localStorage.getItem("alamat_nomor_priority")&&(e=localStorage.getItem("alamat_nomor")||"")}catch(a){console.warn("Could not read priority nomor",a)}e||(e=this.persistedNomor||this.nomor||""),console.log("Force updating with most accurate nomor:",e),e?this.forceAddressUpdateWithNumber(e):this.updateAlamat()},clearStoredAddressData(){console.log("Clearing all stored address data from caches");try{localStorage.removeItem("alamat_jalan"),localStorage.removeItem("alamat_jalan_temp"),localStorage.removeItem("alamat_nomor"),localStorage.removeItem("alamat_nomor_temp"),localStorage.removeItem("alamat_nomor_priority"),sessionStorage.removeItem("alamat_nomor_current")}catch(e){console.warn("Error clearing address storage:",e)}}},created(){console.log("AlamatTinggalForm created with value:",this.value),(window.performance&&("navigate"===window.performance.getEntriesByType("navigation")[0]?.type||"reload"===window.performance.getEntriesByType("navigation")[0]?.type)||"complete"===document.readyState)&&(console.log("Page was loaded/reloaded - clearing address cache"),this.clearStoredAddressData()),void 0!==this.value&&null!==this.value||this.$emit("input","")},mounted(){this.fetchProvinsi(),console.log("AlamatTinggalForm mounted"),setTimeout(()=>{this.prefetchPopularRegions()},1e3);try{const e=sessionStorage.getItem("alamat_last_load_time"),a=Date.now();(!e||a-Number.parseInt(e,10)>2e3)&&(console.log("Detected page load via timestamp - clearing address cache"),this.clearStoredAddressData()),sessionStorage.setItem("alamat_last_load_time",a.toString())}catch(e){console.warn("Error in reload detection:",e)}this.originalAddressState={jalan:this.jalan||"",nomor:this.nomor||"",provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},this.$nextTick(()=>{this.originalAddressState={jalan:this.jalan,nomor:this.nomor,provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},this.value&&(this.selectedProvinsi&&!this.value.includes(this.selectedProvinsi.name)||this.selectedKabupaten&&!this.value.includes(this.selectedKabupaten.name)||this.selectedKecamatan&&!this.value.includes(this.selectedKecamatan.name)||this.selectedKelurahan&&!this.value.includes(this.selectedKelurahan.name))&&(console.log("Found incomplete address, forcing update"),this.forceAddressUpdate())});try{const e=localStorage.getItem("alamat_jalan"),a=localStorage.getItem("alamat_nomor");e&&(this.jalan=e,this.persistedJalan=e,console.log("Restored persisted jalan:",e)),a&&(this.nomor=a,this.persistedNomor=a,console.log("Restored persisted nomor:",a))}catch(e){console.warn("Could not restore persisted address",e)}},beforeDestroy(){let e="";try{"true"===localStorage.getItem("alamat_nomor_priority")&&(e=localStorage.getItem("alamat_nomor")||"")}catch(a){console.warn("Could not read priority nomor on destroy",a)}e||(e=this.persistedNomor||this.nomor||""),e?this.forceAddressUpdateWithNumber(e):this.forceAddressUpdate()},beforeUnmount(){this.cleanupTimers();let e="";try{"true"===localStorage.getItem("alamat_nomor_priority")&&(e=localStorage.getItem("alamat_nomor")||"")}catch(a){console.warn("Could not read priority nomor on unmount",a)}e||(e=this.persistedNomor||this.nomor||""),e?this.forceAddressUpdateWithNumber(e):this.forceAddressUpdate()}},S={class:"form-row"},_=["value"],D={class:"form-row"},C=["value"],I={class:"form-row"},P={class:"suggestions-container"},E={key:0,class:"loading-indicator"},L={key:1,class:"suggestions"},A=["onClick"],U={key:0,class:"form-row"},$={class:"suggestions-container"},T={key:0,class:"loading-indicator"},N={key:1,class:"suggestions"},F=["onClick"],j={key:1,class:"form-row"},O={class:"suggestions-container"},R={key:0,class:"loading-indicator"},J={key:1,class:"suggestions"},H=["onClick"],M={key:2,class:"form-row"},V={class:"suggestions-container"},B={key:0,class:"loading-indicator"},z={key:1,class:"suggestions"},q=["onClick"],G={class:"form-group"},W={class:"form-group"},Y={class:"form-group"},Q={class:"form-group"},X={class:"alamat-tinggal-box"},Z={class:"form-group"},ee=t({props:{formData:{type:Object,required:!0}},components:{AlamatTinggalForm:t(x,[["render",function(e,a,t,i,m,p){return n(),o("div",null,[s("div",S,[a[34]||(a[34]=s("label",{for:"jalan"},"Jalan :",-1)),s("input",{id:"jalan",type:"text",ref:"jalanInputEl",value:m.jalan,onInput:a[0]||(a[0]=(...e)=>p.handleJalanInput&&p.handleJalanInput(...e)),onKeydown:a[1]||(a[1]=(...e)=>p.handleJalanKeydown&&p.handleJalanKeydown(...e)),onChange:a[2]||(a[2]=e=>p.persistJalanChange(e)),placeholder:"Nama Jalan, Gang, dan RT/RW (jika ada).",required:""},null,40,_)]),s("div",D,[a[35]||(a[35]=s("label",{for:"nomor"},"No. :",-1)),s("input",{id:"nomor",type:"text",ref:"nomorInputEl",value:m.nomor,onInput:a[3]||(a[3]=(...e)=>p.handleNomorInput&&p.handleNomorInput(...e)),onKeydown:a[4]||(a[4]=(...e)=>p.handleNomorKeydown&&p.handleNomorKeydown(...e)),onChange:a[5]||(a[5]=e=>p.persistNomorChange(e)),placeholder:"Nomor Rumah. Tulis 0 jika tidak ada.",required:""},null,40,C)]),s("div",I,[a[36]||(a[36]=s("label",{for:"provinsi"},"Provinsi :",-1)),s("div",P,[l(s("input",{id:"provinsi",ref:"inputEl",type:"text","onUpdate:modelValue":a[6]||(a[6]=a=>e.inputValue=a),onInput:a[7]||(a[7]=(...e)=>p.handleInput&&p.handleInput(...e)),onFocus:a[8]||(a[8]=(...e)=>p.handleFocus&&p.handleFocus(...e)),onBlur:a[9]||(a[9]=(...e)=>p.handleBlur&&p.handleBlur(...e)),onKeyup:a[10]||(a[10]=(...a)=>e.handleKeyup&&e.handleKeyup(...a)),onCompositionstart:a[11]||(a[11]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[12]||(a[12]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),placeholder:"Ketik untuk mencari provinsi",required:""},null,544),[[d,e.inputValue]]),m.isLoadingProvinsi?(n(),o("div",E,"Loading...")):r("",!0),e.showSuggestions&&p.filteredProvinsiList.length?(n(),o("div",L,[(n(!0),o(c,null,h(p.filteredProvinsiList,e=>(n(),o("div",{key:e.code,class:"suggestion-item",onClick:a=>p.selectProvinsi(e)},u(e.name),9,A))),128))])):r("",!0)])]),m.kabupatenList.length>0?(n(),o("div",U,[a[37]||(a[37]=s("label",{for:"kabupaten"},"Kota / Kab. :",-1)),s("div",$,[l(s("input",{id:"kabupaten",ref:"kabupatenInputEl",type:"text","onUpdate:modelValue":a[13]||(a[13]=e=>m.kabupatenInput=e),onInput:a[14]||(a[14]=e=>p.handleKabupatenInput(e)),onFocus:a[15]||(a[15]=(...e)=>p.handleKabupatenFocus&&p.handleKabupatenFocus(...e)),onBlur:a[16]||(a[16]=(...e)=>p.handleKabupatenBlur&&p.handleKabupatenBlur(...e)),onKeyup:a[17]||(a[17]=(...e)=>p.handleKabupatenKeyup&&p.handleKabupatenKeyup(...e)),onCompositionstart:a[18]||(a[18]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[19]||(a[19]=(...e)=>p.handleKabupatenCompositionEnd&&p.handleKabupatenCompositionEnd(...e)),placeholder:"Ketik untuk mencari kabupaten",required:""},null,544),[[d,m.kabupatenInput]]),m.isLoadingKabupaten?(n(),o("div",T," Loading... ")):r("",!0),m.showKabupatenSuggestions&&p.filteredKabupatenList.length?(n(),o("div",N,[(n(!0),o(c,null,h(p.filteredKabupatenList,e=>(n(),o("div",{key:e.code,class:"suggestion-item",onClick:a=>p.selectKabupaten(e)},u(e.name),9,F))),128))])):r("",!0)])])):r("",!0),m.kecamatanList.length>0?(n(),o("div",j,[a[38]||(a[38]=s("label",{for:"kecamatan"},"Kecamatan :",-1)),s("div",O,[l(s("input",{id:"kecamatan",ref:"kecamatanInputEl",type:"text","onUpdate:modelValue":a[20]||(a[20]=e=>m.kecamatanInput=e),onInput:a[21]||(a[21]=e=>p.handleKecamatanInput(e)),onFocus:a[22]||(a[22]=(...e)=>p.handleKecamatanFocus&&p.handleKecamatanFocus(...e)),onBlur:a[23]||(a[23]=(...e)=>p.handleKecamatanBlur&&p.handleKecamatanBlur(...e)),onKeyup:a[24]||(a[24]=(...e)=>p.handleKecamatanKeyup&&p.handleKecamatanKeyup(...e)),onCompositionstart:a[25]||(a[25]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[26]||(a[26]=(...e)=>p.handleKecamatanCompositionEnd&&p.handleKecamatanCompositionEnd(...e)),placeholder:"Ketik untuk mencari kecamatan",required:""},null,544),[[d,m.kecamatanInput]]),m.isLoadingKecamatan?(n(),o("div",R," Loading... ")):r("",!0),m.showKecamatanSuggestions&&p.filteredKecamatanList.length?(n(),o("div",J,[(n(!0),o(c,null,h(p.filteredKecamatanList,e=>(n(),o("div",{key:e.code,class:"suggestion-item",onClick:a=>p.selectKecamatan(e)},u(e.name),9,H))),128))])):r("",!0)])])):r("",!0),m.kelurahanList.length>0?(n(),o("div",M,[a[39]||(a[39]=s("label",{for:"kelurahan"},"Desa / Kel. :",-1)),s("div",V,[l(s("input",{id:"kelurahan",ref:"kelurahanInputEl",type:"text","onUpdate:modelValue":a[27]||(a[27]=e=>m.kelurahanInput=e),onInput:a[28]||(a[28]=e=>p.handleKelurahanInput(e)),onFocus:a[29]||(a[29]=(...e)=>p.handleKelurahanFocus&&p.handleKelurahanFocus(...e)),onBlur:a[30]||(a[30]=(...e)=>p.handleKelurahanBlur&&p.handleKelurahanBlur(...e)),onKeyup:a[31]||(a[31]=(...e)=>p.handleKelurahanKeyup&&p.handleKelurahanKeyup(...e)),onCompositionstart:a[32]||(a[32]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[33]||(a[33]=(...e)=>p.handleKelurahanCompositionEnd&&p.handleKelurahanCompositionEnd(...e)),placeholder:"Ketik untuk mencari kelurahan/desa",required:""},null,544),[[d,m.kelurahanInput]]),m.isLoadingKelurahan?(n(),o("div",B," Loading... ")):r("",!0),m.showKelurahanSuggestions&&p.filteredKelurahanList.length?(n(),o("div",z,[(n(!0),o(c,null,h(p.filteredKelurahanList,e=>(n(),o("div",{key:e.code,class:"suggestion-item",onClick:a=>p.selectKelurahan(e)},u(e.name),9,q))),128))])):r("",!0)])])):r("",!0)])}],["__scopeId","data-v-b6cce062"]])},methods:{updateAlamatTinggal(e){let a=e;if(null==e)console.log("PersonalInfoForm received null/undefined alamat_tinggal"),a="";else if(e&&e.target&&void 0!==e.target.value)a=e.target.value;else{if(e&&"object"==typeof e&&(e.constructor&&"InputEvent"===e.constructor.name||e instanceof Event))return void console.log("PersonalInfoForm received InputEvent, ignoring");if("string"!=typeof e){console.log("PersonalInfoForm received non-string value, converting:",e);try{a=String(e)}catch(t){return void console.error("Failed to convert value to string:",t)}}}console.log("PersonalInfoForm updating alamat_tinggal to:",a),this.formData.alamat_tinggal!==a&&(this.formData.alamat_tinggal=a,this.$forceUpdate())}},watch:{"formData.alamat_tinggal":e=>{console.log("PersonalInfoForm detected alamat_tinggal change:",e)}},mounted(){console.log("PersonalInfoForm mounted with alamat_tinggal:",this.formData.alamat_tinggal),null!==this.formData.alamat_tinggal&&void 0!==this.formData.alamat_tinggal||(this.formData.alamat_tinggal="",console.log("Initialized empty alamat_tinggal")),this.$nextTick(()=>{this.$forceUpdate()})}},[["render",function(e,a,t,i,r,c){const h=g("AlamatTinggalForm");return n(),o("div",null,[a[12]||(a[12]=s("div",{class:"section-title"},"Data Pribadi",-1)),a[13]||(a[13]=s("label",{for:"nama_lengkap"},"Nama Lengkap",-1)),l(s("input",{id:"nama_lengkap",type:"text","onUpdate:modelValue":a[0]||(a[0]=e=>t.formData.nama_lengkap=e),placeholder:"Nama Lengkap",required:""},null,512),[[d,t.formData.nama_lengkap]]),a[14]||(a[14]=s("label",{for:"nama_panggilan"},"Nama Panggilan",-1)),l(s("input",{id:"nama_panggilan",type:"text","onUpdate:modelValue":a[1]||(a[1]=e=>t.formData.nama_panggilan=e),placeholder:"Nama Panggilan",required:""},null,512),[[d,t.formData.nama_panggilan]]),s("div",G,[a[7]||(a[7]=s("label",{for:"jenis_kelamin"},"Jenis Kelamin",-1)),l(s("select",{id:"jenis_kelamin","onUpdate:modelValue":a[2]||(a[2]=e=>t.formData.jenis_kelamin=e),required:""},[...a[6]||(a[6]=[s("option",{value:"",disabled:"",selected:""},"Pilih Jenis Kelamin",-1),s("option",{value:"LAKI-LAKI"},"LAKI-LAKI",-1),s("option",{value:"PEREMPUAN"},"PEREMPUAN",-1)])],512),[[m,t.formData.jenis_kelamin]])]),s("div",W,[a[8]||(a[8]=s("label",{for:"kelahiran_tempat"},"Tempat Lahir",-1)),l(s("input",{id:"kelahiran_tempat",type:"text","onUpdate:modelValue":a[3]||(a[3]=e=>t.formData.kelahiran_tempat=e),placeholder:"Tempat Lahir",required:""},null,512),[[d,t.formData.kelahiran_tempat]])]),s("div",Y,[a[9]||(a[9]=s("label",{for:"kelahiran_tanggal"},"Tanggal Lahir",-1)),l(s("input",{id:"kelahiran_tanggal",type:"date","onUpdate:modelValue":a[4]||(a[4]=e=>t.formData.kelahiran_tanggal=e),required:""},null,512),[[d,t.formData.kelahiran_tanggal]])]),s("div",Q,[a[10]||(a[10]=s("label",{for:"alamat_tinggal"},"Alamat Tinggal (Lengkap)",-1)),s("div",X,[p(h,{id:"alamat_tinggal",value:t.formData.alamat_tinggal,onInput:c.updateAlamatTinggal},null,8,["value","onInput"])])]),s("div",Z,[a[11]||(a[11]=s("label",{for:"nomor_hape"},"Nomor HP",-1)),l(s("input",{id:"nomor_hape",type:"tel","onUpdate:modelValue":a[5]||(a[5]=e=>t.formData.nomor_hape=e),placeholder:"Nomor HP"},null,512),[[d,t.formData.nomor_hape]])])])}],["__scopeId","data-v-8aa95022"]]),ae={class:"form-group"},te={class:"form-group"},oe={class:"form-group"},ie={class:"form-group"},ne={class:"form-group"},se={class:"form-group"},re=t({props:{formData:{type:Object,required:!0}}},[["render",function(e,a,t,i,r,c){return n(),o("div",null,[a[14]||(a[14]=s("div",{class:"section-title"},"Data Orang Tua",-1)),s("div",ae,[a[6]||(a[6]=s("label",{for:"nama_ayah"},"Nama Ayah",-1)),l(s("input",{id:"nama_ayah",type:"text","onUpdate:modelValue":a[0]||(a[0]=e=>t.formData.nama_ayah=e),placeholder:"Nama Ayah",required:""},null,512),[[d,t.formData.nama_ayah]])]),s("div",te,[a[8]||(a[8]=s("label",{for:"status_ayah"},"Status Ayah",-1)),l(s("select",{id:"status_ayah","onUpdate:modelValue":a[1]||(a[1]=e=>t.formData.status_ayah=e),required:""},[...a[7]||(a[7]=[s("option",{value:"",disabled:"",selected:""},"Pilih",-1),s("option",{value:"SUDAH NGAJI"},"SUDAH NGAJI",-1),s("option",{value:"BELUM NGAJI"},"BELUM NGAJI",-1)])],512),[[m,t.formData.status_ayah]])]),s("div",oe,[a[9]||(a[9]=s("label",{for:"nomor_hape_ayah"},"Nomor HP Ayah",-1)),l(s("input",{id:"nomor_hape_ayah",type:"tel","onUpdate:modelValue":a[2]||(a[2]=e=>t.formData.nomor_hape_ayah=e),placeholder:"Nomor HP Ayah"},null,512),[[d,t.formData.nomor_hape_ayah]])]),s("div",ie,[a[10]||(a[10]=s("label",{for:"nama_ibu"},"Nama Ibu",-1)),l(s("input",{id:"nama_ibu",type:"text","onUpdate:modelValue":a[3]||(a[3]=e=>t.formData.nama_ibu=e),placeholder:"Nama Ibu",required:""},null,512),[[d,t.formData.nama_ibu]])]),s("div",ne,[a[12]||(a[12]=s("label",{for:"status_ibu"},"Status Ibu",-1)),l(s("select",{id:"status_ibu","onUpdate:modelValue":a[4]||(a[4]=e=>t.formData.status_ibu=e),required:""},[...a[11]||(a[11]=[s("option",{value:"",disabled:"",selected:""},"Pilih",-1),s("option",{value:"SUDAH NGAJI"},"SUDAH NGAJI",-1),s("option",{value:"BELUM NGAJI"},"BELUM NGAJI",-1)])],512),[[m,t.formData.status_ibu]])]),s("div",se,[a[13]||(a[13]=s("label",{for:"nomor_hape_ibu"},"Nomor HP Ibu",-1)),l(s("input",{id:"nomor_hape_ibu",type:"tel","onUpdate:modelValue":a[5]||(a[5]=e=>t.formData.nomor_hape_ibu=e),placeholder:"Nomor HP Ibu"},null,512),[[d,t.formData.nomor_hape_ibu]])])])}]]),le={class:"form-group"},de={class:"suggestions-container"},ce={key:0,class:"selected-hobi-container"},he=["onClick"],ue={key:1,class:"suggestions"},me=["onClick"],pe=t({mixins:[K],props:{hobiOptions:{type:Array,required:!0},selectedHobi:{type:Array,required:!0},placeholder:{type:String,default:"Ketik untuk mencari hobi"}},computed:{filteredHobi(){const e=this.inputValue.toLowerCase();return e?this.hobiOptions.filter(a=>a.kategori.toLowerCase().includes(e)||a.hobi.toLowerCase().includes(e)||a.detail_hobi?.toLowerCase().includes(e)):[]}},methods:{selectHobi(e){this.selectedHobi.some(a=>a.kategori===e.kategori&&a.hobi===e.hobi)||this.selectedHobi.push({kategori:e.kategori,hobi:e.hobi,detail_hobi:e.detail_hobi}),this.inputValue="",this.showSuggestions=!1},removeHobi(e){this.selectedHobi.splice(e,1)}}},[["render",function(e,a,t,i,m,p){return n(),o("div",le,[a[7]||(a[7]=s("label",{for:"hobi"},"Hobi (pilih satu atau lebih)",-1)),s("div",de,[t.selectedHobi.length>0?(n(),o("div",ce,[(n(!0),o(c,null,h(t.selectedHobi,(e,a)=>(n(),o("div",{key:a,class:"hobi-tag"},[f(u(e.kategori)+": "+u(e.hobi)+" ",1),s("span",{class:"remove-hobi",onClick:e=>p.removeHobi(a)},"×",8,he)]))),128))])):r("",!0),l(s("input",{id:"hobi",ref:"inputEl",type:"text","onUpdate:modelValue":a[0]||(a[0]=a=>e.inputValue=a),onInput:a[1]||(a[1]=(...a)=>e.handleInput&&e.handleInput(...a)),onFocus:a[2]||(a[2]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:a[3]||(a[3]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onKeyup:a[4]||(a[4]=(...a)=>e.handleKeyup&&e.handleKeyup(...a)),onCompositionstart:a[5]||(a[5]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[6]||(a[6]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),placeholder:"Ketik untuk mencari hobi"},null,544),[[d,e.inputValue]]),e.showSuggestions&&p.filteredHobi.length?(n(),o("div",ue,[(n(!0),o(c,null,h(p.filteredHobi,(e,a)=>(n(),o("div",{key:a,class:"suggestion-item",onClick:b(a=>p.selectHobi(e),["stop"])},[s("strong",null,u(e.kategori),1),f(": "+u(e.hobi),1)],8,me))),128))])):r("",!0)])])}]]),ge={mixins:[K],props:{formData:{type:Object,required:!0},kelompokOptions:{type:Object,required:!0},flattenedKelompok:{type:Array,required:!0},dataLoaded:{type:Boolean,required:!0},isLoading:{type:Boolean,required:!0},loadError:{type:String,default:null},placeholder:{type:String,default:"Ketik untuk mencari kelompok atau desa."}},computed:{filteredKelompok(){const e=this.inputValue.toLowerCase();if(!e||!this.dataLoaded)return[];const a=new Set;return this.flattenedKelompok.filter(({kelompok:t,desa:o})=>{const i=`${t.toLowerCase()}-${o.toLowerCase()}`;return!(!t.toLowerCase().includes(e)&&!o.toLowerCase().includes(e)||a.has(i)||(a.add(i),0))})}},methods:{handleInput(){this.showSuggestions=!0,this.formData.sambung_desa="",this.formData.sambung_kelompok="",this.$emit("input-change")},selectKelompok({kelompok:e,desa:a}){this.inputValue=`${e} (${a})`,this.formData.sambung_kelompok=e,this.formData.sambung_desa=a,this.showSuggestions=!1}}},fe={class:"form-group"},be={class:"suggestions-container"},ke=["placeholder"],ve={key:0,class:"loading-indicator"},we={key:1,class:"error-message"},ye={key:2,class:"suggestions"},Ke=["onClick"],xe=t(ge,[["render",function(e,a,t,i,m,p){return n(),o("div",fe,[a[7]||(a[7]=s("label",{for:"kelompok"},"Alamat Sambung",-1)),s("div",be,[l(s("input",{id:"kelompok",ref:"inputEl",type:"text","onUpdate:modelValue":a[0]||(a[0]=a=>e.inputValue=a),onInput:a[1]||(a[1]=(...e)=>p.handleInput&&p.handleInput(...e)),onFocus:a[2]||(a[2]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:a[3]||(a[3]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onKeyup:a[4]||(a[4]=(...a)=>e.handleKeyup&&e.handleKeyup(...a)),onCompositionstart:a[5]||(a[5]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[6]||(a[6]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),placeholder:t.placeholder,required:""},null,40,ke),[[d,e.inputValue]]),t.isLoading?(n(),o("div",ve,"Loading...")):r("",!0),t.loadError?(n(),o("div",we,u(t.loadError),1)):r("",!0),e.showSuggestions&&p.filteredKelompok.length?(n(),o("div",ye,[(n(!0),o(c,null,h(p.filteredKelompok,(e,a)=>(n(),o("div",{key:a,class:"suggestion-item",onClick:a=>p.selectKelompok(e)},u(e.kelompok)+" ("+u(e.desa)+") ",9,Ke))),128))])):r("",!0)])])}]]),Se={class:"form-group"},_e={class:"suggestions-container"},De=["placeholder"],Ce={key:0,class:"suggestions"},Ie=["onClick"],Pe=t({mixins:[K],props:{formData:{type:Object,required:!0},sekolahKelasOptions:{type:Array,required:!0},placeholder:{type:String,default:"Ketik saja ..."}},computed:{filteredSekolahKelas(){const e=this.inputValue.toLowerCase();return e?this.sekolahKelasOptions.filter(({jenjang:a,kelas:t})=>a.toLowerCase().includes(e)||t.toLowerCase().includes(e)):this.sekolahKelasOptions}},methods:{handleInput(){this.showSuggestions=!0,this.formData.sekolah_kelas=""},selectSekolahKelas({jenjang:e,kelas:a}){const t=`${e} ${a}`;this.inputValue=t,this.formData.sekolah_kelas=t,this.showSuggestions=!1}}},[["render",function(e,a,t,i,m,p){return n(),o("div",Se,[a[7]||(a[7]=s("label",{for:"sekolah_kelas"},"Kelas Sekolah / Kerja / Wirausaha / Muballigh",-1)),s("div",_e,[l(s("input",{id:"sekolah_kelas",ref:"inputEl",type:"text","onUpdate:modelValue":a[0]||(a[0]=a=>e.inputValue=a),onInput:a[1]||(a[1]=(...e)=>p.handleInput&&p.handleInput(...e)),onFocus:a[2]||(a[2]=(...a)=>e.handleFocus&&e.handleFocus(...a)),onBlur:a[3]||(a[3]=(...a)=>e.handleBlur&&e.handleBlur(...a)),onKeyup:a[4]||(a[4]=(...a)=>e.handleKeyup&&e.handleKeyup(...a)),onCompositionstart:a[5]||(a[5]=(...a)=>e.handleCompositionStart&&e.handleCompositionStart(...a)),onCompositionend:a[6]||(a[6]=(...a)=>e.handleCompositionEnd&&e.handleCompositionEnd(...a)),placeholder:t.placeholder},null,40,De),[[d,e.inputValue]]),e.showSuggestions&&p.filteredSekolahKelas.length?(n(),o("div",Ce,[(n(!0),o(c,null,h(p.filteredSekolahKelas,(e,a)=>(n(),o("div",{key:a,class:"suggestion-item",onClick:a=>p.selectSekolahKelas(e)},u(e.jenjang)+" "+u(e.kelas),9,Ie))),128))])):r("",!0)])])}]]),Ee={props:{formData:{type:Object,required:!0},selectedHobi:{type:Array,default:()=>[]},selectedPhoto:{type:File,default:null},isSubmitting:{type:Boolean,default:!1}},computed:{photoPreviewUrl(){return this.selectedPhoto?URL.createObjectURL(this.selectedPhoto):null}},mounted(){console.log("ReviewDataSection mounted with alamat_tinggal:",this.formData.alamat_tinggal),this.savedAddress=this.formData.alamat_tinggal||""},methods:{formatDate:e=>e?new Date(e).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"}):"",handleSubmit(){this.$emit("submit")},handleEdit(){console.log("ReviewDataSection - alamat_tinggal before edit:",this.formData.alamat_tinggal),this.savedAddress=this.formData.alamat_tinggal||"",this.$emit("edit",{savedAddress:this.savedAddress})}},data:()=>({savedAddress:""})},Le={class:"review-data"},Ae={class:"review-item"},Ue={class:"review-item"},$e={class:"review-item"},Te={class:"review-item"},Ne={class:"review-item"},Fe={class:"review-item"},je={key:0,class:"review-item"},Oe={class:"photo-preview"},Re=["src"],Je={class:"review-item"},He={class:"review-item"},Me={key:1,class:"review-item"},Ve={class:"review-item"},Be={class:"review-item"},ze={class:"review-item"},qe={class:"review-item"},Ge={class:"review-item"},We={class:"review-item"},Ye={class:"review-actions"},Qe=["disabled"],Xe=t(Ee,[["render",function(e,a,t,i,l,d){return n(),o("div",Le,[a[18]||(a[18]=s("div",{class:"section-title"},"Review Data",-1)),s("div",Ae,[a[2]||(a[2]=s("strong",null,"Nama Lengkap:",-1)),f(" "+u(t.formData.nama_lengkap),1)]),s("div",Ue,[a[3]||(a[3]=s("strong",null,"Nama Panggilan:",-1)),f(" "+u(t.formData.nama_panggilan),1)]),s("div",$e,[a[4]||(a[4]=s("strong",null,"Jenis Kelamin:",-1)),f(" "+u(t.formData.jenis_kelamin),1)]),s("div",Te,[a[5]||(a[5]=s("strong",null,"Tempat, Tanggal Lahir:",-1)),f(" "+u(t.formData.kelahiran_tempat)+", "+u(d.formatDate(t.formData.kelahiran_tanggal)),1)]),s("div",Ne,[a[6]||(a[6]=s("strong",null,"Alamat Tinggal:",-1)),f(" "+u(t.formData.alamat_tinggal||"Tidak ada"),1)]),s("div",Fe,[a[7]||(a[7]=s("strong",null,"Nomor HP:",-1)),f(" "+u(t.formData.nomor_hape||"-"),1)]),t.selectedPhoto?(n(),o("div",je,[a[8]||(a[8]=s("strong",null,"Foto:",-1)),s("div",Oe,[s("img",{src:d.photoPreviewUrl,alt:"Preview foto",class:"review-photo"},null,8,Re)])])):r("",!0),s("div",Je,[a[9]||(a[9]=s("strong",null,"Desa/Kelompok:",-1)),f(" "+u(t.formData.sambung_desa)+" / "+u(t.formData.sambung_kelompok),1)]),s("div",He,[a[10]||(a[10]=s("strong",null,"Sekolah & Kelas:",-1)),f(" "+u(t.formData.sekolah_kelas||"-"),1)]),t.selectedHobi.length>0?(n(),o("div",Me,[a[11]||(a[11]=s("strong",null,"Hobi:",-1)),(n(!0),o(c,null,h(t.selectedHobi,(e,a)=>(n(),o("div",{key:a,class:"review-hobi-item"}," - "+u(e.kategori)+": "+u(e.hobi),1))),128))])):r("",!0),s("div",Ve,[a[12]||(a[12]=s("strong",null,"Nama Ayah:",-1)),f(" "+u(t.formData.nama_ayah),1)]),s("div",Be,[a[13]||(a[13]=s("strong",null,"Status Ayah:",-1)),f(" "+u(t.formData.status_ayah),1)]),s("div",ze,[a[14]||(a[14]=s("strong",null,"No. HP Ayah:",-1)),f(" "+u(t.formData.nomor_hape_ayah||"-"),1)]),s("div",qe,[a[15]||(a[15]=s("strong",null,"Nama Ibu:",-1)),f(" "+u(t.formData.nama_ibu),1)]),s("div",Ge,[a[16]||(a[16]=s("strong",null,"Status Ibu:",-1)),f(" "+u(t.formData.status_ibu),1)]),s("div",We,[a[17]||(a[17]=s("strong",null,"No. HP Ibu:",-1)),f(" "+u(t.formData.nomor_hape_ibu||"-"),1)]),s("div",Ye,[s("button",{onClick:a[0]||(a[0]=(...e)=>d.handleSubmit&&d.handleSubmit(...e)),disabled:t.isSubmitting},u(t.isSubmitting?"Mengirim...":"Kirim Data"),9,Qe),s("button",{onClick:a[1]||(a[1]=(...e)=>d.handleEdit&&d.handleEdit(...e)),class:"secondary"},"Edit Data")])])}],["__scopeId","data-v-7761e3a6"]]),Ze={name:"PhotoUpload",props:{modelValue:{type:File,default:null},existingPhotoFilename:{type:String,default:null},apiKey:{type:String,default:null}},emits:["update:modelValue"],data:()=>({previewUrl:null,showCamera:!1,isCapturing:!1,cameraSupported:!1,stream:null,errorMessage:null,currentFacingMode:"user",hasMultipleCameras:!1,loadingExistingPhoto:!1,existingPhotoUrl:null}),computed:{displayPreviewUrl(){return this.previewUrl||this.existingPhotoUrl}},async mounted(){await this.checkCameraSupport(),this.existingPhotoFilename&&this.apiKey&&await this.loadExistingPhoto()},beforeUnmount(){this.stopCamera(),this.existingPhotoUrl&&URL.revokeObjectURL(this.existingPhotoUrl)},watch:{existingPhotoFilename:{handler(e){e&&this.apiKey?this.loadExistingPhoto():this.clearExistingPhoto()},immediate:!0},apiKey:{handler(e){e&&this.existingPhotoFilename&&this.loadExistingPhoto()}}},methods:{async checkCameraSupport(){if(this.cameraSupported=!(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia),this.cameraSupported)try{const e=(await navigator.mediaDevices.enumerateDevices()).filter(e=>"videoinput"===e.kind);this.hasMultipleCameras=e.length>1}catch(e){console.error("Error checking camera devices:",e)}},async openCamera(){if(this.cameraSupported){this.isCapturing=!0,this.errorMessage=null;try{this.stream=await navigator.mediaDevices.getUserMedia({video:{facingMode:this.currentFacingMode,width:{ideal:640},height:{ideal:480}}}),this.showCamera=!0,this.$nextTick(()=>{this.$refs.videoElement&&(this.$refs.videoElement.srcObject=this.stream)})}catch(e){console.error("Error accessing camera:",e),this.errorMessage="Tidak dapat mengakses kamera. Pastikan izin kamera telah diberikan."}finally{this.isCapturing=!1}}else this.errorMessage="Kamera tidak didukung di perangkat ini"},closeCamera(){this.stopCamera(),this.showCamera=!1},stopCamera(){this.stream&&(this.stream.getTracks().forEach(e=>e.stop()),this.stream=null)},async switchCamera(){if(this.hasMultipleCameras){this.stopCamera(),this.currentFacingMode="user"===this.currentFacingMode?"environment":"user";try{this.stream=await navigator.mediaDevices.getUserMedia({video:{facingMode:this.currentFacingMode,width:{ideal:640},height:{ideal:480}}}),this.$nextTick(()=>{this.$refs.videoElement&&(this.$refs.videoElement.srcObject=this.stream)})}catch(e){console.error("Error switching camera:",e),this.errorMessage="Tidak dapat mengganti kamera.",this.currentFacingMode="user"===this.currentFacingMode?"environment":"user"}}},capturePhoto(){const e=this.$refs.videoElement,a=this.$refs.canvasElement;if(!e||!a)return;const t=a.getContext("2d");a.width=e.videoWidth,a.height=e.videoHeight,t.drawImage(e,0,0,a.width,a.height),a.toBlob(e=>{if(e){const a=new File([e],"camera-photo.png",{type:"image/png"});this.setPhoto(a)}},"image/png",.9),this.closeCamera()},handleFileSelect(e){const a=e.target.files[0];a&&this.setPhoto(a)},setPhoto(e){e.size>2097152?this.errorMessage="Ukuran file terlalu besar. Maksimal 2MB.":"image/png"===e.type?(this.errorMessage=null,this.previewUrl=URL.createObjectURL(e),this.$emit("update:modelValue",e)):this.errorMessage="File harus berupa gambar PNG."},removePhoto(){this.previewUrl&&(URL.revokeObjectURL(this.previewUrl),this.previewUrl=null),this.$refs.fileInput&&(this.$refs.fileInput.value=""),this.$emit("update:modelValue",null),this.errorMessage=null},async loadExistingPhoto(){if(this.existingPhotoFilename&&this.apiKey){this.loadingExistingPhoto=!0,this.clearExistingPhoto();try{const e=await fetch(`/api/biodata/generus/foto/${this.existingPhotoFilename}`,{headers:{Authorization:`ApiKey ${this.apiKey}`}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const a=await e.blob();this.existingPhotoUrl=URL.createObjectURL(a)}catch(e){console.error("Error loading existing photo:",e),this.errorMessage="Gagal memuat foto yang sudah ada"}finally{this.loadingExistingPhoto=!1}}},clearExistingPhoto(){this.existingPhotoUrl&&(URL.revokeObjectURL(this.existingPhotoUrl),this.existingPhotoUrl=null)}}},ea={class:"photo-upload-container"},aa={key:0,class:"photo-preview"},ta=["src"],oa={key:0,class:"existing-photo-label"},ia={key:1,class:"loading-photo"},na={key:2,class:"upload-options"},sa={class:"camera-section"},ra=["disabled"],la={key:0,class:"camera-warning"},da={class:"file-section"},ca={class:"camera-header"},ha={class:"camera-controls-header"},ua={class:"camera-viewport"},ma={ref:"videoElement",autoplay:"",playsinline:"",class:"camera-video"},pa={ref:"canvasElement",class:"camera-canvas",style:{display:"none"}},ga={class:"camera-controls"},fa={key:4,class:"error-message"},ba={class:"form-subtitle"},ka={key:0},va={key:2,class:"confirmation-container"};e("default",t({components:{FormContainer:y,PersonalInfoForm:ee,ParentInfoForm:re,HobiSelector:pe,KelompokSelector:xe,SekolahKelasSelector:Pe,ReviewDataSection:Xe,PhotoUpload:t(Ze,[["render",function(e,a,t,i,l,d){return n(),o("div",ea,[a[12]||(a[12]=s("div",{class:"section-title"},"Foto Profil",-1)),d.displayPreviewUrl?(n(),o("div",aa,[s("img",{src:d.displayPreviewUrl,alt:"Preview foto",class:"preview-image"},null,8,ta),s("button",{onClick:a[0]||(a[0]=(...e)=>d.removePhoto&&d.removePhoto(...e)),class:"remove-photo-btn",type:"button"}," × Hapus Foto "),l.existingPhotoUrl&&!l.previewUrl?(n(),o("div",oa," Foto yang sudah ada ")):r("",!0)])):r("",!0),l.loadingExistingPhoto?(n(),o("div",ia,[...a[9]||(a[9]=[s("p",null,"Memuat foto yang sudah ada...",-1)])])):r("",!0),d.displayPreviewUrl||l.loadingExistingPhoto?r("",!0):(n(),o("div",na,[s("div",sa,[s("button",{onClick:a[1]||(a[1]=(...e)=>d.openCamera&&d.openCamera(...e)),disabled:l.isCapturing||!l.cameraSupported,class:"camera-btn",type:"button"}," 📷 "+u(l.isCapturing?"Membuka Kamera...":"Ambil Foto"),9,ra),l.cameraSupported?r("",!0):(n(),o("p",la," Kamera tidak tersedia di perangkat ini "))]),s("div",da,[a[10]||(a[10]=s("label",{for:"photo-file",class:"file-upload-label"}," 📁 Pilih File Foto (PNG, Max 2MB) ",-1)),s("input",{id:"photo-file",type:"file",accept:"image/png",onChange:a[2]||(a[2]=(...e)=>d.handleFileSelect&&d.handleFileSelect(...e)),class:"file-input",ref:"fileInput"},null,544)])])),l.showCamera?(n(),o("div",{key:3,class:"camera-modal",onClick:a[8]||(a[8]=(...e)=>d.closeCamera&&d.closeCamera(...e))},[s("div",{class:"camera-content",onClick:a[7]||(a[7]=b(()=>{},["stop"]))},[s("div",ca,[a[11]||(a[11]=s("h3",null,"Ambil Foto",-1)),s("div",ha,[l.hasMultipleCameras?(n(),o("button",{key:0,onClick:a[3]||(a[3]=(...e)=>d.switchCamera&&d.switchCamera(...e)),class:"switch-camera-btn",type:"button"}," 🔄 Ganti Kamera ")):r("",!0),s("button",{onClick:a[4]||(a[4]=(...e)=>d.closeCamera&&d.closeCamera(...e)),class:"close-btn",type:"button"}," ✕ ")])]),s("div",ua,[s("video",ma,null,512),s("canvas",pa,null,512)]),s("div",ga,[s("button",{onClick:a[5]||(a[5]=(...e)=>d.capturePhoto&&d.capturePhoto(...e)),class:"capture-btn",type:"button"}," 📸 Ambil Foto "),s("button",{onClick:a[6]||(a[6]=(...e)=>d.closeCamera&&d.closeCamera(...e)),class:"cancel-btn",type:"button"}," Batal ")])])])):r("",!0),l.errorMessage?(n(),o("div",fa,u(l.errorMessage),1)):r("",!0)])}],["__scopeId","data-v-6280c6fb"]])},mixins:[{methods:{async fetchDataFromApi(e,a,t,o,i){console.log(`Fetching data from ${e}...`);try{const t=await fetch(e);if(!t.ok)throw new Error("Network response was not ok");const o=await t.json(),n=a?a(o):o;return i.options=n,i.isLoading=!1,i.dataLoaded=!0,i.loadError=null,console.log(`Data loaded successfully from ${e}`),n}catch(n){return console.error(`Error fetching data from ${e}:`,n),i.loadError=t||"Gagal memuat data. Silakan muat ulang halaman.",i.isLoading=!1,i.dataLoaded=!1,o&&setTimeout(o,5e3),null}},async fetchKelompokData(e){if(!e)return console.error("Parameter data tidak ditemukan"),this.kelompokData.loadError="Parameter data tidak ditemukan",void(this.kelompokData.isLoading=!1);const a=`/api/data/daerah/${encodeURIComponent(e)}/`;await this.fetchDataFromApi(a,a=>{const t={};let o=null;return a.forEach(a=>{t[a.ranah]||(t[a.ranah]=[]),t[a.ranah].push(a.detail_ranah),e&&(a.ranah.toLowerCase().includes(e.toLowerCase())||a.detail_ranah.toLowerCase().includes(e.toLowerCase()))&&(o={desa:a.ranah,kelompok:a.detail_ranah})}),o&&setTimeout(()=>{this.formData.sambung_desa=o.desa,this.formData.sambung_kelompok=o.kelompok},0),t},"Gagal memuat data kelompok. Silakan muat ulang halaman.",()=>this.fetchKelompokData(e),this.kelompokData)},async fetchHobiData(){await this.fetchDataFromApi("/api/data/hobi",null,"Gagal memuat data hobi. Silakan muat ulang halaman.",()=>this.fetchHobiData(),this.hobiData)},async fetchSekolahKelasData(){try{const e=await fetch("/api/data/kelas-sekolah");if(!e.ok)throw new Error("Network response was not ok");const a=await e.json();this.sekolahKelasOptions=a}catch(e){console.error("Error fetching sekolah/kelas data:",e)}},validateFormFields(){const{sambung_kelompok:e,sambung_desa:a}=this.formData;return e&&a?!!this.flattenedKelompok.some(t=>t.kelompok===e&&t.desa===a)||(alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1):(alert("Pilihan Desa & Kelompok wajib diisi. Silakan ketik dan pilih dari daftar yang muncul."),!1)},showReviewData(){this.validateFormFields()&&(this.showReview=!0)},editForm(e){console.log("EditForm called with alamat_tinggal:",this.formData.alamat_tinggal);const a=e&&e.savedAddress?e.savedAddress:this.formData.alamat_tinggal;this.showReview=!1,this.$nextTick(()=>{!a||this.formData.alamat_tinggal&&this.formData.alamat_tinggal===a||(console.log("Restoring alamat_tinggal to:",a),this.formData.alamat_tinggal=a),console.log("After toggling, alamat_tinggal is:",this.formData.alamat_tinggal)})},async submitToAPI(){if(!this.isSubmitting){if(console.log("=== SUBMISSION DEBUG START ==="),console.log("API Key present:",!!this.apiKey),!this.formData.sambung_desa||!this.formData.sambung_kelompok)return console.error("Missing required Desa/Kelompok fields"),void alert("Pilihan Desa & Kelompok wajib diisi sebelum mengirim data.");if(!this.apiKey)return console.error("API key missing, aborting submission"),alert("API key diperlukan. Silakan gunakan URL dengan parameter key."),void(this.showApiKeyError=!0);this.isSubmitting=!0,console.log("Starting form submission process"),console.log("Original form data:",JSON.stringify(this.formData)),console.log("Selected hobi items:",this.selectedHobi),console.log("CRITICAL FIELDS CHECK:"),console.log("sambung_desa:",this.formData.sambung_desa),console.log("sambung_kelompok:",this.formData.sambung_kelompok);try{const a=new FormData;if(console.log("Created new FormData object"),a.append("sambung_desa",this.formData.sambung_desa),a.append("sambung_kelompok",this.formData.sambung_kelompok),console.log("Added sambung fields to FormData"),this.selectedHobi.length>0){console.log("Processing hobi data, found",this.selectedHobi.length,"selected items");const e={};this.selectedHobi.forEach(a=>{e[a.kategori]||(e[a.kategori]=[]),e[a.kategori].push(a.hobi)});const t={};for(const[a,i]of Object.entries(e))t[a]=i.join(", ");const o=JSON.stringify(t);a.append("hobi",o),console.log("Added hobi JSON string to FormData")}else a.append("hobi",JSON.stringify({})),console.log("No hobi selected, adding empty JSON object");console.log("Adding other form fields to FormData");for(const e in this.formData)["hobi","sambung_desa","sambung_kelompok"].includes(e)||null!==this.formData[e]&&void 0!==this.formData[e]&&""!==this.formData[e]&&a.append(e,this.formData[e]);const t="/api/biodata/generus";console.log("Sending request to:",t);const o=await fetch(t,{method:"POST",headers:{Authorization:"ApiKey "+this.apiKey},body:a});if(console.log("Response status:",o.status,o.statusText),!o.ok){const a=await o.text();console.error("Error response from server:",a);try{const e=JSON.parse(a);console.error("Parsed error response:",e)}catch(e){console.log("Error response is not valid JSON")}throw new Error(`Server error: ${o.status} - ${a}`)}const i=await o.json();console.log("Success response:",i),this.showReview=!1,this.showSuccess=!0,console.log("Form submitted successfully")}catch(a){console.error("Error in submission process:",a),console.error("Error stack:",a.stack),alert("Terjadi kesalahan saat mengirim data. Mohon coba lagi.")}finally{this.isSubmitting=!1,console.log("=== SUBMISSION DEBUG END ===")}}},resetForm(){window.location.reload()},getCurrentDate(){const e=new Date;return`${e.getFullYear()}-${String(e.getMonth()+1).padStart(2,"0")}-${String(e.getDate()).padStart(2,"0")}`},formatDate:e=>e?new Date(e).toLocaleDateString("id-ID",{day:"numeric",month:"long",year:"numeric"}):"",handleKelompokInputChange(){if(!this.kelompokData.dataLoaded&&!this.kelompokData.isLoading){console.log("Data not loaded, retrying fetch...");const e=new URLSearchParams(window.location.search).get("data");this.fetchKelompokData(e)}}}}],data(){return{formData:{nama_lengkap:"",nama_panggilan:"",jenis_kelamin:"",kelahiran_tempat:"",kelahiran_tanggal:"",alamat_tinggal:"",pendataan_tanggal:this.getCurrentDate(),sambung_desa:"",sambung_kelompok:"",hobi:"",sekolah_kelas:"",nomor_hape:"",nama_ayah:"",nama_ibu:"",status_ayah:"",status_ibu:"",nomor_hape_ayah:"",nomor_hape_ibu:"",daerah:""},daerahParam:"",selectedPhoto:null,showSuccess:!1,showReview:!1,isSubmitting:!1,apiKey:null,showApiKeyError:!1,kelompokData:{options:{},isLoading:!0,loadError:null,dataLoaded:!1},hobiData:{options:[],isLoading:!0,loadError:null,dataLoaded:!1},sekolahKelasOptions:[],selectedHobi:[]}},computed:{flattenedKelompok(){return Object.entries(this.kelompokData.options).flatMap(([e,a])=>a.map(a=>({desa:e,kelompok:a})))}},mounted(){document.title=this.daerahParam?`PENDATAAN GENERUS ${this.daerahParam}`:"PENDATAAN GENERUS",this.formData.pendataan_tanggal=this.getCurrentDate();const e=new URLSearchParams(window.location.search),a=e.get("data");this.daerahParam=e.get("daerah"),this.daerahParam&&(this.formData.daerah=this.daerahParam,document.title=`PENDATAAN GENERUS ${this.daerahParam}`),this.apiKey=e.get("key"),this.fetchKelompokData(a),this.fetchHobiData(),this.fetchSekolahKelasData(),this.$nextTick(()=>{this.formData.alamat_tinggal||(this.formData.alamat_tinggal="")})},methods:{showReviewData(){if(console.log("About to show review with alamat_tinggal:",this.formData.alamat_tinggal),this.formData.alamat_tinggal){const e=this.formData.alamat_tinggal;this.showReview=!0,this.$nextTick(()=>{this.formData.alamat_tinggal||(console.log("Restoring saved alamat_tinggal"),this.formData.alamat_tinggal=e)})}else this.showReview=!0},async submitToAPI(){console.log("=== SUBMISSION DEBUG START ===");try{if(!this.apiKey||""===this.apiKey.trim())return console.error("API key is missing or empty"),this.showApiKeyError=!0,void alert("API key is required but missing from the URL. Please check your link.");this.isSubmitting=!0,this.formData.alamat_tinggal||(console.log("Fixing missing alamat_tinggal"),this.formData.alamat_tinggal="Not specified"),console.log("API Key present:",!!this.apiKey),console.log("API Key length:",this.apiKey.length),console.log("API Key first 4 chars:",this.apiKey?`${this.apiKey.substring(0,4)}...`:"none"),console.log("Starting form submission process");const a={};if(this.selectedHobi.length>0){for(const e of this.selectedHobi)a[e.kategori]||(a[e.kategori]=[]),a[e.kategori].push(String(e.hobi));for(const e of Object.keys(a))a[e]=a[e].join(", ");console.log("Hobi processed:",JSON.stringify(a,null,2))}const t="/api/biodata/generus/",o=new FormData;for(const e in this.formData)null!==this.formData[e]&&void 0!==this.formData[e]&&""!==this.formData[e]&&o.append(e,this.formData[e]);o.append("hobi",JSON.stringify(a)),this.selectedPhoto&&(o.append("foto",this.selectedPhoto),console.log("Photo added to form data:",this.selectedPhoto.name,this.selectedPhoto.size)),console.log("FormData prepared with photo support");const i={Authorization:`ApiKey ${this.apiKey}`};console.log("Headers to be sent:",i),console.log("Using fetch with FormData payload");const n=await fetch(t,{method:"POST",mode:"cors",headers:i,redirect:"manual",body:o,credentials:"include"});if(console.log("Response status:",n.status),!n.ok){const a=await n.text();let t;try{t=JSON.parse(a),console.log("Error response from server:",JSON.stringify(t,null,2))}catch(e){console.log("Error response (not JSON):",a),t={detail:a}}throw 401===n.status?(console.error("Authorization failed - API key may be invalid"),alert("Authorization failed. Please check if your API key is valid and try again.")):alert(`Server error (${n.status}): ${t.detail||"Unknown error"}`),new Error(`Server error: ${n.status} - ${JSON.stringify(t)}`)}console.log("Data submitted successfully!"),this.showSuccess=!0,this.showReview=!1}catch(a){console.error("Error in submission process:",a),console.error("Error stack:",a.stack),a.message.includes("Network error")?alert("Network error occurred. Please check your internet connection and try again."):a.message.includes("API key")?alert("API key error. Please check your access link."):alert("Terjadi kesalahan saat mengirim data. Silakan coba lagi.")}finally{this.isSubmitting=!1,console.log("=== SUBMISSION DEBUG END ===")}},resetForm(){this.formData={nama_lengkap:"",nama_panggilan:"",jenis_kelamin:"",kelahiran_tempat:"",kelahiran_tanggal:"",alamat_tinggal:"",pendataan_tanggal:this.getCurrentDate(),sambung_desa:"",sambung_kelompok:"",hobi:"",sekolah_kelas:"",nomor_hape:"",nama_ayah:"",nama_ibu:"",status_ayah:"",status_ibu:"",nomor_hape_ayah:"",nomor_hape_ibu:"",daerah:this.daerahParam||""},this.selectedHobi=[],this.selectedPhoto=null,this.showSuccess=!1,this.showReview=!1,this.isSubmitting=!1}},watch:{"formData.alamat_tinggal":e=>{console.log("biodata-generus detected alamat_tinggal change:",e)}}},[["render",function(e,a,t,i,l,d){const c=g("PersonalInfoForm"),h=g("KelompokSelector"),m=g("SekolahKelasSelector"),b=g("HobiSelector"),w=g("PhotoUpload"),y=g("ParentInfoForm"),K=g("ReviewDataSection"),x=g("FormContainer");return n(),k(x,null,{default:v(()=>[a[5]||(a[5]=s("div",{class:"form-title"},"FORMULIR PENDATAAN",-1)),s("div",ba,"GENERUS "+u(l.daerahParam||"SKC"),1),l.showReview||l.showSuccess?r("",!0):(n(),o("div",ka,[p(c,{formData:l.formData},null,8,["formData"]),p(h,{formData:l.formData,kelompokOptions:l.kelompokData.options,flattenedKelompok:d.flattenedKelompok,dataLoaded:l.kelompokData.dataLoaded,isLoading:l.kelompokData.isLoading,loadError:l.kelompokData.loadError,onInputChange:e.handleKelompokInputChange},null,8,["formData","kelompokOptions","flattenedKelompok","dataLoaded","isLoading","loadError","onInputChange"]),p(m,{formData:l.formData,sekolahKelasOptions:l.sekolahKelasOptions},null,8,["formData","sekolahKelasOptions"]),p(b,{hobiOptions:l.hobiData.options,selectedHobi:l.selectedHobi},null,8,["hobiOptions","selectedHobi"]),p(w,{modelValue:l.selectedPhoto,"onUpdate:modelValue":a[0]||(a[0]=e=>l.selectedPhoto=e)},null,8,["modelValue"]),a[3]||(a[3]=s("div",{class:"spaci"},null,-1)),p(y,{formData:l.formData},null,8,["formData"]),s("button",{onClick:a[1]||(a[1]=(...e)=>d.showReviewData&&d.showReviewData(...e))},"Review Data")])),l.showReview&&!l.showSuccess?(n(),k(K,{key:1,formData:l.formData,selectedHobi:l.selectedHobi,selectedPhoto:l.selectedPhoto,isSubmitting:l.isSubmitting,onSubmit:d.submitToAPI,onEdit:e.editForm},null,8,["formData","selectedHobi","selectedPhoto","isSubmitting","onSubmit","onEdit"])):r("",!0),l.showSuccess?(n(),o("div",va,[a[4]||(a[4]=s("div",{class:"confirmation-message"},[f(" Data berhasil dikirim!"),s("br"),f(" Alhamdulillah Jazaa Kumullohu Khoiro. ")],-1)),s("button",{onClick:a[2]||(a[2]=(...e)=>d.resetForm&&d.resetForm(...e))},"Isi Formulir Baru")])):r("",!0)]),_:1})}]]))}}});
