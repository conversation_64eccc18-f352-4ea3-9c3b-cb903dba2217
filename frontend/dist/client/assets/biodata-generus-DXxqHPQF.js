import{_ as K}from"./index-DgMiBKWL.js";import{c as u,k as M,o as l,a as o,l as m,m as g,v as f,F as y,n as S,t as h,p as J,b as P,r as w,q as p,s as H,e as $,w as B}from"./vendor-eS0gtEdu.js";const V={},x={class:"form-container"};function q(t,e){return l(),u("div",x,[M(t.$slots,"default",{},void 0)])}const G=K(V,[["render",q],["__scopeId","data-v-a5516b31"]]),j={props:{placeholder:{type:String,default:"Ketik untuk mencari..."}},data(){return{inputValue:"",showSuggestions:!1,isComposing:!1}},methods:{handleInput(){this.showSuggestions=!0,this.$emit("input-change",this.inputValue)},handleFocus(){this.inputValue&&(this.showSuggestions=!0)},handleBlur(){setTimeout(()=>{this.showSuggestions=!1},150)},handleKeyup(t){const e=this.$refs.inputEl;e&&this.inputValue!==e.value&&(this.inputValue=e.value,this.handleInput())},handleCompositionEnd(t){this.isComposing=!1,this.inputValue=t.target.value,this.handleInput()},handleCompositionStart(){this.isComposing=!0}}},z={mixins:[j],props:{value:{type:String,required:!0}},data(){return{jalan:"",nomor:"",provinsiList:[],kabupatenList:[],kecamatanList:[],kelurahanList:[],selectedProvinsi:null,selectedKabupaten:null,selectedKecamatan:null,selectedKelurahan:null,kabupatenInput:"",kecamatanInput:"",kelurahanInput:"",showKabupatenSuggestions:!1,showKecamatanSuggestions:!1,showKelurahanSuggestions:!1,isLoadingProvinsi:!1,isLoadingKabupaten:!1,isLoadingKecamatan:!1,isLoadingKelurahan:!1,isUpdating:!1,updateTimer:null,wilayahCache:new Map,inputDebounceTimers:{kabupaten:null,kecamatan:null,kelurahan:null},provinsiSelected:!1,kabupatenSelected:!1,kecamatanSelected:!1,kelurahanSelected:!1,originalAddressState:null,lastEmittedAddress:"",isUserEditing:!1,userEditingTimer:null,persistedJalan:"",persistedNomor:""}},computed:{filteredProvinsiList(){const t=this.inputValue.toLowerCase();return t?this.provinsiList.filter(e=>e.name.toLowerCase().includes(t)):[]},filteredKabupatenList(){const t=this.kabupatenInput.toLowerCase();return t?this.kabupatenList.filter(e=>e.name.toLowerCase().includes(t)):[]},filteredKecamatanList(){const t=this.kecamatanInput.toLowerCase();return t?this.kecamatanList.filter(e=>e.name.toLowerCase().includes(t)):[]},filteredKelurahanList(){const t=this.kelurahanInput.toLowerCase();return t?this.kelurahanList.filter(e=>e.name.toLowerCase().includes(t)):[]}},watch:{jalan:"updateAlamat",nomor:"updateAlamat",selectedProvinsi:"updateAlamat",selectedKabupaten:"updateAlamat",selectedKecamatan:"updateAlamat",selectedKelurahan:"updateAlamat",value:{immediate:!0,handler(t){console.log("AlamatTinggalForm value changed:",t),!this.isUpdating&&t&&typeof t=="string"&&t.trim()!==""&&this.parseExistingAddress(t)}},"$parent.isActive":function(t){t||this.forceAddressUpdate()}},methods:{handleJalanKeydown(t){t.stopPropagation(),(t.key===" "||t.keyCode===32)&&t.stopPropagation()},handleNomorKeydown(t){t.stopPropagation(),(t.key===" "||t.keyCode===32)&&t.stopPropagation()},handleJalanInput(t){t.stopPropagation();const e=t.target.selectionStart,i=t.target.scrollTop,r=document.activeElement;this.jalan=t.target.value,this.persistedJalan=t.target.value;try{localStorage.setItem("alamat_jalan_temp",t.target.value)}catch(n){console.warn("Could not save temp jalan",n)}this.originalAddressState&&(this.originalAddressState.jalan=t.target.value),this.isUserEditing=!0,clearTimeout(this.userEditingTimer),this.$nextTick(()=>{r===this.$refs.jalanInputEl&&(this.$refs.jalanInputEl.focus(),this.$refs.jalanInputEl.setSelectionRange(e,e),this.$refs.jalanInputEl.scrollTop=i),this.userEditingTimer=setTimeout(()=>{this.isUserEditing=!1},1e3)})},handleNomorInput(t){t.stopPropagation();const e=t.target.selectionStart,i=t.target.scrollTop,r=document.activeElement,n=t.target.value;if(this.nomor=n,this.persistedNomor=n,t.isTrusted!==!1)try{localStorage.setItem("alamat_nomor",n),localStorage.setItem("alamat_nomor_priority","true"),sessionStorage.setItem("alamat_nomor_current",n)}catch(a){console.warn("Could not save temp nomor",a)}try{localStorage.setItem("alamat_nomor_temp",t.target.value)}catch(a){console.warn("Could not save temp nomor",a)}this.originalAddressState&&(this.originalAddressState.nomor=t.target.value),this.isUserEditing=!0,clearTimeout(this.userEditingTimer),this.$nextTick(()=>{r===this.$refs.nomorInputEl&&(this.$refs.nomorInputEl.focus(),this.$refs.nomorInputEl.setSelectionRange(e,e),this.$refs.nomorInputEl.scrollTop=i),this.userEditingTimer=setTimeout(()=>{this.isUserEditing=!1},1e3)})},persistJalanChange(t){this.jalan=t.target.value,this.persistedJalan=t.target.value;try{localStorage.setItem("alamat_jalan",t.target.value)}catch(e){console.warn("Could not save jalan to localStorage",e)}this.isUserEditing=!1,this.forceAddressUpdate()},persistNomorChange(t){const e=t.target.value;console.log("Persisting house number change:",e),this.nomor=e,this.persistedNomor=e;try{localStorage.setItem("alamat_nomor",e),localStorage.setItem("alamat_nomor_priority","true")}catch(i){console.warn("Could not save nomor to localStorage",i)}this.isUserEditing=!1,this.isUpdating=!1,this.forceAddressUpdateWithNumber(e)},forceAddressUpdateWithNumber(t){var r,n;console.log("Force updating address with specific number:",t),clearTimeout(this.updateTimer);const e=this.persistedJalan||this.jalan||"",i=[];if(e.trim()!==""&&i.push("Jl. ".concat(e.trim(),",")),t&&t.trim()!==""&&i.push("No. ".concat(t.trim(),",")),this.selectedKelurahan&&this.kelurahanSelected){const s=this.selectedKelurahan.name.trim();i.push("Desa/Kel. ".concat(s,","))}if(this.selectedKecamatan&&this.kecamatanSelected&&i.push("Kec. ".concat(this.selectedKecamatan.name,",")),(r=this.selectedKabupaten)!=null&&r.name&&i.push("".concat(this.selectedKabupaten.name,",")),(n=this.selectedProvinsi)!=null&&n.name&&i.push("".concat(this.selectedProvinsi.name)),i.length>0){const s=i.join(" ");console.log("Emitting updated alamat with forced number:",t),this.lastEmittedAddress=s,this.$emit("input",s)}},handleInput(t){t&&t.stopPropagation(),this.selectedProvinsi&&this.inputValue!==this.selectedProvinsi.name&&(this.provinsiSelected=!1,this.selectedProvinsi=null),this.provinsiSelected||(this.showSuggestions=!0),this.$emit("input-change")},handleFocus(){!this.provinsiSelected&&this.inputValue&&(this.showSuggestions=!0)},handleBlur(){setTimeout(()=>{this.showSuggestions=!1,!this.selectedProvinsi&&!this.provinsiSelected&&(this.inputValue="")},150)},getCacheKey(t,e=null){return e?"".concat(t,"_").concat(e):t},getCachedData(t,e=null){const i=this.getCacheKey(t,e),r=this.wilayahCache.get(i);if(r){if(Date.now()-r.timestamp<300*1e3)return r.data;this.wilayahCache.delete(i)}return null},setCachedData(t,e,i=null){const r=this.getCacheKey(t,i);if(this.wilayahCache.set(r,{data:e,timestamp:Date.now()}),this.wilayahCache.size>100){const n=Array.from(this.wilayahCache.entries());n.sort((s,a)=>s[1].timestamp-a[1].timestamp);for(let s=0;s<20;s++)this.wilayahCache.delete(n[s][0])}},async prefetchPopularRegions(){const t=["31","32","33","35"];for(const e of t)if(!this.getCachedData("regencies",e))try{const i=await fetch("/api/wilayah/regencies/".concat(e));if(i.ok){const r=await i.json();r.data&&this.setCachedData("regencies",r.data,e)}}catch(i){console.debug("Prefetch failed for province",e,i)}},cleanupTimers(){clearTimeout(this.updateTimer),Object.values(this.inputDebounceTimers).forEach(t=>{t&&clearTimeout(t)})},async fetchProvinsi(t=0){const e=this.getCachedData("provinces");if(e){this.provinsiList=e;return}try{this.isLoadingProvinsi=!0;const i=new AbortController,r=setTimeout(()=>i.abort(),1e4),n=await fetch("/api/wilayah/provinces",{signal:i.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(r),!n.ok){if(n.status===502&&t<3)return console.warn("502 error fetching provinces, retrying... (attempt ".concat(t+1,")")),await new Promise(a=>setTimeout(a,1e3*(t+1))),this.fetchProvinsi(t+1);throw new Error("Failed to fetch provinces: ".concat(n.status," ").concat(n.statusText))}const s=await n.json();if(!s.data||!Array.isArray(s.data))throw console.error("Invalid provinces data format:",s),new Error("Invalid data format received");this.provinsiList=s.data,this.setCachedData("provinces",s.data)}catch(i){if(console.error("Error fetching provinces:",i),(i.name==="AbortError"||i.message.includes("fetch"))&&t<2)return console.warn("Network error fetching provinces, retrying... (attempt ".concat(t+1,")")),await new Promise(r=>setTimeout(r,2e3)),this.fetchProvinsi(t+1);this.provinsiList=[]}finally{this.isLoadingProvinsi=!1}},selectProvinsi(t){if(this.selectedProvinsi&&this.selectedProvinsi.code===t.code){this.showSuggestions=!1,this.provinsiSelected=!0;return}console.log("Selected province:",t.name),this.isUpdating=!0,this.selectedProvinsi=t,this.inputValue=t.name,this.showSuggestions=!1,this.provinsiSelected=!0;const e=this.jalan,i=this.nomor;this.selectedKabupaten=null,this.kabupatenInput="",this.selectedKecamatan=null,this.kecamatanInput="",this.selectedKelurahan=null,this.kelurahanInput="",this.kabupatenSelected=!1,this.kecamatanSelected=!1,this.kelurahanSelected=!1,this.jalan=e,this.nomor=i,this.fetchKabupaten().then(()=>{setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)})},debouncedKabupatenInput(t){clearTimeout(this.inputDebounceTimers.kabupaten),this.inputDebounceTimers.kabupaten=setTimeout(()=>{this.processKabupatenInput(t)},150)},debouncedKecamatanInput(t){clearTimeout(this.inputDebounceTimers.kecamatan),this.inputDebounceTimers.kecamatan=setTimeout(()=>{this.processKecamatanInput(t)},150)},debouncedKelurahanInput(t){clearTimeout(this.inputDebounceTimers.kelurahan),this.inputDebounceTimers.kelurahan=setTimeout(()=>{this.processKelurahanInput(t)},150)},processKabupatenInput(t){this.isUpdating||(this.selectedKabupaten&&t!==this.selectedKabupaten.name&&(this.kabupatenSelected=!1,this.selectedKabupaten=null,this.selectedKecamatan=null,this.kecamatanInput="",this.selectedKelurahan=null,this.kelurahanInput="",this.kecamatanSelected=!1,this.kelurahanSelected=!1,this.kecamatanList=[],this.kelurahanList=[]),this.showKabupatenSuggestions=t.length>0)},processKecamatanInput(t){this.selectedKecamatan&&t!==this.selectedKecamatan.name&&(this.kecamatanSelected=!1,this.selectedKecamatan=null,this.selectedKelurahan=null,this.kelurahanInput="",this.kelurahanSelected=!1,this.kelurahanList=[]),this.showKecamatanSuggestions=t.length>0},processKelurahanInput(t){this.selectedKelurahan&&t!==this.selectedKelurahan.name&&(this.kelurahanSelected=!1,this.selectedKelurahan=null),this.showKelurahanSuggestions=t.length>0},handleKabupatenInput(t){t&&t.stopPropagation(),!this.isUpdating&&(this.debouncedKabupatenInput(this.kabupatenInput),this.isComposing||this.$emit("kabupaten-change"))},handleKabupatenFocus(){!this.kabupatenSelected&&this.kabupatenInput&&(this.showKabupatenSuggestions=!0)},handleKabupatenBlur(){setTimeout(()=>{this.showKabupatenSuggestions=!1,!this.selectedKabupaten&&!this.kabupatenSelected&&(this.kabupatenInput="")},150)},handleKabupatenKeyup(t){const e=this.$refs.kabupatenInputEl;e&&this.kabupatenInput!==e.value&&(this.kabupatenInput=e.value,this.handleKabupatenInput()),t.key==="Enter"&&this.filteredKabupatenList.length>0&&(t.preventDefault(),this.selectKabupaten(this.filteredKabupatenList[0]))},handleKabupatenCompositionEnd(t){this.isComposing=!1,this.kabupatenInput=t.target.value,this.handleKabupatenInput()},selectKabupaten(t){if(this.selectedKabupaten&&this.selectedKabupaten.code===t.code){this.showKabupatenSuggestions=!1,this.kabupatenSelected=!0;return}console.log("Selecting kabupaten:",t.name),this.isUpdating=!0;const e=this.jalan,i=this.nomor;this.selectedKabupaten=t,this.kabupatenInput=t.name,this.showKabupatenSuggestions=!1,this.kabupatenSelected=!0,this.selectedKecamatan=null,this.kecamatanInput="",this.selectedKelurahan=null,this.kelurahanInput="",this.kecamatanSelected=!1,this.kelurahanSelected=!1,this.kecamatanList=[],this.kelurahanList=[],this.jalan=e,this.nomor=i,this.fetchKecamatan().then(()=>{setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)})},handleKecamatanInput(t){t.stopPropagation(),this.debouncedKecamatanInput(this.kecamatanInput),this.isComposing||this.$emit("kecamatan-change")},handleKecamatanFocus(){!this.kecamatanSelected&&this.kecamatanInput&&(this.showKecamatanSuggestions=!0)},handleKecamatanBlur(){setTimeout(()=>{this.showKecamatanSuggestions=!1,!this.selectedKecamatan&&!this.kecamatanSelected&&(this.kecamatanInput="")},150)},handleKecamatanKeyup(t){const e=this.$refs.kecamatanInputEl;e&&this.kecamatanInput!==e.value&&(this.kecamatanInput=e.value,this.handleKecamatanInput()),t.key==="Enter"&&this.filteredKecamatanList.length>0&&(t.preventDefault(),this.selectKecamatan(this.filteredKecamatanList[0]))},handleKecamatanCompositionEnd(t){this.isComposing=!1,this.kecamatanInput=t.target.value,this.handleKecamatanInput()},selectKecamatan(t){if(this.selectedKecamatan&&this.selectedKecamatan.code===t.code){this.showKecamatanSuggestions=!1,this.kecamatanSelected=!0;return}console.log("Selecting kecamatan:",t.name),this.isUpdating=!0,this.selectedKecamatan=t,this.kecamatanInput=t.name,this.showKecamatanSuggestions=!1,this.kecamatanSelected=!0,this.selectedKelurahan=null,this.kelurahanInput="",this.kelurahanSelected=!1,this.kelurahanList=[],this.fetchKelurahan().then(()=>{setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)})},handleKelurahanInput(t){t.stopPropagation(),this.debouncedKelurahanInput(this.kelurahanInput),this.isComposing||this.$emit("kelurahan-change")},handleKelurahanFocus(){!this.kelurahanSelected&&this.kelurahanInput&&(this.showKelurahanSuggestions=!0)},handleKelurahanBlur(){setTimeout(()=>{this.showKelurahanSuggestions=!1,!this.selectedKelurahan&&!this.kelurahanSelected&&(this.kelurahanInput="")},150)},handleKelurahanKeyup(t){const e=this.$refs.kelurahanInputEl;e&&this.kelurahanInput!==e.value&&(this.kelurahanInput=e.value,this.handleKelurahanInput()),t.key==="Enter"&&this.filteredKelurahanList.length>0&&(t.preventDefault(),this.selectKelurahan(this.filteredKelurahanList[0]))},handleKelurahanCompositionEnd(t){this.isComposing=!1,this.kelurahanInput=t.target.value,this.handleKelurahanInput()},selectKelurahan(t){if(this.selectedKelurahan&&this.selectedKelurahan.code===t.code){this.showKelurahanSuggestions=!1,this.kelurahanSelected=!0;return}console.log("Selecting kelurahan:",t.name),this.isUpdating=!0,this.selectedKelurahan={...t,name:t.name.trim()},this.kelurahanInput=t.name.trim(),this.showKelurahanSuggestions=!1,this.kelurahanSelected=!0,setTimeout(()=>{this.isUpdating=!1,this.updateAlamat()},300)},async fetchKabupaten(t=0){if(!this.selectedProvinsi){console.log("Cannot fetch kabupaten: No province selected");return}const e=this.getCachedData("regencies",this.selectedProvinsi.code);if(e)return this.kabupatenList=e,this.$nextTick(()=>{this.$refs.kabupatenInputEl&&!this.kabupatenSelected&&this.$refs.kabupatenInputEl.focus()}),e;console.log("Fetching kabupaten for province:",this.selectedProvinsi.name,"code:",this.selectedProvinsi.code,t>0?"(retry ".concat(t,")"):"");try{this.isLoadingKabupaten=!0,this.kabupatenList=[];const i=new AbortController,r=setTimeout(()=>i.abort(),1e4),n=await fetch("/api/wilayah/regencies/".concat(this.selectedProvinsi.code),{signal:i.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(r),!n.ok){if(n.status===502&&t<3)return console.warn("502 error fetching kabupaten, retrying... (attempt ".concat(t+1,")")),await new Promise(a=>setTimeout(a,1e3*(t+1))),this.fetchKabupaten(t+1);throw new Error("Failed to fetch kabupaten: ".concat(n.status," ").concat(n.statusText))}const s=await n.json();if(!s.data||!Array.isArray(s.data))throw console.error("Invalid kabupaten data format:",s),new Error("Invalid data format received");return this.kabupatenList=s.data,this.setCachedData("regencies",s.data,this.selectedProvinsi.code),console.log("Loaded ".concat(this.kabupatenList.length," kabupaten for ").concat(this.selectedProvinsi.name)),s.data}catch(i){return console.error("Error fetching kabupaten:",i),(i.name==="AbortError"||i.message.includes("fetch"))&&t<2?(console.warn("Network error fetching kabupaten, retrying... (attempt ".concat(t+1,")")),await new Promise(r=>setTimeout(r,2e3)),this.fetchKabupaten(t+1)):(this.kabupatenList=[],i.message.includes("502")&&console.warn("Server temporarily unavailable. Please try selecting the province again."),[])}finally{this.isLoadingKabupaten=!1,this.$nextTick(()=>{this.$refs.kabupatenInputEl&&!this.kabupatenSelected&&this.$refs.kabupatenInputEl.focus()})}},async fetchKecamatan(t=0){if(!this.selectedKabupaten)return console.log("Cannot fetch kecamatan: No kabupaten selected"),[];const e=this.getCachedData("districts",this.selectedKabupaten.code);if(e)return this.kecamatanList=e,this.$nextTick(()=>{this.$refs.kecamatanInputEl&&!this.kecamatanSelected&&this.$refs.kecamatanInputEl.focus()}),e;console.log("Fetching kecamatan for kabupaten:",this.selectedKabupaten.name,"code:",this.selectedKabupaten.code,t>0?"(retry ".concat(t,")"):"");try{this.isLoadingKecamatan=!0,this.kecamatanList=[];const i=new AbortController,r=setTimeout(()=>i.abort(),1e4),n=await fetch("/api/wilayah/districts/".concat(this.selectedKabupaten.code),{signal:i.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(r),!n.ok){if(n.status===502&&t<3)return console.warn("502 error fetching kecamatan, retrying... (attempt ".concat(t+1,")")),await new Promise(a=>setTimeout(a,1e3*(t+1))),this.fetchKecamatan(t+1);throw new Error("Failed to fetch kecamatan: ".concat(n.status," ").concat(n.statusText))}const s=await n.json();if(!s.data||!Array.isArray(s.data))throw console.error("Invalid kecamatan data format:",s),new Error("Invalid data format received");return this.kecamatanList=s.data,this.setCachedData("districts",s.data,this.selectedKabupaten.code),console.log("Loaded ".concat(this.kecamatanList.length," kecamatan for ").concat(this.selectedKabupaten.name)),s.data}catch(i){return console.error("Error fetching kecamatan:",i),(i.name==="AbortError"||i.message.includes("fetch"))&&t<2?(console.warn("Network error fetching kecamatan, retrying... (attempt ".concat(t+1,")")),await new Promise(r=>setTimeout(r,2e3)),this.fetchKecamatan(t+1)):(this.kecamatanList=[],[])}finally{this.isLoadingKecamatan=!1,this.$nextTick(()=>{this.$refs.kecamatanInputEl&&!this.kecamatanSelected&&this.$refs.kecamatanInputEl.focus()})}},async fetchKelurahan(t=0){if(!this.selectedKecamatan){console.log("Cannot fetch kelurahan: No kecamatan selected");return}const e=this.getCachedData("villages",this.selectedKecamatan.code);if(e)return this.kelurahanList=e,this.$nextTick(()=>{this.$refs.kelurahanInputEl&&this.$refs.kelurahanInputEl.focus()}),e;console.log("Fetching kelurahan for kecamatan:",this.selectedKecamatan.name,"code:",this.selectedKecamatan.code,t>0?"(retry ".concat(t,")"):"");try{this.isLoadingKelurahan=!0,this.kelurahanList=[];const i=new AbortController,r=setTimeout(()=>i.abort(),1e4),n=await fetch("/api/wilayah/villages/".concat(this.selectedKecamatan.code),{signal:i.signal,headers:{"Cache-Control":"no-cache",Pragma:"no-cache"}});if(clearTimeout(r),!n.ok){if(n.status===502&&t<3)return console.warn("502 error fetching kelurahan, retrying... (attempt ".concat(t+1,")")),await new Promise(a=>setTimeout(a,1e3*(t+1))),this.fetchKelurahan(t+1);throw new Error("Failed to fetch kelurahan: ".concat(n.status," ").concat(n.statusText))}const s=await n.json();if(!s.data||!Array.isArray(s.data))throw console.error("Invalid kelurahan data format:",s),new Error("Invalid data format received");return this.kelurahanList=s.data,this.setCachedData("villages",s.data,this.selectedKecamatan.code),console.log("Loaded ".concat(this.kelurahanList.length," kelurahan for ").concat(this.selectedKecamatan.name)),this.$nextTick(()=>{this.$refs.kelurahanInputEl&&this.$refs.kelurahanInputEl.focus()}),s.data}catch(i){return console.error("Error fetching kelurahan:",i),(i.name==="AbortError"||i.message.includes("fetch"))&&t<2?(console.warn("Network error fetching kelurahan, retrying... (attempt ".concat(t+1,")")),await new Promise(r=>setTimeout(r,2e3)),this.fetchKelurahan(t+1)):(this.kelurahanList=[],[])}finally{this.isLoadingKelurahan=!1}},updateAlamat(){if(this.isUpdating||this.isUserEditing)return;this.isUpdating=!0,clearTimeout(this.updateTimer);let t=!1;try{t=localStorage.getItem("alamat_nomor_priority")==="true"}catch(r){console.warn("Could not read nomor priority",r)}let e="";if(t)try{const r=localStorage.getItem("alamat_nomor");r&&(e=r,console.log("Using priority stored nomor:",e))}catch(r){console.warn("Could not read stored nomor",r)}e||(e=this.persistedNomor||this.nomor||"");const i=this.persistedJalan||this.jalan||"";this.updateTimer=setTimeout(()=>{var n,s;const r=[];if(i.trim()!==""&&r.push("Jl. ".concat(i.trim(),",")),e.trim()!==""&&r.push("No. ".concat(e.trim(),",")),this.selectedKelurahan&&this.kelurahanSelected){const a=this.selectedKelurahan.name.trim();r.push("Desa/Kel. ".concat(a,","))}if(this.selectedKecamatan&&this.kecamatanSelected&&r.push("Kec. ".concat(this.selectedKecamatan.name,",")),(n=this.selectedKabupaten)!=null&&n.name&&r.push("".concat(this.selectedKabupaten.name,",")),(s=this.selectedProvinsi)!=null&&s.name&&r.push("".concat(this.selectedProvinsi.name)),r.length>0){const a=r.join(" ");if(e&&!a.includes("No. ".concat(e))){console.warn("Number missing from address, forcing rebuild with:",e),this.forceAddressUpdateWithNumber(e);return}console.log("Emitting updated alamat with jalan:",i,"and nomor:",e),this.originalAddressState={...this.originalAddressState,jalan:i,nomor:e,provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},this.$emit("input",a),this.lastEmittedAddress=a}this.isUpdating=!1},300)},parseExistingAddress(t){var s;if(this.isUpdating||this.isUserEditing||!t||typeof t!="string"){console.log("Skipping address parsing:",this.isUpdating?"update in progress":this.isUserEditing?"user is currently editing":"invalid address string");return}console.log("Parsing address:",t);const e=this.jalan,i=this.nomor,r=e&&e.trim()!=="",n=i&&i.trim()!=="";this.isUpdating=!0;try{const a=t.split(",").map(c=>c.trim());let d="",k="";try{d=localStorage.getItem("alamat_jalan")||"",k=localStorage.getItem("alamat_nomor")||""}catch(c){console.warn("Could not read persisted address",c)}if(d?(this.jalan=d,this.persistedJalan=d,console.log("Using persisted jalan value:",d)):r?console.log("Preserving user-set jalan:",e):(s=a[0])!=null&&s.toLowerCase().startsWith("jl.")&&(this.jalan=a[0].substring(3).trim(),console.log("Found jalan in address:",this.jalan)),k)this.nomor=k,this.persistedNomor=k,console.log("Using persisted nomor value:",k);else if(n)console.log("Preserving user-set nomor:",i);else{const c=a.find(E=>E==null?void 0:E.toLowerCase().includes("no."));c&&(this.nomor=c.substring(c.toLowerCase().indexOf("no.")+3).trim(),console.log("Found nomor in address:",this.nomor))}const L=/kec\.|kecamatan/i,A=a.find(c=>c&&L.test(c));let D="";A&&(D=A.replace(L,"").trim(),console.log("Found kecamatan in address:",D));const T=/desa\/kel\.|desa|kel\.|kelurahan/i,U=a.find(c=>c&&T.test(c));let b="";U&&(b=U.replace(T,"").trim(),console.log("Found kelurahan in address:",b));const O=/kab\.|kabupaten|kota/i,R=a.find(c=>c&&O.test(c));let C="";R&&(C=R.replace(O,"").trim(),console.log("Found kabupaten in address:",C)),this.fetchProvinsi().then(async()=>{var E;const c=((E=a[a.length-1])==null?void 0:E.trim())||"";if(c&&this.provinsiList.length>0){const N=this.provinsiList.find(_=>_.name.toLowerCase()===c.toLowerCase());if(N&&(console.log("Found matching province:",N.name),this.selectedProvinsi=N,this.inputValue=N.name,this.provinsiSelected=!0,C))return this.fetchKabupaten().then(async()=>{if(this.kabupatenList.length>0){console.log("Looking for kabupaten:",C);const _=this.kabupatenList.find(I=>C.toLowerCase().includes(I.name.toLowerCase())||I.name.toLowerCase().includes(C.toLowerCase()));if(_&&(console.log("Found matching kabupaten:",_.name),this.selectedKabupaten=_,this.kabupatenInput=_.name,this.kabupatenSelected=!0,D))return this.fetchKecamatan().then(async()=>{if(this.kecamatanList.length>0){console.log("Looking for kecamatan:",D);const I=this.kecamatanList.find(v=>D.toLowerCase().includes(v.name.toLowerCase())||v.name.toLowerCase().includes(D.toLowerCase()));if(I&&(console.log("Found matching kecamatan:",I.name),this.selectedKecamatan=I,this.kecamatanInput=I.name,this.kecamatanSelected=!0,b))return this.fetchKelurahan().then(()=>{if(this.kelurahanList.length>0){console.log("Looking for kelurahan:",b);let v=this.kelurahanList.find(F=>F.name.trim().toLowerCase()===b.toLowerCase());v||(v=this.kelurahanList.find(F=>b.toLowerCase().includes(F.name.trim().toLowerCase())||F.name.trim().toLowerCase().includes(b.toLowerCase()))),v&&(console.log("Found matching kelurahan:",v.name),this.selectedKelurahan={...v,name:v.name.trim()},this.kelurahanInput=v.name.trim(),this.kelurahanSelected=!0)}})}})}})}}).catch(c=>{console.error("Error in address parsing chain:",c)}).finally(()=>{this.originalAddressState={jalan:this.jalan,nomor:this.nomor,provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},setTimeout(()=>{this.isUpdating=!1},1e3)})}catch(a){console.error("Error parsing address:",a),this.isUpdating=!1}},forceAddressUpdate(){console.log("Force updating address before component destroyed");let t="";try{localStorage.getItem("alamat_nomor_priority")==="true"&&(t=localStorage.getItem("alamat_nomor")||"")}catch(e){console.warn("Could not read priority nomor",e)}t||(t=this.persistedNomor||this.nomor||""),console.log("Force updating with most accurate nomor:",t),t?this.forceAddressUpdateWithNumber(t):this.updateAlamat()},clearStoredAddressData(){console.log("Clearing all stored address data from caches");try{localStorage.removeItem("alamat_jalan"),localStorage.removeItem("alamat_jalan_temp"),localStorage.removeItem("alamat_nomor"),localStorage.removeItem("alamat_nomor_temp"),localStorage.removeItem("alamat_nomor_priority"),sessionStorage.removeItem("alamat_nomor_current")}catch(t){console.warn("Error clearing address storage:",t)}}},created(){var e,i;console.log("AlamatTinggalForm created with value:",this.value),(window.performance&&(((e=window.performance.getEntriesByType("navigation")[0])==null?void 0:e.type)==="navigate"||((i=window.performance.getEntriesByType("navigation")[0])==null?void 0:i.type)==="reload")||document.readyState==="complete")&&(console.log("Page was loaded/reloaded - clearing address cache"),this.clearStoredAddressData()),(this.value===void 0||this.value===null)&&this.$emit("input","")},mounted(){this.fetchProvinsi(),console.log("AlamatTinggalForm mounted"),setTimeout(()=>{this.prefetchPopularRegions()},1e3);try{const t=sessionStorage.getItem("alamat_last_load_time"),e=Date.now();(!t||e-Number.parseInt(t,10)>2e3)&&(console.log("Detected page load via timestamp - clearing address cache"),this.clearStoredAddressData()),sessionStorage.setItem("alamat_last_load_time",e.toString())}catch(t){console.warn("Error in reload detection:",t)}this.originalAddressState={jalan:this.jalan||"",nomor:this.nomor||"",provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},this.$nextTick(()=>{this.originalAddressState={jalan:this.jalan,nomor:this.nomor,provinsi:this.selectedProvinsi?JSON.stringify(this.selectedProvinsi):null,kabupaten:this.selectedKabupaten?JSON.stringify(this.selectedKabupaten):null,kecamatan:this.selectedKecamatan?JSON.stringify(this.selectedKecamatan):null,kelurahan:this.selectedKelurahan?JSON.stringify(this.selectedKelurahan):null},this.value&&(this.selectedProvinsi&&!this.value.includes(this.selectedProvinsi.name)||this.selectedKabupaten&&!this.value.includes(this.selectedKabupaten.name)||this.selectedKecamatan&&!this.value.includes(this.selectedKecamatan.name)||this.selectedKelurahan&&!this.value.includes(this.selectedKelurahan.name))&&(console.log("Found incomplete address, forcing update"),this.forceAddressUpdate())});try{const t=localStorage.getItem("alamat_jalan"),e=localStorage.getItem("alamat_nomor");t&&(this.jalan=t,this.persistedJalan=t,console.log("Restored persisted jalan:",t)),e&&(this.nomor=e,this.persistedNomor=e,console.log("Restored persisted nomor:",e))}catch(t){console.warn("Could not restore persisted address",t)}},beforeDestroy(){let t="";try{localStorage.getItem("alamat_nomor_priority")==="true"&&(t=localStorage.getItem("alamat_nomor")||"")}catch(e){console.warn("Could not read priority nomor on destroy",e)}t||(t=this.persistedNomor||this.nomor||""),t?this.forceAddressUpdateWithNumber(t):this.forceAddressUpdate()},beforeUnmount(){this.cleanupTimers();let t="";try{localStorage.getItem("alamat_nomor_priority")==="true"&&(t=localStorage.getItem("alamat_nomor")||"")}catch(e){console.warn("Could not read priority nomor on unmount",e)}t||(t=this.persistedNomor||this.nomor||""),t?this.forceAddressUpdateWithNumber(t):this.forceAddressUpdate()}},W={class:"form-row"},Y=["value"],Q={class:"form-row"},X=["value"],Z={class:"form-row"},ee={class:"suggestions-container"},te={key:0,class:"loading-indicator"},ae={key:1,class:"suggestions"},se=["onClick"],ie={key:0,class:"form-row"},ne={class:"suggestions-container"},oe={key:0,class:"loading-indicator"},re={key:1,class:"suggestions"},le=["onClick"],ue={key:1,class:"form-row"},de={class:"suggestions-container"},he={key:0,class:"loading-indicator"},me={key:1,class:"suggestions"},ce=["onClick"],ge={key:2,class:"form-row"},pe={class:"suggestions-container"},fe={key:0,class:"loading-indicator"},ke={key:1,class:"suggestions"},be=["onClick"];function ve(t,e,i,r,n,s){return l(),u("div",null,[o("div",W,[e[34]||(e[34]=o("label",{for:"jalan"},"Jalan :",-1)),o("input",{id:"jalan",type:"text",ref:"jalanInputEl",value:n.jalan,onInput:e[0]||(e[0]=(...a)=>s.handleJalanInput&&s.handleJalanInput(...a)),onKeydown:e[1]||(e[1]=(...a)=>s.handleJalanKeydown&&s.handleJalanKeydown(...a)),onChange:e[2]||(e[2]=a=>s.persistJalanChange(a)),placeholder:"Nama Jalan, Gang, dan RT/RW (jika ada).",required:""},null,40,Y)]),o("div",Q,[e[35]||(e[35]=o("label",{for:"nomor"},"No. :",-1)),o("input",{id:"nomor",type:"text",ref:"nomorInputEl",value:n.nomor,onInput:e[3]||(e[3]=(...a)=>s.handleNomorInput&&s.handleNomorInput(...a)),onKeydown:e[4]||(e[4]=(...a)=>s.handleNomorKeydown&&s.handleNomorKeydown(...a)),onChange:e[5]||(e[5]=a=>s.persistNomorChange(a)),placeholder:"Nomor Rumah. Tulis 0 jika tidak ada.",required:""},null,40,X)]),o("div",Z,[e[36]||(e[36]=o("label",{for:"provinsi"},"Provinsi :",-1)),o("div",ee,[g(o("input",{id:"provinsi",ref:"inputEl",type:"text","onUpdate:modelValue":e[6]||(e[6]=a=>t.inputValue=a),onInput:e[7]||(e[7]=(...a)=>s.handleInput&&s.handleInput(...a)),onFocus:e[8]||(e[8]=(...a)=>s.handleFocus&&s.handleFocus(...a)),onBlur:e[9]||(e[9]=(...a)=>s.handleBlur&&s.handleBlur(...a)),onKeyup:e[10]||(e[10]=(...a)=>t.handleKeyup&&t.handleKeyup(...a)),onCompositionstart:e[11]||(e[11]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[12]||(e[12]=(...a)=>t.handleCompositionEnd&&t.handleCompositionEnd(...a)),placeholder:"Ketik untuk mencari provinsi",required:""},null,544),[[f,t.inputValue]]),n.isLoadingProvinsi?(l(),u("div",te,"Loading...")):m("",!0),t.showSuggestions&&s.filteredProvinsiList.length?(l(),u("div",ae,[(l(!0),u(y,null,S(s.filteredProvinsiList,a=>(l(),u("div",{key:a.code,class:"suggestion-item",onClick:d=>s.selectProvinsi(a)},h(a.name),9,se))),128))])):m("",!0)])]),n.kabupatenList.length>0?(l(),u("div",ie,[e[37]||(e[37]=o("label",{for:"kabupaten"},"Kota / Kab. :",-1)),o("div",ne,[g(o("input",{id:"kabupaten",ref:"kabupatenInputEl",type:"text","onUpdate:modelValue":e[13]||(e[13]=a=>n.kabupatenInput=a),onInput:e[14]||(e[14]=a=>s.handleKabupatenInput(a)),onFocus:e[15]||(e[15]=(...a)=>s.handleKabupatenFocus&&s.handleKabupatenFocus(...a)),onBlur:e[16]||(e[16]=(...a)=>s.handleKabupatenBlur&&s.handleKabupatenBlur(...a)),onKeyup:e[17]||(e[17]=(...a)=>s.handleKabupatenKeyup&&s.handleKabupatenKeyup(...a)),onCompositionstart:e[18]||(e[18]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[19]||(e[19]=(...a)=>s.handleKabupatenCompositionEnd&&s.handleKabupatenCompositionEnd(...a)),placeholder:"Ketik untuk mencari kabupaten",required:""},null,544),[[f,n.kabupatenInput]]),n.isLoadingKabupaten?(l(),u("div",oe," Loading... ")):m("",!0),n.showKabupatenSuggestions&&s.filteredKabupatenList.length?(l(),u("div",re,[(l(!0),u(y,null,S(s.filteredKabupatenList,a=>(l(),u("div",{key:a.code,class:"suggestion-item",onClick:d=>s.selectKabupaten(a)},h(a.name),9,le))),128))])):m("",!0)])])):m("",!0),n.kecamatanList.length>0?(l(),u("div",ue,[e[38]||(e[38]=o("label",{for:"kecamatan"},"Kecamatan :",-1)),o("div",de,[g(o("input",{id:"kecamatan",ref:"kecamatanInputEl",type:"text","onUpdate:modelValue":e[20]||(e[20]=a=>n.kecamatanInput=a),onInput:e[21]||(e[21]=a=>s.handleKecamatanInput(a)),onFocus:e[22]||(e[22]=(...a)=>s.handleKecamatanFocus&&s.handleKecamatanFocus(...a)),onBlur:e[23]||(e[23]=(...a)=>s.handleKecamatanBlur&&s.handleKecamatanBlur(...a)),onKeyup:e[24]||(e[24]=(...a)=>s.handleKecamatanKeyup&&s.handleKecamatanKeyup(...a)),onCompositionstart:e[25]||(e[25]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[26]||(e[26]=(...a)=>s.handleKecamatanCompositionEnd&&s.handleKecamatanCompositionEnd(...a)),placeholder:"Ketik untuk mencari kecamatan",required:""},null,544),[[f,n.kecamatanInput]]),n.isLoadingKecamatan?(l(),u("div",he," Loading... ")):m("",!0),n.showKecamatanSuggestions&&s.filteredKecamatanList.length?(l(),u("div",me,[(l(!0),u(y,null,S(s.filteredKecamatanList,a=>(l(),u("div",{key:a.code,class:"suggestion-item",onClick:d=>s.selectKecamatan(a)},h(a.name),9,ce))),128))])):m("",!0)])])):m("",!0),n.kelurahanList.length>0?(l(),u("div",ge,[e[39]||(e[39]=o("label",{for:"kelurahan"},"Desa / Kel. :",-1)),o("div",pe,[g(o("input",{id:"kelurahan",ref:"kelurahanInputEl",type:"text","onUpdate:modelValue":e[27]||(e[27]=a=>n.kelurahanInput=a),onInput:e[28]||(e[28]=a=>s.handleKelurahanInput(a)),onFocus:e[29]||(e[29]=(...a)=>s.handleKelurahanFocus&&s.handleKelurahanFocus(...a)),onBlur:e[30]||(e[30]=(...a)=>s.handleKelurahanBlur&&s.handleKelurahanBlur(...a)),onKeyup:e[31]||(e[31]=(...a)=>s.handleKelurahanKeyup&&s.handleKelurahanKeyup(...a)),onCompositionstart:e[32]||(e[32]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[33]||(e[33]=(...a)=>s.handleKelurahanCompositionEnd&&s.handleKelurahanCompositionEnd(...a)),placeholder:"Ketik untuk mencari kelurahan/desa",required:""},null,544),[[f,n.kelurahanInput]]),n.isLoadingKelurahan?(l(),u("div",fe," Loading... ")):m("",!0),n.showKelurahanSuggestions&&s.filteredKelurahanList.length?(l(),u("div",ke,[(l(!0),u(y,null,S(s.filteredKelurahanList,a=>(l(),u("div",{key:a.code,class:"suggestion-item",onClick:d=>s.selectKelurahan(a)},h(a.name),9,be))),128))])):m("",!0)])])):m("",!0)])}const Ke=K(z,[["render",ve],["__scopeId","data-v-b6cce062"]]),we={props:{formData:{type:Object,required:!0}},components:{AlamatTinggalForm:Ke},methods:{updateAlamatTinggal(t){let e=t;if(t==null)console.log("PersonalInfoForm received null/undefined alamat_tinggal"),e="";else if(t&&t.target&&t.target.value!==void 0)e=t.target.value;else if(t&&typeof t=="object"&&(t.constructor&&t.constructor.name==="InputEvent"||t instanceof Event)){console.log("PersonalInfoForm received InputEvent, ignoring");return}else if(typeof t!="string"){console.log("PersonalInfoForm received non-string value, converting:",t);try{e=String(t)}catch(i){console.error("Failed to convert value to string:",i);return}}console.log("PersonalInfoForm updating alamat_tinggal to:",e),this.formData.alamat_tinggal!==e&&(this.formData.alamat_tinggal=e,this.$forceUpdate())}},watch:{"formData.alamat_tinggal":t=>{console.log("PersonalInfoForm detected alamat_tinggal change:",t)}},mounted(){console.log("PersonalInfoForm mounted with alamat_tinggal:",this.formData.alamat_tinggal),(this.formData.alamat_tinggal===null||this.formData.alamat_tinggal===void 0)&&(this.formData.alamat_tinggal="",console.log("Initialized empty alamat_tinggal")),this.$nextTick(()=>{this.$forceUpdate()})}},ye={class:"form-group"},Se={class:"form-group"},De={class:"form-group"},Ie={class:"form-group"},Pe={class:"alamat-tinggal-box"},Ce={class:"form-group"};function Ee(t,e,i,r,n,s){const a=w("AlamatTinggalForm");return l(),u("div",null,[e[12]||(e[12]=o("div",{class:"section-title"},"Data Pribadi",-1)),e[13]||(e[13]=o("label",{for:"nama_lengkap"},"Nama Lengkap",-1)),g(o("input",{id:"nama_lengkap",type:"text","onUpdate:modelValue":e[0]||(e[0]=d=>i.formData.nama_lengkap=d),placeholder:"Nama Lengkap",required:""},null,512),[[f,i.formData.nama_lengkap]]),e[14]||(e[14]=o("label",{for:"nama_panggilan"},"Nama Panggilan",-1)),g(o("input",{id:"nama_panggilan",type:"text","onUpdate:modelValue":e[1]||(e[1]=d=>i.formData.nama_panggilan=d),placeholder:"Nama Panggilan",required:""},null,512),[[f,i.formData.nama_panggilan]]),o("div",ye,[e[7]||(e[7]=o("label",{for:"jenis_kelamin"},"Jenis Kelamin",-1)),g(o("select",{id:"jenis_kelamin","onUpdate:modelValue":e[2]||(e[2]=d=>i.formData.jenis_kelamin=d),required:""},[...e[6]||(e[6]=[o("option",{value:"",disabled:"",selected:""},"Pilih Jenis Kelamin",-1),o("option",{value:"LAKI-LAKI"},"LAKI-LAKI",-1),o("option",{value:"PEREMPUAN"},"PEREMPUAN",-1)])],512),[[J,i.formData.jenis_kelamin]])]),o("div",Se,[e[8]||(e[8]=o("label",{for:"kelahiran_tempat"},"Tempat Lahir",-1)),g(o("input",{id:"kelahiran_tempat",type:"text","onUpdate:modelValue":e[3]||(e[3]=d=>i.formData.kelahiran_tempat=d),placeholder:"Tempat Lahir",required:""},null,512),[[f,i.formData.kelahiran_tempat]])]),o("div",De,[e[9]||(e[9]=o("label",{for:"kelahiran_tanggal"},"Tanggal Lahir",-1)),g(o("input",{id:"kelahiran_tanggal",type:"date","onUpdate:modelValue":e[4]||(e[4]=d=>i.formData.kelahiran_tanggal=d),required:""},null,512),[[f,i.formData.kelahiran_tanggal]])]),o("div",Ie,[e[10]||(e[10]=o("label",{for:"alamat_tinggal"},"Alamat Tinggal (Lengkap)",-1)),o("div",Pe,[P(a,{id:"alamat_tinggal",value:i.formData.alamat_tinggal,onInput:s.updateAlamatTinggal},null,8,["value","onInput"])])]),o("div",Ce,[e[11]||(e[11]=o("label",{for:"nomor_hape"},"Nomor HP",-1)),g(o("input",{id:"nomor_hape",type:"tel","onUpdate:modelValue":e[5]||(e[5]=d=>i.formData.nomor_hape=d),placeholder:"Nomor HP"},null,512),[[f,i.formData.nomor_hape]])])])}const _e=K(we,[["render",Ee],["__scopeId","data-v-8aa95022"]]),Le={props:{formData:{type:Object,required:!0}}},Ae={class:"form-group"},Te={class:"form-group"},Ue={class:"form-group"},Ne={class:"form-group"},Fe={class:"form-group"},je={class:"form-group"};function Je(t,e,i,r,n,s){return l(),u("div",null,[e[14]||(e[14]=o("div",{class:"section-title"},"Data Orang Tua",-1)),o("div",Ae,[e[6]||(e[6]=o("label",{for:"nama_ayah"},"Nama Ayah",-1)),g(o("input",{id:"nama_ayah",type:"text","onUpdate:modelValue":e[0]||(e[0]=a=>i.formData.nama_ayah=a),placeholder:"Nama Ayah",required:""},null,512),[[f,i.formData.nama_ayah]])]),o("div",Te,[e[8]||(e[8]=o("label",{for:"status_ayah"},"Status Ayah",-1)),g(o("select",{id:"status_ayah","onUpdate:modelValue":e[1]||(e[1]=a=>i.formData.status_ayah=a),required:""},[...e[7]||(e[7]=[o("option",{value:"",disabled:"",selected:""},"Pilih",-1),o("option",{value:"SUDAH NGAJI"},"SUDAH NGAJI",-1),o("option",{value:"BELUM NGAJI"},"BELUM NGAJI",-1)])],512),[[J,i.formData.status_ayah]])]),o("div",Ue,[e[9]||(e[9]=o("label",{for:"nomor_hape_ayah"},"Nomor HP Ayah",-1)),g(o("input",{id:"nomor_hape_ayah",type:"tel","onUpdate:modelValue":e[2]||(e[2]=a=>i.formData.nomor_hape_ayah=a),placeholder:"Nomor HP Ayah"},null,512),[[f,i.formData.nomor_hape_ayah]])]),o("div",Ne,[e[10]||(e[10]=o("label",{for:"nama_ibu"},"Nama Ibu",-1)),g(o("input",{id:"nama_ibu",type:"text","onUpdate:modelValue":e[3]||(e[3]=a=>i.formData.nama_ibu=a),placeholder:"Nama Ibu",required:""},null,512),[[f,i.formData.nama_ibu]])]),o("div",Fe,[e[12]||(e[12]=o("label",{for:"status_ibu"},"Status Ibu",-1)),g(o("select",{id:"status_ibu","onUpdate:modelValue":e[4]||(e[4]=a=>i.formData.status_ibu=a),required:""},[...e[11]||(e[11]=[o("option",{value:"",disabled:"",selected:""},"Pilih",-1),o("option",{value:"SUDAH NGAJI"},"SUDAH NGAJI",-1),o("option",{value:"BELUM NGAJI"},"BELUM NGAJI",-1)])],512),[[J,i.formData.status_ibu]])]),o("div",je,[e[13]||(e[13]=o("label",{for:"nomor_hape_ibu"},"Nomor HP Ibu",-1)),g(o("input",{id:"nomor_hape_ibu",type:"tel","onUpdate:modelValue":e[5]||(e[5]=a=>i.formData.nomor_hape_ibu=a),placeholder:"Nomor HP Ibu"},null,512),[[f,i.formData.nomor_hape_ibu]])])])}const Oe=K(Le,[["render",Je]]),Re={mixins:[j],props:{hobiOptions:{type:Array,required:!0},selectedHobi:{type:Array,required:!0},placeholder:{type:String,default:"Ketik untuk mencari hobi"}},computed:{filteredHobi(){const t=this.inputValue.toLowerCase();return t?this.hobiOptions.filter(e=>{var i;return e.kategori.toLowerCase().includes(t)||e.hobi.toLowerCase().includes(t)||((i=e.detail_hobi)==null?void 0:i.toLowerCase().includes(t))}):[]}},methods:{selectHobi(t){this.selectedHobi.some(i=>i.kategori===t.kategori&&i.hobi===t.hobi)||this.selectedHobi.push({kategori:t.kategori,hobi:t.hobi,detail_hobi:t.detail_hobi}),this.inputValue="",this.showSuggestions=!1},removeHobi(t){this.selectedHobi.splice(t,1)}}},$e={class:"form-group"},He={class:"suggestions-container"},Me={key:0,class:"selected-hobi-container"},Be=["onClick"],Ve={key:1,class:"suggestions"},xe=["onClick"];function qe(t,e,i,r,n,s){return l(),u("div",$e,[e[7]||(e[7]=o("label",{for:"hobi"},"Hobi (pilih satu atau lebih)",-1)),o("div",He,[i.selectedHobi.length>0?(l(),u("div",Me,[(l(!0),u(y,null,S(i.selectedHobi,(a,d)=>(l(),u("div",{key:d,class:"hobi-tag"},[p(h(a.kategori)+": "+h(a.hobi)+" ",1),o("span",{class:"remove-hobi",onClick:k=>s.removeHobi(d)},"×",8,Be)]))),128))])):m("",!0),g(o("input",{id:"hobi",ref:"inputEl",type:"text","onUpdate:modelValue":e[0]||(e[0]=a=>t.inputValue=a),onInput:e[1]||(e[1]=(...a)=>t.handleInput&&t.handleInput(...a)),onFocus:e[2]||(e[2]=(...a)=>t.handleFocus&&t.handleFocus(...a)),onBlur:e[3]||(e[3]=(...a)=>t.handleBlur&&t.handleBlur(...a)),onKeyup:e[4]||(e[4]=(...a)=>t.handleKeyup&&t.handleKeyup(...a)),onCompositionstart:e[5]||(e[5]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[6]||(e[6]=(...a)=>t.handleCompositionEnd&&t.handleCompositionEnd(...a)),placeholder:"Ketik untuk mencari hobi"},null,544),[[f,t.inputValue]]),t.showSuggestions&&s.filteredHobi.length?(l(),u("div",Ve,[(l(!0),u(y,null,S(s.filteredHobi,(a,d)=>(l(),u("div",{key:d,class:"suggestion-item",onClick:H(k=>s.selectHobi(a),["stop"])},[o("strong",null,h(a.kategori),1),p(": "+h(a.hobi),1)],8,xe))),128))])):m("",!0)])])}const Ge=K(Re,[["render",qe]]),ze={mixins:[j],props:{formData:{type:Object,required:!0},kelompokOptions:{type:Object,required:!0},flattenedKelompok:{type:Array,required:!0},dataLoaded:{type:Boolean,required:!0},isLoading:{type:Boolean,required:!0},loadError:{type:String,default:null},placeholder:{type:String,default:"Ketik untuk mencari kelompok atau desa."}},computed:{filteredKelompok(){const t=this.inputValue.toLowerCase();if(!t||!this.dataLoaded)return[];const e=new Set;return this.flattenedKelompok.filter(({kelompok:i,desa:r})=>{const n="".concat(i.toLowerCase(),"-").concat(r.toLowerCase());return(i.toLowerCase().includes(t)||r.toLowerCase().includes(t))&&!e.has(n)?(e.add(n),!0):!1})}},methods:{handleInput(){this.showSuggestions=!0,this.formData.sambung_desa="",this.formData.sambung_kelompok="",this.$emit("input-change")},selectKelompok({kelompok:t,desa:e}){this.inputValue="".concat(t," (").concat(e,")"),this.formData.sambung_kelompok=t,this.formData.sambung_desa=e,this.showSuggestions=!1}}},We={class:"form-group"},Ye={class:"suggestions-container"},Qe=["placeholder"],Xe={key:0,class:"loading-indicator"},Ze={key:1,class:"error-message"},et={key:2,class:"suggestions"},tt=["onClick"];function at(t,e,i,r,n,s){return l(),u("div",We,[e[7]||(e[7]=o("label",{for:"kelompok"},"Alamat Sambung",-1)),o("div",Ye,[g(o("input",{id:"kelompok",ref:"inputEl",type:"text","onUpdate:modelValue":e[0]||(e[0]=a=>t.inputValue=a),onInput:e[1]||(e[1]=(...a)=>s.handleInput&&s.handleInput(...a)),onFocus:e[2]||(e[2]=(...a)=>t.handleFocus&&t.handleFocus(...a)),onBlur:e[3]||(e[3]=(...a)=>t.handleBlur&&t.handleBlur(...a)),onKeyup:e[4]||(e[4]=(...a)=>t.handleKeyup&&t.handleKeyup(...a)),onCompositionstart:e[5]||(e[5]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[6]||(e[6]=(...a)=>t.handleCompositionEnd&&t.handleCompositionEnd(...a)),placeholder:i.placeholder,required:""},null,40,Qe),[[f,t.inputValue]]),i.isLoading?(l(),u("div",Xe,"Loading...")):m("",!0),i.loadError?(l(),u("div",Ze,h(i.loadError),1)):m("",!0),t.showSuggestions&&s.filteredKelompok.length?(l(),u("div",et,[(l(!0),u(y,null,S(s.filteredKelompok,(a,d)=>(l(),u("div",{key:d,class:"suggestion-item",onClick:k=>s.selectKelompok(a)},h(a.kelompok)+" ("+h(a.desa)+") ",9,tt))),128))])):m("",!0)])])}const st=K(ze,[["render",at]]),it={mixins:[j],props:{formData:{type:Object,required:!0},sekolahKelasOptions:{type:Array,required:!0},placeholder:{type:String,default:"Ketik saja ..."}},computed:{filteredSekolahKelas(){const t=this.inputValue.toLowerCase();return t?this.sekolahKelasOptions.filter(({jenjang:e,kelas:i})=>e.toLowerCase().includes(t)||i.toLowerCase().includes(t)):this.sekolahKelasOptions}},methods:{handleInput(){this.showSuggestions=!0,this.formData.sekolah_kelas=""},selectSekolahKelas({jenjang:t,kelas:e}){const i="".concat(t," ").concat(e);this.inputValue=i,this.formData.sekolah_kelas=i,this.showSuggestions=!1}}},nt={class:"form-group"},ot={class:"suggestions-container"},rt=["placeholder"],lt={key:0,class:"suggestions"},ut=["onClick"];function dt(t,e,i,r,n,s){return l(),u("div",nt,[e[7]||(e[7]=o("label",{for:"sekolah_kelas"},"Kelas Sekolah / Kerja / Wirausaha / Muballigh",-1)),o("div",ot,[g(o("input",{id:"sekolah_kelas",ref:"inputEl",type:"text","onUpdate:modelValue":e[0]||(e[0]=a=>t.inputValue=a),onInput:e[1]||(e[1]=(...a)=>s.handleInput&&s.handleInput(...a)),onFocus:e[2]||(e[2]=(...a)=>t.handleFocus&&t.handleFocus(...a)),onBlur:e[3]||(e[3]=(...a)=>t.handleBlur&&t.handleBlur(...a)),onKeyup:e[4]||(e[4]=(...a)=>t.handleKeyup&&t.handleKeyup(...a)),onCompositionstart:e[5]||(e[5]=(...a)=>t.handleCompositionStart&&t.handleCompositionStart(...a)),onCompositionend:e[6]||(e[6]=(...a)=>t.handleCompositionEnd&&t.handleCompositionEnd(...a)),placeholder:i.placeholder},null,40,rt),[[f,t.inputValue]]),t.showSuggestions&&s.filteredSekolahKelas.length?(l(),u("div",lt,[(l(!0),u(y,null,S(s.filteredSekolahKelas,(a,d)=>(l(),u("div",{key:d,class:"suggestion-item",onClick:k=>s.selectSekolahKelas(a)},h(a.jenjang)+" "+h(a.kelas),9,ut))),128))])):m("",!0)])])}const ht=K(it,[["render",dt]]),mt={props:{formData:{type:Object,required:!0},selectedHobi:{type:Array,default:()=>[]},selectedPhoto:{type:File,default:null},isSubmitting:{type:Boolean,default:!1}},computed:{photoPreviewUrl(){return this.selectedPhoto?URL.createObjectURL(this.selectedPhoto):null}},mounted(){console.log("ReviewDataSection mounted with alamat_tinggal:",this.formData.alamat_tinggal),this.savedAddress=this.formData.alamat_tinggal||""},methods:{formatDate(t){if(!t)return"";const e={day:"numeric",month:"long",year:"numeric"};return new Date(t).toLocaleDateString("id-ID",e)},handleSubmit(){this.$emit("submit")},handleEdit(){console.log("ReviewDataSection - alamat_tinggal before edit:",this.formData.alamat_tinggal),this.savedAddress=this.formData.alamat_tinggal||"",this.$emit("edit",{savedAddress:this.savedAddress})}},data(){return{savedAddress:""}}},ct={class:"review-data"},gt={class:"review-item"},pt={class:"review-item"},ft={class:"review-item"},kt={class:"review-item"},bt={class:"review-item"},vt={class:"review-item"},Kt={key:0,class:"review-item"},wt={class:"photo-preview"},yt=["src"],St={class:"review-item"},Dt={class:"review-item"},It={key:1,class:"review-item"},Pt={class:"review-item"},Ct={class:"review-item"},Et={class:"review-item"},_t={class:"review-item"},Lt={class:"review-item"},At={class:"review-item"},Tt={class:"review-actions"},Ut=["disabled"];function Nt(t,e,i,r,n,s){return l(),u("div",ct,[e[18]||(e[18]=o("div",{class:"section-title"},"Review Data",-1)),o("div",gt,[e[2]||(e[2]=o("strong",null,"Nama Lengkap:",-1)),p(" "+h(i.formData.nama_lengkap),1)]),o("div",pt,[e[3]||(e[3]=o("strong",null,"Nama Panggilan:",-1)),p(" "+h(i.formData.nama_panggilan),1)]),o("div",ft,[e[4]||(e[4]=o("strong",null,"Jenis Kelamin:",-1)),p(" "+h(i.formData.jenis_kelamin),1)]),o("div",kt,[e[5]||(e[5]=o("strong",null,"Tempat, Tanggal Lahir:",-1)),p(" "+h(i.formData.kelahiran_tempat)+", "+h(s.formatDate(i.formData.kelahiran_tanggal)),1)]),o("div",bt,[e[6]||(e[6]=o("strong",null,"Alamat Tinggal:",-1)),p(" "+h(i.formData.alamat_tinggal||"Tidak ada"),1)]),o("div",vt,[e[7]||(e[7]=o("strong",null,"Nomor HP:",-1)),p(" "+h(i.formData.nomor_hape||"-"),1)]),i.selectedPhoto?(l(),u("div",Kt,[e[8]||(e[8]=o("strong",null,"Foto:",-1)),o("div",wt,[o("img",{src:s.photoPreviewUrl,alt:"Preview foto",class:"review-photo"},null,8,yt)])])):m("",!0),o("div",St,[e[9]||(e[9]=o("strong",null,"Desa/Kelompok:",-1)),p(" "+h(i.formData.sambung_desa)+" / "+h(i.formData.sambung_kelompok),1)]),o("div",Dt,[e[10]||(e[10]=o("strong",null,"Sekolah & Kelas:",-1)),p(" "+h(i.formData.sekolah_kelas||"-"),1)]),i.selectedHobi.length>0?(l(),u("div",It,[e[11]||(e[11]=o("strong",null,"Hobi:",-1)),(l(!0),u(y,null,S(i.selectedHobi,(a,d)=>(l(),u("div",{key:d,class:"review-hobi-item"}," - "+h(a.kategori)+": "+h(a.hobi),1))),128))])):m("",!0),o("div",Pt,[e[12]||(e[12]=o("strong",null,"Nama Ayah:",-1)),p(" "+h(i.formData.nama_ayah),1)]),o("div",Ct,[e[13]||(e[13]=o("strong",null,"Status Ayah:",-1)),p(" "+h(i.formData.status_ayah),1)]),o("div",Et,[e[14]||(e[14]=o("strong",null,"No. HP Ayah:",-1)),p(" "+h(i.formData.nomor_hape_ayah||"-"),1)]),o("div",_t,[e[15]||(e[15]=o("strong",null,"Nama Ibu:",-1)),p(" "+h(i.formData.nama_ibu),1)]),o("div",Lt,[e[16]||(e[16]=o("strong",null,"Status Ibu:",-1)),p(" "+h(i.formData.status_ibu),1)]),o("div",At,[e[17]||(e[17]=o("strong",null,"No. HP Ibu:",-1)),p(" "+h(i.formData.nomor_hape_ibu||"-"),1)]),o("div",Tt,[o("button",{onClick:e[0]||(e[0]=(...a)=>s.handleSubmit&&s.handleSubmit(...a)),disabled:i.isSubmitting},h(i.isSubmitting?"Mengirim...":"Kirim Data"),9,Ut),o("button",{onClick:e[1]||(e[1]=(...a)=>s.handleEdit&&s.handleEdit(...a)),class:"secondary"},"Edit Data")])])}const Ft=K(mt,[["render",Nt],["__scopeId","data-v-7761e3a6"]]),jt={name:"PhotoUpload",props:{modelValue:{type:File,default:null},existingPhotoFilename:{type:String,default:null},apiKey:{type:String,default:null}},emits:["update:modelValue"],data(){return{previewUrl:null,showCamera:!1,isCapturing:!1,cameraSupported:!1,stream:null,errorMessage:null,currentFacingMode:"user",hasMultipleCameras:!1,loadingExistingPhoto:!1,existingPhotoUrl:null}},computed:{displayPreviewUrl(){return this.previewUrl||this.existingPhotoUrl}},async mounted(){await this.checkCameraSupport(),this.existingPhotoFilename&&this.apiKey&&await this.loadExistingPhoto()},beforeUnmount(){this.stopCamera(),this.existingPhotoUrl&&URL.revokeObjectURL(this.existingPhotoUrl)},watch:{existingPhotoFilename:{handler(t){t&&this.apiKey?this.loadExistingPhoto():this.clearExistingPhoto()},immediate:!0},apiKey:{handler(t){t&&this.existingPhotoFilename&&this.loadExistingPhoto()}}},methods:{async checkCameraSupport(){if(this.cameraSupported=!!(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia),this.cameraSupported)try{const e=(await navigator.mediaDevices.enumerateDevices()).filter(i=>i.kind==="videoinput");this.hasMultipleCameras=e.length>1}catch(t){console.error("Error checking camera devices:",t)}},async openCamera(){if(!this.cameraSupported){this.errorMessage="Kamera tidak didukung di perangkat ini";return}this.isCapturing=!0,this.errorMessage=null;try{this.stream=await navigator.mediaDevices.getUserMedia({video:{facingMode:this.currentFacingMode,width:{ideal:640},height:{ideal:480}}}),this.showCamera=!0,this.$nextTick(()=>{this.$refs.videoElement&&(this.$refs.videoElement.srcObject=this.stream)})}catch(t){console.error("Error accessing camera:",t),this.errorMessage="Tidak dapat mengakses kamera. Pastikan izin kamera telah diberikan."}finally{this.isCapturing=!1}},closeCamera(){this.stopCamera(),this.showCamera=!1},stopCamera(){this.stream&&(this.stream.getTracks().forEach(t=>t.stop()),this.stream=null)},async switchCamera(){if(this.hasMultipleCameras){this.stopCamera(),this.currentFacingMode=this.currentFacingMode==="user"?"environment":"user";try{this.stream=await navigator.mediaDevices.getUserMedia({video:{facingMode:this.currentFacingMode,width:{ideal:640},height:{ideal:480}}}),this.$nextTick(()=>{this.$refs.videoElement&&(this.$refs.videoElement.srcObject=this.stream)})}catch(t){console.error("Error switching camera:",t),this.errorMessage="Tidak dapat mengganti kamera.",this.currentFacingMode=this.currentFacingMode==="user"?"environment":"user"}}},capturePhoto(){const t=this.$refs.videoElement,e=this.$refs.canvasElement;if(!t||!e)return;const i=e.getContext("2d");e.width=t.videoWidth,e.height=t.videoHeight,i.drawImage(t,0,0,e.width,e.height),e.toBlob(r=>{if(r){const n=new File([r],"camera-photo.png",{type:"image/png"});this.setPhoto(n)}},"image/png",.9),this.closeCamera()},handleFileSelect(t){const e=t.target.files[0];e&&this.setPhoto(e)},setPhoto(t){if(t.size>2097152){this.errorMessage="Ukuran file terlalu besar. Maksimal 2MB.";return}if(t.type!=="image/png"){this.errorMessage="File harus berupa gambar PNG.";return}this.errorMessage=null,this.previewUrl=URL.createObjectURL(t),this.$emit("update:modelValue",t)},removePhoto(){this.previewUrl&&(URL.revokeObjectURL(this.previewUrl),this.previewUrl=null),this.$refs.fileInput&&(this.$refs.fileInput.value=""),this.$emit("update:modelValue",null),this.errorMessage=null},async loadExistingPhoto(){if(!(!this.existingPhotoFilename||!this.apiKey)){this.loadingExistingPhoto=!0,this.clearExistingPhoto();try{const t=await fetch("/api/biodata/generus/foto/".concat(this.existingPhotoFilename),{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));const e=await t.blob();this.existingPhotoUrl=URL.createObjectURL(e)}catch(t){console.error("Error loading existing photo:",t),this.errorMessage="Gagal memuat foto yang sudah ada"}finally{this.loadingExistingPhoto=!1}}},clearExistingPhoto(){this.existingPhotoUrl&&(URL.revokeObjectURL(this.existingPhotoUrl),this.existingPhotoUrl=null)}}},Jt={class:"photo-upload-container"},Ot={key:0,class:"photo-preview"},Rt=["src"],$t={key:0,class:"existing-photo-label"},Ht={key:1,class:"loading-photo"},Mt={key:2,class:"upload-options"},Bt={class:"camera-section"},Vt=["disabled"],xt={key:0,class:"camera-warning"},qt={class:"file-section"},Gt={class:"camera-header"},zt={class:"camera-controls-header"},Wt={class:"camera-viewport"},Yt={ref:"videoElement",autoplay:"",playsinline:"",class:"camera-video"},Qt={ref:"canvasElement",class:"camera-canvas",style:{display:"none"}},Xt={class:"camera-controls"},Zt={key:4,class:"error-message"};function ea(t,e,i,r,n,s){return l(),u("div",Jt,[e[12]||(e[12]=o("div",{class:"section-title"},"Foto Profil",-1)),s.displayPreviewUrl?(l(),u("div",Ot,[o("img",{src:s.displayPreviewUrl,alt:"Preview foto",class:"preview-image"},null,8,Rt),o("button",{onClick:e[0]||(e[0]=(...a)=>s.removePhoto&&s.removePhoto(...a)),class:"remove-photo-btn",type:"button"}," × Hapus Foto "),n.existingPhotoUrl&&!n.previewUrl?(l(),u("div",$t," Foto yang sudah ada ")):m("",!0)])):m("",!0),n.loadingExistingPhoto?(l(),u("div",Ht,[...e[9]||(e[9]=[o("p",null,"Memuat foto yang sudah ada...",-1)])])):m("",!0),!s.displayPreviewUrl&&!n.loadingExistingPhoto?(l(),u("div",Mt,[o("div",Bt,[o("button",{onClick:e[1]||(e[1]=(...a)=>s.openCamera&&s.openCamera(...a)),disabled:n.isCapturing||!n.cameraSupported,class:"camera-btn",type:"button"}," 📷 "+h(n.isCapturing?"Membuka Kamera...":"Ambil Foto"),9,Vt),n.cameraSupported?m("",!0):(l(),u("p",xt," Kamera tidak tersedia di perangkat ini "))]),o("div",qt,[e[10]||(e[10]=o("label",{for:"photo-file",class:"file-upload-label"}," 📁 Pilih File Foto (PNG, Max 2MB) ",-1)),o("input",{id:"photo-file",type:"file",accept:"image/png",onChange:e[2]||(e[2]=(...a)=>s.handleFileSelect&&s.handleFileSelect(...a)),class:"file-input",ref:"fileInput"},null,544)])])):m("",!0),n.showCamera?(l(),u("div",{key:3,class:"camera-modal",onClick:e[8]||(e[8]=(...a)=>s.closeCamera&&s.closeCamera(...a))},[o("div",{class:"camera-content",onClick:e[7]||(e[7]=H(()=>{},["stop"]))},[o("div",Gt,[e[11]||(e[11]=o("h3",null,"Ambil Foto",-1)),o("div",zt,[n.hasMultipleCameras?(l(),u("button",{key:0,onClick:e[3]||(e[3]=(...a)=>s.switchCamera&&s.switchCamera(...a)),class:"switch-camera-btn",type:"button"}," 🔄 Ganti Kamera ")):m("",!0),o("button",{onClick:e[4]||(e[4]=(...a)=>s.closeCamera&&s.closeCamera(...a)),class:"close-btn",type:"button"}," ✕ ")])]),o("div",Wt,[o("video",Yt,null,512),o("canvas",Qt,null,512)]),o("div",Xt,[o("button",{onClick:e[5]||(e[5]=(...a)=>s.capturePhoto&&s.capturePhoto(...a)),class:"capture-btn",type:"button"}," 📸 Ambil Foto "),o("button",{onClick:e[6]||(e[6]=(...a)=>s.closeCamera&&s.closeCamera(...a)),class:"cancel-btn",type:"button"}," Batal ")])])])):m("",!0),n.errorMessage?(l(),u("div",Zt,h(n.errorMessage),1)):m("",!0)])}const ta=K(jt,[["render",ea],["__scopeId","data-v-6280c6fb"]]),aa={methods:{async fetchDataFromApi(t,e,i,r,n){console.log("Fetching data from ".concat(t,"..."));try{const s=await fetch(t);if(!s.ok)throw new Error("Network response was not ok");const a=await s.json(),d=e?e(a):a;return n.options=d,n.isLoading=!1,n.dataLoaded=!0,n.loadError=null,console.log("Data loaded successfully from ".concat(t)),d}catch(s){return console.error("Error fetching data from ".concat(t,":"),s),n.loadError=i||"Gagal memuat data. Silakan muat ulang halaman.",n.isLoading=!1,n.dataLoaded=!1,r&&setTimeout(r,5e3),null}},async fetchKelompokData(t){if(!t){console.error("Parameter data tidak ditemukan"),this.kelompokData.loadError="Parameter data tidak ditemukan",this.kelompokData.isLoading=!1;return}const e=encodeURIComponent(t),i="/api/data/daerah/".concat(e,"/"),r=n=>{const s={};let a=null;return n.forEach(d=>{s[d.ranah]||(s[d.ranah]=[]),s[d.ranah].push(d.detail_ranah),t&&(d.ranah.toLowerCase().includes(t.toLowerCase())||d.detail_ranah.toLowerCase().includes(t.toLowerCase()))&&(a={desa:d.ranah,kelompok:d.detail_ranah})}),a&&setTimeout(()=>{this.formData.sambung_desa=a.desa,this.formData.sambung_kelompok=a.kelompok},0),s};await this.fetchDataFromApi(i,r,"Gagal memuat data kelompok. Silakan muat ulang halaman.",()=>this.fetchKelompokData(t),this.kelompokData)},async fetchHobiData(){await this.fetchDataFromApi("/api/data/hobi",null,"Gagal memuat data hobi. Silakan muat ulang halaman.",()=>this.fetchHobiData(),this.hobiData)},async fetchSekolahKelasData(){try{const t=await fetch("/api/data/kelas-sekolah");if(!t.ok)throw new Error("Network response was not ok");const e=await t.json();this.sekolahKelasOptions=e}catch(t){console.error("Error fetching sekolah/kelas data:",t)}},validateFormFields(){const{sambung_kelompok:t,sambung_desa:e}=this.formData;return!t||!e?(alert("Pilihan Desa & Kelompok wajib diisi. Silakan ketik dan pilih dari daftar yang muncul."),!1):this.flattenedKelompok.some(r=>r.kelompok===t&&r.desa===e)?!0:(alert("Silahkan pilih kelompok sesuai dengan pilihan yang muncul saat Anda mengetik"),!1)},showReviewData(){this.validateFormFields()&&(this.showReview=!0)},editForm(t){console.log("EditForm called with alamat_tinggal:",this.formData.alamat_tinggal);const e=t&&t.savedAddress?t.savedAddress:this.formData.alamat_tinggal;this.showReview=!1,this.$nextTick(()=>{e&&(!this.formData.alamat_tinggal||this.formData.alamat_tinggal!==e)&&(console.log("Restoring alamat_tinggal to:",e),this.formData.alamat_tinggal=e),console.log("After toggling, alamat_tinggal is:",this.formData.alamat_tinggal)})},async submitToAPI(){if(!this.isSubmitting){if(console.log("=== SUBMISSION DEBUG START ==="),console.log("API Key present:",!!this.apiKey),!this.formData.sambung_desa||!this.formData.sambung_kelompok){console.error("Missing required Desa/Kelompok fields"),alert("Pilihan Desa & Kelompok wajib diisi sebelum mengirim data.");return}if(!this.apiKey){console.error("API key missing, aborting submission"),alert("API key diperlukan. Silakan gunakan URL dengan parameter key."),this.showApiKeyError=!0;return}this.isSubmitting=!0,console.log("Starting form submission process"),console.log("Original form data:",JSON.stringify(this.formData)),console.log("Selected hobi items:",this.selectedHobi),console.log("CRITICAL FIELDS CHECK:"),console.log("sambung_desa:",this.formData.sambung_desa),console.log("sambung_kelompok:",this.formData.sambung_kelompok);try{const t=new FormData;if(console.log("Created new FormData object"),t.append("sambung_desa",this.formData.sambung_desa),t.append("sambung_kelompok",this.formData.sambung_kelompok),console.log("Added sambung fields to FormData"),this.selectedHobi.length>0){console.log("Processing hobi data, found",this.selectedHobi.length,"selected items");const n={};this.selectedHobi.forEach(d=>{n[d.kategori]||(n[d.kategori]=[]),n[d.kategori].push(d.hobi)});const s={};for(const[d,k]of Object.entries(n))s[d]=k.join(", ");const a=JSON.stringify(s);t.append("hobi",a),console.log("Added hobi JSON string to FormData")}else t.append("hobi",JSON.stringify({})),console.log("No hobi selected, adding empty JSON object");console.log("Adding other form fields to FormData");for(const n in this.formData)["hobi","sambung_desa","sambung_kelompok"].includes(n)||this.formData[n]!==null&&this.formData[n]!==void 0&&this.formData[n]!==""&&t.append(n,this.formData[n]);const e="/api/biodata/generus";console.log("Sending request to:",e);const i=await fetch(e,{method:"POST",headers:{Authorization:"ApiKey "+this.apiKey},body:t});if(console.log("Response status:",i.status,i.statusText),!i.ok){const n=await i.text();console.error("Error response from server:",n);try{const s=JSON.parse(n);console.error("Parsed error response:",s)}catch(s){console.log("Error response is not valid JSON")}throw new Error("Server error: ".concat(i.status," - ").concat(n))}const r=await i.json();console.log("Success response:",r),this.showReview=!1,this.showSuccess=!0,console.log("Form submitted successfully")}catch(t){console.error("Error in submission process:",t),console.error("Error stack:",t.stack),alert("Terjadi kesalahan saat mengirim data. Mohon coba lagi.")}finally{this.isSubmitting=!1,console.log("=== SUBMISSION DEBUG END ===")}}},resetForm(){window.location.reload()},getCurrentDate(){const t=new Date,e=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),r=String(t.getDate()).padStart(2,"0");return"".concat(e,"-").concat(i,"-").concat(r)},formatDate(t){if(!t)return"";const e={day:"numeric",month:"long",year:"numeric"};return new Date(t).toLocaleDateString("id-ID",e)},handleKelompokInputChange(){if(!this.kelompokData.dataLoaded&&!this.kelompokData.isLoading){console.log("Data not loaded, retrying fetch...");const t=new URLSearchParams(window.location.search).get("data");this.fetchKelompokData(t)}}}},sa={components:{FormContainer:G,PersonalInfoForm:_e,ParentInfoForm:Oe,HobiSelector:Ge,KelompokSelector:st,SekolahKelasSelector:ht,ReviewDataSection:Ft,PhotoUpload:ta},mixins:[aa],data(){return{formData:{nama_lengkap:"",nama_panggilan:"",jenis_kelamin:"",kelahiran_tempat:"",kelahiran_tanggal:"",alamat_tinggal:"",pendataan_tanggal:this.getCurrentDate(),sambung_desa:"",sambung_kelompok:"",hobi:"",sekolah_kelas:"",nomor_hape:"",nama_ayah:"",nama_ibu:"",status_ayah:"",status_ibu:"",nomor_hape_ayah:"",nomor_hape_ibu:"",daerah:""},daerahParam:"",selectedPhoto:null,showSuccess:!1,showReview:!1,isSubmitting:!1,apiKey:null,showApiKeyError:!1,kelompokData:{options:{},isLoading:!0,loadError:null,dataLoaded:!1},hobiData:{options:[],isLoading:!0,loadError:null,dataLoaded:!1},sekolahKelasOptions:[],selectedHobi:[]}},computed:{flattenedKelompok(){return Object.entries(this.kelompokData.options).flatMap(([t,e])=>e.map(i=>({desa:t,kelompok:i})))}},mounted(){document.title=this.daerahParam?"PENDATAAN GENERUS ".concat(this.daerahParam):"PENDATAAN GENERUS",this.formData.pendataan_tanggal=this.getCurrentDate();const t=new URLSearchParams(window.location.search),e=t.get("data");this.daerahParam=t.get("daerah"),this.daerahParam&&(this.formData.daerah=this.daerahParam,document.title="PENDATAAN GENERUS ".concat(this.daerahParam)),this.apiKey=t.get("key"),this.fetchKelompokData(e),this.fetchHobiData(),this.fetchSekolahKelasData(),this.$nextTick(()=>{this.formData.alamat_tinggal||(this.formData.alamat_tinggal="")})},methods:{showReviewData(){if(console.log("About to show review with alamat_tinggal:",this.formData.alamat_tinggal),this.formData.alamat_tinggal){const t=this.formData.alamat_tinggal;this.showReview=!0,this.$nextTick(()=>{this.formData.alamat_tinggal||(console.log("Restoring saved alamat_tinggal"),this.formData.alamat_tinggal=t)})}else this.showReview=!0},async submitToAPI(){console.log("=== SUBMISSION DEBUG START ===");try{if(!this.apiKey||this.apiKey.trim()===""){console.error("API key is missing or empty"),this.showApiKeyError=!0,alert("API key is required but missing from the URL. Please check your link.");return}this.isSubmitting=!0,this.formData.alamat_tinggal||(console.log("Fixing missing alamat_tinggal"),this.formData.alamat_tinggal="Not specified"),console.log("API Key present:",!!this.apiKey),console.log("API Key length:",this.apiKey.length),console.log("API Key first 4 chars:",this.apiKey?"".concat(this.apiKey.substring(0,4),"..."):"none"),console.log("Starting form submission process");const t={};if(this.selectedHobi.length>0){for(const s of this.selectedHobi)t[s.kategori]||(t[s.kategori]=[]),t[s.kategori].push(String(s.hobi));for(const s of Object.keys(t))t[s]=t[s].join(", ");console.log("Hobi processed:",JSON.stringify(t,null,2))}const e="/api/biodata/generus/",i=new FormData;for(const s in this.formData)this.formData[s]!==null&&this.formData[s]!==void 0&&this.formData[s]!==""&&i.append(s,this.formData[s]);i.append("hobi",JSON.stringify(t)),this.selectedPhoto&&(i.append("foto",this.selectedPhoto),console.log("Photo added to form data:",this.selectedPhoto.name,this.selectedPhoto.size)),console.log("FormData prepared with photo support");const r={Authorization:"ApiKey ".concat(this.apiKey)};console.log("Headers to be sent:",r),console.log("Using fetch with FormData payload");const n=await fetch(e,{method:"POST",mode:"cors",headers:r,redirect:"manual",body:i,credentials:"include"});if(console.log("Response status:",n.status),!n.ok){const s=await n.text();let a;try{a=JSON.parse(s),console.log("Error response from server:",JSON.stringify(a,null,2))}catch(d){console.log("Error response (not JSON):",s),a={detail:s}}throw n.status===401?(console.error("Authorization failed - API key may be invalid"),alert("Authorization failed. Please check if your API key is valid and try again.")):alert("Server error (".concat(n.status,"): ").concat(a.detail||"Unknown error")),new Error("Server error: ".concat(n.status," - ").concat(JSON.stringify(a)))}console.log("Data submitted successfully!"),this.showSuccess=!0,this.showReview=!1}catch(t){console.error("Error in submission process:",t),console.error("Error stack:",t.stack),t.message.includes("Network error")?alert("Network error occurred. Please check your internet connection and try again."):t.message.includes("API key")?alert("API key error. Please check your access link."):alert("Terjadi kesalahan saat mengirim data. Silakan coba lagi.")}finally{this.isSubmitting=!1,console.log("=== SUBMISSION DEBUG END ===")}},resetForm(){this.formData={nama_lengkap:"",nama_panggilan:"",jenis_kelamin:"",kelahiran_tempat:"",kelahiran_tanggal:"",alamat_tinggal:"",pendataan_tanggal:this.getCurrentDate(),sambung_desa:"",sambung_kelompok:"",hobi:"",sekolah_kelas:"",nomor_hape:"",nama_ayah:"",nama_ibu:"",status_ayah:"",status_ibu:"",nomor_hape_ayah:"",nomor_hape_ibu:"",daerah:this.daerahParam||""},this.selectedHobi=[],this.selectedPhoto=null,this.showSuccess=!1,this.showReview=!1,this.isSubmitting=!1}},watch:{"formData.alamat_tinggal":t=>{console.log("biodata-generus detected alamat_tinggal change:",t)}}},ia={class:"form-subtitle"},na={key:0},oa={key:2,class:"confirmation-container"};function ra(t,e,i,r,n,s){const a=w("PersonalInfoForm"),d=w("KelompokSelector"),k=w("SekolahKelasSelector"),L=w("HobiSelector"),A=w("PhotoUpload"),D=w("ParentInfoForm"),T=w("ReviewDataSection"),U=w("FormContainer");return l(),$(U,null,{default:B(()=>[e[5]||(e[5]=o("div",{class:"form-title"},"FORMULIR PENDATAAN",-1)),o("div",ia,"GENERUS "+h(n.daerahParam||"SKC"),1),!n.showReview&&!n.showSuccess?(l(),u("div",na,[P(a,{formData:n.formData},null,8,["formData"]),P(d,{formData:n.formData,kelompokOptions:n.kelompokData.options,flattenedKelompok:s.flattenedKelompok,dataLoaded:n.kelompokData.dataLoaded,isLoading:n.kelompokData.isLoading,loadError:n.kelompokData.loadError,onInputChange:t.handleKelompokInputChange},null,8,["formData","kelompokOptions","flattenedKelompok","dataLoaded","isLoading","loadError","onInputChange"]),P(k,{formData:n.formData,sekolahKelasOptions:n.sekolahKelasOptions},null,8,["formData","sekolahKelasOptions"]),P(L,{hobiOptions:n.hobiData.options,selectedHobi:n.selectedHobi},null,8,["hobiOptions","selectedHobi"]),P(A,{modelValue:n.selectedPhoto,"onUpdate:modelValue":e[0]||(e[0]=b=>n.selectedPhoto=b)},null,8,["modelValue"]),e[3]||(e[3]=o("div",{class:"spaci"},null,-1)),P(D,{formData:n.formData},null,8,["formData"]),o("button",{onClick:e[1]||(e[1]=(...b)=>s.showReviewData&&s.showReviewData(...b))},"Review Data")])):m("",!0),n.showReview&&!n.showSuccess?(l(),$(T,{key:1,formData:n.formData,selectedHobi:n.selectedHobi,selectedPhoto:n.selectedPhoto,isSubmitting:n.isSubmitting,onSubmit:s.submitToAPI,onEdit:t.editForm},null,8,["formData","selectedHobi","selectedPhoto","isSubmitting","onSubmit","onEdit"])):m("",!0),n.showSuccess?(l(),u("div",oa,[e[4]||(e[4]=o("div",{class:"confirmation-message"},[p(" Data berhasil dikirim!"),o("br"),p(" Alhamdulillah Jazaa Kumullohu Khoiro. ")],-1)),o("button",{onClick:e[2]||(e[2]=(...b)=>s.resetForm&&s.resetForm(...b))},"Isi Formulir Baru")])):m("",!0)]),_:1})}const da=K(sa,[["render",ra]]);export{da as default};
