System.register(["./vendor-legacy-wqB_kNia.js"],function(e,t){"use strict";var n,a,i,o,r,s,d,u,c,l,p,m,h;return{setters:[e=>{n=e.d,a=e.c,i=e.o,o=e.a,r=e.b,s=e.w,d=e.r,u=e.e,c=e.f,l=e.g,p=e.h,m=e.i,h=e.j}],execute:function(){var g=document.createElement("style");g.textContent=".app,.main{margin:0;padding:0;border:0;font-size:100%;font:inherit;line-height:1;box-sizing:border-box;background-color:#fff;color:#2c4a3e;width:100%;display:flex;flex-direction:column;align-items:center;min-height:100vh}.loading{display:flex;justify-content:center;align-items:center;height:100vh;font-size:1.5rem}.view-wrapper{width:100%;max-width:100%;display:flex;flex-direction:column;align-items:center}\n/*$vite$:1*/",document.head.appendChild(g);const f=e("_",(e,t)=>{const n=e.__vccOpts||e;for(const[a,i]of t)n[a]=i;return n}),v=n({data:()=>({isLoading:!1}),computed:{currentRoute(){return this.$route}},methods:{setLoading(e){this.isLoading=e}}}),y={class:"app"},w={class:"main",role:"main"},b={key:1,class:"loading"},x=f(v,[["render",function(e,t,n,l,p,m){const h=d("router-view");return i(),a("div",y,[o("main",w,[r(h,null,{default:s(({Component:t,route:n})=>[(i(),a("div",{class:"view-wrapper",key:n.path},[e.isLoading?(i(),a("div",b,"Loading...")):(i(),u(c(t),{key:0,onLoading:e.setLoading},null,40,["onLoading"]))]))]),_:1})])])}]]),L=e("a",function(e,t,n){let a=Promise.resolve();function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then(t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)})}),j={BiodataGenerus:()=>L(()=>t.import("./biodata-generus-legacy-OccE6xh2.js"),void 0),PantauBiodataGenerus:()=>L(()=>t.import("./pantau-biodata-generus-legacy-BwBnmJra.js").then(e=>e.p),void 0)},B=[{path:"/biodata-generus",name:"BiodataGenerus",title:"Biodata Generus"},{path:"/pantau-biodata-generus",name:"PantauBiodataGenerus",title:"Pantauan Biodata Generus"}].map(e=>{const{name:t,path:n}=e;return{name:t,path:n,component:j[t],meta:{title:e.title,requiresAuth:!1}}}),G=l({history:p(),routes:B}),P=m(x),k=h();P.use(k),P.use(G),P.mount("#app")}}});
