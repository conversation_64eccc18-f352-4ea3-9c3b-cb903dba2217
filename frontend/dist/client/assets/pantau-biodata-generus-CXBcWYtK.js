const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-CvtAQiqk.js","assets/index-DgMiBKWL.js","assets/vendor-eS0gtEdu.js","assets/index-CCdYf6ya.css"])))=>i.map(i=>d[i]);
var vc=Object.defineProperty;var bc=(r,t,e)=>t in r?vc(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e;var xe=(r,t,e)=>bc(r,typeof t!="symbol"?t+"":t,e);import{a as Ns,_ as Ua}from"./index-DgMiBKWL.js";import{c as Re,a as xt,m as Ss,p as lu,F as Zs,n as Qs,v as yc,o as Me,t as ue,q as je,l as $i,s as wc,b as yo,r as wo}from"./vendor-eS0gtEdu.js";function _e(r){"@babel/helpers - typeof";return _e=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_e(r)}var on=Uint8Array,Ur=Uint16Array,pl=Int32Array,gl=new on([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ml=new on([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),uu=new on([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Fh=function(r,t){for(var e=new Ur(31),n=0;n<31;++n)e[n]=t+=1<<r[n-1];for(var i=new pl(e[30]),n=1;n<30;++n)for(var a=e[n];a<e[n+1];++a)i[a]=a-e[n]<<5|n;return{b:e,r:i}},Eh=Fh(gl,2),xc=Eh.b,tl=Eh.r;xc[28]=258,tl[258]=28;var _c=Fh(ml,0),hu=_c.r,el=new Ur(32768);for(var De=0;De<32768;++De){var ri=(De&43690)>>1|(De&21845)<<1;ri=(ri&52428)>>2|(ri&13107)<<2,ri=(ri&61680)>>4|(ri&3855)<<4,el[De]=((ri&65280)>>8|(ri&255)<<8)>>1}var ja=(function(r,t,e){for(var n=r.length,i=0,a=new Ur(t);i<n;++i)r[i]&&++a[r[i]-1];var u=new Ur(t);for(i=1;i<t;++i)u[i]=u[i-1]+a[i-1]<<1;var l;if(e){l=new Ur(1<<t);var h=15-t;for(i=0;i<n;++i)if(r[i])for(var f=i<<4|r[i],p=t-r[i],y=u[r[i]-1]++<<p,x=y|(1<<p)-1;y<=x;++y)l[el[y]>>h]=f}else for(l=new Ur(n),i=0;i<n;++i)r[i]&&(l[i]=el[u[r[i]-1]++]>>15-r[i]);return l}),Si=new on(288);for(var De=0;De<144;++De)Si[De]=8;for(var De=144;De<256;++De)Si[De]=9;for(var De=256;De<280;++De)Si[De]=7;for(var De=280;De<288;++De)Si[De]=8;var jo=new on(32);for(var De=0;De<32;++De)jo[De]=5;var Ac=ja(Si,9,0),Lc=ja(jo,5,0),Ih=function(r){return(r+7)/8|0},Nc=function(r,t,e){return(e==null||e>r.length)&&(e=r.length),new on(r.subarray(t,e))},Tn=function(r,t,e){e<<=t&7;var n=t/8|0;r[n]|=e,r[n+1]|=e>>8},Da=function(r,t,e){e<<=t&7;var n=t/8|0;r[n]|=e,r[n+1]|=e>>8,r[n+2]|=e>>16},ks=function(r,t){for(var e=[],n=0;n<r.length;++n)r[n]&&e.push({s:n,f:r[n]});var i=e.length,a=e.slice();if(!i)return{t:Dh,l:0};if(i==1){var u=new on(e[0].s+1);return u[e[0].s]=1,{t:u,l:1}}e.sort(function(at,vt){return at.f-vt.f}),e.push({s:-1,f:25001});var l=e[0],h=e[1],f=0,p=1,y=2;for(e[0]={s:-1,f:l.f+h.f,l,r:h};p!=i-1;)l=e[e[f].f<e[y].f?f++:y++],h=e[f!=p&&e[f].f<e[y].f?f++:y++],e[p++]={s:-1,f:l.f+h.f,l,r:h};for(var x=a[0].s,n=1;n<i;++n)a[n].s>x&&(x=a[n].s);var g=new Ur(x+1),P=rl(e[p-1],g,0);if(P>t){var n=0,C=0,D=P-t,S=1<<D;for(a.sort(function(vt,ot){return g[ot.s]-g[vt.s]||vt.f-ot.f});n<i;++n){var W=a[n].s;if(g[W]>t)C+=S-(1<<P-g[W]),g[W]=t;else break}for(C>>=D;C>0;){var z=a[n].s;g[z]<t?C-=1<<t-g[z]++-1:++n}for(;n>=0&&C;--n){var M=a[n].s;g[M]==t&&(--g[M],++C)}P=t}return{t:new on(g),l:P}},rl=function(r,t,e){return r.s==-1?Math.max(rl(r.l,t,e+1),rl(r.r,t,e+1)):t[r.s]=e},fu=function(r){for(var t=r.length;t&&!r[--t];);for(var e=new Ur(++t),n=0,i=r[0],a=1,u=function(h){e[n++]=h},l=1;l<=t;++l)if(r[l]==i&&l!=t)++a;else{if(!i&&a>2){for(;a>138;a-=138)u(32754);a>2&&(u(a>10?a-11<<5|28690:a-3<<5|12305),a=0)}else if(a>3){for(u(i),--a;a>6;a-=6)u(8304);a>2&&(u(a-3<<5|8208),a=0)}for(;a--;)u(i);a=1,i=r[l]}return{c:e.subarray(0,n),n:t}},Ba=function(r,t){for(var e=0,n=0;n<t.length;++n)e+=r[n]*t[n];return e},Oh=function(r,t,e){var n=e.length,i=Ih(t+2);r[i]=n&255,r[i+1]=n>>8,r[i+2]=r[i]^255,r[i+3]=r[i+1]^255;for(var a=0;a<n;++a)r[i+a+4]=e[a];return(i+4+n)*8},cu=function(r,t,e,n,i,a,u,l,h,f,p){Tn(t,p++,e),++i[256];for(var y=ks(i,15),x=y.t,g=y.l,P=ks(a,15),C=P.t,D=P.l,S=fu(x),W=S.c,z=S.n,M=fu(C),at=M.c,vt=M.n,ot=new Ur(19),$=0;$<W.length;++$)++ot[W[$]&31];for(var $=0;$<at.length;++$)++ot[at[$]&31];for(var R=ks(ot,7),et=R.t,N=R.l,O=19;O>4&&!et[uu[O-1]];--O);var G=f+5<<3,U=Ba(i,Si)+Ba(a,jo)+u,nt=Ba(i,x)+Ba(a,C)+u+14+3*O+Ba(ot,et)+2*ot[16]+3*ot[17]+7*ot[18];if(h>=0&&G<=U&&G<=nt)return Oh(t,p,r.subarray(h,h+f));var ht,dt,rt,ft;if(Tn(t,p,1+(nt<U)),p+=2,nt<U){ht=ja(x,g,0),dt=x,rt=ja(C,D,0),ft=C;var Nt=ja(et,N,0);Tn(t,p,z-257),Tn(t,p+5,vt-1),Tn(t,p+10,O-4),p+=14;for(var $=0;$<O;++$)Tn(t,p+3*$,et[uu[$]]);p+=3*O;for(var wt=[W,at],A=0;A<2;++A)for(var B=wt[A],$=0;$<B.length;++$){var T=B[$]&31;Tn(t,p,Nt[T]),p+=et[T],T>15&&(Tn(t,p,B[$]>>5&127),p+=B[$]>>12)}}else ht=Ac,dt=Si,rt=Lc,ft=jo;for(var $=0;$<l;++$){var V=n[$];if(V>255){var T=V>>18&31;Da(t,p,ht[T+257]),p+=dt[T+257],T>7&&(Tn(t,p,V>>23&31),p+=gl[T]);var Y=V&31;Da(t,p,rt[Y]),p+=ft[Y],Y>3&&(Da(t,p,V>>5&8191),p+=ml[Y])}else Da(t,p,ht[V]),p+=dt[V]}return Da(t,p,ht[256]),p+dt[256]},Sc=new pl([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Dh=new on(0),kc=function(r,t,e,n,i,a){var u=a.z||r.length,l=new on(n+u+5*(1+Math.ceil(u/7e3))+i),h=l.subarray(n,l.length-i),f=a.l,p=(a.r||0)&7;if(t){p&&(h[0]=a.r>>3);for(var y=Sc[t-1],x=y>>13,g=y&8191,P=(1<<e)-1,C=a.p||new Ur(32768),D=a.h||new Ur(P+1),S=Math.ceil(e/3),W=2*S,z=function(st){return(r[st]^r[st+1]<<S^r[st+2]<<W)&P},M=new pl(25e3),at=new Ur(288),vt=new Ur(32),ot=0,$=0,R=a.i||0,et=0,N=a.w||0,O=0;R+2<u;++R){var G=z(R),U=R&32767,nt=D[G];if(C[U]=nt,D[G]=U,N<=R){var ht=u-R;if((ot>7e3||et>24576)&&(ht>423||!f)){p=cu(r,h,0,M,at,vt,$,et,O,R-O,p),et=ot=$=0,O=R;for(var dt=0;dt<286;++dt)at[dt]=0;for(var dt=0;dt<30;++dt)vt[dt]=0}var rt=2,ft=0,Nt=g,wt=U-nt&32767;if(ht>2&&G==z(R-wt))for(var A=Math.min(x,ht)-1,B=Math.min(32767,R),T=Math.min(258,ht);wt<=B&&--Nt&&U!=nt;){if(r[R+rt]==r[R+rt-wt]){for(var V=0;V<T&&r[R+V]==r[R+V-wt];++V);if(V>rt){if(rt=V,ft=wt,V>A)break;for(var Y=Math.min(wt,V-2),Z=0,dt=0;dt<Y;++dt){var lt=R-wt+dt&32767,it=C[lt],mt=lt-it&32767;mt>Z&&(Z=mt,nt=lt)}}}U=nt,nt=C[U],wt+=U-nt&32767}if(ft){M[et++]=268435456|tl[rt]<<18|hu[ft];var Lt=tl[rt]&31,Pt=hu[ft]&31;$+=gl[Lt]+ml[Pt],++at[257+Lt],++vt[Pt],N=R+rt,++ot}else M[et++]=r[R],++at[r[R]]}}for(R=Math.max(R,N);R<u;++R)M[et++]=r[R],++at[r[R]];p=cu(r,h,f,M,at,vt,$,et,O,R-O,p),f||(a.r=p&7|h[p/8|0]<<3,p-=7,a.h=D,a.p=C,a.i=R,a.w=N)}else{for(var R=a.w||0;R<u+f;R+=65535){var Ct=R+65535;Ct>=u&&(h[p/8|0]=f,Ct=u),p=Oh(h,p+1,r.subarray(R,Ct))}a.i=u}return Nc(l,0,n+Ih(p)+i)},Bh=function(){var r=1,t=0;return{p:function(e){for(var n=r,i=t,a=e.length|0,u=0;u!=a;){for(var l=Math.min(u+2655,a);u<l;++u)i+=n+=e[u];n=(n&65535)+15*(n>>16),i=(i&65535)+15*(i>>16)}r=n,t=i},d:function(){return r%=65521,t%=65521,(r&255)<<24|(r&65280)<<8|(t&255)<<8|t>>8}}},Pc=function(r,t,e,n,i){if(!i&&(i={l:1},t.dictionary)){var a=t.dictionary.subarray(-32768),u=new on(a.length+r.length);u.set(a),u.set(r,a.length),r=u,i.w=a.length}return kc(r,t.level==null?6:t.level,t.mem==null?i.l?Math.ceil(Math.max(8,Math.min(13,Math.log(r.length)))*1.5):20:12+t.mem,e,n,i)},jh=function(r,t,e){for(;e;++t)r[t]=e,e>>>=8},Cc=function(r,t){var e=t.level,n=e==0?0:e<6?1:e==9?3:2;if(r[0]=120,r[1]=n<<6|(t.dictionary&&32),r[1]|=31-(r[0]<<8|r[1])%31,t.dictionary){var i=Bh();i.p(t.dictionary),jh(r,2,i.d())}};function nl(r,t){t||(t={});var e=Bh();e.p(r);var n=Pc(r,t,t.dictionary?6:2,4);return Cc(n,t),jh(n,n.length-4,e.d()),n}var Fc=typeof TextDecoder<"u"&&new TextDecoder,Ec=0;try{Fc.decode(Dh,{stream:!0}),Ec=1}catch(r){}function Ic(r){if(Array.isArray(r))return r}function Oc(r,t){var e=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(e!=null){var n,i,a,u,l=[],h=!0,f=!1;try{if(a=(e=e.call(r)).next,t!==0)for(;!(h=(n=a.call(e)).done)&&(l.push(n.value),l.length!==t);h=!0);}catch(p){f=!0,i=p}finally{try{if(!h&&e.return!=null&&(u=e.return(),Object(u)!==u))return}finally{if(f)throw i}}return l}}function du(r,t){(t==null||t>r.length)&&(t=r.length);for(var e=0,n=Array(t);e<t;e++)n[e]=r[e];return n}function Dc(r,t){if(r){if(typeof r=="string")return du(r,t);var e={}.toString.call(r).slice(8,-1);return e==="Object"&&r.constructor&&(e=r.constructor.name),e==="Map"||e==="Set"?Array.from(r):e==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?du(r,t):void 0}}function Bc(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function pu(r,t){return Ic(r)||Oc(r,t)||Dc(r,t)||Bc()}function gu(r,t="utf8"){return new TextDecoder(t).decode(r)}const jc=new TextEncoder;function Tc(r){return jc.encode(r)}const Rc=1024*8,Mc=(()=>{const r=new Uint8Array(4),t=new Uint32Array(r.buffer);return!((t[0]=1)&r[0])})(),Ps={int8:globalThis.Int8Array,uint8:globalThis.Uint8Array,int16:globalThis.Int16Array,uint16:globalThis.Uint16Array,int32:globalThis.Int32Array,uint32:globalThis.Uint32Array,uint64:globalThis.BigUint64Array,int64:globalThis.BigInt64Array,float32:globalThis.Float32Array,float64:globalThis.Float64Array};class vl{constructor(t=Rc,e={}){xe(this,"buffer");xe(this,"byteLength");xe(this,"byteOffset");xe(this,"length");xe(this,"offset");xe(this,"lastWrittenByte");xe(this,"littleEndian");xe(this,"_data");xe(this,"_mark");xe(this,"_marks");let n=!1;typeof t=="number"?t=new ArrayBuffer(t):(n=!0,this.lastWrittenByte=t.byteLength);const i=e.offset?e.offset>>>0:0,a=t.byteLength-i;let u=i;(ArrayBuffer.isView(t)||t instanceof vl)&&(t.byteLength!==t.buffer.byteLength&&(u=t.byteOffset+i),t=t.buffer),n?this.lastWrittenByte=a:this.lastWrittenByte=0,this.buffer=t,this.length=a,this.byteLength=a,this.byteOffset=u,this.offset=0,this.littleEndian=!0,this._data=new DataView(this.buffer,u,a),this._mark=0,this._marks=[]}available(t=1){return this.offset+t<=this.length}isLittleEndian(){return this.littleEndian}setLittleEndian(){return this.littleEndian=!0,this}isBigEndian(){return!this.littleEndian}setBigEndian(){return this.littleEndian=!1,this}skip(t=1){return this.offset+=t,this}back(t=1){return this.offset-=t,this}seek(t){return this.offset=t,this}mark(){return this._mark=this.offset,this}reset(){return this.offset=this._mark,this}pushMark(){return this._marks.push(this.offset),this}popMark(){const t=this._marks.pop();if(t===void 0)throw new Error("Mark stack empty");return this.seek(t),this}rewind(){return this.offset=0,this}ensureAvailable(t=1){if(!this.available(t)){const n=(this.offset+t)*2,i=new Uint8Array(n);i.set(new Uint8Array(this.buffer)),this.buffer=i.buffer,this.length=n,this.byteLength=n,this._data=new DataView(this.buffer)}return this}readBoolean(){return this.readUint8()!==0}readInt8(){return this._data.getInt8(this.offset++)}readUint8(){return this._data.getUint8(this.offset++)}readByte(){return this.readUint8()}readBytes(t=1){return this.readArray(t,"uint8")}readArray(t,e){const n=Ps[e].BYTES_PER_ELEMENT*t,i=this.byteOffset+this.offset,a=this.buffer.slice(i,i+n);if(this.littleEndian===Mc&&e!=="uint8"&&e!=="int8"){const l=new Uint8Array(this.buffer.slice(i,i+n));l.reverse();const h=new Ps[e](l.buffer);return this.offset+=n,h.reverse(),h}const u=new Ps[e](a);return this.offset+=n,u}readInt16(){const t=this._data.getInt16(this.offset,this.littleEndian);return this.offset+=2,t}readUint16(){const t=this._data.getUint16(this.offset,this.littleEndian);return this.offset+=2,t}readInt32(){const t=this._data.getInt32(this.offset,this.littleEndian);return this.offset+=4,t}readUint32(){const t=this._data.getUint32(this.offset,this.littleEndian);return this.offset+=4,t}readFloat32(){const t=this._data.getFloat32(this.offset,this.littleEndian);return this.offset+=4,t}readFloat64(){const t=this._data.getFloat64(this.offset,this.littleEndian);return this.offset+=8,t}readBigInt64(){const t=this._data.getBigInt64(this.offset,this.littleEndian);return this.offset+=8,t}readBigUint64(){const t=this._data.getBigUint64(this.offset,this.littleEndian);return this.offset+=8,t}readChar(){return String.fromCharCode(this.readInt8())}readChars(t=1){let e="";for(let n=0;n<t;n++)e+=this.readChar();return e}readUtf8(t=1){return gu(this.readBytes(t))}decodeText(t=1,e="utf8"){return gu(this.readBytes(t),e)}writeBoolean(t){return this.writeUint8(t?255:0),this}writeInt8(t){return this.ensureAvailable(1),this._data.setInt8(this.offset++,t),this._updateLastWrittenByte(),this}writeUint8(t){return this.ensureAvailable(1),this._data.setUint8(this.offset++,t),this._updateLastWrittenByte(),this}writeByte(t){return this.writeUint8(t)}writeBytes(t){this.ensureAvailable(t.length);for(let e=0;e<t.length;e++)this._data.setUint8(this.offset++,t[e]);return this._updateLastWrittenByte(),this}writeInt16(t){return this.ensureAvailable(2),this._data.setInt16(this.offset,t,this.littleEndian),this.offset+=2,this._updateLastWrittenByte(),this}writeUint16(t){return this.ensureAvailable(2),this._data.setUint16(this.offset,t,this.littleEndian),this.offset+=2,this._updateLastWrittenByte(),this}writeInt32(t){return this.ensureAvailable(4),this._data.setInt32(this.offset,t,this.littleEndian),this.offset+=4,this._updateLastWrittenByte(),this}writeUint32(t){return this.ensureAvailable(4),this._data.setUint32(this.offset,t,this.littleEndian),this.offset+=4,this._updateLastWrittenByte(),this}writeFloat32(t){return this.ensureAvailable(4),this._data.setFloat32(this.offset,t,this.littleEndian),this.offset+=4,this._updateLastWrittenByte(),this}writeFloat64(t){return this.ensureAvailable(8),this._data.setFloat64(this.offset,t,this.littleEndian),this.offset+=8,this._updateLastWrittenByte(),this}writeBigInt64(t){return this.ensureAvailable(8),this._data.setBigInt64(this.offset,t,this.littleEndian),this.offset+=8,this._updateLastWrittenByte(),this}writeBigUint64(t){return this.ensureAvailable(8),this._data.setBigUint64(this.offset,t,this.littleEndian),this.offset+=8,this._updateLastWrittenByte(),this}writeChar(t){return this.writeUint8(t.charCodeAt(0))}writeChars(t){for(let e=0;e<t.length;e++)this.writeUint8(t.charCodeAt(e));return this}writeUtf8(t){return this.writeBytes(Tc(t))}toArray(){return new Uint8Array(this.buffer,this.byteOffset,this.lastWrittenByte)}getWrittenByteLength(){return this.lastWrittenByte-this.byteOffset}_updateLastWrittenByte(){this.offset>this.lastWrittenByte&&(this.lastWrittenByte=this.offset)}}function aa(r){let t=r.length;for(;--t>=0;)r[t]=0}const qc=3,Uc=258,Th=29,zc=256,Hc=zc+1+Th,Rh=30,Wc=512,Gc=new Array((Hc+2)*2);aa(Gc);const Vc=new Array(Rh*2);aa(Vc);const Kc=new Array(Wc);aa(Kc);const Yc=new Array(Uc-qc+1);aa(Yc);const Jc=new Array(Th);aa(Jc);const $c=new Array(Rh);aa($c);const Xc=(r,t,e,n)=>{let i=r&65535|0,a=r>>>16&65535|0,u=0;for(;e!==0;){u=e>2e3?2e3:e,e-=u;do i=i+t[n++]|0,a=a+i|0;while(--u);i%=65521,a%=65521}return i|a<<16|0};var il=Xc;const Zc=()=>{let r,t=[];for(var e=0;e<256;e++){r=e;for(var n=0;n<8;n++)r=r&1?3988292384^r>>>1:r>>>1;t[e]=r}return t},Qc=new Uint32Array(Zc()),t1=(r,t,e,n)=>{const i=Qc,a=n+e;r^=-1;for(let u=n;u<a;u++)r=r>>>8^i[(r^t[u])&255];return r^-1};var yn=t1,al={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},Mh={Z_NO_FLUSH:0,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_DEFLATED:8};const e1=(r,t)=>Object.prototype.hasOwnProperty.call(r,t);var r1=function(r){const t=Array.prototype.slice.call(arguments,1);for(;t.length;){const e=t.shift();if(e){if(typeof e!="object")throw new TypeError(e+"must be non-object");for(const n in e)e1(e,n)&&(r[n]=e[n])}}return r},n1=r=>{let t=0;for(let n=0,i=r.length;n<i;n++)t+=r[n].length;const e=new Uint8Array(t);for(let n=0,i=0,a=r.length;n<a;n++){let u=r[n];e.set(u,i),i+=u.length}return e},qh={assign:r1,flattenChunks:n1};let Uh=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(r){Uh=!1}const Ma=new Uint8Array(256);for(let r=0;r<256;r++)Ma[r]=r>=252?6:r>=248?5:r>=240?4:r>=224?3:r>=192?2:1;Ma[254]=Ma[254]=1;var i1=r=>{if(typeof TextEncoder=="function"&&TextEncoder.prototype.encode)return new TextEncoder().encode(r);let t,e,n,i,a,u=r.length,l=0;for(i=0;i<u;i++)e=r.charCodeAt(i),(e&64512)===55296&&i+1<u&&(n=r.charCodeAt(i+1),(n&64512)===56320&&(e=65536+(e-55296<<10)+(n-56320),i++)),l+=e<128?1:e<2048?2:e<65536?3:4;for(t=new Uint8Array(l),a=0,i=0;a<l;i++)e=r.charCodeAt(i),(e&64512)===55296&&i+1<u&&(n=r.charCodeAt(i+1),(n&64512)===56320&&(e=65536+(e-55296<<10)+(n-56320),i++)),e<128?t[a++]=e:e<2048?(t[a++]=192|e>>>6,t[a++]=128|e&63):e<65536?(t[a++]=224|e>>>12,t[a++]=128|e>>>6&63,t[a++]=128|e&63):(t[a++]=240|e>>>18,t[a++]=128|e>>>12&63,t[a++]=128|e>>>6&63,t[a++]=128|e&63);return t};const a1=(r,t)=>{if(t<65534&&r.subarray&&Uh)return String.fromCharCode.apply(null,r.length===t?r:r.subarray(0,t));let e="";for(let n=0;n<t;n++)e+=String.fromCharCode(r[n]);return e};var o1=(r,t)=>{const e=t||r.length;if(typeof TextDecoder=="function"&&TextDecoder.prototype.decode)return new TextDecoder().decode(r.subarray(0,t));let n,i;const a=new Array(e*2);for(i=0,n=0;n<e;){let u=r[n++];if(u<128){a[i++]=u;continue}let l=Ma[u];if(l>4){a[i++]=65533,n+=l-1;continue}for(u&=l===2?31:l===3?15:7;l>1&&n<e;)u=u<<6|r[n++]&63,l--;if(l>1){a[i++]=65533;continue}u<65536?a[i++]=u:(u-=65536,a[i++]=55296|u>>10&1023,a[i++]=56320|u&1023)}return a1(a,i)},s1=(r,t)=>{t=t||r.length,t>r.length&&(t=r.length);let e=t-1;for(;e>=0&&(r[e]&192)===128;)e--;return e<0||e===0?t:e+Ma[r[e]]>t?e:t},ol={string2buf:i1,buf2string:o1,utf8border:s1};function l1(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var u1=l1;const xo=16209,h1=16191;var f1=function(t,e){let n,i,a,u,l,h,f,p,y,x,g,P,C,D,S,W,z,M,at,vt,ot,$,R,et;const N=t.state;n=t.next_in,R=t.input,i=n+(t.avail_in-5),a=t.next_out,et=t.output,u=a-(e-t.avail_out),l=a+(t.avail_out-257),h=N.dmax,f=N.wsize,p=N.whave,y=N.wnext,x=N.window,g=N.hold,P=N.bits,C=N.lencode,D=N.distcode,S=(1<<N.lenbits)-1,W=(1<<N.distbits)-1;t:do{P<15&&(g+=R[n++]<<P,P+=8,g+=R[n++]<<P,P+=8),z=C[g&S];e:for(;;){if(M=z>>>24,g>>>=M,P-=M,M=z>>>16&255,M===0)et[a++]=z&65535;else if(M&16){at=z&65535,M&=15,M&&(P<M&&(g+=R[n++]<<P,P+=8),at+=g&(1<<M)-1,g>>>=M,P-=M),P<15&&(g+=R[n++]<<P,P+=8,g+=R[n++]<<P,P+=8),z=D[g&W];r:for(;;){if(M=z>>>24,g>>>=M,P-=M,M=z>>>16&255,M&16){if(vt=z&65535,M&=15,P<M&&(g+=R[n++]<<P,P+=8,P<M&&(g+=R[n++]<<P,P+=8)),vt+=g&(1<<M)-1,vt>h){t.msg="invalid distance too far back",N.mode=xo;break t}if(g>>>=M,P-=M,M=a-u,vt>M){if(M=vt-M,M>p&&N.sane){t.msg="invalid distance too far back",N.mode=xo;break t}if(ot=0,$=x,y===0){if(ot+=f-M,M<at){at-=M;do et[a++]=x[ot++];while(--M);ot=a-vt,$=et}}else if(y<M){if(ot+=f+y-M,M-=y,M<at){at-=M;do et[a++]=x[ot++];while(--M);if(ot=0,y<at){M=y,at-=M;do et[a++]=x[ot++];while(--M);ot=a-vt,$=et}}}else if(ot+=y-M,M<at){at-=M;do et[a++]=x[ot++];while(--M);ot=a-vt,$=et}for(;at>2;)et[a++]=$[ot++],et[a++]=$[ot++],et[a++]=$[ot++],at-=3;at&&(et[a++]=$[ot++],at>1&&(et[a++]=$[ot++]))}else{ot=a-vt;do et[a++]=et[ot++],et[a++]=et[ot++],et[a++]=et[ot++],at-=3;while(at>2);at&&(et[a++]=et[ot++],at>1&&(et[a++]=et[ot++]))}}else if((M&64)===0){z=D[(z&65535)+(g&(1<<M)-1)];continue r}else{t.msg="invalid distance code",N.mode=xo;break t}break}}else if((M&64)===0){z=C[(z&65535)+(g&(1<<M)-1)];continue e}else if(M&32){N.mode=h1;break t}else{t.msg="invalid literal/length code",N.mode=xo;break t}break}}while(n<i&&a<l);at=P>>3,n-=at,P-=at<<3,g&=(1<<P)-1,t.next_in=n,t.next_out=a,t.avail_in=n<i?5+(i-n):5-(n-i),t.avail_out=a<l?257+(l-a):257-(a-l),N.hold=g,N.bits=P};const Ki=15,mu=852,vu=592,bu=0,Cs=1,yu=2,c1=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),d1=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),p1=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),g1=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),m1=(r,t,e,n,i,a,u,l)=>{const h=l.bits;let f=0,p=0,y=0,x=0,g=0,P=0,C=0,D=0,S=0,W=0,z,M,at,vt,ot,$=null,R;const et=new Uint16Array(Ki+1),N=new Uint16Array(Ki+1);let O=null,G,U,nt;for(f=0;f<=Ki;f++)et[f]=0;for(p=0;p<n;p++)et[t[e+p]]++;for(g=h,x=Ki;x>=1&&et[x]===0;x--);if(g>x&&(g=x),x===0)return i[a++]=1<<24|64<<16|0,i[a++]=1<<24|64<<16|0,l.bits=1,0;for(y=1;y<x&&et[y]===0;y++);for(g<y&&(g=y),D=1,f=1;f<=Ki;f++)if(D<<=1,D-=et[f],D<0)return-1;if(D>0&&(r===bu||x!==1))return-1;for(N[1]=0,f=1;f<Ki;f++)N[f+1]=N[f]+et[f];for(p=0;p<n;p++)t[e+p]!==0&&(u[N[t[e+p]]++]=p);if(r===bu?($=O=u,R=20):r===Cs?($=c1,O=d1,R=257):($=p1,O=g1,R=0),W=0,p=0,f=y,ot=a,P=g,C=0,at=-1,S=1<<g,vt=S-1,r===Cs&&S>mu||r===yu&&S>vu)return 1;for(;;){G=f-C,u[p]+1<R?(U=0,nt=u[p]):u[p]>=R?(U=O[u[p]-R],nt=$[u[p]-R]):(U=96,nt=0),z=1<<f-C,M=1<<P,y=M;do M-=z,i[ot+(W>>C)+M]=G<<24|U<<16|nt|0;while(M!==0);for(z=1<<f-1;W&z;)z>>=1;if(z!==0?(W&=z-1,W+=z):W=0,p++,--et[f]===0){if(f===x)break;f=t[e+u[p]]}if(f>g&&(W&vt)!==at){for(C===0&&(C=g),ot+=y,P=f-C,D=1<<P;P+C<x&&(D-=et[P+C],!(D<=0));)P++,D<<=1;if(S+=1<<P,r===Cs&&S>mu||r===yu&&S>vu)return 1;at=W&vt,i[at]=g<<24|P<<16|ot-a|0}}return W!==0&&(i[ot+W]=f-C<<24|64<<16|0),l.bits=g,0};var Ta=m1;const v1=0,zh=1,Hh=2,{Z_FINISH:wu,Z_BLOCK:b1,Z_TREES:_o,Z_OK:ki,Z_STREAM_END:y1,Z_NEED_DICT:w1,Z_STREAM_ERROR:Jr,Z_DATA_ERROR:Wh,Z_MEM_ERROR:Gh,Z_BUF_ERROR:x1,Z_DEFLATED:xu}=Mh,zo=16180,_u=16181,Au=16182,Lu=16183,Nu=16184,Su=16185,ku=16186,Pu=16187,Cu=16188,Fu=16189,To=16190,Rn=16191,Fs=16192,Eu=16193,Es=16194,Iu=16195,Ou=16196,Du=16197,Bu=16198,Ao=16199,Lo=16200,ju=16201,Tu=16202,Ru=16203,Mu=16204,qu=16205,Is=16206,Uu=16207,zu=16208,Be=16209,Vh=16210,Kh=16211,_1=852,A1=592,L1=15,N1=L1,Hu=r=>(r>>>24&255)+(r>>>8&65280)+((r&65280)<<8)+((r&255)<<24);function S1(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}const Pi=r=>{if(!r)return 1;const t=r.state;return!t||t.strm!==r||t.mode<zo||t.mode>Kh?1:0},Yh=r=>{if(Pi(r))return Jr;const t=r.state;return r.total_in=r.total_out=t.total=0,r.msg="",t.wrap&&(r.adler=t.wrap&1),t.mode=zo,t.last=0,t.havedict=0,t.flags=-1,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(_1),t.distcode=t.distdyn=new Int32Array(A1),t.sane=1,t.back=-1,ki},Jh=r=>{if(Pi(r))return Jr;const t=r.state;return t.wsize=0,t.whave=0,t.wnext=0,Yh(r)},$h=(r,t)=>{let e;if(Pi(r))return Jr;const n=r.state;return t<0?(e=0,t=-t):(e=(t>>4)+5,t<48&&(t&=15)),t&&(t<8||t>15)?Jr:(n.window!==null&&n.wbits!==t&&(n.window=null),n.wrap=e,n.wbits=t,Jh(r))},Xh=(r,t)=>{if(!r)return Jr;const e=new S1;r.state=e,e.strm=r,e.window=null,e.mode=zo;const n=$h(r,t);return n!==ki&&(r.state=null),n},k1=r=>Xh(r,N1);let Wu=!0,Os,Ds;const P1=r=>{if(Wu){Os=new Int32Array(512),Ds=new Int32Array(32);let t=0;for(;t<144;)r.lens[t++]=8;for(;t<256;)r.lens[t++]=9;for(;t<280;)r.lens[t++]=7;for(;t<288;)r.lens[t++]=8;for(Ta(zh,r.lens,0,288,Os,0,r.work,{bits:9}),t=0;t<32;)r.lens[t++]=5;Ta(Hh,r.lens,0,32,Ds,0,r.work,{bits:5}),Wu=!1}r.lencode=Os,r.lenbits=9,r.distcode=Ds,r.distbits=5},Zh=(r,t,e,n)=>{let i;const a=r.state;return a.window===null&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new Uint8Array(a.wsize)),n>=a.wsize?(a.window.set(t.subarray(e-a.wsize,e),0),a.wnext=0,a.whave=a.wsize):(i=a.wsize-a.wnext,i>n&&(i=n),a.window.set(t.subarray(e-n,e-n+i),a.wnext),n-=i,n?(a.window.set(t.subarray(e-n,e),0),a.wnext=n,a.whave=a.wsize):(a.wnext+=i,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=i))),0},C1=(r,t)=>{let e,n,i,a,u,l,h,f,p,y,x,g,P,C,D=0,S,W,z,M,at,vt,ot,$;const R=new Uint8Array(4);let et,N;const O=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(Pi(r)||!r.output||!r.input&&r.avail_in!==0)return Jr;e=r.state,e.mode===Rn&&(e.mode=Fs),u=r.next_out,i=r.output,h=r.avail_out,a=r.next_in,n=r.input,l=r.avail_in,f=e.hold,p=e.bits,y=l,x=h,$=ki;t:for(;;)switch(e.mode){case zo:if(e.wrap===0){e.mode=Fs;break}for(;p<16;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(e.wrap&2&&f===35615){e.wbits===0&&(e.wbits=15),e.check=0,R[0]=f&255,R[1]=f>>>8&255,e.check=yn(e.check,R,2,0),f=0,p=0,e.mode=_u;break}if(e.head&&(e.head.done=!1),!(e.wrap&1)||(((f&255)<<8)+(f>>8))%31){r.msg="incorrect header check",e.mode=Be;break}if((f&15)!==xu){r.msg="unknown compression method",e.mode=Be;break}if(f>>>=4,p-=4,ot=(f&15)+8,e.wbits===0&&(e.wbits=ot),ot>15||ot>e.wbits){r.msg="invalid window size",e.mode=Be;break}e.dmax=1<<e.wbits,e.flags=0,r.adler=e.check=1,e.mode=f&512?Fu:Rn,f=0,p=0;break;case _u:for(;p<16;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(e.flags=f,(e.flags&255)!==xu){r.msg="unknown compression method",e.mode=Be;break}if(e.flags&57344){r.msg="unknown header flags set",e.mode=Be;break}e.head&&(e.head.text=f>>8&1),e.flags&512&&e.wrap&4&&(R[0]=f&255,R[1]=f>>>8&255,e.check=yn(e.check,R,2,0)),f=0,p=0,e.mode=Au;case Au:for(;p<32;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}e.head&&(e.head.time=f),e.flags&512&&e.wrap&4&&(R[0]=f&255,R[1]=f>>>8&255,R[2]=f>>>16&255,R[3]=f>>>24&255,e.check=yn(e.check,R,4,0)),f=0,p=0,e.mode=Lu;case Lu:for(;p<16;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}e.head&&(e.head.xflags=f&255,e.head.os=f>>8),e.flags&512&&e.wrap&4&&(R[0]=f&255,R[1]=f>>>8&255,e.check=yn(e.check,R,2,0)),f=0,p=0,e.mode=Nu;case Nu:if(e.flags&1024){for(;p<16;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}e.length=f,e.head&&(e.head.extra_len=f),e.flags&512&&e.wrap&4&&(R[0]=f&255,R[1]=f>>>8&255,e.check=yn(e.check,R,2,0)),f=0,p=0}else e.head&&(e.head.extra=null);e.mode=Su;case Su:if(e.flags&1024&&(g=e.length,g>l&&(g=l),g&&(e.head&&(ot=e.head.extra_len-e.length,e.head.extra||(e.head.extra=new Uint8Array(e.head.extra_len)),e.head.extra.set(n.subarray(a,a+g),ot)),e.flags&512&&e.wrap&4&&(e.check=yn(e.check,n,g,a)),l-=g,a+=g,e.length-=g),e.length))break t;e.length=0,e.mode=ku;case ku:if(e.flags&2048){if(l===0)break t;g=0;do ot=n[a+g++],e.head&&ot&&e.length<65536&&(e.head.name+=String.fromCharCode(ot));while(ot&&g<l);if(e.flags&512&&e.wrap&4&&(e.check=yn(e.check,n,g,a)),l-=g,a+=g,ot)break t}else e.head&&(e.head.name=null);e.length=0,e.mode=Pu;case Pu:if(e.flags&4096){if(l===0)break t;g=0;do ot=n[a+g++],e.head&&ot&&e.length<65536&&(e.head.comment+=String.fromCharCode(ot));while(ot&&g<l);if(e.flags&512&&e.wrap&4&&(e.check=yn(e.check,n,g,a)),l-=g,a+=g,ot)break t}else e.head&&(e.head.comment=null);e.mode=Cu;case Cu:if(e.flags&512){for(;p<16;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(e.wrap&4&&f!==(e.check&65535)){r.msg="header crc mismatch",e.mode=Be;break}f=0,p=0}e.head&&(e.head.hcrc=e.flags>>9&1,e.head.done=!0),r.adler=e.check=0,e.mode=Rn;break;case Fu:for(;p<32;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}r.adler=e.check=Hu(f),f=0,p=0,e.mode=To;case To:if(e.havedict===0)return r.next_out=u,r.avail_out=h,r.next_in=a,r.avail_in=l,e.hold=f,e.bits=p,w1;r.adler=e.check=1,e.mode=Rn;case Rn:if(t===b1||t===_o)break t;case Fs:if(e.last){f>>>=p&7,p-=p&7,e.mode=Is;break}for(;p<3;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}switch(e.last=f&1,f>>>=1,p-=1,f&3){case 0:e.mode=Eu;break;case 1:if(P1(e),e.mode=Ao,t===_o){f>>>=2,p-=2;break t}break;case 2:e.mode=Ou;break;case 3:r.msg="invalid block type",e.mode=Be}f>>>=2,p-=2;break;case Eu:for(f>>>=p&7,p-=p&7;p<32;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if((f&65535)!==(f>>>16^65535)){r.msg="invalid stored block lengths",e.mode=Be;break}if(e.length=f&65535,f=0,p=0,e.mode=Es,t===_o)break t;case Es:e.mode=Iu;case Iu:if(g=e.length,g){if(g>l&&(g=l),g>h&&(g=h),g===0)break t;i.set(n.subarray(a,a+g),u),l-=g,a+=g,h-=g,u+=g,e.length-=g;break}e.mode=Rn;break;case Ou:for(;p<14;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(e.nlen=(f&31)+257,f>>>=5,p-=5,e.ndist=(f&31)+1,f>>>=5,p-=5,e.ncode=(f&15)+4,f>>>=4,p-=4,e.nlen>286||e.ndist>30){r.msg="too many length or distance symbols",e.mode=Be;break}e.have=0,e.mode=Du;case Du:for(;e.have<e.ncode;){for(;p<3;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}e.lens[O[e.have++]]=f&7,f>>>=3,p-=3}for(;e.have<19;)e.lens[O[e.have++]]=0;if(e.lencode=e.lendyn,e.lenbits=7,et={bits:e.lenbits},$=Ta(v1,e.lens,0,19,e.lencode,0,e.work,et),e.lenbits=et.bits,$){r.msg="invalid code lengths set",e.mode=Be;break}e.have=0,e.mode=Bu;case Bu:for(;e.have<e.nlen+e.ndist;){for(;D=e.lencode[f&(1<<e.lenbits)-1],S=D>>>24,W=D>>>16&255,z=D&65535,!(S<=p);){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(z<16)f>>>=S,p-=S,e.lens[e.have++]=z;else{if(z===16){for(N=S+2;p<N;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(f>>>=S,p-=S,e.have===0){r.msg="invalid bit length repeat",e.mode=Be;break}ot=e.lens[e.have-1],g=3+(f&3),f>>>=2,p-=2}else if(z===17){for(N=S+3;p<N;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}f>>>=S,p-=S,ot=0,g=3+(f&7),f>>>=3,p-=3}else{for(N=S+7;p<N;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}f>>>=S,p-=S,ot=0,g=11+(f&127),f>>>=7,p-=7}if(e.have+g>e.nlen+e.ndist){r.msg="invalid bit length repeat",e.mode=Be;break}for(;g--;)e.lens[e.have++]=ot}}if(e.mode===Be)break;if(e.lens[256]===0){r.msg="invalid code -- missing end-of-block",e.mode=Be;break}if(e.lenbits=9,et={bits:e.lenbits},$=Ta(zh,e.lens,0,e.nlen,e.lencode,0,e.work,et),e.lenbits=et.bits,$){r.msg="invalid literal/lengths set",e.mode=Be;break}if(e.distbits=6,e.distcode=e.distdyn,et={bits:e.distbits},$=Ta(Hh,e.lens,e.nlen,e.ndist,e.distcode,0,e.work,et),e.distbits=et.bits,$){r.msg="invalid distances set",e.mode=Be;break}if(e.mode=Ao,t===_o)break t;case Ao:e.mode=Lo;case Lo:if(l>=6&&h>=258){r.next_out=u,r.avail_out=h,r.next_in=a,r.avail_in=l,e.hold=f,e.bits=p,f1(r,x),u=r.next_out,i=r.output,h=r.avail_out,a=r.next_in,n=r.input,l=r.avail_in,f=e.hold,p=e.bits,e.mode===Rn&&(e.back=-1);break}for(e.back=0;D=e.lencode[f&(1<<e.lenbits)-1],S=D>>>24,W=D>>>16&255,z=D&65535,!(S<=p);){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(W&&(W&240)===0){for(M=S,at=W,vt=z;D=e.lencode[vt+((f&(1<<M+at)-1)>>M)],S=D>>>24,W=D>>>16&255,z=D&65535,!(M+S<=p);){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}f>>>=M,p-=M,e.back+=M}if(f>>>=S,p-=S,e.back+=S,e.length=z,W===0){e.mode=qu;break}if(W&32){e.back=-1,e.mode=Rn;break}if(W&64){r.msg="invalid literal/length code",e.mode=Be;break}e.extra=W&15,e.mode=ju;case ju:if(e.extra){for(N=e.extra;p<N;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}e.length+=f&(1<<e.extra)-1,f>>>=e.extra,p-=e.extra,e.back+=e.extra}e.was=e.length,e.mode=Tu;case Tu:for(;D=e.distcode[f&(1<<e.distbits)-1],S=D>>>24,W=D>>>16&255,z=D&65535,!(S<=p);){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if((W&240)===0){for(M=S,at=W,vt=z;D=e.distcode[vt+((f&(1<<M+at)-1)>>M)],S=D>>>24,W=D>>>16&255,z=D&65535,!(M+S<=p);){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}f>>>=M,p-=M,e.back+=M}if(f>>>=S,p-=S,e.back+=S,W&64){r.msg="invalid distance code",e.mode=Be;break}e.offset=z,e.extra=W&15,e.mode=Ru;case Ru:if(e.extra){for(N=e.extra;p<N;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}e.offset+=f&(1<<e.extra)-1,f>>>=e.extra,p-=e.extra,e.back+=e.extra}if(e.offset>e.dmax){r.msg="invalid distance too far back",e.mode=Be;break}e.mode=Mu;case Mu:if(h===0)break t;if(g=x-h,e.offset>g){if(g=e.offset-g,g>e.whave&&e.sane){r.msg="invalid distance too far back",e.mode=Be;break}g>e.wnext?(g-=e.wnext,P=e.wsize-g):P=e.wnext-g,g>e.length&&(g=e.length),C=e.window}else C=i,P=u-e.offset,g=e.length;g>h&&(g=h),h-=g,e.length-=g;do i[u++]=C[P++];while(--g);e.length===0&&(e.mode=Lo);break;case qu:if(h===0)break t;i[u++]=e.length,h--,e.mode=Lo;break;case Is:if(e.wrap){for(;p<32;){if(l===0)break t;l--,f|=n[a++]<<p,p+=8}if(x-=h,r.total_out+=x,e.total+=x,e.wrap&4&&x&&(r.adler=e.check=e.flags?yn(e.check,i,x,u-x):il(e.check,i,x,u-x)),x=h,e.wrap&4&&(e.flags?f:Hu(f))!==e.check){r.msg="incorrect data check",e.mode=Be;break}f=0,p=0}e.mode=Uu;case Uu:if(e.wrap&&e.flags){for(;p<32;){if(l===0)break t;l--,f+=n[a++]<<p,p+=8}if(e.wrap&4&&f!==(e.total&**********)){r.msg="incorrect length check",e.mode=Be;break}f=0,p=0}e.mode=zu;case zu:$=y1;break t;case Be:$=Wh;break t;case Vh:return Gh;case Kh:default:return Jr}return r.next_out=u,r.avail_out=h,r.next_in=a,r.avail_in=l,e.hold=f,e.bits=p,(e.wsize||x!==r.avail_out&&e.mode<Be&&(e.mode<Is||t!==wu))&&Zh(r,r.output,r.next_out,x-r.avail_out),y-=r.avail_in,x-=r.avail_out,r.total_in+=y,r.total_out+=x,e.total+=x,e.wrap&4&&x&&(r.adler=e.check=e.flags?yn(e.check,i,x,r.next_out-x):il(e.check,i,x,r.next_out-x)),r.data_type=e.bits+(e.last?64:0)+(e.mode===Rn?128:0)+(e.mode===Ao||e.mode===Es?256:0),(y===0&&x===0||t===wu)&&$===ki&&($=x1),$},F1=r=>{if(Pi(r))return Jr;let t=r.state;return t.window&&(t.window=null),r.state=null,ki},E1=(r,t)=>{if(Pi(r))return Jr;const e=r.state;return(e.wrap&2)===0?Jr:(e.head=t,t.done=!1,ki)},I1=(r,t)=>{const e=t.length;let n,i,a;return Pi(r)||(n=r.state,n.wrap!==0&&n.mode!==To)?Jr:n.mode===To&&(i=1,i=il(i,t,e,0),i!==n.check)?Wh:(a=Zh(r,t,e,e),a?(n.mode=Vh,Gh):(n.havedict=1,ki))};var O1=Jh,D1=$h,B1=Yh,j1=k1,T1=Xh,R1=C1,M1=F1,q1=E1,U1=I1,z1="pako inflate (from Nodeca project)",Mn={inflateReset:O1,inflateReset2:D1,inflateResetKeep:B1,inflateInit:j1,inflateInit2:T1,inflate:R1,inflateEnd:M1,inflateGetHeader:q1,inflateSetDictionary:U1,inflateInfo:z1};function H1(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var W1=H1;const Qh=Object.prototype.toString,{Z_NO_FLUSH:G1,Z_FINISH:V1,Z_OK:qa,Z_STREAM_END:Bs,Z_NEED_DICT:js,Z_STREAM_ERROR:K1,Z_DATA_ERROR:Gu,Z_MEM_ERROR:Y1}=Mh;function za(r){this.options=qh.assign({chunkSize:1024*64,windowBits:15,to:""},r||{});const t=this.options;t.raw&&t.windowBits>=0&&t.windowBits<16&&(t.windowBits=-t.windowBits,t.windowBits===0&&(t.windowBits=-15)),t.windowBits>=0&&t.windowBits<16&&!(r&&r.windowBits)&&(t.windowBits+=32),t.windowBits>15&&t.windowBits<48&&(t.windowBits&15)===0&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new u1,this.strm.avail_out=0;let e=Mn.inflateInit2(this.strm,t.windowBits);if(e!==qa)throw new Error(al[e]);if(this.header=new W1,Mn.inflateGetHeader(this.strm,this.header),t.dictionary&&(typeof t.dictionary=="string"?t.dictionary=ol.string2buf(t.dictionary):Qh.call(t.dictionary)==="[object ArrayBuffer]"&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw&&(e=Mn.inflateSetDictionary(this.strm,t.dictionary),e!==qa)))throw new Error(al[e])}za.prototype.push=function(r,t){const e=this.strm,n=this.options.chunkSize,i=this.options.dictionary;let a,u,l;if(this.ended)return!1;for(t===~~t?u=t:u=t===!0?V1:G1,Qh.call(r)==="[object ArrayBuffer]"?e.input=new Uint8Array(r):e.input=r,e.next_in=0,e.avail_in=e.input.length;;){for(e.avail_out===0&&(e.output=new Uint8Array(n),e.next_out=0,e.avail_out=n),a=Mn.inflate(e,u),a===js&&i&&(a=Mn.inflateSetDictionary(e,i),a===qa?a=Mn.inflate(e,u):a===Gu&&(a=js));e.avail_in>0&&a===Bs&&e.state.wrap>0&&r[e.next_in]!==0;)Mn.inflateReset(e),a=Mn.inflate(e,u);switch(a){case K1:case Gu:case js:case Y1:return this.onEnd(a),this.ended=!0,!1}if(l=e.avail_out,e.next_out&&(e.avail_out===0||a===Bs))if(this.options.to==="string"){let h=ol.utf8border(e.output,e.next_out),f=e.next_out-h,p=ol.buf2string(e.output,h);e.next_out=f,e.avail_out=n-f,f&&e.output.set(e.output.subarray(h,h+f),0),this.onData(p)}else this.onData(e.output.length===e.next_out?e.output:e.output.subarray(0,e.next_out));if(!(a===qa&&l===0)){if(a===Bs)return a=Mn.inflateEnd(this.strm),this.onEnd(a),this.ended=!0,!0;if(e.avail_in===0)break}}return!0};za.prototype.onData=function(r){this.chunks.push(r)};za.prototype.onEnd=function(r){r===qa&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=qh.flattenChunks(this.chunks)),this.chunks=[],this.err=r,this.msg=this.strm.msg};function J1(r,t){const e=new za(t);if(e.push(r),e.err)throw e.msg||al[e.err];return e.result}var $1=za,X1=J1,Z1={Inflate:$1,inflate:X1};const{Inflate:Q1,inflate:t2}=Z1;var Vu=Q1,e2=t2;const tf=[];for(let r=0;r<256;r++){let t=r;for(let e=0;e<8;e++)t&1?t=3988292384^t>>>1:t=t>>>1;tf[r]=t}const Ku=**********;function r2(r,t,e){let n=r;for(let i=0;i<e;i++)n=tf[(n^t[i])&255]^n>>>8;return n}function n2(r,t){return(r2(Ku,r,t)^Ku)>>>0}function Yu(r,t,e){const n=r.readUint32(),i=n2(new Uint8Array(r.buffer,r.byteOffset+r.offset-t-4,t),t);if(i!==n)throw new Error("CRC mismatch for chunk ".concat(e,". Expected ").concat(n,", found ").concat(i))}function ef(r,t,e){for(let n=0;n<e;n++)t[n]=r[n]}function rf(r,t,e,n){let i=0;for(;i<n;i++)t[i]=r[i];for(;i<e;i++)t[i]=r[i]+t[i-n]&255}function nf(r,t,e,n){let i=0;if(e.length===0)for(;i<n;i++)t[i]=r[i];else for(;i<n;i++)t[i]=r[i]+e[i]&255}function af(r,t,e,n,i){let a=0;if(e.length===0){for(;a<i;a++)t[a]=r[a];for(;a<n;a++)t[a]=r[a]+(t[a-i]>>1)&255}else{for(;a<i;a++)t[a]=r[a]+(e[a]>>1)&255;for(;a<n;a++)t[a]=r[a]+(t[a-i]+e[a]>>1)&255}}function of(r,t,e,n,i){let a=0;if(e.length===0){for(;a<i;a++)t[a]=r[a];for(;a<n;a++)t[a]=r[a]+t[a-i]&255}else{for(;a<i;a++)t[a]=r[a]+e[a]&255;for(;a<n;a++)t[a]=r[a]+i2(t[a-i],e[a],e[a-i])&255}}function i2(r,t,e){const n=r+t-e,i=Math.abs(n-r),a=Math.abs(n-t),u=Math.abs(n-e);return i<=a&&i<=u?r:a<=u?t:e}function a2(r,t,e,n,i,a){switch(r){case 0:ef(t,e,i);break;case 1:rf(t,e,i,a);break;case 2:nf(t,e,n,i);break;case 3:af(t,e,n,i,a);break;case 4:of(t,e,n,i,a);break;default:throw new Error("Unsupported filter: ".concat(r))}}const o2=new Uint16Array([255]),s2=new Uint8Array(o2.buffer),l2=s2[0]===255;function u2(r){const{data:t,width:e,height:n,channels:i,depth:a}=r,u=[{x:0,y:0,xStep:8,yStep:8},{x:4,y:0,xStep:8,yStep:8},{x:0,y:4,xStep:4,yStep:8},{x:2,y:0,xStep:4,yStep:4},{x:0,y:2,xStep:2,yStep:4},{x:1,y:0,xStep:2,yStep:2},{x:0,y:1,xStep:1,yStep:2}],l=Math.ceil(a/8)*i,h=new Uint8Array(n*e*l);let f=0;for(let p=0;p<7;p++){const y=u[p],x=Math.ceil((e-y.x)/y.xStep),g=Math.ceil((n-y.y)/y.yStep);if(x<=0||g<=0)continue;const P=x*l,C=new Uint8Array(P);for(let D=0;D<g;D++){const S=t[f++],W=t.subarray(f,f+P);f+=P;const z=new Uint8Array(P);a2(S,W,z,C,P,l),C.set(z);for(let M=0;M<x;M++){const at=y.x+M*y.xStep,vt=y.y+D*y.yStep;if(!(at>=e||vt>=n))for(let ot=0;ot<l;ot++)h[(vt*e+at)*l+ot]=z[M*l+ot]}}}if(a===16){const p=new Uint16Array(h.buffer);if(l2)for(let y=0;y<p.length;y++)p[y]=h2(p[y]);return p}else return h}function h2(r){return(r&255)<<8|r>>8&255}const f2=new Uint16Array([255]),c2=new Uint8Array(f2.buffer),d2=c2[0]===255,p2=new Uint8Array(0);function Ju(r){const{data:t,width:e,height:n,channels:i,depth:a}=r,u=Math.ceil(a/8)*i,l=Math.ceil(a/8*i*e),h=new Uint8Array(n*l);let f=p2,p=0,y,x;for(let g=0;g<n;g++){switch(y=t.subarray(p+1,p+1+l),x=h.subarray(g*l,(g+1)*l),t[p]){case 0:ef(y,x,l);break;case 1:rf(y,x,l,u);break;case 2:nf(y,x,f,l);break;case 3:af(y,x,f,l,u);break;case 4:of(y,x,f,l,u);break;default:throw new Error("Unsupported filter: ".concat(t[p]))}f=x,p+=l+1}if(a===16){const g=new Uint16Array(h.buffer);if(d2)for(let P=0;P<g.length;P++)g[P]=g2(g[P]);return g}else return h}function g2(r){return(r&255)<<8|r>>8&255}const Fo=Uint8Array.of(137,80,78,71,13,10,26,10);function $u(r){if(!m2(r.readBytes(Fo.length)))throw new Error("wrong PNG signature")}function m2(r){if(r.length<Fo.length)return!1;for(let t=0;t<Fo.length;t++)if(r[t]!==Fo[t])return!1;return!0}const v2="tEXt",b2=0,sf=new TextDecoder("latin1");function y2(r){if(x2(r),r.length===0||r.length>79)throw new Error("keyword length must be between 1 and 79")}const w2=/^[\u0000-\u00FF]*$/;function x2(r){if(!w2.test(r))throw new Error("invalid latin1 text")}function _2(r,t,e){const n=lf(t);r[n]=A2(t,e-n.length-1)}function lf(r){for(r.mark();r.readByte()!==b2;);const t=r.offset;r.reset();const e=sf.decode(r.readBytes(t-r.offset-1));return r.skip(1),y2(e),e}function A2(r,t){return sf.decode(r.readBytes(t))}const qr={UNKNOWN:-1,GREYSCALE:0,TRUECOLOUR:2,INDEXED_COLOUR:3,GREYSCALE_ALPHA:4,TRUECOLOUR_ALPHA:6},Ts={UNKNOWN:-1,DEFLATE:0},Xu={UNKNOWN:-1,ADAPTIVE:0},Rs={UNKNOWN:-1,NO_INTERLACE:0,ADAM7:1},No={NONE:0,BACKGROUND:1,PREVIOUS:2},Ms={SOURCE:0,OVER:1};class L2 extends vl{constructor(e,n={}){super(e);xe(this,"_checkCrc");xe(this,"_inflator");xe(this,"_png");xe(this,"_apng");xe(this,"_end");xe(this,"_hasPalette");xe(this,"_palette");xe(this,"_hasTransparency");xe(this,"_transparency");xe(this,"_compressionMethod");xe(this,"_filterMethod");xe(this,"_interlaceMethod");xe(this,"_colorType");xe(this,"_isAnimated");xe(this,"_numberOfFrames");xe(this,"_numberOfPlays");xe(this,"_frames");xe(this,"_writingDataChunks");const{checkCrc:i=!1}=n;this._checkCrc=i,this._inflator=new Vu,this._png={width:-1,height:-1,channels:-1,data:new Uint8Array(0),depth:1,text:{}},this._apng={width:-1,height:-1,channels:-1,depth:1,numberOfFrames:1,numberOfPlays:0,text:{},frames:[]},this._end=!1,this._hasPalette=!1,this._palette=[],this._hasTransparency=!1,this._transparency=new Uint16Array(0),this._compressionMethod=Ts.UNKNOWN,this._filterMethod=Xu.UNKNOWN,this._interlaceMethod=Rs.UNKNOWN,this._colorType=qr.UNKNOWN,this._isAnimated=!1,this._numberOfFrames=1,this._numberOfPlays=0,this._frames=[],this._writingDataChunks=!1,this.setBigEndian()}decode(){for($u(this);!this._end;){const e=this.readUint32(),n=this.readChars(4);this.decodeChunk(e,n)}return this.decodeImage(),this._png}decodeApng(){for($u(this);!this._end;){const e=this.readUint32(),n=this.readChars(4);this.decodeApngChunk(e,n)}return this.decodeApngImage(),this._apng}decodeChunk(e,n){const i=this.offset;switch(n){case"IHDR":this.decodeIHDR();break;case"PLTE":this.decodePLTE(e);break;case"IDAT":this.decodeIDAT(e);break;case"IEND":this._end=!0;break;case"tRNS":this.decodetRNS(e);break;case"iCCP":this.decodeiCCP(e);break;case v2:_2(this._png.text,this,e);break;case"pHYs":this.decodepHYs();break;default:this.skip(e);break}if(this.offset-i!==e)throw new Error("Length mismatch while decoding chunk ".concat(n));this._checkCrc?Yu(this,e+4,n):this.skip(4)}decodeApngChunk(e,n){const i=this.offset;switch(n!=="fdAT"&&n!=="IDAT"&&this._writingDataChunks&&this.pushDataToFrame(),n){case"acTL":this.decodeACTL();break;case"fcTL":this.decodeFCTL();break;case"fdAT":this.decodeFDAT(e);break;default:this.decodeChunk(e,n),this.offset=i+e;break}if(this.offset-i!==e)throw new Error("Length mismatch while decoding chunk ".concat(n));this._checkCrc?Yu(this,e+4,n):this.skip(4)}decodeIHDR(){const e=this._png;e.width=this.readUint32(),e.height=this.readUint32(),e.depth=N2(this.readUint8());const n=this.readUint8();this._colorType=n;let i;switch(n){case qr.GREYSCALE:i=1;break;case qr.TRUECOLOUR:i=3;break;case qr.INDEXED_COLOUR:i=1;break;case qr.GREYSCALE_ALPHA:i=2;break;case qr.TRUECOLOUR_ALPHA:i=4;break;case qr.UNKNOWN:default:throw new Error("Unknown color type: ".concat(n))}if(this._png.channels=i,this._compressionMethod=this.readUint8(),this._compressionMethod!==Ts.DEFLATE)throw new Error("Unsupported compression method: ".concat(this._compressionMethod));this._filterMethod=this.readUint8(),this._interlaceMethod=this.readUint8()}decodeACTL(){this._numberOfFrames=this.readUint32(),this._numberOfPlays=this.readUint32(),this._isAnimated=!0}decodeFCTL(){const e={sequenceNumber:this.readUint32(),width:this.readUint32(),height:this.readUint32(),xOffset:this.readUint32(),yOffset:this.readUint32(),delayNumber:this.readUint16(),delayDenominator:this.readUint16(),disposeOp:this.readUint8(),blendOp:this.readUint8(),data:new Uint8Array(0)};this._frames.push(e)}decodePLTE(e){if(e%3!==0)throw new RangeError("PLTE field length must be a multiple of 3. Got ".concat(e));const n=e/3;this._hasPalette=!0;const i=[];this._palette=i;for(let a=0;a<n;a++)i.push([this.readUint8(),this.readUint8(),this.readUint8()])}decodeIDAT(e){this._writingDataChunks=!0;const n=e,i=this.offset+this.byteOffset;if(this._inflator.push(new Uint8Array(this.buffer,i,n)),this._inflator.err)throw new Error("Error while decompressing the data: ".concat(this._inflator.err));this.skip(e)}decodeFDAT(e){this._writingDataChunks=!0;let n=e,i=this.offset+this.byteOffset;if(i+=4,n-=4,this._inflator.push(new Uint8Array(this.buffer,i,n)),this._inflator.err)throw new Error("Error while decompressing the data: ".concat(this._inflator.err));this.skip(e)}decodetRNS(e){switch(this._colorType){case qr.GREYSCALE:case qr.TRUECOLOUR:{if(e%2!==0)throw new RangeError("tRNS chunk length must be a multiple of 2. Got ".concat(e));if(e/2>this._png.width*this._png.height)throw new Error("tRNS chunk contains more alpha values than there are pixels (".concat(e/2," vs ").concat(this._png.width*this._png.height,")"));this._hasTransparency=!0,this._transparency=new Uint16Array(e/2);for(let n=0;n<e/2;n++)this._transparency[n]=this.readUint16();break}case qr.INDEXED_COLOUR:{if(e>this._palette.length)throw new Error("tRNS chunk contains more alpha values than there are palette colors (".concat(e," vs ").concat(this._palette.length,")"));let n=0;for(;n<e;n++){const i=this.readByte();this._palette[n].push(i)}for(;n<this._palette.length;n++)this._palette[n].push(255);break}case qr.UNKNOWN:case qr.GREYSCALE_ALPHA:case qr.TRUECOLOUR_ALPHA:default:throw new Error("tRNS chunk is not supported for color type ".concat(this._colorType))}}decodeiCCP(e){const n=lf(this),i=this.readUint8();if(i!==Ts.DEFLATE)throw new Error("Unsupported iCCP compression method: ".concat(i));const a=this.readBytes(e-n.length-2);this._png.iccEmbeddedProfile={name:n,profile:e2(a)}}decodepHYs(){const e=this.readUint32(),n=this.readUint32(),i=this.readByte();this._png.resolution={x:e,y:n,unit:i}}decodeApngImage(){this._apng.width=this._png.width,this._apng.height=this._png.height,this._apng.channels=this._png.channels,this._apng.depth=this._png.depth,this._apng.numberOfFrames=this._numberOfFrames,this._apng.numberOfPlays=this._numberOfPlays,this._apng.text=this._png.text,this._apng.resolution=this._png.resolution;for(let e=0;e<this._numberOfFrames;e++){const n={sequenceNumber:this._frames[e].sequenceNumber,delayNumber:this._frames[e].delayNumber,delayDenominator:this._frames[e].delayDenominator,data:this._apng.depth===8?new Uint8Array(this._apng.width*this._apng.height*this._apng.channels):new Uint16Array(this._apng.width*this._apng.height*this._apng.channels)},i=this._frames.at(e);if(i){if(i.data=Ju({data:i.data,width:i.width,height:i.height,channels:this._apng.channels,depth:this._apng.depth}),this._hasPalette&&(this._apng.palette=this._palette),this._hasTransparency&&(this._apng.transparency=this._transparency),e===0||i.xOffset===0&&i.yOffset===0&&i.width===this._png.width&&i.height===this._png.height)n.data=i.data;else{const a=this._apng.frames.at(e-1);this.disposeFrame(i,a,n),this.addFrameDataToCanvas(n,i)}this._apng.frames.push(n)}}return this._apng}disposeFrame(e,n,i){switch(e.disposeOp){case No.NONE:break;case No.BACKGROUND:for(let a=0;a<this._png.height;a++)for(let u=0;u<this._png.width;u++){const l=(a*e.width+u)*this._png.channels;for(let h=0;h<this._png.channels;h++)i.data[l+h]=0}break;case No.PREVIOUS:i.data.set(n.data);break;default:throw new Error("Unknown disposeOp")}}addFrameDataToCanvas(e,n){const i=1<<this._png.depth,a=(u,l)=>{const h=((u+n.yOffset)*this._png.width+n.xOffset+l)*this._png.channels,f=(u*n.width+l)*this._png.channels;return{index:h,frameIndex:f}};switch(n.blendOp){case Ms.SOURCE:for(let u=0;u<n.height;u++)for(let l=0;l<n.width;l++){const{index:h,frameIndex:f}=a(u,l);for(let p=0;p<this._png.channels;p++)e.data[h+p]=n.data[f+p]}break;case Ms.OVER:for(let u=0;u<n.height;u++)for(let l=0;l<n.width;l++){const{index:h,frameIndex:f}=a(u,l);for(let p=0;p<this._png.channels;p++){const y=n.data[f+this._png.channels-1]/i,x=p%(this._png.channels-1)===0?1:n.data[f+p],g=Math.floor(y*x+(1-y)*e.data[h+p]);e.data[h+p]+=g}}break;default:throw new Error("Unknown blendOp")}}decodeImage(){var n;if(this._inflator.err)throw new Error("Error while decompressing the data: ".concat(this._inflator.err));const e=this._isAnimated?((n=this._frames)==null?void 0:n.at(0)).data:this._inflator.result;if(this._filterMethod!==Xu.ADAPTIVE)throw new Error("Filter method ".concat(this._filterMethod," not supported"));if(this._interlaceMethod===Rs.NO_INTERLACE)this._png.data=Ju({data:e,width:this._png.width,height:this._png.height,channels:this._png.channels,depth:this._png.depth});else if(this._interlaceMethod===Rs.ADAM7)this._png.data=u2({data:e,width:this._png.width,height:this._png.height,channels:this._png.channels,depth:this._png.depth});else throw new Error("Interlace method ".concat(this._interlaceMethod," not supported"));this._hasPalette&&(this._png.palette=this._palette),this._hasTransparency&&(this._png.transparency=this._transparency)}pushDataToFrame(){const e=this._inflator.result,n=this._frames.at(-1);n?n.data=e:this._frames.push({sequenceNumber:0,width:this._png.width,height:this._png.height,xOffset:0,yOffset:0,delayNumber:0,delayDenominator:0,disposeOp:No.NONE,blendOp:Ms.SOURCE,data:e}),this._inflator=new Vu,this._writingDataChunks=!1}}function N2(r){if(r!==1&&r!==2&&r!==4&&r!==8&&r!==16)throw new Error("invalid bit depth: ".concat(r));return r}var Zu;(function(r){r[r.UNKNOWN=0]="UNKNOWN",r[r.METRE=1]="METRE"})(Zu||(Zu={}));function S2(r,t){return new L2(r,t).decode()}var Yt=(function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this})();function qs(){Yt.console&&typeof Yt.console.log=="function"&&Yt.console.log.apply(Yt.console,arguments)}var Pe={log:qs,warn:function(r){Yt.console&&(typeof Yt.console.warn=="function"?Yt.console.warn.apply(Yt.console,arguments):qs.call(null,arguments))},error:function(r){Yt.console&&(typeof Yt.console.error=="function"?Yt.console.error.apply(Yt.console,arguments):qs(r))}};function Us(r,t,e){var n=new XMLHttpRequest;n.open("GET",r),n.responseType="blob",n.onload=function(){Ai(n.response,t,e)},n.onerror=function(){Pe.error("could not download file")},n.send()}function Qu(r){var t=new XMLHttpRequest;t.open("HEAD",r,!1);try{t.send()}catch(e){}return t.status>=200&&t.status<=299}function So(r){try{r.dispatchEvent(new MouseEvent("click"))}catch(e){var t=document.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),r.dispatchEvent(t)}}var Ai=Yt.saveAs||((typeof window>"u"?"undefined":_e(window))!=="object"||window!==Yt?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(r,t,e){var n=Yt.URL||Yt.webkitURL,i=document.createElement("a");t=t||r.name||"download",i.download=t,i.rel="noopener",typeof r=="string"?(i.href=r,i.origin!==location.origin?Qu(i.href)?Us(r,t,e):So(i,i.target="_blank"):So(i)):(i.href=n.createObjectURL(r),setTimeout(function(){n.revokeObjectURL(i.href)},4e4),setTimeout(function(){So(i)},0))}:"msSaveOrOpenBlob"in navigator?function(r,t,e){if(t=t||r.name||"download",typeof r=="string")if(Qu(r))Us(r,t,e);else{var n=document.createElement("a");n.href=r,n.target="_blank",setTimeout(function(){So(n)})}else navigator.msSaveOrOpenBlob((function(i,a){return a===void 0?a={autoBom:!1}:_e(a)!=="object"&&(Pe.warn("Deprecated: Expected third argument to be a object"),a={autoBom:!a}),a.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(i.type)?new Blob(["\uFEFF",i],{type:i.type}):i})(r,e),t)}:function(r,t,e,n){if((n=n||open("","_blank"))&&(n.document.title=n.document.body.innerText="downloading..."),typeof r=="string")return Us(r,t,e);var i=r.type==="application/octet-stream",a=/constructor/i.test(Yt.HTMLElement)||Yt.safari,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||i&&a)&&(typeof FileReader>"u"?"undefined":_e(FileReader))==="object"){var l=new FileReader;l.onloadend=function(){var p=l.result;p=u?p:p.replace(/^data:[^;]*;/,"data:attachment/file;"),n?n.location.href=p:location=p,n=null},l.readAsDataURL(r)}else{var h=Yt.URL||Yt.webkitURL,f=h.createObjectURL(r);n?n.location=f:location.href=f,n=null,setTimeout(function(){h.revokeObjectURL(f)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function uf(r){var t;r=r||"",this.ok=!1,r.charAt(0)=="#"&&(r=r.substr(1,6)),r={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[r=(r=r.replace(/ /g,"")).toLowerCase()]||r;for(var e=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(l){return[parseInt(l[1]),parseInt(l[2]),parseInt(l[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(l){return[parseInt(l[1],16),parseInt(l[2],16),parseInt(l[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(l){return[parseInt(l[1]+l[1],16),parseInt(l[2]+l[2],16),parseInt(l[3]+l[3],16)]}}],n=0;n<e.length;n++){var i=e[n].re,a=e[n].process,u=i.exec(r);u&&(t=a(u),this.r=t[0],this.g=t[1],this.b=t[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var l=this.r.toString(16),h=this.g.toString(16),f=this.b.toString(16);return l.length==1&&(l="0"+l),h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),"#"+l+h+f}}var Eo=Yt.atob.bind(Yt),th=Yt.btoa.bind(Yt);/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function zs(r,t){var e=r[0],n=r[1],i=r[2],a=r[3];e=yr(e,n,i,a,t[0],7,-680876936),a=yr(a,e,n,i,t[1],12,-389564586),i=yr(i,a,e,n,t[2],17,606105819),n=yr(n,i,a,e,t[3],22,-**********),e=yr(e,n,i,a,t[4],7,-176418897),a=yr(a,e,n,i,t[5],12,**********),i=yr(i,a,e,n,t[6],17,-**********),n=yr(n,i,a,e,t[7],22,-45705983),e=yr(e,n,i,a,t[8],7,**********),a=yr(a,e,n,i,t[9],12,-**********),i=yr(i,a,e,n,t[10],17,-42063),n=yr(n,i,a,e,t[11],22,-**********),e=yr(e,n,i,a,t[12],7,**********),a=yr(a,e,n,i,t[13],12,-40341101),i=yr(i,a,e,n,t[14],17,-**********),e=wr(e,n=yr(n,i,a,e,t[15],22,**********),i,a,t[1],5,-165796510),a=wr(a,e,n,i,t[6],9,-**********),i=wr(i,a,e,n,t[11],14,643717713),n=wr(n,i,a,e,t[0],20,-373897302),e=wr(e,n,i,a,t[5],5,-701558691),a=wr(a,e,n,i,t[10],9,38016083),i=wr(i,a,e,n,t[15],14,-660478335),n=wr(n,i,a,e,t[4],20,-405537848),e=wr(e,n,i,a,t[9],5,568446438),a=wr(a,e,n,i,t[14],9,-1019803690),i=wr(i,a,e,n,t[3],14,-187363961),n=wr(n,i,a,e,t[8],20,1163531501),e=wr(e,n,i,a,t[13],5,-1444681467),a=wr(a,e,n,i,t[2],9,-51403784),i=wr(i,a,e,n,t[7],14,1735328473),e=xr(e,n=wr(n,i,a,e,t[12],20,-1926607734),i,a,t[5],4,-378558),a=xr(a,e,n,i,t[8],11,-2022574463),i=xr(i,a,e,n,t[11],16,1839030562),n=xr(n,i,a,e,t[14],23,-35309556),e=xr(e,n,i,a,t[1],4,-1530992060),a=xr(a,e,n,i,t[4],11,1272893353),i=xr(i,a,e,n,t[7],16,-155497632),n=xr(n,i,a,e,t[10],23,-1094730640),e=xr(e,n,i,a,t[13],4,681279174),a=xr(a,e,n,i,t[0],11,-358537222),i=xr(i,a,e,n,t[3],16,-722521979),n=xr(n,i,a,e,t[6],23,76029189),e=xr(e,n,i,a,t[9],4,-640364487),a=xr(a,e,n,i,t[12],11,-421815835),i=xr(i,a,e,n,t[15],16,530742520),e=_r(e,n=xr(n,i,a,e,t[2],23,-995338651),i,a,t[0],6,-198630844),a=_r(a,e,n,i,t[7],10,1126891415),i=_r(i,a,e,n,t[14],15,-1416354905),n=_r(n,i,a,e,t[5],21,-57434055),e=_r(e,n,i,a,t[12],6,1700485571),a=_r(a,e,n,i,t[3],10,-1894986606),i=_r(i,a,e,n,t[10],15,-1051523),n=_r(n,i,a,e,t[1],21,-2054922799),e=_r(e,n,i,a,t[8],6,1873313359),a=_r(a,e,n,i,t[15],10,-30611744),i=_r(i,a,e,n,t[6],15,-1560198380),n=_r(n,i,a,e,t[13],21,1309151649),e=_r(e,n,i,a,t[4],6,-145523070),a=_r(a,e,n,i,t[11],10,-1120210379),i=_r(i,a,e,n,t[2],15,718787259),n=_r(n,i,a,e,t[9],21,-343485551),r[0]=ii(e,r[0]),r[1]=ii(n,r[1]),r[2]=ii(i,r[2]),r[3]=ii(a,r[3])}function Ho(r,t,e,n,i,a){return t=ii(ii(t,r),ii(n,a)),ii(t<<i|t>>>32-i,e)}function yr(r,t,e,n,i,a,u){return Ho(t&e|~t&n,r,t,i,a,u)}function wr(r,t,e,n,i,a,u){return Ho(t&n|e&~n,r,t,i,a,u)}function xr(r,t,e,n,i,a,u){return Ho(t^e^n,r,t,i,a,u)}function _r(r,t,e,n,i,a,u){return Ho(e^(t|~n),r,t,i,a,u)}function hf(r){var t,e=r.length,n=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=r.length;t+=64)zs(n,k2(r.substring(t-64,t)));r=r.substring(t-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r.length;t++)i[t>>2]|=r.charCodeAt(t)<<(t%4<<3);if(i[t>>2]|=128<<(t%4<<3),t>55)for(zs(n,i),t=0;t<16;t++)i[t]=0;return i[14]=8*e,zs(n,i),n}function k2(r){var t,e=[];for(t=0;t<64;t+=4)e[t>>2]=r.charCodeAt(t)+(r.charCodeAt(t+1)<<8)+(r.charCodeAt(t+2)<<16)+(r.charCodeAt(t+3)<<24);return e}var eh="0123456789abcdef".split("");function P2(r){for(var t="",e=0;e<4;e++)t+=eh[r>>8*e+4&15]+eh[r>>8*e&15];return t}function C2(r){return String.fromCharCode(255&r,(65280&r)>>8,(16711680&r)>>16,(**********&r)>>24)}function sl(r){return hf(r).map(C2).join("")}var F2=(function(r){for(var t=0;t<r.length;t++)r[t]=P2(r[t]);return r.join("")})(hf("hello"))!="5d41402abc4b2a76b9719d911017c592";function ii(r,t){if(F2){var e=(65535&r)+(65535&t);return(r>>16)+(t>>16)+(e>>16)<<16|65535&e}return r+t&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function ll(r,t){var e,n,i,a;if(r!==e){for(var u=(i=r,a=1+(256/r.length|0),new Array(a+1).join(i)),l=[],h=0;h<256;h++)l[h]=h;var f=0;for(h=0;h<256;h++){var p=l[h];f=(f+p+u.charCodeAt(h))%256,l[h]=l[f],l[f]=p}e=r,n=l}else l=n;var y=t.length,x=0,g=0,P="";for(h=0;h<y;h++)g=(g+(p=l[x=(x+1)%256]))%256,l[x]=l[g],l[g]=p,u=l[(l[x]+l[g])%256],P+=String.fromCharCode(t.charCodeAt(h)^u);return P}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var rh={print:4,modify:8,copy:16,"annot-forms":32};function Xi(r,t,e,n){this.v=1,this.r=2;var i=192;r.forEach(function(l){if(rh.perm!==void 0)throw new Error("Invalid permission: "+l);i+=rh[l]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var a=(t+this.padding).substr(0,32),u=(e+this.padding).substr(0,32);this.O=this.processOwnerPassword(a,u),this.P=-(1+(255^i)),this.encryptionKey=sl(a+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(n)).substr(0,5),this.U=ll(this.encryptionKey,this.padding)}function Zi(r){if(/[^\u0000-\u00ff]/.test(r))throw new Error("Invalid PDF Name Object: "+r+", Only accept ASCII characters.");for(var t="",e=r.length,n=0;n<e;n++){var i=r.charCodeAt(n);t+=i<33||i===35||i===37||i===40||i===41||i===47||i===60||i===62||i===91||i===93||i===123||i===125||i>126?"#"+("0"+i.toString(16)).slice(-2):r[n]}return t}function nh(r){if(_e(r)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var t={};this.subscribe=function(e,n,i){if(i=i||!1,typeof e!="string"||typeof n!="function"||typeof i!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");t.hasOwnProperty(e)||(t[e]={});var a=Math.random().toString(35);return t[e][a]=[n,!!i],a},this.unsubscribe=function(e){for(var n in t)if(t[n][e])return delete t[n][e],Object.keys(t[n]).length===0&&delete t[n],!0;return!1},this.publish=function(e){if(t.hasOwnProperty(e)){var n=Array.prototype.slice.call(arguments,1),i=[];for(var a in t[e]){var u=t[e][a];try{u[0].apply(r,n)}catch(l){Yt.console&&Pe.error("jsPDF PubSub Error",l.message,l)}u[1]&&i.push(a)}i.length&&i.forEach(this.unsubscribe)}},this.getTopics=function(){return t}}function Ro(r){if(!(this instanceof Ro))return new Ro(r);var t="opacity,stroke-opacity".split(",");for(var e in r)r.hasOwnProperty(e)&&t.indexOf(e)>=0&&(this[e]=r[e]);this.id="",this.objectNumber=-1}function ff(r,t){this.gState=r,this.matrix=t,this.id="",this.objectNumber=-1}function Li(r,t,e,n,i){if(!(this instanceof Li))return new Li(r,t,e,n,i);this.type=r==="axial"?2:3,this.coords=t,this.colors=e,ff.call(this,n,i)}function Qi(r,t,e,n,i){if(!(this instanceof Qi))return new Qi(r,t,e,n,i);this.boundingBox=r,this.xStep=t,this.yStep=e,this.stream="",this.cloneIndex=0,ff.call(this,n,i)}function Rt(r){var t,e=typeof arguments[0]=="string"?arguments[0]:"p",n=arguments[1],i=arguments[2],a=arguments[3],u=[],l=1,h=16,f="S",p=null;_e(r=r||{})==="object"&&(e=r.orientation,n=r.unit||n,i=r.format||i,a=r.compress||r.compressPdf||a,(p=r.encryption||null)!==null&&(p.userPassword=p.userPassword||"",p.ownerPassword=p.ownerPassword||"",p.userPermissions=p.userPermissions||[]),l=typeof r.userUnit=="number"?Math.abs(r.userUnit):1,r.precision!==void 0&&(t=r.precision),r.floatPrecision!==void 0&&(h=r.floatPrecision),f=r.defaultPathOperation||"S"),u=r.filters||(a===!0?["FlateEncode"]:u),n=n||"mm",e=(""+(e||"P")).toLowerCase();var y=r.putOnlyUsedFonts||!1,x={},g={internal:{},__private__:{}};g.__private__.PubSub=nh;var P="1.3",C=g.__private__.getPdfVersion=function(){return P};g.__private__.setPdfVersion=function(c){P=c};var D={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};g.__private__.getPageFormats=function(){return D};var S=g.__private__.getPageFormat=function(c){return D[c]};i=i||"a4";var W="compat",z="advanced",M=W;function at(){this.saveGraphicsState(),j(new Gt(Vt,0,0,-Vt,0,Ir()*Vt).toString()+" cm"),this.setFontSize(this.getFontSize()/Vt),f="n",M=z}function vt(){this.restoreGraphicsState(),f="S",M=W}var ot=g.__private__.combineFontStyleAndFontWeight=function(c,b){if(c=="bold"&&b=="normal"||c=="bold"&&b==400||c=="normal"&&b=="italic"||c=="bold"&&b=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return b&&(c=b==400||b==="normal"?c==="italic"?"italic":"normal":b!=700&&b!=="bold"||c!=="normal"?(b==700?"bold":b)+""+c:"bold"),c};g.advancedAPI=function(c){var b=M===W;return b&&at.call(this),typeof c!="function"||(c(this),b&&vt.call(this)),this},g.compatAPI=function(c){var b=M===z;return b&&vt.call(this),typeof c!="function"||(c(this),b&&at.call(this)),this},g.isAdvancedAPI=function(){return M===z};var $,R=function(c){if(M!==z)throw new Error(c+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},et=g.roundToPrecision=g.__private__.roundToPrecision=function(c,b){var I=t||b;if(isNaN(c)||isNaN(I))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return c.toFixed(I).replace(/0+$/,"")};$=g.hpf=g.__private__.hpf=typeof h=="number"?function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.hpf");return et(c,h)}:h==="smart"?function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.hpf");return et(c,c>-1&&c<1?16:5)}:function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.hpf");return et(c,16)};var N=g.f2=g.__private__.f2=function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.f2");return et(c,2)},O=g.__private__.f3=function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.f3");return et(c,3)},G=g.scale=g.__private__.scale=function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.scale");return M===W?c*Vt:M===z?c:void 0},U=function(c){return G((function(b){return M===W?Ir()-b:M===z?b:void 0})(c))};g.__private__.setPrecision=g.setPrecision=function(c){typeof parseInt(c,10)=="number"&&(t=parseInt(c,10))};var nt,ht="00000000000000000000000000000000",dt=g.__private__.getFileId=function(){return ht},rt=g.__private__.setFileId=function(c){return ht=c!==void 0&&/^[a-fA-F0-9]{32}$/.test(c)?c.toUpperCase():ht.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),p!==null&&(Ge=new Xi(p.userPermissions,p.userPassword,p.ownerPassword,ht)),ht};g.setFileId=function(c){return rt(c),this},g.getFileId=function(){return dt()};var ft=g.__private__.convertDateToPDFDate=function(c){var b=c.getTimezoneOffset(),I=b<0?"+":"-",q=Math.floor(Math.abs(b/60)),J=Math.abs(b%60),pt=[I,T(q),"'",T(J),"'"].join("");return["D:",c.getFullYear(),T(c.getMonth()+1),T(c.getDate()),T(c.getHours()),T(c.getMinutes()),T(c.getSeconds()),pt].join("")},Nt=g.__private__.convertPDFDateToDate=function(c){var b=parseInt(c.substr(2,4),10),I=parseInt(c.substr(6,2),10)-1,q=parseInt(c.substr(8,2),10),J=parseInt(c.substr(10,2),10),pt=parseInt(c.substr(12,2),10),yt=parseInt(c.substr(14,2),10);return new Date(b,I,q,J,pt,yt,0)},wt=g.__private__.setCreationDate=function(c){var b;if(c===void 0&&(c=new Date),c instanceof Date)b=ft(c);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(c))throw new Error("Invalid argument passed to jsPDF.setCreationDate");b=c}return nt=b},A=g.__private__.getCreationDate=function(c){var b=nt;return c==="jsDate"&&(b=Nt(nt)),b};g.setCreationDate=function(c){return wt(c),this},g.getCreationDate=function(c){return A(c)};var B,T=g.__private__.padd2=function(c){return("0"+parseInt(c)).slice(-2)},V=g.__private__.padd2Hex=function(c){return("00"+(c=c.toString())).substr(c.length)},Y=0,Z=[],lt=[],it=0,mt=[],Lt=[],Pt=!1,Ct=lt;g.__private__.setCustomOutputDestination=function(c){Pt=!0,Ct=c};var st=function(c){Pt||(Ct=c)};g.__private__.resetCustomOutputDestination=function(){Pt=!1,Ct=lt};var j=g.__private__.out=function(c){return c=c.toString(),it+=c.length+1,Ct.push(c),Ct},he=g.__private__.write=function(c){return j(arguments.length===1?c.toString():Array.prototype.join.call(arguments," "))},ne=g.__private__.getArrayBuffer=function(c){for(var b=c.length,I=new ArrayBuffer(b),q=new Uint8Array(I);b--;)q[b]=c.charCodeAt(b);return I},Ht=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];g.__private__.getStandardFonts=function(){return Ht};var At=r.fontSize||16;g.__private__.setFontSize=g.setFontSize=function(c){return At=M===z?c/Vt:c,this};var Mt,kt=g.__private__.getFontSize=g.getFontSize=function(){return M===W?At:At*Vt},ie=r.R2L||!1;g.__private__.setR2L=g.setR2L=function(c){return ie=c,this},g.__private__.getR2L=g.getR2L=function(){return ie};var zt,ge=g.__private__.setZoomMode=function(c){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(c))Mt=c;else if(isNaN(c)){if([void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(c)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+c+'" is not recognized.');Mt=c}else Mt=parseInt(c,10)};g.__private__.getZoomMode=function(){return Mt};var Zt,Qt=g.__private__.setPageMode=function(c){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(c)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+c+'" is not recognized.');zt=c};g.__private__.getPageMode=function(){return zt};var Ae=g.__private__.setLayoutMode=function(c){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(c)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+c+'" is not recognized.');Zt=c};g.__private__.getLayoutMode=function(){return Zt},g.__private__.setDisplayMode=g.setDisplayMode=function(c,b,I){return ge(c),Ae(b),Qt(I),this};var ae={title:"",subject:"",author:"",keywords:"",creator:""};g.__private__.getDocumentProperty=function(c){if(Object.keys(ae).indexOf(c)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return ae[c]},g.__private__.getDocumentProperties=function(){return ae},g.__private__.setDocumentProperties=g.setProperties=g.setDocumentProperties=function(c){for(var b in ae)ae.hasOwnProperty(b)&&c[b]&&(ae[b]=c[b]);return this},g.__private__.setDocumentProperty=function(c,b){if(Object.keys(ae).indexOf(c)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return ae[c]=b};var qt,Vt,Ut,Ce,me,jt={},te={},Ne=[],Wt={},fe={},ee={},We={},nr=null,Se=0,Jt=[],be=new nh(g),oi=r.hotfixes||[],vr={},An={},un=[],Gt=function c(b,I,q,J,pt,yt){if(!(this instanceof c))return new c(b,I,q,J,pt,yt);isNaN(b)&&(b=1),isNaN(I)&&(I=0),isNaN(q)&&(q=0),isNaN(J)&&(J=1),isNaN(pt)&&(pt=0),isNaN(yt)&&(yt=0),this._matrix=[b,I,q,J,pt,yt]};Object.defineProperty(Gt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(c){this._matrix[0]=c}}),Object.defineProperty(Gt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(c){this._matrix[1]=c}}),Object.defineProperty(Gt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(c){this._matrix[2]=c}}),Object.defineProperty(Gt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(c){this._matrix[3]=c}}),Object.defineProperty(Gt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(c){this._matrix[4]=c}}),Object.defineProperty(Gt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(c){this._matrix[5]=c}}),Object.defineProperty(Gt.prototype,"a",{get:function(){return this._matrix[0]},set:function(c){this._matrix[0]=c}}),Object.defineProperty(Gt.prototype,"b",{get:function(){return this._matrix[1]},set:function(c){this._matrix[1]=c}}),Object.defineProperty(Gt.prototype,"c",{get:function(){return this._matrix[2]},set:function(c){this._matrix[2]=c}}),Object.defineProperty(Gt.prototype,"d",{get:function(){return this._matrix[3]},set:function(c){this._matrix[3]=c}}),Object.defineProperty(Gt.prototype,"e",{get:function(){return this._matrix[4]},set:function(c){this._matrix[4]=c}}),Object.defineProperty(Gt.prototype,"f",{get:function(){return this._matrix[5]},set:function(c){this._matrix[5]=c}}),Object.defineProperty(Gt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Gt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Gt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Gt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),Gt.prototype.join=function(c){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map($).join(c)},Gt.prototype.multiply=function(c){var b=c.sx*this.sx+c.shy*this.shx,I=c.sx*this.shy+c.shy*this.sy,q=c.shx*this.sx+c.sy*this.shx,J=c.shx*this.shy+c.sy*this.sy,pt=c.tx*this.sx+c.ty*this.shx+this.tx,yt=c.tx*this.shy+c.ty*this.sy+this.ty;return new Gt(b,I,q,J,pt,yt)},Gt.prototype.decompose=function(){var c=this.sx,b=this.shy,I=this.shx,q=this.sy,J=this.tx,pt=this.ty,yt=Math.sqrt(c*c+b*b),It=(c/=yt)*I+(b/=yt)*q;I-=c*It,q-=b*It;var Bt=Math.sqrt(I*I+q*q);return It/=Bt,c*(q/=Bt)<b*(I/=Bt)&&(c=-c,b=-b,It=-It,yt=-yt),{scale:new Gt(yt,0,0,Bt,0,0),translate:new Gt(1,0,0,1,J,pt),rotate:new Gt(c,b,-b,c,0,0),skew:new Gt(1,0,It,1,0,0)}},Gt.prototype.toString=function(c){return this.join(" ")},Gt.prototype.inversed=function(){var c=this.sx,b=this.shy,I=this.shx,q=this.sy,J=this.tx,pt=this.ty,yt=1/(c*q-b*I),It=q*yt,Bt=-b*yt,Kt=-I*yt,oe=c*yt;return new Gt(It,Bt,Kt,oe,-It*J-Kt*pt,-Bt*J-oe*pt)},Gt.prototype.applyToPoint=function(c){var b=c.x*this.sx+c.y*this.shx+this.tx,I=c.x*this.shy+c.y*this.sy+this.ty;return new mi(b,I)},Gt.prototype.applyToRectangle=function(c){var b=this.applyToPoint(c),I=this.applyToPoint(new mi(c.x+c.w,c.y+c.h));return new ma(b.x,b.y,I.x-b.x,I.y-b.y)},Gt.prototype.clone=function(){var c=this.sx,b=this.shy,I=this.shx,q=this.sy,J=this.tx,pt=this.ty;return new Gt(c,b,I,q,J,pt)},g.Matrix=Gt;var Ln=g.matrixMult=function(c,b){return b.multiply(c)},Nn=new Gt(1,0,0,1,0,0);g.unitMatrix=g.identityMatrix=Nn;var zr=function(c,b){if(!fe[c]){var I=(b instanceof Li?"Sh":"P")+(Object.keys(Wt).length+1).toString(10);b.id=I,fe[c]=I,Wt[I]=b,be.publish("addPattern",b)}};g.ShadingPattern=Li,g.TilingPattern=Qi,g.addShadingPattern=function(c,b){return R("addShadingPattern()"),zr(c,b),this},g.beginTilingPattern=function(c){R("beginTilingPattern()"),va(c.boundingBox[0],c.boundingBox[1],c.boundingBox[2]-c.boundingBox[0],c.boundingBox[3]-c.boundingBox[1],c.matrix)},g.endTilingPattern=function(c,b){R("endTilingPattern()"),b.stream=Lt[B].join("\n"),zr(c,b),be.publish("endTilingPattern",b),un.pop().restore()};var Hr,qe=g.__private__.newObject=function(){var c=ur();return cr(c,!0),c},ur=g.__private__.newObjectDeferred=function(){return Y++,Z[Y]=function(){return it},Y},cr=function(c,b){return b=typeof b=="boolean"&&b,Z[c]=it,b&&j(c+" 0 obj"),c},si=g.__private__.newAdditionalObject=function(){var c={objId:ur(),content:""};return mt.push(c),c},zn=ur(),$r=ur(),hn=g.__private__.decodeColorString=function(c){var b=c.split(" ");if(b.length!==2||b[1]!=="g"&&b[1]!=="G")b.length!==5||b[4]!=="k"&&b[4]!=="K"||(b=[(1-b[0])*(1-b[3]),(1-b[1])*(1-b[3]),(1-b[2])*(1-b[3]),"r"]);else{var I=parseFloat(b[0]);b=[I,I,I,"r"]}for(var q="#",J=0;J<3;J++)q+=("0"+Math.floor(255*parseFloat(b[J])).toString(16)).slice(-2);return q},Xr=g.__private__.encodeColorString=function(c){var b;typeof c=="string"&&(c={ch1:c});var I=c.ch1,q=c.ch2,J=c.ch3,pt=c.ch4,yt=c.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof I=="string"&&I.charAt(0)!=="#"){var It=new uf(I);if(It.ok)I=It.toHex();else if(!/^\d*\.?\d*$/.test(I))throw new Error('Invalid color "'+I+'" passed to jsPDF.encodeColorString.')}if(typeof I=="string"&&/^#[0-9A-Fa-f]{3}$/.test(I)&&(I="#"+I[1]+I[1]+I[2]+I[2]+I[3]+I[3]),typeof I=="string"&&/^#[0-9A-Fa-f]{6}$/.test(I)){var Bt=parseInt(I.substr(1),16);I=Bt>>16&255,q=Bt>>8&255,J=255&Bt}if(q===void 0||pt===void 0&&I===q&&q===J)b=typeof I=="string"?I+" "+yt[0]:c.precision===2?N(I/255)+" "+yt[0]:O(I/255)+" "+yt[0];else if(pt===void 0||_e(pt)==="object"){if(pt&&!isNaN(pt.a)&&pt.a===0)return["1.","1.","1.",yt[1]].join(" ");b=typeof I=="string"?[I,q,J,yt[1]].join(" "):c.precision===2?[N(I/255),N(q/255),N(J/255),yt[1]].join(" "):[O(I/255),O(q/255),O(J/255),yt[1]].join(" ")}else b=typeof I=="string"?[I,q,J,pt,yt[2]].join(" "):c.precision===2?[N(I),N(q),N(J),N(pt),yt[2]].join(" "):[O(I),O(q),O(J),O(pt),yt[2]].join(" ");return b},Zr=g.__private__.getFilters=function(){return u},Wr=g.__private__.putStream=function(c){var b=(c=c||{}).data||"",I=c.filters||Zr(),q=c.alreadyAppliedFilters||[],J=c.addLength1||!1,pt=b.length,yt=c.objectId,It=function(ir){return ir};if(p!==null&&yt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");p!==null&&(It=Ge.encryptor(yt,0));var Bt={};I===!0&&(I=["FlateEncode"]);var Kt=c.additionalKeyValues||[],oe=(Bt=Rt.API.processDataByFilters!==void 0?Rt.API.processDataByFilters(b,I):{data:b,reverseChain:[]}).reverseChain+(Array.isArray(q)?q.join(" "):q.toString());if(Bt.data.length!==0&&(Kt.push({key:"Length",value:Bt.data.length}),J===!0&&Kt.push({key:"Length1",value:pt})),oe.length!=0)if(oe.split("/").length-1==1)Kt.push({key:"Filter",value:oe});else{Kt.push({key:"Filter",value:"["+oe+"]"});for(var ve=0;ve<Kt.length;ve+=1)if(Kt[ve].key==="DecodeParms"){for(var Te=[],er=0;er<Bt.reverseChain.split("/").length-1;er+=1)Te.push("null");Te.push(Kt[ve].value),Kt[ve].value="["+Te.join(" ")+"]"}}j("<<");for(var Le=0;Le<Kt.length;Le++)j("/"+Kt[Le].key+" "+Kt[Le].value);j(">>"),Bt.data.length!==0&&(j("stream"),j(It(Bt.data)),j("endstream"))},li=g.__private__.putPage=function(c){var b=c.number,I=c.data,q=c.objId,J=c.contentsObjId;cr(q,!0),j("<</Type /Page"),j("/Parent "+c.rootDictionaryObjId+" 0 R"),j("/Resources "+c.resourceDictionaryObjId+" 0 R"),j("/MediaBox ["+parseFloat($(c.mediaBox.bottomLeftX))+" "+parseFloat($(c.mediaBox.bottomLeftY))+" "+$(c.mediaBox.topRightX)+" "+$(c.mediaBox.topRightY)+"]"),c.cropBox!==null&&j("/CropBox ["+$(c.cropBox.bottomLeftX)+" "+$(c.cropBox.bottomLeftY)+" "+$(c.cropBox.topRightX)+" "+$(c.cropBox.topRightY)+"]"),c.bleedBox!==null&&j("/BleedBox ["+$(c.bleedBox.bottomLeftX)+" "+$(c.bleedBox.bottomLeftY)+" "+$(c.bleedBox.topRightX)+" "+$(c.bleedBox.topRightY)+"]"),c.trimBox!==null&&j("/TrimBox ["+$(c.trimBox.bottomLeftX)+" "+$(c.trimBox.bottomLeftY)+" "+$(c.trimBox.topRightX)+" "+$(c.trimBox.topRightY)+"]"),c.artBox!==null&&j("/ArtBox ["+$(c.artBox.bottomLeftX)+" "+$(c.artBox.bottomLeftY)+" "+$(c.artBox.topRightX)+" "+$(c.artBox.topRightY)+"]"),typeof c.userUnit=="number"&&c.userUnit!==1&&j("/UserUnit "+c.userUnit),be.publish("putPage",{objId:q,pageContext:Jt[b],pageNumber:b,page:I}),j("/Contents "+J+" 0 R"),j(">>"),j("endobj");var pt=I.join("\n");return M===z&&(pt+="\nQ"),cr(J,!0),Wr({data:pt,filters:Zr(),objectId:J}),j("endobj"),q},fn=g.__private__.putPages=function(){var c,b,I=[];for(c=1;c<=Se;c++)Jt[c].objId=ur(),Jt[c].contentsObjId=ur();for(c=1;c<=Se;c++)I.push(li({number:c,data:Lt[c],objId:Jt[c].objId,contentsObjId:Jt[c].contentsObjId,mediaBox:Jt[c].mediaBox,cropBox:Jt[c].cropBox,bleedBox:Jt[c].bleedBox,trimBox:Jt[c].trimBox,artBox:Jt[c].artBox,userUnit:Jt[c].userUnit,rootDictionaryObjId:zn,resourceDictionaryObjId:$r}));cr(zn,!0),j("<</Type /Pages");var q="/Kids [";for(b=0;b<Se;b++)q+=I[b]+" 0 R ";j(q+"]"),j("/Count "+Se),j(">>"),j("endobj"),be.publish("postPutPages")},ui=function(c){be.publish("putFont",{font:c,out:j,newObject:qe,putStream:Wr}),c.isAlreadyPutted!==!0&&(c.objectNumber=qe(),j("<<"),j("/Type /Font"),j("/BaseFont /"+Zi(c.postScriptName)),j("/Subtype /Type1"),typeof c.encoding=="string"&&j("/Encoding /"+c.encoding),j("/FirstChar 32"),j("/LastChar 255"),j(">>"),j("endobj"))},Fi=function(c){c.objectNumber=qe();var b=[];b.push({key:"Type",value:"/XObject"}),b.push({key:"Subtype",value:"/Form"}),b.push({key:"BBox",value:"["+[$(c.x),$(c.y),$(c.x+c.width),$(c.y+c.height)].join(" ")+"]"}),b.push({key:"Matrix",value:"["+c.matrix.toString()+"]"});var I=c.pages[1].join("\n");Wr({data:I,additionalKeyValues:b,objectId:c.objectNumber}),j("endobj")},Ei=function(c,b){b||(b=21);var I=qe(),q=(function(yt,It){var Bt,Kt=[],oe=1/(It-1);for(Bt=0;Bt<1;Bt+=oe)Kt.push(Bt);if(Kt.push(1),yt[0].offset!=0){var ve={offset:0,color:yt[0].color};yt.unshift(ve)}if(yt[yt.length-1].offset!=1){var Te={offset:1,color:yt[yt.length-1].color};yt.push(Te)}for(var er="",Le=0,ir=0;ir<Kt.length;ir++){for(Bt=Kt[ir];Bt>yt[Le+1].offset;)Le++;var rr=yt[Le].offset,dr=(Bt-rr)/(yt[Le+1].offset-rr),Cn=yt[Le].color,Gn=yt[Le+1].color;er+=V(Math.round((1-dr)*Cn[0]+dr*Gn[0]).toString(16))+V(Math.round((1-dr)*Cn[1]+dr*Gn[1]).toString(16))+V(Math.round((1-dr)*Cn[2]+dr*Gn[2]).toString(16))}return er.trim()})(c.colors,b),J=[];J.push({key:"FunctionType",value:"0"}),J.push({key:"Domain",value:"[0.0 1.0]"}),J.push({key:"Size",value:"["+b+"]"}),J.push({key:"BitsPerSample",value:"8"}),J.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),J.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Wr({data:q,additionalKeyValues:J,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:I}),j("endobj"),c.objectNumber=qe(),j("<< /ShadingType "+c.type),j("/ColorSpace /DeviceRGB");var pt="/Coords ["+$(parseFloat(c.coords[0]))+" "+$(parseFloat(c.coords[1]))+" ";c.type===2?pt+=$(parseFloat(c.coords[2]))+" "+$(parseFloat(c.coords[3])):pt+=$(parseFloat(c.coords[2]))+" "+$(parseFloat(c.coords[3]))+" "+$(parseFloat(c.coords[4]))+" "+$(parseFloat(c.coords[5])),j(pt+="]"),c.matrix&&j("/Matrix ["+c.matrix.toString()+"]"),j("/Function "+I+" 0 R"),j("/Extend [true true]"),j(">>"),j("endobj")},Ii=function(c,b){var I=ur(),q=qe();b.push({resourcesOid:I,objectOid:q}),c.objectNumber=q;var J=[];J.push({key:"Type",value:"/Pattern"}),J.push({key:"PatternType",value:"1"}),J.push({key:"PaintType",value:"1"}),J.push({key:"TilingType",value:"1"}),J.push({key:"BBox",value:"["+c.boundingBox.map($).join(" ")+"]"}),J.push({key:"XStep",value:$(c.xStep)}),J.push({key:"YStep",value:$(c.yStep)}),J.push({key:"Resources",value:I+" 0 R"}),c.matrix&&J.push({key:"Matrix",value:"["+c.matrix.toString()+"]"}),Wr({data:c.stream,additionalKeyValues:J,objectId:c.objectNumber}),j("endobj")},Go=function(c){for(var b in c.objectNumber=qe(),j("<<"),c)switch(b){case"opacity":j("/ca "+N(c[b]));break;case"stroke-opacity":j("/CA "+N(c[b]))}j(">>"),j("endobj")},oa=function(c){cr(c.resourcesOid,!0),j("<<"),j("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),(function(){for(var b in j("/Font <<"),jt)jt.hasOwnProperty(b)&&(y===!1||y===!0&&x.hasOwnProperty(b))&&j("/"+b+" "+jt[b].objectNumber+" 0 R");j(">>")})(),(function(){if(Object.keys(Wt).length>0){for(var b in j("/Shading <<"),Wt)Wt.hasOwnProperty(b)&&Wt[b]instanceof Li&&Wt[b].objectNumber>=0&&j("/"+b+" "+Wt[b].objectNumber+" 0 R");be.publish("putShadingPatternDict"),j(">>")}})(),(function(b){if(Object.keys(Wt).length>0){for(var I in j("/Pattern <<"),Wt)Wt.hasOwnProperty(I)&&Wt[I]instanceof g.TilingPattern&&Wt[I].objectNumber>=0&&Wt[I].objectNumber<b&&j("/"+I+" "+Wt[I].objectNumber+" 0 R");be.publish("putTilingPatternDict"),j(">>")}})(c.objectOid),(function(){if(Object.keys(ee).length>0){var b;for(b in j("/ExtGState <<"),ee)ee.hasOwnProperty(b)&&ee[b].objectNumber>=0&&j("/"+b+" "+ee[b].objectNumber+" 0 R");be.publish("putGStateDict"),j(">>")}})(),(function(){for(var b in j("/XObject <<"),vr)vr.hasOwnProperty(b)&&vr[b].objectNumber>=0&&j("/"+b+" "+vr[b].objectNumber+" 0 R");be.publish("putXobjectDict"),j(">>")})(),j(">>"),j("endobj")},Ha=function(c){te[c.fontName]=te[c.fontName]||{},te[c.fontName][c.fontStyle]=c.id},Wa=function(c,b,I,q,J){var pt={id:"F"+(Object.keys(jt).length+1).toString(10),postScriptName:c,fontName:b,fontStyle:I,encoding:q,isStandardFont:J||!1,metadata:{}};return be.publish("addFont",{font:pt,instance:this}),jt[pt.id]=pt,Ha(pt),pt.id},Gr=g.__private__.pdfEscape=g.pdfEscape=function(c,b){return(function(I,q){var J,pt,yt,It,Bt,Kt,oe,ve,Te;if(yt=(q=q||{}).sourceEncoding||"Unicode",Bt=q.outputEncoding,(q.autoencode||Bt)&&jt[qt].metadata&&jt[qt].metadata[yt]&&jt[qt].metadata[yt].encoding&&(It=jt[qt].metadata[yt].encoding,!Bt&&jt[qt].encoding&&(Bt=jt[qt].encoding),!Bt&&It.codePages&&(Bt=It.codePages[0]),typeof Bt=="string"&&(Bt=It[Bt]),Bt)){for(oe=!1,Kt=[],J=0,pt=I.length;J<pt;J++)(ve=Bt[I.charCodeAt(J)])?Kt.push(String.fromCharCode(ve)):Kt.push(I[J]),Kt[J].charCodeAt(0)>>8&&(oe=!0);I=Kt.join("")}for(J=I.length;oe===void 0&&J!==0;)I.charCodeAt(J-1)>>8&&(oe=!0),J--;if(!oe)return I;for(Kt=q.noBOM?[]:[254,255],J=0,pt=I.length;J<pt;J++){if((Te=(ve=I.charCodeAt(J))>>8)>>8)throw new Error("Character at position "+J+" of string '"+I+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Kt.push(Te),Kt.push(ve-(Te<<8))}return String.fromCharCode.apply(void 0,Kt)})(c,b).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},sa=g.__private__.beginPage=function(c){Lt[++Se]=[],Jt[Se]={objId:0,contentsObjId:0,userUnit:Number(l),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(c[0]),topRightY:Number(c[1])}},Va(Se),st(Lt[B])},Ga=function(c,b){var I,q,J;switch(e=b||e,typeof c=="string"&&(I=S(c.toLowerCase()),Array.isArray(I)&&(q=I[0],J=I[1])),Array.isArray(c)&&(q=c[0]*Vt,J=c[1]*Vt),isNaN(q)&&(q=i[0],J=i[1]),(q>14400||J>14400)&&(Pe.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),q=Math.min(14400,q),J=Math.min(14400,J)),i=[q,J],e.substr(0,1)){case"l":J>q&&(i=[J,q]);break;case"p":q>J&&(i=[J,q])}sa(i),ts(ca),j(Di),pa!==0&&j(pa+" J"),ga!==0&&j(ga+" j"),be.publish("addPage",{pageNumber:Se})},la=function(c){c>0&&c<=Se&&(Lt.splice(c,1),Jt.splice(c,1),Se--,B>Se&&(B=Se),this.setPage(B))},Va=function(c){c>0&&c<=Se&&(B=c)},Ka=g.__private__.getNumberOfPages=g.getNumberOfPages=function(){return Lt.length-1},Ya=function(c,b,I){var q,J=void 0;return I=I||{},c=c!==void 0?c:jt[qt].fontName,b=b!==void 0?b:jt[qt].fontStyle,q=c.toLowerCase(),te[q]!==void 0&&te[q][b]!==void 0?J=te[q][b]:te[c]!==void 0&&te[c][b]!==void 0?J=te[c][b]:I.disableWarning===!1&&Pe.warn("Unable to look up font label for font '"+c+"', '"+b+"'. Refer to getFontList() for available fonts."),J||I.noFallback||(J=te.times[b])==null&&(J=te.times.normal),J},Oi=g.__private__.putInfo=function(){var c=qe(),b=function(q){return q};for(var I in p!==null&&(b=Ge.encryptor(c,0)),j("<<"),j("/Producer ("+Gr(b("jsPDF "+Rt.version))+")"),ae)ae.hasOwnProperty(I)&&ae[I]&&j("/"+I.substr(0,1).toUpperCase()+I.substr(1)+" ("+Gr(b(ae[I]))+")");j("/CreationDate ("+Gr(b(nt))+")"),j(">>"),j("endobj")},Vo=g.__private__.putCatalog=function(c){var b=(c=c||{}).rootDictionaryObjId||zn;switch(qe(),j("<<"),j("/Type /Catalog"),j("/Pages "+b+" 0 R"),Mt||(Mt="fullwidth"),Mt){case"fullwidth":j("/OpenAction [3 0 R /FitH null]");break;case"fullheight":j("/OpenAction [3 0 R /FitV null]");break;case"fullpage":j("/OpenAction [3 0 R /Fit]");break;case"original":j("/OpenAction [3 0 R /XYZ null null 1]");break;default:var I=""+Mt;I.substr(I.length-1)==="%"&&(Mt=parseInt(Mt)/100),typeof Mt=="number"&&j("/OpenAction [3 0 R /XYZ null null "+N(Mt)+"]")}switch(Zt||(Zt="continuous"),Zt){case"continuous":j("/PageLayout /OneColumn");break;case"single":j("/PageLayout /SinglePage");break;case"two":case"twoleft":j("/PageLayout /TwoColumnLeft");break;case"tworight":j("/PageLayout /TwoColumnRight")}zt&&j("/PageMode /"+zt),be.publish("putCatalog"),j(">>"),j("endobj")},tr=g.__private__.putTrailer=function(){j("trailer"),j("<<"),j("/Size "+(Y+1)),j("/Root "+Y+" 0 R"),j("/Info "+(Y-1)+" 0 R"),p!==null&&j("/Encrypt "+Ge.oid+" 0 R"),j("/ID [ <"+ht+"> <"+ht+"> ]"),j(">>")},Ja=g.__private__.putHeader=function(){j("%PDF-"+P),j("%ºß¬à")},$a=g.__private__.putXRef=function(){var c="0000000000";j("xref"),j("0 "+(Y+1)),j("0000000000 65535 f ");for(var b=1;b<=Y;b++)typeof Z[b]=="function"?j((c+Z[b]()).slice(-10)+" 00000 n "):Z[b]!==void 0?j((c+Z[b]).slice(-10)+" 00000 n "):j("0000000000 00000 n ")},Sn=g.__private__.buildDocument=function(){var c;Y=0,it=0,lt=[],Z=[],mt=[],zn=ur(),$r=ur(),st(lt),be.publish("buildDocument"),Ja(),fn(),(function(){be.publish("putAdditionalObjects");for(var I=0;I<mt.length;I++){var q=mt[I];cr(q.objId,!0),j(q.content),j("endobj")}be.publish("postPutAdditionalObjects")})(),c=[],(function(){for(var I in jt)jt.hasOwnProperty(I)&&(y===!1||y===!0&&x.hasOwnProperty(I))&&ui(jt[I])})(),(function(){var I;for(I in ee)ee.hasOwnProperty(I)&&Go(ee[I])})(),(function(){for(var I in vr)vr.hasOwnProperty(I)&&Fi(vr[I])})(),(function(I){var q;for(q in Wt)Wt.hasOwnProperty(q)&&(Wt[q]instanceof Li?Ei(Wt[q]):Wt[q]instanceof Qi&&Ii(Wt[q],I))})(c),be.publish("putResources"),c.forEach(oa),oa({resourcesOid:$r,objectOid:Number.MAX_SAFE_INTEGER}),be.publish("postPutResources"),p!==null&&(Ge.oid=qe(),j("<<"),j("/Filter /Standard"),j("/V "+Ge.v),j("/R "+Ge.r),j("/U <"+Ge.toHexString(Ge.U)+">"),j("/O <"+Ge.toHexString(Ge.O)+">"),j("/P "+Ge.P),j(">>"),j("endobj")),Oi(),Vo();var b=it;return $a(),tr(),j("startxref"),j(""+b),j("%%EOF"),st(Lt[B]),lt.join("\n")},hi=g.__private__.getBlob=function(c){return new Blob([ne(c)],{type:"application/pdf"})},Qr=g.output=g.__private__.output=(Hr=function(c,b){switch(typeof(b=b||{})=="string"?b={filename:b}:b.filename=b.filename||"generated.pdf",c){case void 0:return Sn();case"save":g.save(b.filename);break;case"arraybuffer":return ne(Sn());case"blob":return hi(Sn());case"bloburi":case"bloburl":if(Yt.URL!==void 0&&typeof Yt.URL.createObjectURL=="function")return Yt.URL&&Yt.URL.createObjectURL(hi(Sn()))||void 0;Pe.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var I="",q=Sn();try{I=th(q)}catch(er){I=th(unescape(encodeURIComponent(q)))}return"data:application/pdf;filename="+b.filename+";base64,"+I;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Yt)==="[object Window]"){var J="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",pt=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';b.pdfObjectUrl&&(J=b.pdfObjectUrl,pt="");var yt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+J+'"'+pt+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(b)+");<\/script></body></html>",It=Yt.open();return It!==null&&It.document.write(yt),It}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Yt)==="[object Window]"){var Bt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(b.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+b.filename+'" width="500px" height="400px" /></body></html>',Kt=Yt.open();if(Kt!==null){Kt.document.write(Bt);var oe=this;Kt.document.documentElement.querySelector("#pdfViewer").onload=function(){Kt.document.title=b.filename,Kt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(oe.output("bloburl"))}}return Kt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Yt)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var ve='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",b)+'"></iframe></body></html>',Te=Yt.open();if(Te!==null&&(Te.document.write(ve),Te.document.title=b.filename),Te||typeof safari>"u")return Te;break;case"datauri":case"dataurl":return Yt.document.location.href=this.output("datauristring",b);default:return null}},Hr.foo=function(){try{return Hr.apply(this,arguments)}catch(I){var c=I.stack||"";~c.indexOf(" at ")&&(c=c.split(" at ")[1]);var b="Error in function "+c.split("\n")[0].split("<")[0]+": "+I.message;if(!Yt.console)throw new Error(b);Yt.console.error(b,I),Yt.alert&&alert(b)}},Hr.foo.bar=Hr,Hr.foo),fi=function(c){return Array.isArray(oi)===!0&&oi.indexOf(c)>-1};switch(n){case"pt":Vt=1;break;case"mm":Vt=72/25.4;break;case"cm":Vt=72/2.54;break;case"in":Vt=72;break;case"px":Vt=fi("px_scaling")==1?.75:96/72;break;case"pc":case"em":Vt=12;break;case"ex":Vt=6;break;default:if(typeof n!="number")throw new Error("Invalid unit: "+n);Vt=n}var Ge=null;wt(),rt();var ua=g.__private__.getPageInfo=g.getPageInfo=function(c){if(isNaN(c)||c%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Jt[c].objId,pageNumber:c,pageContext:Jt[c]}},Ko=g.__private__.getPageInfoByObjId=function(c){if(isNaN(c)||c%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var b in Jt)if(Jt[b].objId===c)break;return ua(b)},Yo=g.__private__.getCurrentPageInfo=g.getCurrentPageInfo=function(){return{objId:Jt[B].objId,pageNumber:B,pageContext:Jt[B]}};g.addPage=function(){return Ga.apply(this,arguments),this},g.setPage=function(){return Va.apply(this,arguments),st.call(this,Lt[B]),this},g.insertPage=function(c){return this.addPage(),this.movePage(B,c),this},g.movePage=function(c,b){var I,q;if(c>b){I=Lt[c],q=Jt[c];for(var J=c;J>b;J--)Lt[J]=Lt[J-1],Jt[J]=Jt[J-1];Lt[b]=I,Jt[b]=q,this.setPage(b)}else if(c<b){I=Lt[c],q=Jt[c];for(var pt=c;pt<b;pt++)Lt[pt]=Lt[pt+1],Jt[pt]=Jt[pt+1];Lt[b]=I,Jt[b]=q,this.setPage(b)}return this},g.deletePage=function(){return la.apply(this,arguments),this},g.__private__.text=g.text=function(c,b,I,q,J){var pt,yt,It,Bt,Kt,oe,ve,Te,er,Le=(q=q||{}).scope||this;if(typeof c=="number"&&typeof b=="number"&&(typeof I=="string"||Array.isArray(I))){var ir=I;I=b,b=c,c=ir}if(arguments[3]instanceof Gt==0?(It=arguments[4],Bt=arguments[5],_e(ve=arguments[3])==="object"&&ve!==null||(typeof It=="string"&&(Bt=It,It=null),typeof ve=="string"&&(Bt=ve,ve=null),typeof ve=="number"&&(It=ve,ve=null),q={flags:ve,angle:It,align:Bt})):(R("The transform parameter of text() with a Matrix value"),er=J),isNaN(b)||isNaN(I)||c==null)throw new Error("Invalid arguments passed to jsPDF.text");if(c.length===0)return Le;var rr,dr="",Cn=typeof q.lineHeightFactor=="number"?q.lineHeightFactor:di,Gn=Le.internal.scaleFactor;function ss(Fe){return Fe=Fe.split("	").join(Array(q.TabLen||9).join(" ")),Gr(Fe,ve)}function Qa(Fe){for(var Ee,Ke=Fe.concat(),Ye=[],On=Ke.length;On--;)typeof(Ee=Ke.shift())=="string"?Ye.push(Ee):Array.isArray(Fe)&&(Ee.length===1||Ee[1]===void 0&&Ee[2]===void 0)?Ye.push(Ee[0]):Ye.push([Ee[0],Ee[1],Ee[2]]);return Ye}function bi(Fe,Ee){var Ke;if(typeof Fe=="string")Ke=Ee(Fe)[0];else if(Array.isArray(Fe)){for(var Ye,On,Gi=Fe.concat(),bn=[],ka=Gi.length;ka--;)typeof(Ye=Gi.shift())=="string"?bn.push(Ee(Ye)[0]):Array.isArray(Ye)&&typeof Ye[0]=="string"&&(On=Ee(Ye[0],Ye[1],Ye[2]),bn.push([On[0],On[1],On[2]]));Ke=bn}return Ke}var tn=!1,wa=!0;if(typeof c=="string")tn=!0;else if(Array.isArray(c)){var Ri=c.concat();yt=[];for(var Vn,hr=Ri.length;hr--;)(typeof(Vn=Ri.shift())!="string"||Array.isArray(Vn)&&typeof Vn[0]!="string")&&(wa=!1);tn=wa}if(tn===!1)throw new Error('Type of text must be string or Array. "'+c+'" is not recognized.');typeof c=="string"&&(c=c.match(/[\r?\n]/)?c.split(/\r\n|\r|\n/g):[c]);var Fn=At/Le.internal.scaleFactor,En=Fn*(Cn-1);switch(q.baseline){case"bottom":I-=En;break;case"top":I+=Fn-En;break;case"hanging":I+=Fn-2*En;break;case"middle":I+=Fn/2-En}if((oe=q.maxWidth||0)>0&&(typeof c=="string"?c=Le.splitTextToSize(c,oe):Object.prototype.toString.call(c)==="[object Array]"&&(c=c.reduce(function(Fe,Ee){return Fe.concat(Le.splitTextToSize(Ee,oe))},[]))),pt={text:c,x:b,y:I,options:q,mutex:{pdfEscape:Gr,activeFontKey:qt,fonts:jt,activeFontSize:At}},be.publish("preProcessText",pt),c=pt.text,It=(q=pt.options).angle,er instanceof Gt==0&&It&&typeof It=="number"){It*=Math.PI/180,q.rotationDirection===0&&(It=-It),M===z&&(It=-It);var Mi=Math.cos(It),xa=Math.sin(It);er=new Gt(Mi,xa,-xa,Mi,0,0)}else It&&It instanceof Gt&&(er=It);M!==z||er||(er=Nn),(Kt=q.charSpace||ji)!==void 0&&(dr+=$(G(Kt))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),(Te=q.horizontalScale)!==void 0&&(dr+=$(100*Te)+" Tz\n"),q.lang;var Ar=-1,to=q.renderingMode!==void 0?q.renderingMode:q.stroke,qi=Le.internal.getCurrentPageInfo().pageContext;switch(to){case 0:case!1:case"fill":Ar=0;break;case 1:case!0:case"stroke":Ar=1;break;case 2:case"fillThenStroke":Ar=2;break;case 3:case"invisible":Ar=3;break;case 4:case"fillAndAddForClipping":Ar=4;break;case 5:case"strokeAndAddPathForClipping":Ar=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":Ar=6;break;case 7:case"addToPathForClipping":Ar=7}var eo=qi.usedRenderingMode!==void 0?qi.usedRenderingMode:-1;Ar!==-1?dr+=Ar+" Tr\n":eo!==-1&&(dr+="0 Tr\n"),Ar!==-1&&(qi.usedRenderingMode=Ar),Bt=q.align||"left";var ar,Vr=At*Cn,_a=Le.internal.pageSize.getWidth(),Aa=jt[qt];Kt=q.charSpace||ji,oe=q.maxWidth||0,ve=Object.assign({autoencode:!0,noBOM:!0},q.flags);var Kn=[],Ui=function(Fe){return Le.getStringUnitWidth(Fe,{font:Aa,charSpace:Kt,fontSize:At,doKerning:!1})*At/Gn};if(Object.prototype.toString.call(c)==="[object Array]"){var kr;yt=Qa(c),Bt!=="left"&&(ar=yt.map(Ui));var Pr,yi=0;if(Bt==="right"){b-=ar[0],c=[],hr=yt.length;for(var pn=0;pn<hr;pn++)pn===0?(Pr=kn(b),kr=Pn(I)):(Pr=G(yi-ar[pn]),kr=-Vr),c.push([yt[pn],Pr,kr]),yi=ar[pn]}else if(Bt==="center"){b-=ar[0]/2,c=[],hr=yt.length;for(var Yn=0;Yn<hr;Yn++)Yn===0?(Pr=kn(b),kr=Pn(I)):(Pr=G((yi-ar[Yn])/2),kr=-Vr),c.push([yt[Yn],Pr,kr]),yi=ar[Yn]}else if(Bt==="left"){c=[],hr=yt.length;for(var La=0;La<hr;La++)c.push(yt[La])}else if(Bt==="justify"&&Aa.encoding==="Identity-H"){c=[],hr=yt.length,oe=oe!==0?oe:_a;for(var Jn=0,Ve=0;Ve<hr;Ve++)if(kr=Ve===0?Pn(I):-Vr,Pr=Ve===0?kn(b):Jn,Ve<hr-1){var ls=G((oe-ar[Ve])/(yt[Ve].split(" ").length-1)),gn=yt[Ve].split(" ");c.push([gn[0]+" ",Pr,kr]),Jn=0;for(var mn=1;mn<gn.length;mn++){var zi=(Ui(gn[mn-1]+" "+gn[mn])-Ui(gn[mn]))*Gn+ls;mn==gn.length-1?c.push([gn[mn],zi,0]):c.push([gn[mn]+" ",zi,0]),Jn-=zi}}else c.push([yt[Ve],Pr,kr]);c.push(["",Jn,0])}else{if(Bt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(c=[],hr=yt.length,oe=oe!==0?oe:_a,Ve=0;Ve<hr;Ve++)kr=Ve===0?Pn(I):-Vr,Pr=Ve===0?kn(b):0,Ve<hr-1?Kn.push($(G((oe-ar[Ve])/(yt[Ve].split(" ").length-1)))):Kn.push(0),c.push([yt[Ve],Pr,kr])}}(typeof q.R2L=="boolean"?q.R2L:ie)===!0&&(c=bi(c,function(Fe,Ee,Ke){return[Fe.split("").reverse().join(""),Ee,Ke]})),pt={text:c,x:b,y:I,options:q,mutex:{pdfEscape:Gr,activeFontKey:qt,fonts:jt,activeFontSize:At}},be.publish("postProcessText",pt),c=pt.text,rr=pt.mutex.isHex||!1;var Na=jt[qt].encoding;Na!=="WinAnsiEncoding"&&Na!=="StandardEncoding"||(c=bi(c,function(Fe,Ee,Ke){return[ss(Fe),Ee,Ke]})),yt=Qa(c),c=[];for(var Hi,Wi,wi,xi=Array.isArray(yt[0])?1:0,In="",Sa=function(Fe,Ee,Ke){var Ye="";return Ke instanceof Gt?(Ke=typeof q.angle=="number"?Ln(Ke,new Gt(1,0,0,1,Fe,Ee)):Ln(new Gt(1,0,0,1,Fe,Ee),Ke),M===z&&(Ke=Ln(new Gt(1,0,0,-1,0,0),Ke)),Ye=Ke.join(" ")+" Tm\n"):Ye=$(Fe)+" "+$(Ee)+" Td\n",Ye},Or=0;Or<yt.length;Or++){switch(In="",xi){case 1:wi=(rr?"<":"(")+yt[Or][0]+(rr?">":")"),Hi=parseFloat(yt[Or][1]),Wi=parseFloat(yt[Or][2]);break;case 0:wi=(rr?"<":"(")+yt[Or]+(rr?">":")"),Hi=kn(b),Wi=Pn(I)}Kn!==void 0&&Kn[Or]!==void 0&&(In=Kn[Or]+" Tw\n"),Or===0?c.push(In+Sa(Hi,Wi,er)+wi):xi===0?c.push(In+wi):xi===1&&c.push(In+Sa(Hi,Wi,er)+wi)}c=xi===0?c.join(" Tj\nT* "):c.join(" Tj\n"),c+=" Tj\n";var vn="BT\n/";return vn+=qt+" "+At+" Tf\n",vn+=$(At*Cn)+" TL\n",vn+=pi+"\n",vn+=dr,vn+=c,j(vn+="ET"),x[qt]=!0,Le};var Jo=g.__private__.clip=g.clip=function(c){return j(c==="evenodd"?"W*":"W"),this};g.clipEvenOdd=function(){return Jo("evenodd")},g.__private__.discardPath=g.discardPath=function(){return j("n"),this};var cn=g.__private__.isValidStyle=function(c){var b=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(c)!==-1&&(b=!0),b};g.__private__.setDefaultPathOperation=g.setDefaultPathOperation=function(c){return cn(c)&&(f=c),this};var Xa=g.__private__.getStyle=g.getStyle=function(c){var b=f;switch(c){case"D":case"S":b="S";break;case"F":b="f";break;case"FD":case"DF":b="B";break;case"f":case"f*":case"B":case"B*":b=c}return b},Za=g.close=function(){return j("h"),this};g.stroke=function(){return j("S"),this},g.fill=function(c){return Hn("f",c),this},g.fillEvenOdd=function(c){return Hn("f*",c),this},g.fillStroke=function(c){return Hn("B",c),this},g.fillStrokeEvenOdd=function(c){return Hn("B*",c),this};var Hn=function(c,b){_e(b)==="object"?Xo(b,c):j(c)},ha=function(c){c===null||M===z&&c===void 0||(c=Xa(c),j(c))};function $o(c,b,I,q,J){var pt=new Qi(b||this.boundingBox,I||this.xStep,q||this.yStep,this.gState,J||this.matrix);pt.stream=this.stream;var yt=c+"$$"+this.cloneIndex+++"$$";return zr(yt,pt),pt}var Xo=function(c,b){var I=fe[c.key],q=Wt[I];if(q instanceof Li)j("q"),j(Zo(b)),q.gState&&g.setGState(q.gState),j(c.matrix.toString()+" cm"),j("/"+I+" sh"),j("Q");else if(q instanceof Qi){var J=new Gt(1,0,0,-1,0,Ir());c.matrix&&(J=J.multiply(c.matrix||Nn),I=$o.call(q,c.key,c.boundingBox,c.xStep,c.yStep,J).id),j("q"),j("/Pattern cs"),j("/"+I+" scn"),q.gState&&g.setGState(q.gState),j(b),j("Q")}},Zo=function(c){switch(c){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},fa=g.moveTo=function(c,b){return j($(G(c))+" "+$(U(b))+" m"),this},ci=g.lineTo=function(c,b){return j($(G(c))+" "+$(U(b))+" l"),this},Wn=g.curveTo=function(c,b,I,q,J,pt){return j([$(G(c)),$(U(b)),$(G(I)),$(U(q)),$(G(J)),$(U(pt)),"c"].join(" ")),this};g.__private__.line=g.line=function(c,b,I,q,J){if(isNaN(c)||isNaN(b)||isNaN(I)||isNaN(q)||!cn(J))throw new Error("Invalid arguments passed to jsPDF.line");return M===W?this.lines([[I-c,q-b]],c,b,[1,1],J||"S"):this.lines([[I-c,q-b]],c,b,[1,1]).stroke()},g.__private__.lines=g.lines=function(c,b,I,q,J,pt){var yt,It,Bt,Kt,oe,ve,Te,er,Le,ir,rr,dr;if(typeof c=="number"&&(dr=I,I=b,b=c,c=dr),q=q||[1,1],pt=pt||!1,isNaN(b)||isNaN(I)||!Array.isArray(c)||!Array.isArray(q)||!cn(J)||typeof pt!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(fa(b,I),yt=q[0],It=q[1],Kt=c.length,ir=b,rr=I,Bt=0;Bt<Kt;Bt++)(oe=c[Bt]).length===2?(ir=oe[0]*yt+ir,rr=oe[1]*It+rr,ci(ir,rr)):(ve=oe[0]*yt+ir,Te=oe[1]*It+rr,er=oe[2]*yt+ir,Le=oe[3]*It+rr,ir=oe[4]*yt+ir,rr=oe[5]*It+rr,Wn(ve,Te,er,Le,ir,rr));return pt&&Za(),ha(J),this},g.path=function(c){for(var b=0;b<c.length;b++){var I=c[b],q=I.c;switch(I.op){case"m":fa(q[0],q[1]);break;case"l":ci(q[0],q[1]);break;case"c":Wn.apply(this,q);break;case"h":Za()}}return this},g.__private__.rect=g.rect=function(c,b,I,q,J){if(isNaN(c)||isNaN(b)||isNaN(I)||isNaN(q)||!cn(J))throw new Error("Invalid arguments passed to jsPDF.rect");return M===W&&(q=-q),j([$(G(c)),$(U(b)),$(G(I)),$(G(q)),"re"].join(" ")),ha(J),this},g.__private__.triangle=g.triangle=function(c,b,I,q,J,pt,yt){if(isNaN(c)||isNaN(b)||isNaN(I)||isNaN(q)||isNaN(J)||isNaN(pt)||!cn(yt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[I-c,q-b],[J-I,pt-q],[c-J,b-pt]],c,b,[1,1],yt,!0),this},g.__private__.roundedRect=g.roundedRect=function(c,b,I,q,J,pt,yt){if(isNaN(c)||isNaN(b)||isNaN(I)||isNaN(q)||isNaN(J)||isNaN(pt)||!cn(yt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var It=4/3*(Math.SQRT2-1);return J=Math.min(J,.5*I),pt=Math.min(pt,.5*q),this.lines([[I-2*J,0],[J*It,0,J,pt-pt*It,J,pt],[0,q-2*pt],[0,pt*It,-J*It,pt,-J,pt],[2*J-I,0],[-J*It,0,-J,-pt*It,-J,-pt],[0,2*pt-q],[0,-pt*It,J*It,-pt,J,-pt]],c+J,b,[1,1],yt,!0),this},g.__private__.ellipse=g.ellipse=function(c,b,I,q,J){if(isNaN(c)||isNaN(b)||isNaN(I)||isNaN(q)||!cn(J))throw new Error("Invalid arguments passed to jsPDF.ellipse");var pt=4/3*(Math.SQRT2-1)*I,yt=4/3*(Math.SQRT2-1)*q;return fa(c+I,b),Wn(c+I,b-yt,c+pt,b-q,c,b-q),Wn(c-pt,b-q,c-I,b-yt,c-I,b),Wn(c-I,b+yt,c-pt,b+q,c,b+q),Wn(c+pt,b+q,c+I,b+yt,c+I,b),ha(J),this},g.__private__.circle=g.circle=function(c,b,I,q){if(isNaN(c)||isNaN(b)||isNaN(I)||!cn(q))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(c,b,I,I,q)},g.setFont=function(c,b,I){return I&&(b=ot(b,I)),qt=Ya(c,b,{disableWarning:!1}),this};var Qo=g.__private__.getFont=g.getFont=function(){return jt[Ya.apply(g,arguments)]};g.__private__.getFontList=g.getFontList=function(){var c,b,I={};for(c in te)if(te.hasOwnProperty(c))for(b in I[c]=[],te[c])te[c].hasOwnProperty(b)&&I[c].push(b);return I},g.addFont=function(c,b,I,q,J){var pt=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&pt.indexOf(arguments[3])!==-1?J=arguments[3]:arguments[3]&&pt.indexOf(arguments[3])==-1&&(I=ot(I,q)),Wa.call(this,c,b,I,J=J||"Identity-H")};var di,ca=r.lineWidth||.200025,$t=g.__private__.getLineWidth=g.getLineWidth=function(){return ca},ts=g.__private__.setLineWidth=g.setLineWidth=function(c){return ca=c,j($(G(c))+" w"),this};g.__private__.setLineDash=Rt.API.setLineDash=Rt.API.setLineDashPattern=function(c,b){if(c=c||[],b=b||0,isNaN(b)||!Array.isArray(c))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return c=c.map(function(I){return $(G(I))}).join(" "),b=$(G(b)),j("["+c+"] "+b+" d"),this};var es=g.__private__.getLineHeight=g.getLineHeight=function(){return At*di};g.__private__.getLineHeight=g.getLineHeight=function(){return At*di};var rs=g.__private__.setLineHeightFactor=g.setLineHeightFactor=function(c){return typeof(c=c||1.15)=="number"&&(di=c),this},ns=g.__private__.getLineHeightFactor=g.getLineHeightFactor=function(){return di};rs(r.lineHeight);var kn=g.__private__.getHorizontalCoordinate=function(c){return G(c)},Pn=g.__private__.getVerticalCoordinate=function(c){return M===z?c:Jt[B].mediaBox.topRightY-Jt[B].mediaBox.bottomLeftY-G(c)},is=g.__private__.getHorizontalCoordinateString=g.getHorizontalCoordinateString=function(c){return $(kn(c))},as=g.__private__.getVerticalCoordinateString=g.getVerticalCoordinateString=function(c){return $(Pn(c))},Di=r.strokeColor||"0 G";g.__private__.getStrokeColor=g.getDrawColor=function(){return hn(Di)},g.__private__.setStrokeColor=g.setDrawColor=function(c,b,I,q){return Di=Xr({ch1:c,ch2:b,ch3:I,ch4:q,pdfColorType:"draw",precision:2}),j(Di),this};var da=r.fillColor||"0 g";g.__private__.getFillColor=g.getFillColor=function(){return hn(da)},g.__private__.setFillColor=g.setFillColor=function(c,b,I,q){return da=Xr({ch1:c,ch2:b,ch3:I,ch4:q,pdfColorType:"fill",precision:2}),j(da),this};var pi=r.textColor||"0 g",Bi=g.__private__.getTextColor=g.getTextColor=function(){return hn(pi)};g.__private__.setTextColor=g.setTextColor=function(c,b,I,q){return pi=Xr({ch1:c,ch2:b,ch3:I,ch4:q,pdfColorType:"text",precision:3}),this};var ji=r.charSpace,os=g.__private__.getCharSpace=g.getCharSpace=function(){return parseFloat(ji||0)};g.__private__.setCharSpace=g.setCharSpace=function(c){if(isNaN(c))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return ji=c,this};var pa=0;g.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},g.__private__.setLineCap=g.setLineCap=function(c){var b=g.CapJoinStyles[c];if(b===void 0)throw new Error("Line cap style of '"+c+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return pa=b,j(b+" J"),this};var ga=0;g.__private__.setLineJoin=g.setLineJoin=function(c){var b=g.CapJoinStyles[c];if(b===void 0)throw new Error("Line join style of '"+c+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return ga=b,j(b+" j"),this},g.__private__.setLineMiterLimit=g.__private__.setMiterLimit=g.setLineMiterLimit=g.setMiterLimit=function(c){if(c=c||0,isNaN(c))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return j($(G(c))+" M"),this},g.GState=Ro,g.setGState=function(c){(c=typeof c=="string"?ee[We[c]]:gi(null,c)).equals(nr)||(j("/"+c.id+" gs"),nr=c)};var gi=function(c,b){if(!c||!We[c]){var I=!1;for(var q in ee)if(ee.hasOwnProperty(q)&&ee[q].equals(b)){I=!0;break}if(I)b=ee[q];else{var J="GS"+(Object.keys(ee).length+1).toString(10);ee[J]=b,b.id=J}return c&&(We[c]=b.id),be.publish("addGState",b),b}};g.addGState=function(c,b){return gi(c,b),this},g.saveGraphicsState=function(){return j("q"),Ne.push({key:qt,size:At,color:pi}),this},g.restoreGraphicsState=function(){j("Q");var c=Ne.pop();return qt=c.key,At=c.size,pi=c.color,nr=null,this},g.setCurrentTransformationMatrix=function(c){return j(c.toString()+" cm"),this},g.comment=function(c){return j("#"+c),this};var mi=function(c,b){var I=c||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return I},set:function(pt){isNaN(pt)||(I=parseFloat(pt))}});var q=b||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return q},set:function(pt){isNaN(pt)||(q=parseFloat(pt))}});var J="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return J},set:function(pt){J=pt.toString()}}),this},ma=function(c,b,I,q){mi.call(this,c,b),this.type="rect";var J=I||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return J},set:function(yt){isNaN(yt)||(J=parseFloat(yt))}});var pt=q||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return pt},set:function(yt){isNaN(yt)||(pt=parseFloat(yt))}}),this},Ti=function(){this.page=Se,this.currentPage=B,this.pages=Lt.slice(0),this.pagesContext=Jt.slice(0),this.x=Ut,this.y=Ce,this.matrix=me,this.width=ba(B),this.height=Ir(B),this.outputDestination=Ct,this.id="",this.objectNumber=-1};Ti.prototype.restore=function(){Se=this.page,B=this.currentPage,Jt=this.pagesContext,Lt=this.pages,Ut=this.x,Ce=this.y,me=this.matrix,ya(B,this.width),dn(B,this.height),Ct=this.outputDestination};var va=function(c,b,I,q,J){un.push(new Ti),Se=B=0,Lt=[],Ut=c,Ce=b,me=J,sa([I,q])};for(var vi in g.beginFormObject=function(c,b,I,q,J){return va(c,b,I,q,J),this},g.endFormObject=function(c){return(function(b){if(An[b])un.pop().restore();else{var I=new Ti,q="Xo"+(Object.keys(vr).length+1).toString(10);I.id=q,An[b]=q,vr[q]=I,be.publish("addFormObject",I),un.pop().restore()}})(c),this},g.doFormObject=function(c,b){var I=vr[An[c]];return j("q"),j(b.toString()+" cm"),j("/"+I.id+" Do"),j("Q"),this},g.getFormObject=function(c){var b=vr[An[c]];return{x:b.x,y:b.y,width:b.width,height:b.height,matrix:b.matrix}},g.save=function(c,b){return c=c||"generated.pdf",(b=b||{}).returnPromise=b.returnPromise||!1,b.returnPromise===!1?(Ai(hi(Sn()),c),typeof Ai.unload=="function"&&Yt.setTimeout&&setTimeout(Ai.unload,911),this):new Promise(function(I,q){try{var J=Ai(hi(Sn()),c);typeof Ai.unload=="function"&&Yt.setTimeout&&setTimeout(Ai.unload,911),I(J)}catch(pt){q(pt.message)}})},Rt.API)Rt.API.hasOwnProperty(vi)&&(vi==="events"&&Rt.API.events.length?(function(c,b){var I,q,J;for(J=b.length-1;J!==-1;J--)I=b[J][0],q=b[J][1],c.subscribe.apply(c,[I].concat(typeof q=="function"?[q]:q))})(be,Rt.API.events):g[vi]=Rt.API[vi]);var ba=g.getPageWidth=function(c){return(Jt[c=c||B].mediaBox.topRightX-Jt[c].mediaBox.bottomLeftX)/Vt},ya=g.setPageWidth=function(c,b){Jt[c].mediaBox.topRightX=b*Vt+Jt[c].mediaBox.bottomLeftX},Ir=g.getPageHeight=function(c){return(Jt[c=c||B].mediaBox.topRightY-Jt[c].mediaBox.bottomLeftY)/Vt},dn=g.setPageHeight=function(c,b){Jt[c].mediaBox.topRightY=b*Vt+Jt[c].mediaBox.bottomLeftY};return g.internal={pdfEscape:Gr,getStyle:Xa,getFont:Qo,getFontSize:kt,getCharSpace:os,getTextColor:Bi,getLineHeight:es,getLineHeightFactor:ns,getLineWidth:$t,write:he,getHorizontalCoordinate:kn,getVerticalCoordinate:Pn,getCoordinateString:is,getVerticalCoordinateString:as,collections:{},newObject:qe,newAdditionalObject:si,newObjectDeferred:ur,newObjectDeferredBegin:cr,getFilters:Zr,putStream:Wr,events:be,scaleFactor:Vt,pageSize:{getWidth:function(){return ba(B)},setWidth:function(c){ya(B,c)},getHeight:function(){return Ir(B)},setHeight:function(c){dn(B,c)}},encryptionOptions:p,encryption:Ge,getEncryptor:function(c){return p!==null?Ge.encryptor(c,0):function(b){return b}},output:Qr,getNumberOfPages:Ka,pages:Lt,out:j,f2:N,f3:O,getPageInfo:ua,getPageInfoByObjId:Ko,getCurrentPageInfo:Yo,getPDFVersion:C,Point:mi,Rectangle:ma,Matrix:Gt,hasHotfix:fi},Object.defineProperty(g.internal.pageSize,"width",{get:function(){return ba(B)},set:function(c){ya(B,c)},enumerable:!0,configurable:!0}),Object.defineProperty(g.internal.pageSize,"height",{get:function(){return Ir(B)},set:function(c){dn(B,c)},enumerable:!0,configurable:!0}),(function(c){for(var b=0,I=Ht.length;b<I;b++){var q=Wa.call(this,c[b][0],c[b][1],c[b][2],Ht[b][3],!0);y===!1&&(x[q]=!0);var J=c[b][0].split("-");Ha({id:q,fontName:J[0],fontStyle:J[1]||""})}be.publish("addFonts",{fonts:jt,dictionary:te})}).call(g,Ht),qt="F1",Ga(i,e),be.publish("initialized"),g}Xi.prototype.lsbFirstWord=function(r){return String.fromCharCode(255&r,r>>8&255,r>>16&255,r>>24&255)},Xi.prototype.toHexString=function(r){return r.split("").map(function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)}).join("")},Xi.prototype.hexToBytes=function(r){for(var t=[],e=0;e<r.length;e+=2)t.push(String.fromCharCode(parseInt(r.substr(e,2),16)));return t.join("")},Xi.prototype.processOwnerPassword=function(r,t){return ll(sl(t).substr(0,5),r)},Xi.prototype.encryptor=function(r,t){var e=sl(this.encryptionKey+String.fromCharCode(255&r,r>>8&255,r>>16&255,255&t,t>>8&255)).substr(0,10);return function(n){return ll(e,n)}},Ro.prototype.equals=function(r){var t,e="id,objectNumber,equals";if(!r||_e(r)!==_e(this))return!1;var n=0;for(t in this)if(!(e.indexOf(t)>=0)){if(this.hasOwnProperty(t)&&!r.hasOwnProperty(t)||this[t]!==r[t])return!1;n++}for(t in r)r.hasOwnProperty(t)&&e.indexOf(t)<0&&n--;return n===0},Rt.API={events:[]},Rt.version="3.0.2";var He=Rt.API,bl=1,Ci=function(r){return r.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Yi=function(r){return r.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Xt=function(r){return r.toFixed(2)},ni=function(r){return r.toFixed(5)};He.__acroform__={};var Er=function(r,t){r.prototype=Object.create(t.prototype),r.prototype.constructor=r},ih=function(r){return r*bl},wn=function(r){var t=new df,e=Et.internal.getHeight(r)||0,n=Et.internal.getWidth(r)||0;return t.BBox=[0,0,Number(Xt(n)),Number(Xt(e))],t},E2=He.__acroform__.setBit=function(r,t){if(r=r||0,t=t||0,isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return r|1<<t},I2=He.__acroform__.clearBit=function(r,t){if(r=r||0,t=t||0,isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return r&~(1<<t)},O2=He.__acroform__.getBit=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return r&1<<t?1:0},Xe=He.__acroform__.getBitForPdf=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return O2(r,t-1)},Ze=He.__acroform__.setBitForPdf=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return E2(r,t-1)},Qe=He.__acroform__.clearBitForPdf=function(r,t){if(isNaN(r)||isNaN(t))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return I2(r,t-1)},D2=He.__acroform__.calculateCoordinates=function(r,t){var e=t.internal.getHorizontalCoordinate,n=t.internal.getVerticalCoordinate,i=r[0],a=r[1],u=r[2],l=r[3],h={};return h.lowerLeft_X=e(i)||0,h.lowerLeft_Y=n(a+l)||0,h.upperRight_X=e(i+u)||0,h.upperRight_Y=n(a)||0,[Number(Xt(h.lowerLeft_X)),Number(Xt(h.lowerLeft_Y)),Number(Xt(h.upperRight_X)),Number(Xt(h.upperRight_Y))]},B2=function(r){if(r.appearanceStreamContent)return r.appearanceStreamContent;if(r.V||r.DV){var t=[],e=r._V||r.DV,n=ul(r,e),i=r.scope.internal.getFont(r.fontName,r.fontStyle).id;t.push("/Tx BMC"),t.push("q"),t.push("BT"),t.push(r.scope.__private__.encodeColorString(r.color)),t.push("/"+i+" "+Xt(n.fontSize)+" Tf"),t.push("1 0 0 1 0 0 Tm"),t.push(n.text),t.push("ET"),t.push("Q"),t.push("EMC");var a=wn(r);return a.scope=r.scope,a.stream=t.join("\n"),a}},ul=function(r,t){var e=r.fontSize===0?r.maxFontSize:r.fontSize,n={text:"",fontSize:""},i=(t=(t=t.substr(0,1)=="("?t.substr(1):t).substr(t.length-1)==")"?t.substr(0,t.length-1):t).split(" ");i=r.multiline?i.map(function(N){return N.split("\n")}):i.map(function(N){return[N]});var a=e,u=Et.internal.getHeight(r)||0;u=u<0?-u:u;var l=Et.internal.getWidth(r)||0;l=l<0?-l:l;var h=function(N,O,G){if(N+1<i.length){var U=O+" "+i[N+1][0];return ko(U,r,G).width<=l-4}return!1};a++;t:for(;a>0;){t="",a--;var f,p,y=ko("3",r,a).height,x=r.multiline?u-a:(u-y)/2,g=x+=2,P=0,C=0,D=0;if(a<=0){t="(...) Tj\n",t+="% Width of Text: "+ko(t,r,a=12).width+", FieldWidth:"+l+"\n";break}for(var S="",W=0,z=0;z<i.length;z++)if(i.hasOwnProperty(z)){var M=!1;if(i[z].length!==1&&D!==i[z].length-1){if((y+2)*(W+2)+2>u)continue t;S+=i[z][D],M=!0,C=z,z--}else{S=(S+=i[z][D]+" ").substr(S.length-1)==" "?S.substr(0,S.length-1):S;var at=parseInt(z),vt=h(at,S,a),ot=z>=i.length-1;if(vt&&!ot){S+=" ",D=0;continue}if(vt||ot){if(ot)C=at;else if(r.multiline&&(y+2)*(W+2)+2>u)continue t}else{if(!r.multiline||(y+2)*(W+2)+2>u)continue t;C=at}}for(var $="",R=P;R<=C;R++){var et=i[R];if(r.multiline){if(R===C){$+=et[D]+" ",D=(D+1)%et.length;continue}if(R===P){$+=et[et.length-1]+" ";continue}}$+=et[0]+" "}switch($=$.substr($.length-1)==" "?$.substr(0,$.length-1):$,p=ko($,r,a).width,r.textAlign){case"right":f=l-p-2;break;case"center":f=(l-p)/2;break;default:f=2}t+=Xt(f)+" "+Xt(g)+" Td\n",t+="("+Ci($)+") Tj\n",t+=-Xt(f)+" 0 Td\n",g=-(a+2),p=0,P=M?C:C+1,W++,S=""}break}return n.text=t,n.fontSize=a,n},ko=function(r,t,e){var n=t.scope.internal.getFont(t.fontName,t.fontStyle),i=t.scope.getStringUnitWidth(r,{font:n,fontSize:parseFloat(e),charSpace:0})*parseFloat(e);return{height:t.scope.getStringUnitWidth("3",{font:n,fontSize:parseFloat(e),charSpace:0})*parseFloat(e)*1.5,width:i}},j2={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},T2=function(r,t){var e={type:"reference",object:r};t.internal.getPageInfo(r.page).pageContext.annotations.find(function(n){return n.type===e.type&&n.object===e.object})===void 0&&t.internal.getPageInfo(r.page).pageContext.annotations.push(e)},R2=function(r,t){if(t.scope=r,r.internal!==void 0&&(r.internal.acroformPlugin===void 0||r.internal.acroformPlugin.isInitialized===!1)){if(sn.FieldNum=0,r.internal.acroformPlugin=JSON.parse(JSON.stringify(j2)),r.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");bl=r.internal.scaleFactor,r.internal.acroformPlugin.acroFormDictionaryRoot=new pf,r.internal.acroformPlugin.acroFormDictionaryRoot.scope=r,r.internal.acroformPlugin.acroFormDictionaryRoot._eventID=r.internal.events.subscribe("postPutResources",function(){(function(e){e.internal.events.unsubscribe(e.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete e.internal.acroformPlugin.acroFormDictionaryRoot._eventID,e.internal.acroformPlugin.printedOut=!0})(r)}),r.internal.events.subscribe("buildDocument",function(){(function(e){e.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var n=e.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var i in n)if(n.hasOwnProperty(i)){var a=n[i];a.objId=void 0,a.hasAnnotation&&T2(a,e)}})(r)}),r.internal.events.subscribe("putCatalog",function(){(function(e){if(e.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");e.internal.write("/AcroForm "+e.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(r)}),r.internal.events.subscribe("postPutPages",function(e){(function(n,i){var a=!n;for(var u in n||(i.internal.newObjectDeferredBegin(i.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),i.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),n=n||i.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(n.hasOwnProperty(u)){var l=n[u],h=[],f=l.Rect;if(l.Rect&&(l.Rect=D2(l.Rect,i)),i.internal.newObjectDeferredBegin(l.objId,!0),l.DA=Et.createDefaultAppearanceStream(l),_e(l)==="object"&&typeof l.getKeyValueListForStream=="function"&&(h=l.getKeyValueListForStream()),l.Rect=f,l.hasAppearanceStream&&!l.appearanceStreamContent){var p=B2(l);h.push({key:"AP",value:"<</N "+p+">>"}),i.internal.acroformPlugin.xForms.push(p)}if(l.appearanceStreamContent){var y="";for(var x in l.appearanceStreamContent)if(l.appearanceStreamContent.hasOwnProperty(x)){var g=l.appearanceStreamContent[x];if(y+="/"+x+" ",y+="<<",Object.keys(g).length>=1||Array.isArray(g)){for(var u in g)if(g.hasOwnProperty(u)){var P=g[u];typeof P=="function"&&(P=P.call(i,l)),y+="/"+u+" "+P+" ",i.internal.acroformPlugin.xForms.indexOf(P)>=0||i.internal.acroformPlugin.xForms.push(P)}}else typeof(P=g)=="function"&&(P=P.call(i,l)),y+="/"+u+" "+P,i.internal.acroformPlugin.xForms.indexOf(P)>=0||i.internal.acroformPlugin.xForms.push(P);y+=">>"}h.push({key:"AP",value:"<<\n"+y+">>"})}i.internal.putStream({additionalKeyValues:h,objectId:l.objId}),i.internal.out("endobj")}a&&(function(C,D){for(var S in C)if(C.hasOwnProperty(S)){var W=S,z=C[S];D.internal.newObjectDeferredBegin(z.objId,!0),_e(z)==="object"&&typeof z.putStream=="function"&&z.putStream(),delete C[W]}})(i.internal.acroformPlugin.xForms,i)})(e,r)}),r.internal.acroformPlugin.isInitialized=!0}},cf=He.__acroform__.arrayToPdfArray=function(r,t,e){var n=function(u){return u};if(Array.isArray(r)){for(var i="[",a=0;a<r.length;a++)switch(a!==0&&(i+=" "),_e(r[a])){case"boolean":case"number":case"object":i+=r[a].toString();break;case"string":r[a].substr(0,1)!=="/"?(t!==void 0&&e&&(n=e.internal.getEncryptor(t)),i+="("+Ci(n(r[a].toString()))+")"):i+=r[a].toString()}return i+"]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},Hs=function(r,t,e){var n=function(i){return i};return t!==void 0&&e&&(n=e.internal.getEncryptor(t)),(r=r||"").toString(),"("+Ci(n(r))+")"},xn=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(r){this._objId=r}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};xn.prototype.toString=function(){return this.objId+" 0 R"},xn.prototype.putStream=function(){var r=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:r,objectId:this.objId}),this.scope.internal.out("endobj")},xn.prototype.getKeyValueListForStream=function(){var r=[],t=Object.getOwnPropertyNames(this).filter(function(a){return a!="content"&&a!="appearanceStreamContent"&&a!="scope"&&a!="objId"&&a.substring(0,1)!="_"});for(var e in t)if(Object.getOwnPropertyDescriptor(this,t[e]).configurable===!1){var n=t[e],i=this[n];i&&(Array.isArray(i)?r.push({key:n,value:cf(i,this.objId,this.scope)}):i instanceof xn?(i.scope=this.scope,r.push({key:n,value:i.objId+" 0 R"})):typeof i!="function"&&r.push({key:n,value:i}))}return r};var df=function(){xn.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var r,t=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){r=e.trim()},get:function(){return r||null}})};Er(df,xn);var pf=function(){xn.call(this);var r,t=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return t.length>0?t:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return t}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(r){var e=function(n){return n};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+Ci(e(r))+")"}},set:function(e){r=e}})};Er(pf,xn);var sn=function r(){xn.call(this);var t=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return t},set:function(S){if(isNaN(S))throw new Error('Invalid value "'+S+'" for attribute F supplied.');t=S}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Xe(t,3)},set:function(S){S?this.F=Ze(t,3):this.F=Qe(t,3)}});var e=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return e},set:function(S){if(isNaN(S))throw new Error('Invalid value "'+S+'" for attribute Ff supplied.');e=S}});var n=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(n.length!==0)return n},set:function(S){n=S!==void 0?S:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[0])?0:n[0]},set:function(S){n[0]=S}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[1])?0:n[1]},set:function(S){n[1]=S}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[2])?0:n[2]},set:function(S){n[2]=S}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!n||isNaN(n[3])?0:n[3]},set:function(S){n[3]=S}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(S){switch(S){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=S;break;default:throw new Error('Invalid value "'+S+'" for attribute FT supplied.')}}});var a=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!a||a.length<1){if(this instanceof Mo)return;a="FieldObject"+r.FieldNum++}var S=function(W){return W};return this.scope&&(S=this.scope.internal.getEncryptor(this.objId)),"("+Ci(S(a))+")"},set:function(S){a=S.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return a},set:function(S){a=S}});var u="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return u},set:function(S){u=S}});var l="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return l},set:function(S){l=S}});var h=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return h},set:function(S){h=S}});var f=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return f===void 0?50/bl:f},set:function(S){f=S}});var p="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return p},set:function(S){p=S}});var y="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!y||this instanceof Mo||this instanceof Ni))return Hs(y,this.objId,this.scope)},set:function(S){S=S.toString(),y=S}});var x=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(x)return this instanceof fr==0?Hs(x,this.objId,this.scope):x},set:function(S){S=S.toString(),x=this instanceof fr==0?S.substr(0,1)==="("?Yi(S.substr(1,S.length-2)):Yi(S):S}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof fr==1?Yi(x.substr(1,x.length-1)):x},set:function(S){S=S.toString(),x=this instanceof fr==1?"/"+S:S}});var g=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(g)return g},set:function(S){this.V=S}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(g)return this instanceof fr==0?Hs(g,this.objId,this.scope):g},set:function(S){S=S.toString(),g=this instanceof fr==0?S.substr(0,1)==="("?Yi(S.substr(1,S.length-2)):Yi(S):S}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof fr==1?Yi(g.substr(1,g.length-1)):g},set:function(S){S=S.toString(),g=this instanceof fr==1?"/"+S:S}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var P,C=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return C},set:function(S){S=!!S,C=S}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(P)return P},set:function(S){P=S}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,1)},set:function(S){S?this.Ff=Ze(this.Ff,1):this.Ff=Qe(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,2)},set:function(S){S?this.Ff=Ze(this.Ff,2):this.Ff=Qe(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,3)},set:function(S){S?this.Ff=Ze(this.Ff,3):this.Ff=Qe(this.Ff,3)}});var D=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(D!==null)return D},set:function(S){if([0,1,2].indexOf(S)===-1)throw new Error('Invalid value "'+S+'" for attribute Q supplied.');D=S}}),Object.defineProperty(this,"textAlign",{get:function(){var S;switch(D){case 0:default:S="left";break;case 1:S="center";break;case 2:S="right"}return S},configurable:!0,enumerable:!0,set:function(S){switch(S){case"right":case 2:D=2;break;case"center":case 1:D=1;break;default:D=0}}})};Er(sn,xn);var ta=function(){sn.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var r=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return r},set:function(e){r=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return r},set:function(e){r=e}});var t=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return cf(t,this.objId,this.scope)},set:function(e){var n,i;i=[],typeof(n=e)=="string"&&(i=(function(a,u,l){l||(l=1);for(var h,f=[];h=u.exec(a);)f.push(h[l]);return f})(n,/\((.*?)\)/g)),t=i}}),this.getOptions=function(){return t},this.setOptions=function(e){t=e,this.sort&&t.sort()},this.addOption=function(e){e=(e=e||"").toString(),t.push(e),this.sort&&t.sort()},this.removeOption=function(e,n){for(n=n||!1,e=(e=e||"").toString();t.indexOf(e)!==-1&&(t.splice(t.indexOf(e),1),n!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,18)},set:function(e){e?this.Ff=Ze(this.Ff,18):this.Ff=Qe(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,19)},set:function(e){this.combo===!0&&(e?this.Ff=Ze(this.Ff,19):this.Ff=Qe(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,20)},set:function(e){e?(this.Ff=Ze(this.Ff,20),t.sort()):this.Ff=Qe(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,22)},set:function(e){e?this.Ff=Ze(this.Ff,22):this.Ff=Qe(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,23)},set:function(e){e?this.Ff=Ze(this.Ff,23):this.Ff=Qe(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,27)},set:function(e){e?this.Ff=Ze(this.Ff,27):this.Ff=Qe(this.Ff,27)}}),this.hasAppearanceStream=!1};Er(ta,sn);var ea=function(){ta.call(this),this.fontName="helvetica",this.combo=!1};Er(ea,ta);var ra=function(){ea.call(this),this.combo=!0};Er(ra,ea);var Io=function(){ra.call(this),this.edit=!0};Er(Io,ra);var fr=function(){sn.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,15)},set:function(e){e?this.Ff=Ze(this.Ff,15):this.Ff=Qe(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,16)},set:function(e){e?this.Ff=Ze(this.Ff,16):this.Ff=Qe(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,17)},set:function(e){e?this.Ff=Ze(this.Ff,17):this.Ff=Qe(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,26)},set:function(e){e?this.Ff=Ze(this.Ff,26):this.Ff=Qe(this.Ff,26)}});var r,t={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var e=function(a){return a};if(this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),Object.keys(t).length!==0){var n,i=[];for(n in i.push("<<"),t)i.push("/"+n+" ("+Ci(e(t[n]))+")");return i.push(">>"),i.join("\n")}},set:function(e){_e(e)==="object"&&(t=e)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return t.CA||""},set:function(e){typeof e=="string"&&(t.CA=e)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(e){r=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(e){r="/"+e}})};Er(fr,sn);var Oo=function(){fr.call(this),this.pushButton=!0};Er(Oo,fr);var na=function(){fr.call(this),this.radio=!0,this.pushButton=!1;var r=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return r},set:function(t){r=t!==void 0?t:[]}})};Er(na,fr);var Mo=function(){var r,t;sn.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return r},set:function(i){r=i}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return t},set:function(i){t=i}});var e,n={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var i=function(l){return l};this.scope&&(i=this.scope.internal.getEncryptor(this.objId));var a,u=[];for(a in u.push("<<"),n)u.push("/"+a+" ("+Ci(i(n[a]))+")");return u.push(">>"),u.join("\n")},set:function(i){_e(i)==="object"&&(n=i)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return n.CA||""},set:function(i){typeof i=="string"&&(n.CA=i)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return e},set:function(i){e=i}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return e.substr(1,e.length-1)},set:function(i){e="/"+i}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Et.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};Er(Mo,sn),na.prototype.setAppearance=function(r){if(!("createAppearanceStream"in r)||!("getCA"in r))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var t in this.Kids)if(this.Kids.hasOwnProperty(t)){var e=this.Kids[t];e.appearanceStreamContent=r.createAppearanceStream(e.optionName),e.caption=r.getCA()}},na.prototype.createOption=function(r){var t=new Mo;return t.Parent=this,t.optionName=r,this.Kids.push(t),M2.call(this.scope,t),t};var Do=function(){fr.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Et.CheckBox.createAppearanceStream()};Er(Do,fr);var Ni=function(){sn.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,13)},set:function(t){t?this.Ff=Ze(this.Ff,13):this.Ff=Qe(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,21)},set:function(t){t?this.Ff=Ze(this.Ff,21):this.Ff=Qe(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,23)},set:function(t){t?this.Ff=Ze(this.Ff,23):this.Ff=Qe(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,24)},set:function(t){t?this.Ff=Ze(this.Ff,24):this.Ff=Qe(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,25)},set:function(t){t?this.Ff=Ze(this.Ff,25):this.Ff=Qe(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,26)},set:function(t){t?this.Ff=Ze(this.Ff,26):this.Ff=Qe(this.Ff,26)}});var r=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return r},set:function(t){Number.isInteger(t)&&(r=t)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};Er(Ni,sn);var Bo=function(){Ni.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Xe(this.Ff,14)},set:function(r){r?this.Ff=Ze(this.Ff,14):this.Ff=Qe(this.Ff,14)}}),this.password=!0};Er(Bo,Ni);var Et={CheckBox:{createAppearanceStream:function(){return{N:{On:Et.CheckBox.YesNormal},D:{On:Et.CheckBox.YesPushDown,Off:Et.CheckBox.OffPushDown}}},YesPushDown:function(r){var t=wn(r);t.scope=r.scope;var e=[],n=r.scope.internal.getFont(r.fontName,r.fontStyle).id,i=r.scope.__private__.encodeColorString(r.color),a=ul(r,r.caption);return e.push("0.749023 g"),e.push("0 0 "+Xt(Et.internal.getWidth(r))+" "+Xt(Et.internal.getHeight(r))+" re"),e.push("f"),e.push("BMC"),e.push("q"),e.push("0 0 1 rg"),e.push("/"+n+" "+Xt(a.fontSize)+" Tf "+i),e.push("BT"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC"),t.stream=e.join("\n"),t},YesNormal:function(r){var t=wn(r);t.scope=r.scope;var e=r.scope.internal.getFont(r.fontName,r.fontStyle).id,n=r.scope.__private__.encodeColorString(r.color),i=[],a=Et.internal.getHeight(r),u=Et.internal.getWidth(r),l=ul(r,r.caption);return i.push("1 g"),i.push("0 0 "+Xt(u)+" "+Xt(a)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+Xt(u-1)+" "+Xt(a-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+e+" "+Xt(l.fontSize)+" Tf "+n),i.push(l.text),i.push("ET"),i.push("Q"),t.stream=i.join("\n"),t},OffPushDown:function(r){var t=wn(r);t.scope=r.scope;var e=[];return e.push("0.749023 g"),e.push("0 0 "+Xt(Et.internal.getWidth(r))+" "+Xt(Et.internal.getHeight(r))+" re"),e.push("f"),t.stream=e.join("\n"),t}},RadioButton:{Circle:{createAppearanceStream:function(r){var t={D:{Off:Et.RadioButton.Circle.OffPushDown},N:{}};return t.N[r]=Et.RadioButton.Circle.YesNormal,t.D[r]=Et.RadioButton.Circle.YesPushDown,t},getCA:function(){return"l"},YesNormal:function(r){var t=wn(r);t.scope=r.scope;var e=[],n=Et.internal.getWidth(r)<=Et.internal.getHeight(r)?Et.internal.getWidth(r)/4:Et.internal.getHeight(r)/4;n=Number((.9*n).toFixed(5));var i=Et.internal.Bezier_C,a=Number((n*i).toFixed(5));return e.push("q"),e.push("1 0 0 1 "+ni(Et.internal.getWidth(r)/2)+" "+ni(Et.internal.getHeight(r)/2)+" cm"),e.push(n+" 0 m"),e.push(n+" "+a+" "+a+" "+n+" 0 "+n+" c"),e.push("-"+a+" "+n+" -"+n+" "+a+" -"+n+" 0 c"),e.push("-"+n+" -"+a+" -"+a+" -"+n+" 0 -"+n+" c"),e.push(a+" -"+n+" "+n+" -"+a+" "+n+" 0 c"),e.push("f"),e.push("Q"),t.stream=e.join("\n"),t},YesPushDown:function(r){var t=wn(r);t.scope=r.scope;var e=[],n=Et.internal.getWidth(r)<=Et.internal.getHeight(r)?Et.internal.getWidth(r)/4:Et.internal.getHeight(r)/4;n=Number((.9*n).toFixed(5));var i=Number((2*n).toFixed(5)),a=Number((i*Et.internal.Bezier_C).toFixed(5)),u=Number((n*Et.internal.Bezier_C).toFixed(5));return e.push("0.749023 g"),e.push("q"),e.push("1 0 0 1 "+ni(Et.internal.getWidth(r)/2)+" "+ni(Et.internal.getHeight(r)/2)+" cm"),e.push(i+" 0 m"),e.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),e.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),e.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),e.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),e.push("f"),e.push("Q"),e.push("0 g"),e.push("q"),e.push("1 0 0 1 "+ni(Et.internal.getWidth(r)/2)+" "+ni(Et.internal.getHeight(r)/2)+" cm"),e.push(n+" 0 m"),e.push(n+" "+u+" "+u+" "+n+" 0 "+n+" c"),e.push("-"+u+" "+n+" -"+n+" "+u+" -"+n+" 0 c"),e.push("-"+n+" -"+u+" -"+u+" -"+n+" 0 -"+n+" c"),e.push(u+" -"+n+" "+n+" -"+u+" "+n+" 0 c"),e.push("f"),e.push("Q"),t.stream=e.join("\n"),t},OffPushDown:function(r){var t=wn(r);t.scope=r.scope;var e=[],n=Et.internal.getWidth(r)<=Et.internal.getHeight(r)?Et.internal.getWidth(r)/4:Et.internal.getHeight(r)/4;n=Number((.9*n).toFixed(5));var i=Number((2*n).toFixed(5)),a=Number((i*Et.internal.Bezier_C).toFixed(5));return e.push("0.749023 g"),e.push("q"),e.push("1 0 0 1 "+ni(Et.internal.getWidth(r)/2)+" "+ni(Et.internal.getHeight(r)/2)+" cm"),e.push(i+" 0 m"),e.push(i+" "+a+" "+a+" "+i+" 0 "+i+" c"),e.push("-"+a+" "+i+" -"+i+" "+a+" -"+i+" 0 c"),e.push("-"+i+" -"+a+" -"+a+" -"+i+" 0 -"+i+" c"),e.push(a+" -"+i+" "+i+" -"+a+" "+i+" 0 c"),e.push("f"),e.push("Q"),t.stream=e.join("\n"),t}},Cross:{createAppearanceStream:function(r){var t={D:{Off:Et.RadioButton.Cross.OffPushDown},N:{}};return t.N[r]=Et.RadioButton.Cross.YesNormal,t.D[r]=Et.RadioButton.Cross.YesPushDown,t},getCA:function(){return"8"},YesNormal:function(r){var t=wn(r);t.scope=r.scope;var e=[],n=Et.internal.calculateCross(r);return e.push("q"),e.push("1 1 "+Xt(Et.internal.getWidth(r)-2)+" "+Xt(Et.internal.getHeight(r)-2)+" re"),e.push("W"),e.push("n"),e.push(Xt(n.x1.x)+" "+Xt(n.x1.y)+" m"),e.push(Xt(n.x2.x)+" "+Xt(n.x2.y)+" l"),e.push(Xt(n.x4.x)+" "+Xt(n.x4.y)+" m"),e.push(Xt(n.x3.x)+" "+Xt(n.x3.y)+" l"),e.push("s"),e.push("Q"),t.stream=e.join("\n"),t},YesPushDown:function(r){var t=wn(r);t.scope=r.scope;var e=Et.internal.calculateCross(r),n=[];return n.push("0.749023 g"),n.push("0 0 "+Xt(Et.internal.getWidth(r))+" "+Xt(Et.internal.getHeight(r))+" re"),n.push("f"),n.push("q"),n.push("1 1 "+Xt(Et.internal.getWidth(r)-2)+" "+Xt(Et.internal.getHeight(r)-2)+" re"),n.push("W"),n.push("n"),n.push(Xt(e.x1.x)+" "+Xt(e.x1.y)+" m"),n.push(Xt(e.x2.x)+" "+Xt(e.x2.y)+" l"),n.push(Xt(e.x4.x)+" "+Xt(e.x4.y)+" m"),n.push(Xt(e.x3.x)+" "+Xt(e.x3.y)+" l"),n.push("s"),n.push("Q"),t.stream=n.join("\n"),t},OffPushDown:function(r){var t=wn(r);t.scope=r.scope;var e=[];return e.push("0.749023 g"),e.push("0 0 "+Xt(Et.internal.getWidth(r))+" "+Xt(Et.internal.getHeight(r))+" re"),e.push("f"),t.stream=e.join("\n"),t}}},createDefaultAppearanceStream:function(r){var t=r.scope.internal.getFont(r.fontName,r.fontStyle).id,e=r.scope.__private__.encodeColorString(r.color);return"/"+t+" "+r.fontSize+" Tf "+e}};Et.internal={Bezier_C:.551915024494,calculateCross:function(r){var t=Et.internal.getWidth(r),e=Et.internal.getHeight(r),n=Math.min(t,e);return{x1:{x:(t-n)/2,y:(e-n)/2+n},x2:{x:(t-n)/2+n,y:(e-n)/2},x3:{x:(t-n)/2,y:(e-n)/2},x4:{x:(t-n)/2+n,y:(e-n)/2+n}}}},Et.internal.getWidth=function(r){var t=0;return _e(r)==="object"&&(t=ih(r.Rect[2])),t},Et.internal.getHeight=function(r){var t=0;return _e(r)==="object"&&(t=ih(r.Rect[3])),t};var M2=He.addField=function(r){if(R2(this,r),!(r instanceof sn))throw new Error("Invalid argument passed to jsPDF.addField.");var t;return(t=r).scope.internal.acroformPlugin.printedOut&&(t.scope.internal.acroformPlugin.printedOut=!1,t.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),t.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t),r.page=r.scope.internal.getCurrentPageInfo().pageNumber,this};He.AcroFormChoiceField=ta,He.AcroFormListBox=ea,He.AcroFormComboBox=ra,He.AcroFormEditBox=Io,He.AcroFormButton=fr,He.AcroFormPushButton=Oo,He.AcroFormRadioButton=na,He.AcroFormCheckBox=Do,He.AcroFormTextField=Ni,He.AcroFormPasswordField=Bo,He.AcroFormAppearance=Et,He.AcroForm={ChoiceField:ta,ListBox:ea,ComboBox:ra,EditBox:Io,Button:fr,PushButton:Oo,RadioButton:na,CheckBox:Do,TextField:Ni,PasswordField:Bo,Appearance:Et},Rt.AcroForm={ChoiceField:ta,ListBox:ea,ComboBox:ra,EditBox:Io,Button:fr,PushButton:Oo,RadioButton:na,CheckBox:Do,TextField:Ni,PasswordField:Bo,Appearance:Et};Rt.AcroForm;function gf(r){return r.reduce(function(t,e,n){return t[e]=n,t},{})}(function(r){var t="addImage_";r.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},i=r.__addimage__.getImageFileTypeByImageData=function(N,O){var G,U,nt,ht,dt,rt=e;if((O=O||e)==="RGBA"||N.data!==void 0&&N.data instanceof Uint8ClampedArray&&"height"in N&&"width"in N)return"RGBA";if(vt(N))for(dt in n)for(nt=n[dt],G=0;G<nt.length;G+=1){for(ht=!0,U=0;U<nt[G].length;U+=1)if(nt[G][U]!==void 0&&nt[G][U]!==N[U]){ht=!1;break}if(ht===!0){rt=dt;break}}else for(dt in n)for(nt=n[dt],G=0;G<nt.length;G+=1){for(ht=!0,U=0;U<nt[G].length;U+=1)if(nt[G][U]!==void 0&&nt[G][U]!==N.charCodeAt(U)){ht=!1;break}if(ht===!0){rt=dt;break}}return rt===e&&O!==e&&(rt=O),rt},a=function N(O){for(var G=this.internal.write,U=this.internal.putStream,nt=(0,this.internal.getFilters)();nt.indexOf("FlateEncode")!==-1;)nt.splice(nt.indexOf("FlateEncode"),1);O.objectId=this.internal.newObject();var ht=[];if(ht.push({key:"Type",value:"/XObject"}),ht.push({key:"Subtype",value:"/Image"}),ht.push({key:"Width",value:O.width}),ht.push({key:"Height",value:O.height}),O.colorSpace===S.INDEXED?ht.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(O.palette.length/3-1)+" "+("sMask"in O&&O.sMask!==void 0?O.objectId+2:O.objectId+1)+" 0 R]"}):(ht.push({key:"ColorSpace",value:"/"+O.colorSpace}),O.colorSpace===S.DEVICE_CMYK&&ht.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),ht.push({key:"BitsPerComponent",value:O.bitsPerComponent}),"decodeParameters"in O&&O.decodeParameters!==void 0&&ht.push({key:"DecodeParms",value:"<<"+O.decodeParameters+">>"}),"transparency"in O&&Array.isArray(O.transparency)){for(var dt="",rt=0,ft=O.transparency.length;rt<ft;rt++)dt+=O.transparency[rt]+" "+O.transparency[rt]+" ";ht.push({key:"Mask",value:"["+dt+"]"})}O.sMask!==void 0&&ht.push({key:"SMask",value:O.objectId+1+" 0 R"});var Nt=O.filter!==void 0?["/"+O.filter]:void 0;if(U({data:O.data,additionalKeyValues:ht,alreadyAppliedFilters:Nt,objectId:O.objectId}),G("endobj"),"sMask"in O&&O.sMask!==void 0){var wt=(O.predictor!=null?"/Predictor "+O.predictor:"")+" /Colors 1 /BitsPerComponent 8 /Columns "+O.width,A={width:O.width,height:O.height,colorSpace:"DeviceGray",bitsPerComponent:O.bitsPerComponent,decodeParameters:wt,data:O.sMask};"filter"in O&&(A.filter=O.filter),N.call(this,A)}if(O.colorSpace===S.INDEXED){var B=this.internal.newObject();U({data:$(new Uint8Array(O.palette)),objectId:B}),G("endobj")}},u=function(){var N=this.internal.collections[t+"images"];for(var O in N)a.call(this,N[O])},l=function(){var N,O=this.internal.collections[t+"images"],G=this.internal.write;for(var U in O)G("/I"+(N=O[U]).index,N.objectId,"0","R")},h=function(){this.internal.collections[t+"images"]||(this.internal.collections[t+"images"]={},this.internal.events.subscribe("putResources",u),this.internal.events.subscribe("putXobjectDict",l))},f=function(){var N=this.internal.collections[t+"images"];return h.call(this),N},p=function(){return Object.keys(this.internal.collections[t+"images"]).length},y=function(N){return typeof r["process"+N.toUpperCase()]=="function"},x=function(N){return _e(N)==="object"&&N.nodeType===1},g=function(N,O){if(N.nodeName==="IMG"&&N.hasAttribute("src")){var G=""+N.getAttribute("src");if(G.indexOf("data:image/")===0)return Eo(unescape(G).split("base64,").pop());var U=r.loadFile(G,!0);if(U!==void 0)return U}if(N.nodeName==="CANVAS"){if(N.width===0||N.height===0)throw new Error("Given canvas must have data. Canvas width: "+N.width+", height: "+N.height);var nt;switch(O){case"PNG":nt="image/png";break;case"WEBP":nt="image/webp";break;default:nt="image/jpeg"}return Eo(N.toDataURL(nt,1).split("base64,").pop())}},P=function(N){var O=this.internal.collections[t+"images"];if(O){for(var G in O)if(N===O[G].alias)return O[G]}},C=function(N,O,G){return N||O||(N=-96,O=-96),N<0&&(N=-1*G.width*72/N/this.internal.scaleFactor),O<0&&(O=-1*G.height*72/O/this.internal.scaleFactor),N===0&&(N=O*G.width/G.height),O===0&&(O=N*G.height/G.width),[N,O]},D=function(N,O,G,U,nt,ht){var dt=C.call(this,G,U,nt),rt=this.internal.getCoordinateString,ft=this.internal.getVerticalCoordinateString,Nt=f.call(this);if(G=dt[0],U=dt[1],Nt[nt.index]=nt,ht){ht*=Math.PI/180;var wt=Math.cos(ht),A=Math.sin(ht),B=function(V){return V.toFixed(4)},T=[B(wt),B(A),B(-1*A),B(wt),0,0,"cm"]}this.internal.write("q"),ht?(this.internal.write([1,"0","0",1,rt(N),ft(O+U),"cm"].join(" ")),this.internal.write(T.join(" ")),this.internal.write([rt(G),"0","0",rt(U),"0","0","cm"].join(" "))):this.internal.write([rt(G),"0","0",rt(U),rt(N),ft(O+U),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+nt.index+" Do"),this.internal.write("Q")},S=r.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};r.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var W=r.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},z=r.__addimage__.sHashCode=function(N){var O,G,U=0;if(typeof N=="string")for(G=N.length,O=0;O<G;O++)U=(U<<5)-U+N.charCodeAt(O),U|=0;else if(vt(N))for(G=N.byteLength/2,O=0;O<G;O++)U=(U<<5)-U+N[O],U|=0;return U},M=r.__addimage__.validateStringAsBase64=function(N){(N=N||"").toString().trim();var O=!0;return N.length===0&&(O=!1),N.length%4!=0&&(O=!1),/^[A-Za-z0-9+/]+$/.test(N.substr(0,N.length-2))===!1&&(O=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(N.substr(-2))===!1&&(O=!1),O},at=r.__addimage__.extractImageFromDataUrl=function(N){if(N==null||!(N=N.trim()).startsWith("data:"))return null;var O=N.indexOf(",");return O<0?null:N.substring(0,O).trim().endsWith("base64")?N.substring(O+1):null};r.__addimage__.isArrayBuffer=function(N){return N instanceof ArrayBuffer};var vt=r.__addimage__.isArrayBufferView=function(N){return N instanceof Int8Array||N instanceof Uint8Array||N instanceof Uint8ClampedArray||N instanceof Int16Array||N instanceof Uint16Array||N instanceof Int32Array||N instanceof Uint32Array||N instanceof Float32Array||N instanceof Float64Array},ot=r.__addimage__.binaryStringToUint8Array=function(N){for(var O=N.length,G=new Uint8Array(O),U=0;U<O;U++)G[U]=N.charCodeAt(U);return G},$=r.__addimage__.arrayBufferToBinaryString=function(N){for(var O="",G=vt(N)?N:new Uint8Array(N),U=0;U<G.length;U+=8192)O+=String.fromCharCode.apply(null,G.subarray(U,U+8192));return O};r.addImage=function(){var N,O,G,U,nt,ht,dt,rt,ft;if(typeof arguments[1]=="number"?(O=e,G=arguments[1],U=arguments[2],nt=arguments[3],ht=arguments[4],dt=arguments[5],rt=arguments[6],ft=arguments[7]):(O=arguments[1],G=arguments[2],U=arguments[3],nt=arguments[4],ht=arguments[5],dt=arguments[6],rt=arguments[7],ft=arguments[8]),_e(N=arguments[0])==="object"&&!x(N)&&"imageData"in N){var Nt=N;N=Nt.imageData,O=Nt.format||O||e,G=Nt.x||G||0,U=Nt.y||U||0,nt=Nt.w||Nt.width||nt,ht=Nt.h||Nt.height||ht,dt=Nt.alias||dt,rt=Nt.compression||rt,ft=Nt.rotation||Nt.angle||ft}var wt=this.internal.getFilters();if(rt===void 0&&wt.indexOf("FlateEncode")!==-1&&(rt="SLOW"),isNaN(G)||isNaN(U))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var A=R.call(this,N,O,dt,rt);return D.call(this,G,U,nt,ht,A,ft),this};var R=function(N,O,G,U){var nt,ht,dt;if(typeof N=="string"&&i(N)===e){N=unescape(N);var rt=et(N,!1);(rt!==""||(rt=r.loadFile(N,!0))!==void 0)&&(N=rt)}if(x(N)&&(N=g(N,O)),O=i(N,O),!y(O))throw new Error("addImage does not support files of type '"+O+"', please ensure that a plugin for '"+O+"' support is added.");if(((dt=G)==null||dt.length===0)&&(G=(function(ft){return typeof ft=="string"||vt(ft)?z(ft):vt(ft.data)?z(ft.data):null})(N)),(nt=P.call(this,G))||(N instanceof Uint8Array||O==="RGBA"||(ht=N,N=ot(N)),nt=this["process"+O.toUpperCase()](N,p.call(this),G,(function(ft){return ft&&typeof ft=="string"&&(ft=ft.toUpperCase()),ft in r.image_compression?ft:W.NONE})(U),ht)),!nt)throw new Error("An unknown error occurred whilst processing the image.");return nt},et=r.__addimage__.convertBase64ToBinaryString=function(N,O){O=typeof O!="boolean"||O;var G,U="";if(typeof N=="string"){var nt;G=(nt=at(N))!==null&&nt!==void 0?nt:N;try{U=Eo(G)}catch(ht){if(O)throw M(G)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+ht.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return U};r.getImageProperties=function(N){var O,G,U="";if(x(N)&&(N=g(N)),typeof N=="string"&&i(N)===e&&((U=et(N,!1))===""&&(U=r.loadFile(N)||""),N=U),G=i(N),!y(G))throw new Error("addImage does not support files of type '"+G+"', please ensure that a plugin for '"+G+"' support is added.");if(N instanceof Uint8Array||(N=ot(N)),!(O=this["process"+G.toUpperCase()](N)))throw new Error("An unknown error occurred whilst processing the image");return O.fileType=G,O}})(Rt.API),(function(r){var t=function(e){if(e!==void 0&&e!="")return!0};Rt.API.events.push(["addPage",function(e){this.internal.getPageInfo(e.pageNumber).pageContext.annotations=[]}]),r.events.push(["putPage",function(e){for(var n,i,a,u=this.internal.getCoordinateString,l=this.internal.getVerticalCoordinateString,h=this.internal.getPageInfoByObjId(e.objId),f=e.pageContext.annotations,p=!1,y=0;y<f.length&&!p;y++)switch((n=f[y]).type){case"link":(t(n.options.url)||t(n.options.pageNumber))&&(p=!0);break;case"reference":case"text":case"freetext":p=!0}if(p!=0){this.internal.write("/Annots [");for(var x=0;x<f.length;x++){n=f[x];var g=this.internal.pdfEscape,P=this.internal.getEncryptor(e.objId);switch(n.type){case"reference":this.internal.write(" "+n.object.objId+" 0 R ");break;case"text":var C=this.internal.newAdditionalObject(),D=this.internal.newAdditionalObject(),S=this.internal.getEncryptor(C.objId),W=n.title||"Note";a="<</Type /Annot /Subtype /Text "+(i="/Rect ["+u(n.bounds.x)+" "+l(n.bounds.y+n.bounds.h)+" "+u(n.bounds.x+n.bounds.w)+" "+l(n.bounds.y)+"] ")+"/Contents ("+g(S(n.contents))+")",a+=" /Popup "+D.objId+" 0 R",a+=" /P "+h.objId+" 0 R",a+=" /T ("+g(S(W))+") >>",C.content=a;var z=C.objId+" 0 R";a="<</Type /Annot /Subtype /Popup "+(i="/Rect ["+u(n.bounds.x+30)+" "+l(n.bounds.y+n.bounds.h)+" "+u(n.bounds.x+n.bounds.w+30)+" "+l(n.bounds.y)+"] ")+" /Parent "+z,n.open&&(a+=" /Open true"),a+=" >>",D.content=a,this.internal.write(C.objId,"0 R",D.objId,"0 R");break;case"freetext":i="/Rect ["+u(n.bounds.x)+" "+l(n.bounds.y)+" "+u(n.bounds.x+n.bounds.w)+" "+l(n.bounds.y+n.bounds.h)+"] ";var M=n.color||"#000000";a="<</Type /Annot /Subtype /FreeText "+i+"/Contents ("+g(P(n.contents))+")",a+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+M+")",a+=" /Border [0 0 0]",a+=" >>",this.internal.write(a);break;case"link":if(n.options.name){var at=this.annotations._nameMap[n.options.name];n.options.pageNumber=at.page,n.options.top=at.y}else n.options.top||(n.options.top=0);if(i="/Rect ["+n.finalBounds.x+" "+n.finalBounds.y+" "+n.finalBounds.w+" "+n.finalBounds.h+"] ",a="",n.options.url)a="<</Type /Annot /Subtype /Link "+i+"/Border [0 0 0] /A <</S /URI /URI ("+g(P(n.options.url))+") >>";else if(n.options.pageNumber)switch(a="<</Type /Annot /Subtype /Link "+i+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(n.options.pageNumber).objId+" 0 R",n.options.magFactor=n.options.magFactor||"XYZ",n.options.magFactor){case"Fit":a+=" /Fit]";break;case"FitH":a+=" /FitH "+n.options.top+"]";break;case"FitV":n.options.left=n.options.left||0,a+=" /FitV "+n.options.left+"]";break;default:var vt=l(n.options.top);n.options.left=n.options.left||0,n.options.zoom===void 0&&(n.options.zoom=0),a+=" /XYZ "+n.options.left+" "+vt+" "+n.options.zoom+"]"}a!=""&&(a+=" >>",this.internal.write(a))}}this.internal.write("]")}}]),r.createAnnotation=function(e){var n=this.internal.getCurrentPageInfo();switch(e.type){case"link":this.link(e.bounds.x,e.bounds.y,e.bounds.w,e.bounds.h,e);break;case"text":case"freetext":n.pageContext.annotations.push(e)}},r.link=function(e,n,i,a,u){var l=this.internal.getCurrentPageInfo(),h=this.internal.getCoordinateString,f=this.internal.getVerticalCoordinateString;l.pageContext.annotations.push({finalBounds:{x:h(e),y:f(n),w:h(e+i),h:f(n+a)},options:u,type:"link"})},r.textWithLink=function(e,n,i,a){var u,l,h=this.getTextWidth(e),f=this.internal.getLineHeight()/this.internal.scaleFactor;if(a.maxWidth!==void 0){l=a.maxWidth;var p=this.splitTextToSize(e,l).length;u=Math.ceil(f*p)}else l=h,u=f;return this.text(e,n,i,a),i+=.2*f,a.align==="center"&&(n-=h/2),a.align==="right"&&(n-=h),this.link(n,i-f,l,u,a),h},r.getTextWidth=function(e){var n=this.internal.getFontSize();return this.getStringUnitWidth(e)*n/this.internal.scaleFactor}})(Rt.API),(function(r){var t={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},e={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},n={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},i=[1570,1571,1573,1575];r.__arabicParser__={};var a=r.__arabicParser__.isInArabicSubstitutionA=function(C){return t[C.charCodeAt(0)]!==void 0},u=r.__arabicParser__.isArabicLetter=function(C){return typeof C=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(C)},l=r.__arabicParser__.isArabicEndLetter=function(C){return u(C)&&a(C)&&t[C.charCodeAt(0)].length<=2},h=r.__arabicParser__.isArabicAlfLetter=function(C){return u(C)&&i.indexOf(C.charCodeAt(0))>=0};r.__arabicParser__.arabicLetterHasIsolatedForm=function(C){return u(C)&&a(C)&&t[C.charCodeAt(0)].length>=1};var f=r.__arabicParser__.arabicLetterHasFinalForm=function(C){return u(C)&&a(C)&&t[C.charCodeAt(0)].length>=2};r.__arabicParser__.arabicLetterHasInitialForm=function(C){return u(C)&&a(C)&&t[C.charCodeAt(0)].length>=3};var p=r.__arabicParser__.arabicLetterHasMedialForm=function(C){return u(C)&&a(C)&&t[C.charCodeAt(0)].length==4},y=r.__arabicParser__.resolveLigatures=function(C){var D=0,S=e,W="",z=0;for(D=0;D<C.length;D+=1)S[C.charCodeAt(D)]!==void 0?(z++,typeof(S=S[C.charCodeAt(D)])=="number"&&(W+=String.fromCharCode(S),S=e,z=0),D===C.length-1&&(S=e,W+=C.charAt(D-(z-1)),D-=z-1,z=0)):(S=e,W+=C.charAt(D-z),D-=z,z=0);return W};r.__arabicParser__.isArabicDiacritic=function(C){return C!==void 0&&n[C.charCodeAt(0)]!==void 0};var x=r.__arabicParser__.getCorrectForm=function(C,D,S){return u(C)?a(C)===!1?-1:!f(C)||!u(D)&&!u(S)||!u(S)&&l(D)||l(C)&&!u(D)||l(C)&&h(D)||l(C)&&l(D)?0:p(C)&&u(D)&&!l(D)&&u(S)&&f(S)?3:l(C)||!u(S)?1:2:-1},g=function(C){var D=0,S=0,W=0,z="",M="",at="",vt=(C=C||"").split("\\s+"),ot=[];for(D=0;D<vt.length;D+=1){for(ot.push(""),S=0;S<vt[D].length;S+=1)z=vt[D][S],M=vt[D][S-1],at=vt[D][S+1],u(z)?(W=x(z,M,at),ot[D]+=W!==-1?String.fromCharCode(t[z.charCodeAt(0)][W]):z):ot[D]+=z;ot[D]=y(ot[D])}return ot.join(" ")},P=r.__arabicParser__.processArabic=r.processArabic=function(){var C,D=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,S=[];if(Array.isArray(D)){var W=0;for(S=[],W=0;W<D.length;W+=1)Array.isArray(D[W])?S.push([g(D[W][0]),D[W][1],D[W][2]]):S.push([g(D[W])]);C=S}else C=g(D);return typeof arguments[0]=="string"?C:(arguments[0].text=C,arguments[0])};r.events.push(["preProcessText",P])})(Rt.API),Rt.API.autoPrint=function(r){var t;return(r=r||{}).variant=r.variant||"non-conform",r.variant==="javascript"?this.addJS("print({});"):(this.internal.events.subscribe("postPutResources",function(){t=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+t+" 0 R")})),this},(function(r){var t=function(){var e=void 0;Object.defineProperty(this,"pdf",{get:function(){return e},set:function(l){e=l}});var n=150;Object.defineProperty(this,"width",{get:function(){return n},set:function(l){n=isNaN(l)||Number.isInteger(l)===!1||l<0?150:l,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=n+1)}});var i=300;Object.defineProperty(this,"height",{get:function(){return i},set:function(l){i=isNaN(l)||Number.isInteger(l)===!1||l<0?300:l,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=i+1)}});var a=[];Object.defineProperty(this,"childNodes",{get:function(){return a},set:function(l){a=l}});var u={};Object.defineProperty(this,"style",{get:function(){return u},set:function(l){u=l}}),Object.defineProperty(this,"parentNode",{})};t.prototype.getContext=function(e,n){var i;if((e=e||"2d")!=="2d")return null;for(i in n)this.pdf.context2d.hasOwnProperty(i)&&(this.pdf.context2d[i]=n[i]);return this.pdf.context2d._canvas=this,this.pdf.context2d},t.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},r.events.push(["initialized",function(){this.canvas=new t,this.canvas.pdf=this}])})(Rt.API),(function(r){var t={left:0,top:0,bottom:0,right:0},e=!1,n=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},t),this.internal.__cell__.margins.width=this.getPageWidth(),i.call(this))},i=function(){this.internal.__cell__.lastCell=new a,this.internal.__cell__.pages=1},a=function(){var h=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return h},set:function(C){h=C}});var f=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return f},set:function(C){f=C}});var p=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return p},set:function(C){p=C}});var y=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return y},set:function(C){y=C}});var x=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return x},set:function(C){x=C}});var g=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return g},set:function(C){g=C}});var P=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return P},set:function(C){P=C}}),this};a.prototype.clone=function(){return new a(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},a.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},r.setHeaderFunction=function(h){return n.call(this),this.internal.__cell__.headerFunction=typeof h=="function"?h:void 0,this},r.getTextDimensions=function(h,f){n.call(this);var p=(f=f||{}).fontSize||this.getFontSize(),y=f.font||this.getFont(),x=f.scaleFactor||this.internal.scaleFactor,g=0,P=0,C=0,D=this;if(!Array.isArray(h)&&typeof h!="string"){if(typeof h!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");h=String(h)}var S=f.maxWidth;S>0?typeof h=="string"?h=this.splitTextToSize(h,S):Object.prototype.toString.call(h)==="[object Array]"&&(h=h.reduce(function(z,M){return z.concat(D.splitTextToSize(M,S))},[])):h=Array.isArray(h)?h:[h];for(var W=0;W<h.length;W++)g<(C=this.getStringUnitWidth(h[W],{font:y})*p)&&(g=C);return g!==0&&(P=h.length),{w:g/=x,h:Math.max((P*p*this.getLineHeightFactor()-p*(this.getLineHeightFactor()-1))/x,0)}},r.cellAddPage=function(){n.call(this),this.addPage();var h=this.internal.__cell__.margins||t;return this.internal.__cell__.lastCell=new a(h.left,h.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var u=r.cell=function(){var h;h=arguments[0]instanceof a?arguments[0]:new a(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),n.call(this);var f=this.internal.__cell__.lastCell,p=this.internal.__cell__.padding,y=this.internal.__cell__.margins||t,x=this.internal.__cell__.tableHeaderRow,g=this.internal.__cell__.printHeaders;return f.lineNumber!==void 0&&(f.lineNumber===h.lineNumber?(h.x=(f.x||0)+(f.width||0),h.y=f.y||0):f.y+f.height+h.height+y.bottom>this.getPageHeight()?(this.cellAddPage(),h.y=y.top,g&&x&&(this.printHeaderRow(h.lineNumber,!0),h.y+=x[0].height)):h.y=f.y+f.height||h.y),h.text[0]!==void 0&&(this.rect(h.x,h.y,h.width,h.height,e===!0?"FD":void 0),h.align==="right"?this.text(h.text,h.x+h.width-p,h.y+p,{align:"right",baseline:"top"}):h.align==="center"?this.text(h.text,h.x+h.width/2,h.y+p,{align:"center",baseline:"top",maxWidth:h.width-p-p}):this.text(h.text,h.x+p,h.y+p,{align:"left",baseline:"top",maxWidth:h.width-p-p})),this.internal.__cell__.lastCell=h,this};r.table=function(h,f,p,y,x){if(n.call(this),!p)throw new Error("No data for PDF table.");var g,P,C,D,S=[],W=[],z=[],M={},at={},vt=[],ot=[],$=(x=x||{}).autoSize||!1,R=x.printHeaders!==!1,et=x.css&&x.css["font-size"]!==void 0?16*x.css["font-size"]:x.fontSize||12,N=x.margins||Object.assign({width:this.getPageWidth()},t),O=typeof x.padding=="number"?x.padding:3,G=x.headerBackgroundColor||"#c8c8c8",U=x.headerTextColor||"#000";if(i.call(this),this.internal.__cell__.printHeaders=R,this.internal.__cell__.margins=N,this.internal.__cell__.table_font_size=et,this.internal.__cell__.padding=O,this.internal.__cell__.headerBackgroundColor=G,this.internal.__cell__.headerTextColor=U,this.setFontSize(et),y==null)W=S=Object.keys(p[0]),z=S.map(function(){return"left"});else if(Array.isArray(y)&&_e(y[0])==="object")for(S=y.map(function(Nt){return Nt.name}),W=y.map(function(Nt){return Nt.prompt||Nt.name||""}),z=y.map(function(Nt){return Nt.align||"left"}),g=0;g<y.length;g+=1)at[y[g].name]=.7499990551181103*y[g].width;else Array.isArray(y)&&typeof y[0]=="string"&&(W=S=y,z=S.map(function(){return"left"}));if($||Array.isArray(y)&&typeof y[0]=="string")for(g=0;g<S.length;g+=1){for(M[D=S[g]]=p.map(function(Nt){return Nt[D]}),this.setFont(void 0,"bold"),vt.push(this.getTextDimensions(W[g],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),P=M[D],this.setFont(void 0,"normal"),C=0;C<P.length;C+=1)vt.push(this.getTextDimensions(P[C],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);at[D]=Math.max.apply(null,vt)+O+O,vt=[]}if(R){var nt={};for(g=0;g<S.length;g+=1)nt[S[g]]={},nt[S[g]].text=W[g],nt[S[g]].align=z[g];var ht=l.call(this,nt,at);ot=S.map(function(Nt){return new a(h,f,at[Nt],ht,nt[Nt].text,void 0,nt[Nt].align)}),this.setTableHeaderRow(ot),this.printHeaderRow(1,!1)}var dt=y.reduce(function(Nt,wt){return Nt[wt.name]=wt.align,Nt},{});for(g=0;g<p.length;g+=1){"rowStart"in x&&x.rowStart instanceof Function&&x.rowStart({row:g,data:p[g]},this);var rt=l.call(this,p[g],at);for(C=0;C<S.length;C+=1){var ft=p[g][S[C]];"cellStart"in x&&x.cellStart instanceof Function&&x.cellStart({row:g,col:C,data:ft},this),u.call(this,new a(h,f,at[S[C]],rt,ft,g+2,dt[S[C]]))}}return this.internal.__cell__.table_x=h,this.internal.__cell__.table_y=f,this};var l=function(h,f){var p=this.internal.__cell__.padding,y=this.internal.__cell__.table_font_size,x=this.internal.scaleFactor;return Object.keys(h).map(function(g){var P=h[g];return this.splitTextToSize(P.hasOwnProperty("text")?P.text:P,f[g]-p-p)},this).map(function(g){return this.getLineHeightFactor()*g.length*y/x+p+p},this).reduce(function(g,P){return Math.max(g,P)},0)};r.setTableHeaderRow=function(h){n.call(this),this.internal.__cell__.tableHeaderRow=h},r.printHeaderRow=function(h,f){if(n.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var p;if(e=!0,typeof this.internal.__cell__.headerFunction=="function"){var y=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new a(y[0],y[1],y[2],y[3],void 0,-1)}this.setFont(void 0,"bold");for(var x=[],g=0;g<this.internal.__cell__.tableHeaderRow.length;g+=1){p=this.internal.__cell__.tableHeaderRow[g].clone(),f&&(p.y=this.internal.__cell__.margins.top||0,x.push(p)),p.lineNumber=h;var P=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),u.call(this,p),this.setTextColor(P)}x.length>0&&this.setTableHeaderRow(x),this.setFont(void 0,"normal"),e=!1}})(Rt.API);var mf={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},vf=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],hl=gf(vf),bf=[100,200,300,400,500,600,700,800,900],q2=gf(bf);function Ws(r){var t=r.family.replace(/"|'/g,"").toLowerCase(),e=(function(a){return mf[a=a||"normal"]?a:"normal"})(r.style),n=(function(a){return a?typeof a=="number"?a>=100&&a<=900&&a%100==0?a:400:/^\d00$/.test(a)?parseInt(a):a==="bold"?700:400:400})(r.weight),i=(function(a){return typeof hl[a=a||"normal"]=="number"?a:"normal"})(r.stretch);return{family:t,style:e,weight:n,stretch:i,src:r.src||[],ref:r.ref||{name:t,style:[i,e,n].join(" ")}}}function ah(r,t,e,n){var i;for(i=e;i>=0&&i<t.length;i+=n)if(r[t[i]])return r[t[i]];for(i=e;i>=0&&i<t.length;i-=n)if(r[t[i]])return r[t[i]]}var U2={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},oh={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function sh(r){return[r.stretch,r.style,r.weight,r.family].join(" ")}function lh(r){return r.trimLeft()}function z2(r,t){for(var e=0;e<r.length;){if(r.charAt(e)===t)return[r.substring(0,e),r.substring(e+1)];e+=1}return null}function H2(r){var t=r.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return t===null?null:[t[0],r.substring(t[0].length)]}var Po,uh,hh,Ji,Co,fh,ch,dh,Gs=["times"];function ph(r,t,e,n){var i=4,a=mh;switch(n){case Rt.API.image_compression.FAST:i=1,a=gh;break;case Rt.API.image_compression.MEDIUM:i=6,a=vh;break;case Rt.API.image_compression.SLOW:i=9,a=bh}r=(function(l,h,f,p){for(var y,x=l.length/h,g=new Uint8Array(l.length+x),P=[W2,gh,mh,vh,bh],C=0;C<x;C+=1){var D=C*h,S=l.subarray(D,D+h);if(p)g.set(p(S,f,y),D+C);else{for(var W=P.length,z=[],M=0;M<W;M+=1)z[M]=P[M](S,f,y);var at=V2(z.concat());g.set(z[at],D+C)}y=S}return g})(r,t,e,a);var u=nl(r,{level:i});return Rt.API.__addimage__.arrayBufferToBinaryString(u)}function W2(r){var t=Array.apply([],r);return t.unshift(0),t}function gh(r,t){var e=r.length,n=[];n[0]=1;for(var i=0;i<e;i+=1){var a=r[i-t]||0;n[i+1]=r[i]-a+256&255}return n}function mh(r,t,e){var n=r.length,i=[];i[0]=2;for(var a=0;a<n;a+=1){var u=e&&e[a]||0;i[a+1]=r[a]-u+256&255}return i}function vh(r,t,e){var n=r.length,i=[];i[0]=3;for(var a=0;a<n;a+=1){var u=r[a-t]||0,l=e&&e[a]||0;i[a+1]=r[a]+256-(u+l>>>1)&255}return i}function bh(r,t,e){var n=r.length,i=[];i[0]=4;for(var a=0;a<n;a+=1){var u=G2(r[a-t]||0,e&&e[a]||0,e&&e[a-t]||0);i[a+1]=r[a]-u+256&255}return i}function G2(r,t,e){if(r===t&&t===e)return r;var n=Math.abs(t-e),i=Math.abs(r-e),a=Math.abs(r+t-e-e);return n<=i&&n<=a?r:i<=a?t:e}function V2(r){var t=r.map(function(e){return e.reduce(function(n,i){return n+Math.abs(i)},0)});return t.indexOf(Math.min.apply(null,t))}function Vs(r,t,e){var n=t*e,i=Math.floor(n/8),a=16-(n-8*i+e),u=(1<<e)-1;return yf(r,i)>>a&u}function yh(r,t,e,n){var i=e*n,a=Math.floor(i/8),u=16-(i-8*a+n),l=(1<<n)-1,h=(t&l)<<u;(function(f,p,y){if(p+1<f.byteLength)f.setUint16(p,y,!1);else{var x=y>>8&255;f.setUint8(p,x)}})(r,a,yf(r,a)&~(l<<u)&65535|h)}function yf(r,t){return t+1<r.byteLength?r.getUint16(t,!1):r.getUint8(t)<<8}function K2(r){var t=0;if(r[t++]!==71||r[t++]!==73||r[t++]!==70||r[t++]!==56||(r[t++]+1&253)!=56||r[t++]!==97)throw new Error("Invalid GIF 87a/89a header.");var e=r[t++]|r[t++]<<8,n=r[t++]|r[t++]<<8,i=r[t++],a=i>>7,u=1<<1+(7&i);r[t++],r[t++];var l=null,h=null;a&&(l=t,h=u,t+=3*u);var f=!0,p=[],y=0,x=null,g=0,P=null;for(this.width=e,this.height=n;f&&t<r.length;)switch(r[t++]){case 33:switch(r[t++]){case 255:if(r[t]!==11||r[t+1]==78&&r[t+2]==69&&r[t+3]==84&&r[t+4]==83&&r[t+5]==67&&r[t+6]==65&&r[t+7]==80&&r[t+8]==69&&r[t+9]==50&&r[t+10]==46&&r[t+11]==48&&r[t+12]==3&&r[t+13]==1&&r[t+16]==0)t+=14,P=r[t++]|r[t++]<<8,t++;else for(t+=12;;){if(!((N=r[t++])>=0))throw Error("Invalid block size");if(N===0)break;t+=N}break;case 249:if(r[t++]!==4||r[t+4]!==0)throw new Error("Invalid graphics extension block.");var C=r[t++];y=r[t++]|r[t++]<<8,x=r[t++],1&C||(x=null),g=C>>2&7,t++;break;case 254:for(;;){if(!((N=r[t++])>=0))throw Error("Invalid block size");if(N===0)break;t+=N}break;default:throw new Error("Unknown graphic control label: 0x"+r[t-1].toString(16))}break;case 44:var D=r[t++]|r[t++]<<8,S=r[t++]|r[t++]<<8,W=r[t++]|r[t++]<<8,z=r[t++]|r[t++]<<8,M=r[t++],at=M>>6&1,vt=1<<1+(7&M),ot=l,$=h,R=!1;M>>7&&(R=!0,ot=t,$=vt,t+=3*vt);var et=t;for(t++;;){var N;if(!((N=r[t++])>=0))throw Error("Invalid block size");if(N===0)break;t+=N}p.push({x:D,y:S,width:W,height:z,has_local_palette:R,palette_offset:ot,palette_size:$,data_offset:et,data_length:t-et,transparent_index:x,interlaced:!!at,delay:y,disposal:g});break;case 59:f=!1;break;default:throw new Error("Unknown gif block: 0x"+r[t-1].toString(16))}this.numFrames=function(){return p.length},this.loopCount=function(){return P},this.frameInfo=function(O){if(O<0||O>=p.length)throw new Error("Frame index out of range.");return p[O]},this.decodeAndBlitFrameBGRA=function(O,G){var U=this.frameInfo(O),nt=U.width*U.height,ht=new Uint8Array(nt);wh(r,U.data_offset,ht,nt);var dt=U.palette_offset,rt=U.transparent_index;rt===null&&(rt=256);var ft=U.width,Nt=e-ft,wt=ft,A=4*(U.y*e+U.x),B=4*((U.y+U.height)*e+U.x),T=A,V=4*Nt;U.interlaced===!0&&(V+=4*e*7);for(var Y=8,Z=0,lt=ht.length;Z<lt;++Z){var it=ht[Z];if(wt===0&&(wt=ft,(T+=V)>=B&&(V=4*Nt+4*e*(Y-1),T=A+(ft+Nt)*(Y<<1),Y>>=1)),it===rt)T+=4;else{var mt=r[dt+3*it],Lt=r[dt+3*it+1],Pt=r[dt+3*it+2];G[T++]=Pt,G[T++]=Lt,G[T++]=mt,G[T++]=255}--wt}},this.decodeAndBlitFrameRGBA=function(O,G){var U=this.frameInfo(O),nt=U.width*U.height,ht=new Uint8Array(nt);wh(r,U.data_offset,ht,nt);var dt=U.palette_offset,rt=U.transparent_index;rt===null&&(rt=256);var ft=U.width,Nt=e-ft,wt=ft,A=4*(U.y*e+U.x),B=4*((U.y+U.height)*e+U.x),T=A,V=4*Nt;U.interlaced===!0&&(V+=4*e*7);for(var Y=8,Z=0,lt=ht.length;Z<lt;++Z){var it=ht[Z];if(wt===0&&(wt=ft,(T+=V)>=B&&(V=4*Nt+4*e*(Y-1),T=A+(ft+Nt)*(Y<<1),Y>>=1)),it===rt)T+=4;else{var mt=r[dt+3*it],Lt=r[dt+3*it+1],Pt=r[dt+3*it+2];G[T++]=mt,G[T++]=Lt,G[T++]=Pt,G[T++]=255}--wt}}}function wh(r,t,e,n){for(var i=r[t++],a=1<<i,u=a+1,l=u+1,h=i+1,f=(1<<h)-1,p=0,y=0,x=0,g=r[t++],P=new Int32Array(4096),C=null;;){for(;p<16&&g!==0;)y|=r[t++]<<p,p+=8,g===1?g=r[t++]:--g;if(p<h)break;var D=y&f;if(y>>=h,p-=h,D!==a){if(D===u)break;for(var S=D<l?D:C,W=0,z=S;z>a;)z=P[z]>>8,++W;var M=z;if(x+W+(S!==D?1:0)>n)return void Pe.log("Warning, gif stream longer than expected.");e[x++]=M;var at=x+=W;for(S!==D&&(e[x++]=M),z=S;W--;)z=P[z],e[--at]=255&z,z>>=8;C!==null&&l<4096&&(P[l++]=C<<8|M,l>=f+1&&h<12&&(++h,f=f<<1|1)),C=D}else l=u+1,f=(1<<(h=i+1))-1,C=null}return x!==n&&Pe.log("Warning, gif stream shorter than expected."),e}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function Ks(r){var t,e,n,i,a,u=Math.floor,l=new Array(64),h=new Array(64),f=new Array(64),p=new Array(64),y=new Array(65535),x=new Array(65535),g=new Array(64),P=new Array(64),C=[],D=0,S=7,W=new Array(64),z=new Array(64),M=new Array(64),at=new Array(256),vt=new Array(2048),ot=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],$=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],R=[0,1,2,3,4,5,6,7,8,9,10,11],et=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],N=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],O=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],G=[0,1,2,3,4,5,6,7,8,9,10,11],U=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],nt=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function ht(A,B){for(var T=0,V=0,Y=new Array,Z=1;Z<=16;Z++){for(var lt=1;lt<=A[Z];lt++)Y[B[V]]=[],Y[B[V]][0]=T,Y[B[V]][1]=Z,V++,T++;T*=2}return Y}function dt(A){for(var B=A[0],T=A[1]-1;T>=0;)B&1<<T&&(D|=1<<S),T--,--S<0&&(D==255?(rt(255),rt(0)):rt(D),S=7,D=0)}function rt(A){C.push(A)}function ft(A){rt(A>>8&255),rt(255&A)}function Nt(A,B,T,V,Y){for(var Z,lt=Y[0],it=Y[240],mt=(function(At,Mt){var kt,ie,zt,ge,Zt,Qt,Ae,ae,qt,Vt,Ut=0;for(qt=0;qt<8;++qt){kt=At[Ut],ie=At[Ut+1],zt=At[Ut+2],ge=At[Ut+3],Zt=At[Ut+4],Qt=At[Ut+5],Ae=At[Ut+6];var Ce=kt+(ae=At[Ut+7]),me=kt-ae,jt=ie+Ae,te=ie-Ae,Ne=zt+Qt,Wt=zt-Qt,fe=ge+Zt,ee=ge-Zt,We=Ce+fe,nr=Ce-fe,Se=jt+Ne,Jt=jt-Ne;At[Ut]=We+Se,At[Ut+4]=We-Se;var be=.707106781*(Jt+nr);At[Ut+2]=nr+be,At[Ut+6]=nr-be;var oi=.382683433*((We=ee+Wt)-(Jt=te+me)),vr=.5411961*We+oi,An=1.306562965*Jt+oi,un=.707106781*(Se=Wt+te),Gt=me+un,Ln=me-un;At[Ut+5]=Ln+vr,At[Ut+3]=Ln-vr,At[Ut+1]=Gt+An,At[Ut+7]=Gt-An,Ut+=8}for(Ut=0,qt=0;qt<8;++qt){kt=At[Ut],ie=At[Ut+8],zt=At[Ut+16],ge=At[Ut+24],Zt=At[Ut+32],Qt=At[Ut+40],Ae=At[Ut+48];var Nn=kt+(ae=At[Ut+56]),zr=kt-ae,Hr=ie+Ae,qe=ie-Ae,ur=zt+Qt,cr=zt-Qt,si=ge+Zt,zn=ge-Zt,$r=Nn+si,hn=Nn-si,Xr=Hr+ur,Zr=Hr-ur;At[Ut]=$r+Xr,At[Ut+32]=$r-Xr;var Wr=.707106781*(Zr+hn);At[Ut+16]=hn+Wr,At[Ut+48]=hn-Wr;var li=.382683433*(($r=zn+cr)-(Zr=qe+zr)),fn=.5411961*$r+li,ui=1.306562965*Zr+li,Fi=.707106781*(Xr=cr+qe),Ei=zr+Fi,Ii=zr-Fi;At[Ut+40]=Ii+fn,At[Ut+24]=Ii-fn,At[Ut+8]=Ei+ui,At[Ut+56]=Ei-ui,Ut++}for(qt=0;qt<64;++qt)Vt=At[qt]*Mt[qt],g[qt]=Vt>0?Vt+.5|0:Vt-.5|0;return g})(A,B),Lt=0;Lt<64;++Lt)P[ot[Lt]]=mt[Lt];var Pt=P[0]-T;T=P[0],Pt==0?dt(V[0]):(dt(V[x[Z=32767+Pt]]),dt(y[Z]));for(var Ct=63;Ct>0&&P[Ct]==0;)Ct--;if(Ct==0)return dt(lt),T;for(var st,j=1;j<=Ct;){for(var he=j;P[j]==0&&j<=Ct;)++j;var ne=j-he;if(ne>=16){st=ne>>4;for(var Ht=1;Ht<=st;++Ht)dt(it);ne&=15}Z=32767+P[j],dt(Y[(ne<<4)+x[Z]]),dt(y[Z]),j++}return Ct!=63&&dt(lt),T}function wt(A){A=Math.min(Math.max(A,1),100),a!=A&&((function(B){for(var T=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],V=0;V<64;V++){var Y=u((T[V]*B+50)/100);Y=Math.min(Math.max(Y,1),255),l[ot[V]]=Y}for(var Z=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],lt=0;lt<64;lt++){var it=u((Z[lt]*B+50)/100);it=Math.min(Math.max(it,1),255),h[ot[lt]]=it}for(var mt=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Lt=0,Pt=0;Pt<8;Pt++)for(var Ct=0;Ct<8;Ct++)f[Lt]=1/(l[ot[Lt]]*mt[Pt]*mt[Ct]*8),p[Lt]=1/(h[ot[Lt]]*mt[Pt]*mt[Ct]*8),Lt++})(A<50?Math.floor(5e3/A):Math.floor(200-2*A)),a=A)}this.encode=function(A,B){B&&wt(B),C=new Array,D=0,S=7,ft(65496),ft(65504),ft(16),rt(74),rt(70),rt(73),rt(70),rt(0),rt(1),rt(1),rt(0),ft(1),ft(1),rt(0),rt(0),(function(){ft(65499),ft(132),rt(0);for(var ie=0;ie<64;ie++)rt(l[ie]);rt(1);for(var zt=0;zt<64;zt++)rt(h[zt])})(),(function(ie,zt){ft(65472),ft(17),rt(8),ft(zt),ft(ie),rt(3),rt(1),rt(17),rt(0),rt(2),rt(17),rt(1),rt(3),rt(17),rt(1)})(A.width,A.height),(function(){ft(65476),ft(418),rt(0);for(var ie=0;ie<16;ie++)rt($[ie+1]);for(var zt=0;zt<=11;zt++)rt(R[zt]);rt(16);for(var ge=0;ge<16;ge++)rt(et[ge+1]);for(var Zt=0;Zt<=161;Zt++)rt(N[Zt]);rt(1);for(var Qt=0;Qt<16;Qt++)rt(O[Qt+1]);for(var Ae=0;Ae<=11;Ae++)rt(G[Ae]);rt(17);for(var ae=0;ae<16;ae++)rt(U[ae+1]);for(var qt=0;qt<=161;qt++)rt(nt[qt])})(),ft(65498),ft(12),rt(3),rt(1),rt(0),rt(2),rt(17),rt(3),rt(17),rt(0),rt(63),rt(0);var T=0,V=0,Y=0;D=0,S=7,this.encode.displayName="_encode_";for(var Z,lt,it,mt,Lt,Pt,Ct,st,j,he=A.data,ne=A.width,Ht=A.height,At=4*ne,Mt=0;Mt<Ht;){for(Z=0;Z<At;){for(Lt=At*Mt+Z,Ct=-1,st=0,j=0;j<64;j++)Pt=Lt+(st=j>>3)*At+(Ct=4*(7&j)),Mt+st>=Ht&&(Pt-=At*(Mt+1+st-Ht)),Z+Ct>=At&&(Pt-=Z+Ct-At+4),lt=he[Pt++],it=he[Pt++],mt=he[Pt++],W[j]=(vt[lt]+vt[it+256|0]+vt[mt+512|0]>>16)-128,z[j]=(vt[lt+768|0]+vt[it+1024|0]+vt[mt+1280|0]>>16)-128,M[j]=(vt[lt+1280|0]+vt[it+1536|0]+vt[mt+1792|0]>>16)-128;T=Nt(W,f,T,t,n),V=Nt(z,p,V,e,i),Y=Nt(M,p,Y,e,i),Z+=32}Mt+=8}if(S>=0){var kt=[];kt[1]=S+1,kt[0]=(1<<S+1)-1,dt(kt)}return ft(65497),new Uint8Array(C)},r=r||50,(function(){for(var A=String.fromCharCode,B=0;B<256;B++)at[B]=A(B)})(),t=ht($,R),e=ht(O,G),n=ht(et,N),i=ht(U,nt),(function(){for(var A=1,B=2,T=1;T<=15;T++){for(var V=A;V<B;V++)x[32767+V]=T,y[32767+V]=[],y[32767+V][1]=T,y[32767+V][0]=V;for(var Y=-(B-1);Y<=-A;Y++)x[32767+Y]=T,y[32767+Y]=[],y[32767+Y][1]=T,y[32767+Y][0]=B-1+Y;A<<=1,B<<=1}})(),(function(){for(var A=0;A<256;A++)vt[A]=19595*A,vt[A+256|0]=38470*A,vt[A+512|0]=7471*A+32768,vt[A+768|0]=-11059*A,vt[A+1024|0]=-21709*A,vt[A+1280|0]=32768*A+8421375,vt[A+1536|0]=-27439*A,vt[A+1792|0]=-5329*A})(),wt(r)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function an(r,t){if(this.pos=0,this.buffer=r,this.datav=new DataView(r.buffer),this.is_with_alpha=!!t,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function xh(r){function t(R){if(!R)throw Error("assert :P")}function e(R,et,N){for(var O=0;4>O;O++)if(R[et+O]!=N.charCodeAt(O))return!0;return!1}function n(R,et,N,O,G){for(var U=0;U<G;U++)R[et+U]=N[O+U]}function i(R,et,N,O){for(var G=0;G<O;G++)R[et+G]=N}function a(R){return new Int32Array(R)}function u(R,et){for(var N=[],O=0;O<R;O++)N.push(new et);return N}function l(R,et){var N=[];return(function O(G,U,nt){for(var ht=nt[U],dt=0;dt<ht&&(G.push(nt.length>U+1?[]:new et),!(nt.length<U+1));dt++)O(G[dt],U+1,nt)})(N,0,R),N}var h=function(){var R=this;function et(o,s){for(var d=1<<s-1>>>0;o&d;)d>>>=1;return d?(o&d-1)+d:o}function N(o,s,d,m,v){t(!(m%d));do o[s+(m-=d)]=v;while(0<m)}function O(o,s,d,m,v){if(t(2328>=v),512>=v)var w=a(512);else if((w=a(v))==null)return 0;return(function(_,L,k,F,H,tt){var E,K,X=L,ut=1<<k,Q=a(16),ct=a(16);for(t(H!=0),t(F!=null),t(_!=null),t(0<k),K=0;K<H;++K){if(15<F[K])return 0;++Q[F[K]]}if(Q[0]==H)return 0;for(ct[1]=0,E=1;15>E;++E){if(Q[E]>1<<E)return 0;ct[E+1]=ct[E]+Q[E]}for(K=0;K<H;++K)E=F[K],0<F[K]&&(tt[ct[E]++]=K);if(ct[15]==1)return(F=new G).g=0,F.value=tt[0],N(_,X,1,ut,F),ut;var gt,_t=-1,bt=ut-1,Tt=0,Ft=1,se=1,Ot=1<<k;for(K=0,E=1,H=2;E<=k;++E,H<<=1){if(Ft+=se<<=1,0>(se-=Q[E]))return 0;for(;0<Q[E];--Q[E])(F=new G).g=E,F.value=tt[K++],N(_,X+Tt,H,Ot,F),Tt=et(Tt,E)}for(E=k+1,H=2;15>=E;++E,H<<=1){if(Ft+=se<<=1,0>(se-=Q[E]))return 0;for(;0<Q[E];--Q[E]){if(F=new G,(Tt&bt)!=_t){for(X+=Ot,gt=1<<(_t=E)-k;15>_t&&!(0>=(gt-=Q[_t]));)++_t,gt<<=1;ut+=Ot=1<<(gt=_t-k),_[L+(_t=Tt&bt)].g=gt+k,_[L+_t].value=X-L-_t}F.g=E-k,F.value=tt[K++],N(_,X+(Tt>>k),H,Ot,F),Tt=et(Tt,E)}}return Ft!=2*ct[15]-1?0:ut})(o,s,d,m,v,w)}function G(){this.value=this.g=0}function U(){this.value=this.g=0}function nt(){this.G=u(5,G),this.H=a(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=u(pn,U)}function ht(o,s,d,m){t(o!=null),t(s!=null),t(2147483648>m),o.Ca=254,o.I=0,o.b=-8,o.Ka=0,o.oa=s,o.pa=d,o.Jd=s,o.Yc=d+m,o.Zc=4<=m?d+m-4+1:d,lt(o)}function dt(o,s){for(var d=0;0<s--;)d|=mt(o,128)<<s;return d}function rt(o,s){var d=dt(o,s);return it(o)?-d:d}function ft(o,s,d,m){var v,w=0;for(t(o!=null),t(s!=null),t(4294967288>m),o.Sb=m,o.Ra=0,o.u=0,o.h=0,4<m&&(m=4),v=0;v<m;++v)w+=s[d+v]<<8*v;o.Ra=w,o.bb=m,o.oa=s,o.pa=d}function Nt(o){for(;8<=o.u&&o.bb<o.Sb;)o.Ra>>>=8,o.Ra+=o.oa[o.pa+o.bb]<<Jn-8>>>0,++o.bb,o.u-=8;V(o)&&(o.h=1,o.u=0)}function wt(o,s){if(t(0<=s),!o.h&&s<=La){var d=T(o)&Yn[s];return o.u+=s,Nt(o),d}return o.h=1,o.u=0}function A(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function B(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function T(o){return o.Ra>>>(o.u&Jn-1)>>>0}function V(o){return t(o.bb<=o.Sb),o.h||o.bb==o.Sb&&o.u>Jn}function Y(o,s){o.u=s,o.h=V(o)}function Z(o){o.u>=Ve&&(t(o.u>=Ve),Nt(o))}function lt(o){t(o!=null&&o.oa!=null),o.pa<o.Zc?(o.I=(o.oa[o.pa++]|o.I<<8)>>>0,o.b+=8):(t(o!=null&&o.oa!=null),o.pa<o.Yc?(o.b+=8,o.I=o.oa[o.pa++]|o.I<<8):o.Ka?o.b=0:(o.I<<=8,o.b+=8,o.Ka=1))}function it(o){return dt(o,1)}function mt(o,s){var d=o.Ca;0>o.b&&lt(o);var m=o.b,v=d*s>>>8,w=(o.I>>>m>v)+0;for(w?(d-=v,o.I-=v+1<<m>>>0):d=v+1,m=d,v=0;256<=m;)v+=8,m>>=8;return m=7^v+ls[m],o.b-=m,o.Ca=(d<<m)-1,w}function Lt(o,s,d){o[s+0]=d>>24&255,o[s+1]=d>>16&255,o[s+2]=d>>8&255,o[s+3]=255&d}function Pt(o,s){return o[s+0]|o[s+1]<<8}function Ct(o,s){return Pt(o,s)|o[s+2]<<16}function st(o,s){return Pt(o,s)|Pt(o,s+2)<<16}function j(o,s){var d=1<<s;return t(o!=null),t(0<s),o.X=a(d),o.X==null?0:(o.Mb=32-s,o.Xa=s,1)}function he(o,s){t(o!=null),t(s!=null),t(o.Xa==s.Xa),n(s.X,0,o.X,0,1<<s.Xa)}function ne(){this.X=[],this.Xa=this.Mb=0}function Ht(o,s,d,m){t(d!=null),t(m!=null);var v=d[0],w=m[0];return v==0&&(v=(o*w+s/2)/s),w==0&&(w=(s*v+o/2)/o),0>=v||0>=w?0:(d[0]=v,m[0]=w,1)}function At(o,s){return o+(1<<s)-1>>>s}function Mt(o,s){return((4278255360&o)+(4278255360&s)>>>0&4278255360)+((16711935&o)+(16711935&s)>>>0&16711935)>>>0}function kt(o,s){R[s]=function(d,m,v,w,_,L,k){var F;for(F=0;F<_;++F){var H=R[o](L[k+F-1],v,w+F);L[k+F]=Mt(d[m+F],H)}}}function ie(){this.ud=this.hd=this.jd=0}function zt(o,s){return((4278124286&(o^s))>>>1)+(o&s)>>>0}function ge(o){return 0<=o&&256>o?o:0>o?0:255<o?255:void 0}function Zt(o,s){return ge(o+(o-s+.5>>1))}function Qt(o,s,d){return Math.abs(s-d)-Math.abs(o-d)}function Ae(o,s,d,m,v,w,_){for(m=w[_-1],d=0;d<v;++d)w[_+d]=m=Mt(o[s+d],m)}function ae(o,s,d,m,v){var w;for(w=0;w<d;++w){var _=o[s+w],L=_>>8&255,k=16711935&(k=(k=16711935&_)+((L<<16)+L));m[v+w]=(4278255360&_)+k>>>0}}function qt(o,s){s.jd=255&o,s.hd=o>>8&255,s.ud=o>>16&255}function Vt(o,s,d,m,v,w){var _;for(_=0;_<m;++_){var L=s[d+_],k=L>>>8,F=L,H=255&(H=(H=L>>>16)+((o.jd<<24>>24)*(k<<24>>24)>>>5));F=255&(F=(F+=(o.hd<<24>>24)*(k<<24>>24)>>>5)+((o.ud<<24>>24)*(H<<24>>24)>>>5)),v[w+_]=(4278255360&L)+(H<<16)+F}}function Ut(o,s,d,m,v){R[s]=function(w,_,L,k,F,H,tt,E,K){for(k=tt;k<E;++k)for(tt=0;tt<K;++tt)F[H++]=v(L[m(w[_++])])},R[o]=function(w,_,L,k,F,H,tt){var E=8>>w.b,K=w.Ea,X=w.K[0],ut=w.w;if(8>E)for(w=(1<<w.b)-1,ut=(1<<E)-1;_<L;++_){var Q,ct=0;for(Q=0;Q<K;++Q)Q&w||(ct=m(k[F++])),H[tt++]=v(X[ct&ut]),ct>>=E}else R["VP8LMapColor"+d](k,F,X,ut,H,tt,_,L,K)}}function Ce(o,s,d,m,v){for(d=s+d;s<d;){var w=o[s++];m[v++]=w>>16&255,m[v++]=w>>8&255,m[v++]=255&w}}function me(o,s,d,m,v){for(d=s+d;s<d;){var w=o[s++];m[v++]=w>>16&255,m[v++]=w>>8&255,m[v++]=255&w,m[v++]=w>>24&255}}function jt(o,s,d,m,v){for(d=s+d;s<d;){var w=(_=o[s++])>>16&240|_>>12&15,_=240&_|_>>28&15;m[v++]=w,m[v++]=_}}function te(o,s,d,m,v){for(d=s+d;s<d;){var w=(_=o[s++])>>16&248|_>>13&7,_=_>>5&224|_>>3&31;m[v++]=w,m[v++]=_}}function Ne(o,s,d,m,v){for(d=s+d;s<d;){var w=o[s++];m[v++]=255&w,m[v++]=w>>8&255,m[v++]=w>>16&255}}function Wt(o,s,d,m,v,w){if(w==0)for(d=s+d;s<d;)Lt(m,((w=o[s++])[0]>>24|w[1]>>8&65280|w[2]<<8&16711680|w[3]<<24)>>>0),v+=32;else n(m,v,o,s,d)}function fe(o,s){R[s][0]=R[o+"0"],R[s][1]=R[o+"1"],R[s][2]=R[o+"2"],R[s][3]=R[o+"3"],R[s][4]=R[o+"4"],R[s][5]=R[o+"5"],R[s][6]=R[o+"6"],R[s][7]=R[o+"7"],R[s][8]=R[o+"8"],R[s][9]=R[o+"9"],R[s][10]=R[o+"10"],R[s][11]=R[o+"11"],R[s][12]=R[o+"12"],R[s][13]=R[o+"13"],R[s][14]=R[o+"0"],R[s][15]=R[o+"0"]}function ee(o){return o==fs||o==cs||o==so||o==ds}function We(){this.eb=[],this.size=this.A=this.fb=0}function nr(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function Se(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new We,this.f.kb=new nr,this.sd=null}function Jt(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function be(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function oi(o){return alert("todo:WebPSamplerProcessPlane"),o.T}function vr(o,s){var d=o.T,m=s.ba.f.RGBA,v=m.eb,w=m.fb+o.ka*m.A,_=Yr[s.ba.S],L=o.y,k=o.O,F=o.f,H=o.N,tt=o.ea,E=o.W,K=s.cc,X=s.dc,ut=s.Mc,Q=s.Nc,ct=o.ka,gt=o.ka+o.T,_t=o.U,bt=_t+1>>1;for(ct==0?_(L,k,null,null,F,H,tt,E,F,H,tt,E,v,w,null,null,_t):(_(s.ec,s.fc,L,k,K,X,ut,Q,F,H,tt,E,v,w-m.A,v,w,_t),++d);ct+2<gt;ct+=2)K=F,X=H,ut=tt,Q=E,H+=o.Rc,E+=o.Rc,w+=2*m.A,_(L,(k+=2*o.fa)-o.fa,L,k,K,X,ut,Q,F,H,tt,E,v,w-m.A,v,w,_t);return k+=o.fa,o.j+gt<o.o?(n(s.ec,s.fc,L,k,_t),n(s.cc,s.dc,F,H,bt),n(s.Mc,s.Nc,tt,E,bt),d--):1&gt||_(L,k,null,null,F,H,tt,E,F,H,tt,E,v,w+m.A,null,null,_t),d}function An(o,s,d){var m=o.F,v=[o.J];if(m!=null){var w=o.U,_=s.ba.S,L=_==oo||_==so;s=s.ba.f.RGBA;var k=[0],F=o.ka;k[0]=o.T,o.Kb&&(F==0?--k[0]:(--F,v[0]-=o.width),o.j+o.ka+o.T==o.o&&(k[0]=o.o-o.j-F));var H=s.eb;F=s.fb+F*s.A,o=Il(m,v[0],o.width,w,k,H,F+(L?0:3),s.A),t(d==k),o&&ee(_)&&Pa(H,F,L,w,k,s.A)}return 0}function un(o){var s=o.ma,d=s.ba.S,m=11>d,v=d==io||d==ao||d==oo||d==hs||d==12||ee(d);if(s.memory=null,s.Ib=null,s.Jb=null,s.Nd=null,!kr(s.Oa,o,v?11:12))return 0;if(v&&ee(d)&&wa(),o.da)alert("todo:use_scaling");else{if(m){if(s.Ib=oi,o.Kb){if(d=o.U+1>>1,s.memory=a(o.U+2*d),s.memory==null)return 0;s.ec=s.memory,s.fc=0,s.cc=s.ec,s.dc=s.fc+o.U,s.Mc=s.cc,s.Nc=s.dc+d,s.Ib=vr,wa()}}else alert("todo:EmitYUV");v&&(s.Jb=An,m&&bi())}if(m&&!Yl){for(o=0;256>o;++o)Zf[o]=89858*(o-128)+uo>>lo,ec[o]=-22014*(o-128)+uo,tc[o]=-45773*(o-128),Qf[o]=113618*(o-128)+uo>>lo;for(o=Fa;o<ms;++o)s=76283*(o-16)+uo>>lo,rc[o-Fa]=ar(s,255),nc[o-Fa]=ar(s+8>>4,15);Yl=1}return 1}function Gt(o){var s=o.ma,d=o.U,m=o.T;return t(!(1&o.ka)),0>=d||0>=m?0:(d=s.Ib(o,s),s.Jb!=null&&s.Jb(o,s,d),s.Dc+=d,1)}function Ln(o){o.ma.memory=null}function Nn(o,s,d,m){return wt(o,8)!=47?0:(s[0]=wt(o,14)+1,d[0]=wt(o,14)+1,m[0]=wt(o,1),wt(o,3)!=0?0:!o.h)}function zr(o,s){if(4>o)return o+1;var d=o-2>>1;return(2+(1&o)<<d)+wt(s,d)+1}function Hr(o,s){return 120<s?s-120:1<=(d=((d=Rf[s-1])>>4)*o+(8-(15&d)))?d:1;var d}function qe(o,s,d){var m=T(d),v=o[s+=255&m].g-8;return 0<v&&(Y(d,d.u+8),m=T(d),s+=o[s].value,s+=m&(1<<v)-1),Y(d,d.u+o[s].g),o[s].value}function ur(o,s,d){return d.g+=o.g,d.value+=o.value<<s>>>0,t(8>=d.g),o.g}function cr(o,s,d){var m=o.xc;return t((s=m==0?0:o.vc[o.md*(d>>m)+(s>>m)])<o.Wb),o.Ya[s]}function si(o,s,d,m){var v=o.ab,w=o.c*s,_=o.C;s=_+s;var L=d,k=m;for(m=o.Ta,d=o.Ua;0<v--;){var F=o.gc[v],H=_,tt=s,E=L,K=k,X=(k=m,L=d,F.Ea);switch(t(H<tt),t(tt<=F.nc),F.hc){case 2:zi(E,K,(tt-H)*X,k,L);break;case 0:var ut=H,Q=tt,ct=k,gt=L,_t=(Ot=F).Ea;ut==0&&(gn(E,K,null,null,1,ct,gt),Ae(E,K+1,0,0,_t-1,ct,gt+1),K+=_t,gt+=_t,++ut);for(var bt=1<<Ot.b,Tt=bt-1,Ft=At(_t,Ot.b),se=Ot.K,Ot=Ot.w+(ut>>Ot.b)*Ft;ut<Q;){var ce=se,or=Ot,le=1;for(mn(E,K,ct,gt-_t,1,ct,gt);le<_t;){var Dt=(le&~Tt)+bt;Dt>_t&&(Dt=_t),(0,xi[ce[or++]>>8&15])(E,K+ +le,ct,gt+le-_t,Dt-le,ct,gt+le),le=Dt}K+=_t,gt+=_t,++ut&Tt||(Ot+=Ft)}tt!=F.nc&&n(k,L-X,k,L+(tt-H-1)*X,X);break;case 1:for(X=E,Q=K,_t=(E=F.Ea)-(gt=E&~(ct=(K=1<<F.b)-1)),ut=At(E,F.b),bt=F.K,F=F.w+(H>>F.b)*ut;H<tt;){for(Tt=bt,Ft=F,se=new ie,Ot=Q+gt,ce=Q+E;Q<Ot;)qt(Tt[Ft++],se),In(se,X,Q,K,k,L),Q+=K,L+=K;Q<ce&&(qt(Tt[Ft++],se),In(se,X,Q,_t,k,L),Q+=_t,L+=_t),++H&ct||(F+=ut)}break;case 3:if(E==k&&K==L&&0<F.b){for(Q=k,E=X=L+(tt-H)*X-(gt=(tt-H)*At(F.Ea,F.b)),K=k,ct=L,ut=[],gt=(_t=gt)-1;0<=gt;--gt)ut[gt]=K[ct+gt];for(gt=_t-1;0<=gt;--gt)Q[E+gt]=ut[gt];Na(F,H,tt,k,X,k,L)}else Na(F,H,tt,E,K,k,L)}L=m,k=d}k!=d&&n(m,d,L,k,w)}function zn(o,s){var d=o.V,m=o.Ba+o.c*o.C,v=s-o.C;if(t(s<=o.l.o),t(16>=v),0<v){var w=o.l,_=o.Ta,L=o.Ua,k=w.width;if(si(o,v,d,m),v=L=[L],t((d=o.C)<(m=s)),t(w.v<w.va),m>w.o&&(m=w.o),d<w.j){var F=w.j-d;d=w.j,v[0]+=F*k}if(d>=m?d=0:(v[0]+=4*w.v,w.ka=d-w.j,w.U=w.va-w.v,w.T=m-d,d=1),d){if(L=L[0],11>(d=o.ca).S){var H=d.f.RGBA,tt=(m=d.S,v=w.U,w=w.T,F=H.eb,H.A),E=w;for(H=H.fb+o.Ma*H.A;0<E--;){var K=_,X=L,ut=v,Q=F,ct=H;switch(m){case no:Sa(K,X,ut,Q,ct);break;case io:Or(K,X,ut,Q,ct);break;case fs:Or(K,X,ut,Q,ct),Pa(Q,ct,0,ut,1,0);break;case Ml:Ee(K,X,ut,Q,ct);break;case ao:Wt(K,X,ut,Q,ct,1);break;case cs:Wt(K,X,ut,Q,ct,1),Pa(Q,ct,0,ut,1,0);break;case oo:Wt(K,X,ut,Q,ct,0);break;case so:Wt(K,X,ut,Q,ct,0),Pa(Q,ct,1,ut,1,0);break;case hs:vn(K,X,ut,Q,ct);break;case ds:vn(K,X,ut,Q,ct),El(Q,ct,ut,1,0);break;case ql:Fe(K,X,ut,Q,ct);break;default:t(0)}L+=k,H+=tt}o.Ma+=w}else alert("todo:EmitRescaledRowsYUVA");t(o.Ma<=d.height)}}o.C=s,t(o.C<=o.i)}function $r(o){var s;if(0<o.ua)return 0;for(s=0;s<o.Wb;++s){var d=o.Ya[s].G,m=o.Ya[s].H;if(0<d[1][m[1]+0].g||0<d[2][m[2]+0].g||0<d[3][m[3]+0].g)return 0}return 1}function hn(o,s,d,m,v,w){if(o.Z!=0){var _=o.qd,L=o.rd;for(t(Xn[o.Z]!=null);s<d;++s)Xn[o.Z](_,L,m,v,m,v,w),_=m,L=v,v+=w;o.qd=_,o.rd=L}}function Xr(o,s){var d=o.l.ma,m=d.Z==0||d.Z==1?o.l.j:o.C;if(m=o.C<m?m:o.C,t(s<=o.l.o),s>m){var v=o.l.width,w=d.ca,_=d.tb+v*m,L=o.V,k=o.Ba+o.c*m,F=o.gc;t(o.ab==1),t(F[0].hc==3),Wi(F[0],m,s,L,k,w,_),hn(d,m,s,w,_,v)}o.C=o.Ma=s}function Zr(o,s,d,m,v,w,_){var L=o.$/m,k=o.$%m,F=o.m,H=o.s,tt=d+o.$,E=tt;v=d+m*v;var K=d+m*w,X=280+H.ua,ut=o.Pb?L:16777216,Q=0<H.ua?H.Wa:null,ct=H.wc,gt=tt<K?cr(H,k,L):null;t(o.C<w),t(K<=v);var _t=!1;t:for(;;){for(;_t||tt<K;){var bt=0;if(L>=ut){var Tt=tt-d;t((ut=o).Pb),ut.wd=ut.m,ut.xd=Tt,0<ut.s.ua&&he(ut.s.Wa,ut.s.vb),ut=L+qf}if(k&ct||(gt=cr(H,k,L)),t(gt!=null),gt.Qb&&(s[tt]=gt.qb,_t=!0),!_t)if(Z(F),gt.jc){bt=F,Tt=s;var Ft=tt,se=gt.pd[T(bt)&pn-1];t(gt.jc),256>se.g?(Y(bt,bt.u+se.g),Tt[Ft]=se.value,bt=0):(Y(bt,bt.u+se.g-256),t(256<=se.value),bt=se.value),bt==0&&(_t=!0)}else bt=qe(gt.G[0],gt.H[0],F);if(F.h)break;if(_t||256>bt){if(!_t)if(gt.nd)s[tt]=(gt.qb|bt<<8)>>>0;else{if(Z(F),_t=qe(gt.G[1],gt.H[1],F),Z(F),Tt=qe(gt.G[2],gt.H[2],F),Ft=qe(gt.G[3],gt.H[3],F),F.h)break;s[tt]=(Ft<<24|_t<<16|bt<<8|Tt)>>>0}if(_t=!1,++tt,++k>=m&&(k=0,++L,_!=null&&L<=w&&!(L%16)&&_(o,L),Q!=null))for(;E<tt;)bt=s[E++],Q.X[(506832829*bt&**********)>>>Q.Mb]=bt}else if(280>bt){if(bt=zr(bt-256,F),Tt=qe(gt.G[4],gt.H[4],F),Z(F),Tt=Hr(m,Tt=zr(Tt,F)),F.h)break;if(tt-d<Tt||v-tt<bt)break t;for(Ft=0;Ft<bt;++Ft)s[tt+Ft]=s[tt+Ft-Tt];for(tt+=bt,k+=bt;k>=m;)k-=m,++L,_!=null&&L<=w&&!(L%16)&&_(o,L);if(t(tt<=v),k&ct&&(gt=cr(H,k,L)),Q!=null)for(;E<tt;)bt=s[E++],Q.X[(506832829*bt&**********)>>>Q.Mb]=bt}else{if(!(bt<X))break t;for(_t=bt-280,t(Q!=null);E<tt;)bt=s[E++],Q.X[(506832829*bt&**********)>>>Q.Mb]=bt;bt=tt,t(!(_t>>>(Tt=Q).Xa)),s[bt]=Tt.X[_t],_t=!0}_t||t(F.h==V(F))}if(o.Pb&&F.h&&tt<v)t(o.m.h),o.a=5,o.m=o.wd,o.$=o.xd,0<o.s.ua&&he(o.s.vb,o.s.Wa);else{if(F.h)break t;_!=null&&_(o,L>w?w:L),o.a=0,o.$=tt-d}return 1}return o.a=3,0}function Wr(o){t(o!=null),o.vc=null,o.yc=null,o.Ya=null;var s=o.Wa;s!=null&&(s.X=null),o.vb=null,t(o!=null)}function li(){var o=new Te;return o==null?null:(o.a=0,o.xb=Hl,fe("Predictor","VP8LPredictors"),fe("Predictor","VP8LPredictors_C"),fe("PredictorAdd","VP8LPredictorsAdd"),fe("PredictorAdd","VP8LPredictorsAdd_C"),zi=ae,In=Vt,Sa=Ce,Or=me,vn=jt,Fe=te,Ee=Ne,R.VP8LMapColor32b=Hi,R.VP8LMapColor8b=wi,o)}function fn(o,s,d,m,v){var w=1,_=[o],L=[s],k=m.m,F=m.s,H=null,tt=0;t:for(;;){if(d)for(;w&&wt(k,1);){var E=_,K=L,X=m,ut=1,Q=X.m,ct=X.gc[X.ab],gt=wt(Q,2);if(X.Oc&1<<gt)w=0;else{switch(X.Oc|=1<<gt,ct.hc=gt,ct.Ea=E[0],ct.nc=K[0],ct.K=[null],++X.ab,t(4>=X.ab),gt){case 0:case 1:ct.b=wt(Q,3)+2,ut=fn(At(ct.Ea,ct.b),At(ct.nc,ct.b),0,X,ct.K),ct.K=ct.K[0];break;case 3:var _t,bt=wt(Q,8)+1,Tt=16<bt?0:4<bt?1:2<bt?2:3;if(E[0]=At(ct.Ea,Tt),ct.b=Tt,_t=ut=fn(bt,1,0,X,ct.K)){var Ft,se=bt,Ot=ct,ce=1<<(8>>Ot.b),or=a(ce);if(or==null)_t=0;else{var le=Ot.K[0],Dt=Ot.w;for(or[0]=Ot.K[0][0],Ft=1;Ft<1*se;++Ft)or[Ft]=Mt(le[Dt+Ft],or[Ft-1]);for(;Ft<4*ce;++Ft)or[Ft]=0;Ot.K[0]=null,Ot.K[0]=or,_t=1}}ut=_t;break;case 2:break;default:t(0)}w=ut}}if(_=_[0],L=L[0],w&&wt(k,1)&&!(w=1<=(tt=wt(k,4))&&11>=tt)){m.a=3;break t}var St;if(St=w)e:{var Je,re,de,Oe=m,pr=_,Lr=L,Ue=tt,br=d,Nr=Oe.m,sr=Oe.s,pe=[null],ye=1,ze=0,we=Mf[Ue];r:for(;;){if(br&&wt(Nr,1)){var gr=wt(Nr,3)+2,rn=At(pr,gr),$e=At(Lr,gr),Cr=rn*$e;if(!fn(rn,$e,0,Oe,pe))break r;for(pe=pe[0],sr.xc=gr,Je=0;Je<Cr;++Je){var ke=pe[Je]>>8&65535;pe[Je]=ke,ke>=ye&&(ye=ke+1)}}if(Nr.h)break r;for(re=0;5>re;++re){var lr=Ul[re];!re&&0<Ue&&(lr+=1<<Ue),ze<lr&&(ze=lr)}var Br=u(ye*we,G),Sr=ye,jr=u(Sr,nt);if(jr==null)var Tr=null;else t(65536>=Sr),Tr=jr;var Fr=a(ze);if(Tr==null||Fr==null||Br==null){Oe.a=1;break r}var Rr=Br;for(Je=de=0;Je<ye;++Je){var Ie=Tr[Je],Mr=Ie.G,nn=Ie.H,_i=0,Bn=1,mr=0;for(re=0;5>re;++re){lr=Ul[re],Mr[re]=Rr,nn[re]=de,!re&&0<Ue&&(lr+=1<<Ue);i:{var fo,vs=lr,co=Oe,Ea=Fr,oc=Rr,sc=de,bs=0,Zn=co.m,lc=wt(Zn,1);if(i(Ea,0,0,vs),lc){var uc=wt(Zn,1)+1,hc=wt(Zn,1),Xl=wt(Zn,hc==0?1:8);Ea[Xl]=1,uc==2&&(Ea[Xl=wt(Zn,8)]=1);var po=1}else{var Zl=a(19),Ql=wt(Zn,4)+4;if(19<Ql){co.a=3;var go=0;break i}for(fo=0;fo<Ql;++fo)Zl[Tf[fo]]=wt(Zn,3);var ys=void 0,Ia=void 0,tu=co,fc=Zl,mo=vs,eu=Ea,ws=0,Qn=tu.m,ru=8,nu=u(128,G);n:for(;O(nu,0,7,fc,19);){if(wt(Qn,1)){var cc=2+2*wt(Qn,3);if((ys=2+wt(Qn,cc))>mo)break n}else ys=mo;for(Ia=0;Ia<mo&&ys--;){Z(Qn);var iu=nu[0+(127&T(Qn))];Y(Qn,Qn.u+iu.g);var Vi=iu.value;if(16>Vi)eu[Ia++]=Vi,Vi!=0&&(ru=Vi);else{var dc=Vi==16,au=Vi-16,pc=Bf[au],ou=wt(Qn,Df[au])+pc;if(Ia+ou>mo)break n;for(var gc=dc?ru:0;0<ou--;)eu[Ia++]=gc}}ws=1;break n}ws||(tu.a=3),po=ws}(po=po&&!Zn.h)&&(bs=O(oc,sc,8,Ea,vs)),po&&bs!=0?go=bs:(co.a=3,go=0)}if(go==0)break r;if(Bn&&jf[re]==1&&(Bn=Rr[de].g==0),_i+=Rr[de].g,de+=go,3>=re){var Oa,xs=Fr[0];for(Oa=1;Oa<lr;++Oa)Fr[Oa]>xs&&(xs=Fr[Oa]);mr+=xs}}if(Ie.nd=Bn,Ie.Qb=0,Bn&&(Ie.qb=(Mr[3][nn[3]+0].value<<24|Mr[1][nn[1]+0].value<<16|Mr[2][nn[2]+0].value)>>>0,_i==0&&256>Mr[0][nn[0]+0].value&&(Ie.Qb=1,Ie.qb+=Mr[0][nn[0]+0].value<<8)),Ie.jc=!Ie.Qb&&6>mr,Ie.jc){var vo,jn=Ie;for(vo=0;vo<pn;++vo){var ti=vo,ei=jn.pd[ti],bo=jn.G[0][jn.H[0]+ti];256<=bo.value?(ei.g=bo.g+256,ei.value=bo.value):(ei.g=0,ei.value=0,ti>>=ur(bo,8,ei),ti>>=ur(jn.G[1][jn.H[1]+ti],16,ei),ti>>=ur(jn.G[2][jn.H[2]+ti],0,ei),ur(jn.G[3][jn.H[3]+ti],24,ei))}}}sr.vc=pe,sr.Wb=ye,sr.Ya=Tr,sr.yc=Br,St=1;break e}St=0}if(!(w=St)){m.a=3;break t}if(0<tt){if(F.ua=1<<tt,!j(F.Wa,tt)){m.a=1,w=0;break t}}else F.ua=0;var _s=m,su=_,mc=L,As=_s.s,Ls=As.xc;if(_s.c=su,_s.i=mc,As.md=At(su,Ls),As.wc=Ls==0?-1:(1<<Ls)-1,d){m.xb=Kf;break t}if((H=a(_*L))==null){m.a=1,w=0;break t}w=(w=Zr(m,H,0,_,L,L,null))&&!k.h;break t}return w?(v!=null?v[0]=H:(t(H==null),t(d)),m.$=0,d||Wr(F)):Wr(F),w}function ui(o,s){var d=o.c*o.i,m=d+s+16*s;return t(o.c<=s),o.V=a(m),o.V==null?(o.Ta=null,o.Ua=0,o.a=1,0):(o.Ta=o.V,o.Ua=o.Ba+d+s,1)}function Fi(o,s){var d=o.C,m=s-d,v=o.V,w=o.Ba+o.c*d;for(t(s<=o.l.o);0<m;){var _=16<m?16:m,L=o.l.ma,k=o.l.width,F=k*_,H=L.ca,tt=L.tb+k*d,E=o.Ta,K=o.Ua;si(o,_,v,w),Ol(E,K,H,tt,F),hn(L,d,d+_,H,tt,k),m-=_,v+=_*o.c,d+=_}t(d==s),o.C=o.Ma=s}function Ei(){this.ub=this.yd=this.td=this.Rb=0}function Ii(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Go(){this.Fb=this.Bb=this.Cb=0,this.Zb=a(4),this.Lb=a(4)}function oa(){this.Yb=(function(){var o=[];return(function s(d,m,v){for(var w=v[m],_=0;_<w&&(d.push(v.length>m+1?[]:0),!(v.length<m+1));_++)s(d[_],m+1,v)})(o,0,[3,11]),o})()}function Ha(){this.jb=a(3),this.Wc=l([4,8],oa),this.Xc=l([4,17],oa)}function Wa(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new a(4),this.od=new a(4)}function Gr(){this.ld=this.La=this.dd=this.tc=0}function sa(){this.Na=this.la=0}function Ga(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function la(){this.ad=a(384),this.Za=0,this.Ob=a(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Va(){this.uc=this.M=this.Nb=0,this.wa=Array(new Gr),this.Y=0,this.ya=Array(new la),this.aa=0,this.l=new Oi}function Ka(){this.y=a(16),this.f=a(8),this.ea=a(8)}function Ya(){this.cb=this.a=0,this.sc="",this.m=new A,this.Od=new Ei,this.Kc=new Ii,this.ed=new Wa,this.Qa=new Go,this.Ic=this.$c=this.Aa=0,this.D=new Va,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=u(8,A),this.ia=0,this.pb=u(4,Ga),this.Pa=new Ha,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Ka),this.Hd=0,this.rb=Array(new sa),this.sb=0,this.wa=Array(new Gr),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new la),this.L=this.aa=0,this.gd=l([4,2],Gr),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Oi(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Vo(){var o=new Ya;return o!=null&&(o.a=0,o.sc="OK",o.cb=0,o.Xb=0,Ca||(Ca=Sn)),o}function tr(o,s,d){return o.a==0&&(o.a=s,o.sc=d,o.cb=0),0}function Ja(o,s,d){return 3<=d&&o[s+0]==157&&o[s+1]==1&&o[s+2]==42}function $a(o,s){if(o==null)return 0;if(o.a=0,o.sc="OK",s==null)return tr(o,2,"null VP8Io passed to VP8GetHeaders()");var d=s.data,m=s.w,v=s.ha;if(4>v)return tr(o,7,"Truncated header.");var w=d[m+0]|d[m+1]<<8|d[m+2]<<16,_=o.Od;if(_.Rb=!(1&w),_.td=w>>1&7,_.yd=w>>4&1,_.ub=w>>5,3<_.td)return tr(o,3,"Incorrect keyframe parameters.");if(!_.yd)return tr(o,4,"Frame not displayable.");m+=3,v-=3;var L=o.Kc;if(_.Rb){if(7>v)return tr(o,7,"cannot parse picture header");if(!Ja(d,m,v))return tr(o,3,"Bad code word");L.c=16383&(d[m+4]<<8|d[m+3]),L.Td=d[m+4]>>6,L.i=16383&(d[m+6]<<8|d[m+5]),L.Ud=d[m+6]>>6,m+=7,v-=7,o.za=L.c+15>>4,o.Ub=L.i+15>>4,s.width=L.c,s.height=L.i,s.Da=0,s.j=0,s.v=0,s.va=s.width,s.o=s.height,s.da=0,s.ib=s.width,s.hb=s.height,s.U=s.width,s.T=s.height,i((w=o.Pa).jb,0,255,w.jb.length),t((w=o.Qa)!=null),w.Cb=0,w.Bb=0,w.Fb=1,i(w.Zb,0,0,w.Zb.length),i(w.Lb,0,0,w.Lb)}if(_.ub>v)return tr(o,7,"bad partition length");ht(w=o.m,d,m,_.ub),m+=_.ub,v-=_.ub,_.Rb&&(L.Ld=it(w),L.Kd=it(w)),L=o.Qa;var k,F=o.Pa;if(t(w!=null),t(L!=null),L.Cb=it(w),L.Cb){if(L.Bb=it(w),it(w)){for(L.Fb=it(w),k=0;4>k;++k)L.Zb[k]=it(w)?rt(w,7):0;for(k=0;4>k;++k)L.Lb[k]=it(w)?rt(w,6):0}if(L.Bb)for(k=0;3>k;++k)F.jb[k]=it(w)?dt(w,8):255}else L.Bb=0;if(w.Ka)return tr(o,3,"cannot parse segment header");if((L=o.ed).zd=it(w),L.Tb=dt(w,6),L.wb=dt(w,3),L.Pc=it(w),L.Pc&&it(w)){for(F=0;4>F;++F)it(w)&&(L.vd[F]=rt(w,6));for(F=0;4>F;++F)it(w)&&(L.od[F]=rt(w,6))}if(o.L=L.Tb==0?0:L.zd?1:2,w.Ka)return tr(o,3,"cannot parse filter header");var H=v;if(v=k=m,m=k+H,L=H,o.Xb=(1<<dt(o.m,2))-1,H<3*(F=o.Xb))d=7;else{for(k+=3*F,L-=3*F,H=0;H<F;++H){var tt=d[v+0]|d[v+1]<<8|d[v+2]<<16;tt>L&&(tt=L),ht(o.Jc[+H],d,k,tt),k+=tt,L-=tt,v+=3}ht(o.Jc[+F],d,k,L),d=k<m?0:5}if(d!=0)return tr(o,d,"cannot parse partitions");for(d=dt(k=o.m,7),v=it(k)?rt(k,4):0,m=it(k)?rt(k,4):0,L=it(k)?rt(k,4):0,F=it(k)?rt(k,4):0,k=it(k)?rt(k,4):0,H=o.Qa,tt=0;4>tt;++tt){if(H.Cb){var E=H.Zb[tt];H.Fb||(E+=d)}else{if(0<tt){o.pb[tt]=o.pb[0];continue}E=d}var K=o.pb[tt];K.Sc[0]=ps[ar(E+v,127)],K.Sc[1]=gs[ar(E+0,127)],K.Eb[0]=2*ps[ar(E+m,127)],K.Eb[1]=101581*gs[ar(E+L,127)]>>16,8>K.Eb[1]&&(K.Eb[1]=8),K.Qc[0]=ps[ar(E+F,117)],K.Qc[1]=gs[ar(E+k,127)],K.lc=E+k}if(!_.Rb)return tr(o,4,"Not a key frame.");for(it(w),_=o.Pa,d=0;4>d;++d){for(v=0;8>v;++v)for(m=0;3>m;++m)for(L=0;11>L;++L)F=mt(w,Gf[d][v][m][L])?dt(w,8):Hf[d][v][m][L],_.Wc[d][v].Yb[m][L]=F;for(v=0;17>v;++v)_.Xc[d][v]=_.Wc[d][Vf[v]]}return o.kc=it(w),o.kc&&(o.Bd=dt(w,8)),o.cb=1}function Sn(o,s,d,m,v,w,_){var L=s[v].Yb[d];for(d=0;16>v;++v){if(!mt(o,L[d+0]))return v;for(;!mt(o,L[d+1]);)if(L=s[++v].Yb[0],d=0,v==16)return 16;var k=s[v+1].Yb;if(mt(o,L[d+2])){var F=o,H=0;if(mt(F,(E=L)[(tt=d)+3]))if(mt(F,E[tt+6])){for(L=0,tt=2*(H=mt(F,E[tt+8]))+(E=mt(F,E[tt+9+H])),H=0,E=Uf[tt];E[L];++L)H+=H+mt(F,E[L]);H+=3+(8<<tt)}else mt(F,E[tt+7])?(H=7+2*mt(F,165),H+=mt(F,145)):H=5+mt(F,159);else H=mt(F,E[tt+4])?3+mt(F,E[tt+5]):2;L=k[2]}else H=1,L=k[1];k=_+zf[v],0>(F=o).b&&lt(F);var tt,E=F.b,K=(tt=F.Ca>>1)-(F.I>>E)>>31;--F.b,F.Ca+=K,F.Ca|=1,F.I-=(tt+1&K)<<E,w[k]=((H^K)-K)*m[(0<v)+0]}return 16}function hi(o){var s=o.rb[o.sb-1];s.la=0,s.Na=0,i(o.zc,0,0,o.zc.length),o.ja=0}function Qr(o,s,d,m,v){v=o[s+d+32*m]+(v>>3),o[s+d+32*m]=-256&v?0>v?0:255:v}function fi(o,s,d,m,v,w){Qr(o,s,0,d,m+v),Qr(o,s,1,d,m+w),Qr(o,s,2,d,m-w),Qr(o,s,3,d,m-v)}function Ge(o){return(20091*o>>16)+o}function ua(o,s,d,m){var v,w=0,_=a(16);for(v=0;4>v;++v){var L=o[s+0]+o[s+8],k=o[s+0]-o[s+8],F=(35468*o[s+4]>>16)-Ge(o[s+12]),H=Ge(o[s+4])+(35468*o[s+12]>>16);_[w+0]=L+H,_[w+1]=k+F,_[w+2]=k-F,_[w+3]=L-H,w+=4,s++}for(v=w=0;4>v;++v)L=(o=_[w+0]+4)+_[w+8],k=o-_[w+8],F=(35468*_[w+4]>>16)-Ge(_[w+12]),Qr(d,m,0,0,L+(H=Ge(_[w+4])+(35468*_[w+12]>>16))),Qr(d,m,1,0,k+F),Qr(d,m,2,0,k-F),Qr(d,m,3,0,L-H),w++,m+=32}function Ko(o,s,d,m){var v=o[s+0]+4,w=35468*o[s+4]>>16,_=Ge(o[s+4]),L=35468*o[s+1]>>16;fi(d,m,0,v+_,o=Ge(o[s+1]),L),fi(d,m,1,v+w,o,L),fi(d,m,2,v-w,o,L),fi(d,m,3,v-_,o,L)}function Yo(o,s,d,m,v){ua(o,s,d,m),v&&ua(o,s+16,d,m+4)}function Jo(o,s,d,m){Ye(o,s+0,d,m,1),Ye(o,s+32,d,m+128,1)}function cn(o,s,d,m){var v;for(o=o[s+0]+4,v=0;4>v;++v)for(s=0;4>s;++s)Qr(d,m,s,v,o)}function Xa(o,s,d,m){o[s+0]&&bn(o,s+0,d,m),o[s+16]&&bn(o,s+16,d,m+4),o[s+32]&&bn(o,s+32,d,m+128),o[s+48]&&bn(o,s+48,d,m+128+4)}function Za(o,s,d,m){var v,w=a(16);for(v=0;4>v;++v){var _=o[s+0+v]+o[s+12+v],L=o[s+4+v]+o[s+8+v],k=o[s+4+v]-o[s+8+v],F=o[s+0+v]-o[s+12+v];w[0+v]=_+L,w[8+v]=_-L,w[4+v]=F+k,w[12+v]=F-k}for(v=0;4>v;++v)_=(o=w[0+4*v]+3)+w[3+4*v],L=w[1+4*v]+w[2+4*v],k=w[1+4*v]-w[2+4*v],F=o-w[3+4*v],d[m+0]=_+L>>3,d[m+16]=F+k>>3,d[m+32]=_-L>>3,d[m+48]=F-k>>3,m+=64}function Hn(o,s,d){var m,v=s-32,w=Dr,_=255-o[v-1];for(m=0;m<d;++m){var L,k=w,F=_+o[s-1];for(L=0;L<d;++L)o[s+L]=k[F+o[v+L]];s+=32}}function ha(o,s){Hn(o,s,4)}function $o(o,s){Hn(o,s,8)}function Xo(o,s){Hn(o,s,16)}function Zo(o,s){var d;for(d=0;16>d;++d)n(o,s+32*d,o,s-32,16)}function fa(o,s){var d;for(d=16;0<d;--d)i(o,s,o[s-1],16),s+=32}function ci(o,s,d){var m;for(m=0;16>m;++m)i(s,d+32*m,o,16)}function Wn(o,s){var d,m=16;for(d=0;16>d;++d)m+=o[s-1+32*d]+o[s+d-32];ci(m>>5,o,s)}function Qo(o,s){var d,m=8;for(d=0;16>d;++d)m+=o[s-1+32*d];ci(m>>4,o,s)}function di(o,s){var d,m=8;for(d=0;16>d;++d)m+=o[s+d-32];ci(m>>4,o,s)}function ca(o,s){ci(128,o,s)}function $t(o,s,d){return o+2*s+d+2>>2}function ts(o,s){var d,m=s-32;for(m=new Uint8Array([$t(o[m-1],o[m+0],o[m+1]),$t(o[m+0],o[m+1],o[m+2]),$t(o[m+1],o[m+2],o[m+3]),$t(o[m+2],o[m+3],o[m+4])]),d=0;4>d;++d)n(o,s+32*d,m,0,m.length)}function es(o,s){var d=o[s-1],m=o[s-1+32],v=o[s-1+64],w=o[s-1+96];Lt(o,s+0,16843009*$t(o[s-1-32],d,m)),Lt(o,s+32,16843009*$t(d,m,v)),Lt(o,s+64,16843009*$t(m,v,w)),Lt(o,s+96,16843009*$t(v,w,w))}function rs(o,s){var d,m=4;for(d=0;4>d;++d)m+=o[s+d-32]+o[s-1+32*d];for(m>>=3,d=0;4>d;++d)i(o,s+32*d,m,4)}function ns(o,s){var d=o[s-1+0],m=o[s-1+32],v=o[s-1+64],w=o[s-1-32],_=o[s+0-32],L=o[s+1-32],k=o[s+2-32],F=o[s+3-32];o[s+0+96]=$t(m,v,o[s-1+96]),o[s+1+96]=o[s+0+64]=$t(d,m,v),o[s+2+96]=o[s+1+64]=o[s+0+32]=$t(w,d,m),o[s+3+96]=o[s+2+64]=o[s+1+32]=o[s+0+0]=$t(_,w,d),o[s+3+64]=o[s+2+32]=o[s+1+0]=$t(L,_,w),o[s+3+32]=o[s+2+0]=$t(k,L,_),o[s+3+0]=$t(F,k,L)}function kn(o,s){var d=o[s+1-32],m=o[s+2-32],v=o[s+3-32],w=o[s+4-32],_=o[s+5-32],L=o[s+6-32],k=o[s+7-32];o[s+0+0]=$t(o[s+0-32],d,m),o[s+1+0]=o[s+0+32]=$t(d,m,v),o[s+2+0]=o[s+1+32]=o[s+0+64]=$t(m,v,w),o[s+3+0]=o[s+2+32]=o[s+1+64]=o[s+0+96]=$t(v,w,_),o[s+3+32]=o[s+2+64]=o[s+1+96]=$t(w,_,L),o[s+3+64]=o[s+2+96]=$t(_,L,k),o[s+3+96]=$t(L,k,k)}function Pn(o,s){var d=o[s-1+0],m=o[s-1+32],v=o[s-1+64],w=o[s-1-32],_=o[s+0-32],L=o[s+1-32],k=o[s+2-32],F=o[s+3-32];o[s+0+0]=o[s+1+64]=w+_+1>>1,o[s+1+0]=o[s+2+64]=_+L+1>>1,o[s+2+0]=o[s+3+64]=L+k+1>>1,o[s+3+0]=k+F+1>>1,o[s+0+96]=$t(v,m,d),o[s+0+64]=$t(m,d,w),o[s+0+32]=o[s+1+96]=$t(d,w,_),o[s+1+32]=o[s+2+96]=$t(w,_,L),o[s+2+32]=o[s+3+96]=$t(_,L,k),o[s+3+32]=$t(L,k,F)}function is(o,s){var d=o[s+0-32],m=o[s+1-32],v=o[s+2-32],w=o[s+3-32],_=o[s+4-32],L=o[s+5-32],k=o[s+6-32],F=o[s+7-32];o[s+0+0]=d+m+1>>1,o[s+1+0]=o[s+0+64]=m+v+1>>1,o[s+2+0]=o[s+1+64]=v+w+1>>1,o[s+3+0]=o[s+2+64]=w+_+1>>1,o[s+0+32]=$t(d,m,v),o[s+1+32]=o[s+0+96]=$t(m,v,w),o[s+2+32]=o[s+1+96]=$t(v,w,_),o[s+3+32]=o[s+2+96]=$t(w,_,L),o[s+3+64]=$t(_,L,k),o[s+3+96]=$t(L,k,F)}function as(o,s){var d=o[s-1+0],m=o[s-1+32],v=o[s-1+64],w=o[s-1+96];o[s+0+0]=d+m+1>>1,o[s+2+0]=o[s+0+32]=m+v+1>>1,o[s+2+32]=o[s+0+64]=v+w+1>>1,o[s+1+0]=$t(d,m,v),o[s+3+0]=o[s+1+32]=$t(m,v,w),o[s+3+32]=o[s+1+64]=$t(v,w,w),o[s+3+64]=o[s+2+64]=o[s+0+96]=o[s+1+96]=o[s+2+96]=o[s+3+96]=w}function Di(o,s){var d=o[s-1+0],m=o[s-1+32],v=o[s-1+64],w=o[s-1+96],_=o[s-1-32],L=o[s+0-32],k=o[s+1-32],F=o[s+2-32];o[s+0+0]=o[s+2+32]=d+_+1>>1,o[s+0+32]=o[s+2+64]=m+d+1>>1,o[s+0+64]=o[s+2+96]=v+m+1>>1,o[s+0+96]=w+v+1>>1,o[s+3+0]=$t(L,k,F),o[s+2+0]=$t(_,L,k),o[s+1+0]=o[s+3+32]=$t(d,_,L),o[s+1+32]=o[s+3+64]=$t(m,d,_),o[s+1+64]=o[s+3+96]=$t(v,m,d),o[s+1+96]=$t(w,v,m)}function da(o,s){var d;for(d=0;8>d;++d)n(o,s+32*d,o,s-32,8)}function pi(o,s){var d;for(d=0;8>d;++d)i(o,s,o[s-1],8),s+=32}function Bi(o,s,d){var m;for(m=0;8>m;++m)i(s,d+32*m,o,8)}function ji(o,s){var d,m=8;for(d=0;8>d;++d)m+=o[s+d-32]+o[s-1+32*d];Bi(m>>4,o,s)}function os(o,s){var d,m=4;for(d=0;8>d;++d)m+=o[s+d-32];Bi(m>>3,o,s)}function pa(o,s){var d,m=4;for(d=0;8>d;++d)m+=o[s-1+32*d];Bi(m>>3,o,s)}function ga(o,s){Bi(128,o,s)}function gi(o,s,d){var m=o[s-d],v=o[s+0],w=3*(v-m)+us[1020+o[s-2*d]-o[s+d]],_=ro[112+(w+4>>3)];o[s-d]=Dr[255+m+ro[112+(w+3>>3)]],o[s+0]=Dr[255+v-_]}function mi(o,s,d,m){var v=o[s+0],w=o[s+d];return Kr[255+o[s-2*d]-o[s-d]]>m||Kr[255+w-v]>m}function ma(o,s,d,m){return 4*Kr[255+o[s-d]-o[s+0]]+Kr[255+o[s-2*d]-o[s+d]]<=m}function Ti(o,s,d,m,v){var w=o[s-3*d],_=o[s-2*d],L=o[s-d],k=o[s+0],F=o[s+d],H=o[s+2*d],tt=o[s+3*d];return 4*Kr[255+L-k]+Kr[255+_-F]>m?0:Kr[255+o[s-4*d]-w]<=v&&Kr[255+w-_]<=v&&Kr[255+_-L]<=v&&Kr[255+tt-H]<=v&&Kr[255+H-F]<=v&&Kr[255+F-k]<=v}function va(o,s,d,m){var v=2*m+1;for(m=0;16>m;++m)ma(o,s+m,d,v)&&gi(o,s+m,d)}function vi(o,s,d,m){var v=2*m+1;for(m=0;16>m;++m)ma(o,s+m*d,1,v)&&gi(o,s+m*d,1)}function ba(o,s,d,m){var v;for(v=3;0<v;--v)va(o,s+=4*d,d,m)}function ya(o,s,d,m){var v;for(v=3;0<v;--v)vi(o,s+=4,d,m)}function Ir(o,s,d,m,v,w,_,L){for(w=2*w+1;0<v--;){if(Ti(o,s,d,w,_))if(mi(o,s,d,L))gi(o,s,d);else{var k=o,F=s,H=d,tt=k[F-2*H],E=k[F-H],K=k[F+0],X=k[F+H],ut=k[F+2*H],Q=27*(gt=us[1020+3*(K-E)+us[1020+tt-X]])+63>>7,ct=18*gt+63>>7,gt=9*gt+63>>7;k[F-3*H]=Dr[255+k[F-3*H]+gt],k[F-2*H]=Dr[255+tt+ct],k[F-H]=Dr[255+E+Q],k[F+0]=Dr[255+K-Q],k[F+H]=Dr[255+X-ct],k[F+2*H]=Dr[255+ut-gt]}s+=m}}function dn(o,s,d,m,v,w,_,L){for(w=2*w+1;0<v--;){if(Ti(o,s,d,w,_))if(mi(o,s,d,L))gi(o,s,d);else{var k=o,F=s,H=d,tt=k[F-H],E=k[F+0],K=k[F+H],X=ro[112+(4+(ut=3*(E-tt))>>3)],ut=ro[112+(ut+3>>3)],Q=X+1>>1;k[F-2*H]=Dr[255+k[F-2*H]+Q],k[F-H]=Dr[255+tt+ut],k[F+0]=Dr[255+E-X],k[F+H]=Dr[255+K-Q]}s+=m}}function c(o,s,d,m,v,w){Ir(o,s,d,1,16,m,v,w)}function b(o,s,d,m,v,w){Ir(o,s,1,d,16,m,v,w)}function I(o,s,d,m,v,w){var _;for(_=3;0<_;--_)dn(o,s+=4*d,d,1,16,m,v,w)}function q(o,s,d,m,v,w){var _;for(_=3;0<_;--_)dn(o,s+=4,1,d,16,m,v,w)}function J(o,s,d,m,v,w,_,L){Ir(o,s,v,1,8,w,_,L),Ir(d,m,v,1,8,w,_,L)}function pt(o,s,d,m,v,w,_,L){Ir(o,s,1,v,8,w,_,L),Ir(d,m,1,v,8,w,_,L)}function yt(o,s,d,m,v,w,_,L){dn(o,s+4*v,v,1,8,w,_,L),dn(d,m+4*v,v,1,8,w,_,L)}function It(o,s,d,m,v,w,_,L){dn(o,s+4,1,v,8,w,_,L),dn(d,m+4,1,v,8,w,_,L)}function Bt(){this.ba=new Se,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new be,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Kt(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function oe(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function ve(){this.ua=0,this.Wa=new ne,this.vb=new ne,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new nt,this.yc=new G}function Te(){this.xb=this.a=0,this.l=new Oi,this.ca=new Se,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new B,this.Pb=0,this.wd=new B,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new ve,this.ab=0,this.gc=u(4,oe),this.Oc=0}function er(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Oi,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function Le(o,s,d,m,v,w,_){for(o=o==null?0:o[s+0],s=0;s<_;++s)v[w+s]=o+d[m+s]&255,o=v[w+s]}function ir(o,s,d,m,v,w,_){var L;if(o==null)Le(null,null,d,m,v,w,_);else for(L=0;L<_;++L)v[w+L]=o[s+L]+d[m+L]&255}function rr(o,s,d,m,v,w,_){if(o==null)Le(null,null,d,m,v,w,_);else{var L,k=o[s+0],F=k,H=k;for(L=0;L<_;++L)F=H+(k=o[s+L])-F,H=d[m+L]+(-256&F?0>F?0:255:F)&255,F=k,v[w+L]=H}}function dr(o,s,d,m){var v=s.width,w=s.o;if(t(o!=null&&s!=null),0>d||0>=m||d+m>w)return null;if(!o.Cc){if(o.ga==null){var _;if(o.ga=new er,(_=o.ga==null)||(_=s.width*s.o,t(o.Gb.length==0),o.Gb=a(_),o.Uc=0,o.Gb==null?_=0:(o.mb=o.Gb,o.nb=o.Uc,o.rc=null,_=1),_=!_),!_){_=o.ga;var L=o.Fa,k=o.P,F=o.qc,H=o.mb,tt=o.nb,E=k+1,K=F-1,X=_.l;if(t(L!=null&&H!=null&&s!=null),Xn[0]=null,Xn[1]=Le,Xn[2]=ir,Xn[3]=rr,_.ca=H,_.tb=tt,_.c=s.width,_.i=s.height,t(0<_.c&&0<_.i),1>=F)s=0;else if(_.$a=3&L[k+0],_.Z=L[k+0]>>2&3,_.Lc=L[k+0]>>4&3,k=L[k+0]>>6&3,0>_.$a||1<_.$a||4<=_.Z||1<_.Lc||k)s=0;else if(X.put=Gt,X.ac=un,X.bc=Ln,X.ma=_,X.width=s.width,X.height=s.height,X.Da=s.Da,X.v=s.v,X.va=s.va,X.j=s.j,X.o=s.o,_.$a)t:{t(_.$a==1),s=li();e:for(;;){if(s==null){s=0;break t}if(t(_!=null),_.mc=s,s.c=_.c,s.i=_.i,s.l=_.l,s.l.ma=_,s.l.width=_.c,s.l.height=_.i,s.a=0,ft(s.m,L,E,K),!fn(_.c,_.i,1,s,null)||(s.ab==1&&s.gc[0].hc==3&&$r(s.s)?(_.ic=1,L=s.c*s.i,s.Ta=null,s.Ua=0,s.V=a(L),s.Ba=0,s.V==null?(s.a=1,s=0):s=1):(_.ic=0,s=ui(s,_.c)),!s))break e;s=1;break t}_.mc=null,s=0}else s=K>=_.c*_.i;_=!s}if(_)return null;o.ga.Lc!=1?o.Ga=0:m=w-d}t(o.ga!=null),t(d+m<=w);t:{if(s=(L=o.ga).c,w=L.l.o,L.$a==0){if(E=o.rc,K=o.Vc,X=o.Fa,k=o.P+1+d*s,F=o.mb,H=o.nb+d*s,t(k<=o.P+o.qc),L.Z!=0)for(t(Xn[L.Z]!=null),_=0;_<m;++_)Xn[L.Z](E,K,X,k,F,H,s),E=F,K=H,H+=s,k+=s;else for(_=0;_<m;++_)n(F,H,X,k,s),E=F,K=H,H+=s,k+=s;o.rc=E,o.Vc=K}else{if(t(L.mc!=null),s=d+m,t((_=L.mc)!=null),t(s<=_.i),_.C>=s)s=1;else if(L.ic||bi(),L.ic){L=_.V,E=_.Ba,K=_.c;var ut=_.i,Q=(X=1,k=_.$/K,F=_.$%K,H=_.m,tt=_.s,_.$),ct=K*ut,gt=K*s,_t=tt.wc,bt=Q<gt?cr(tt,F,k):null;t(Q<=ct),t(s<=ut),t($r(tt));e:for(;;){for(;!H.h&&Q<gt;){if(F&_t||(bt=cr(tt,F,k)),t(bt!=null),Z(H),256>(ut=qe(bt.G[0],bt.H[0],H)))L[E+Q]=ut,++Q,++F>=K&&(F=0,++k<=s&&!(k%16)&&Xr(_,k));else{if(!(280>ut)){X=0;break e}ut=zr(ut-256,H);var Tt,Ft=qe(bt.G[4],bt.H[4],H);if(Z(H),!(Q>=(Ft=Hr(K,Ft=zr(Ft,H)))&&ct-Q>=ut)){X=0;break e}for(Tt=0;Tt<ut;++Tt)L[E+Q+Tt]=L[E+Q+Tt-Ft];for(Q+=ut,F+=ut;F>=K;)F-=K,++k<=s&&!(k%16)&&Xr(_,k);Q<gt&&F&_t&&(bt=cr(tt,F,k))}t(H.h==V(H))}Xr(_,k>s?s:k);break e}!X||H.h&&Q<ct?(X=0,_.a=H.h?5:3):_.$=Q,s=X}else s=Zr(_,_.V,_.Ba,_.c,_.i,s,Fi);if(!s){m=0;break t}}d+m>=w&&(o.Cc=1),m=1}if(!m)return null;if(o.Cc&&((m=o.ga)!=null&&(m.mc=null),o.ga=null,0<o.Ga))return alert("todo:WebPDequantizeLevels"),null}return o.nb+d*v}function Cn(o,s,d,m,v,w){for(;0<v--;){var _,L=o,k=s+(d?1:0),F=o,H=s+(d?0:3);for(_=0;_<m;++_){var tt=F[H+4*_];tt!=255&&(tt*=32897,L[k+4*_+0]=L[k+4*_+0]*tt>>23,L[k+4*_+1]=L[k+4*_+1]*tt>>23,L[k+4*_+2]=L[k+4*_+2]*tt>>23)}s+=w}}function Gn(o,s,d,m,v){for(;0<m--;){var w;for(w=0;w<d;++w){var _=o[s+2*w+0],L=15&(F=o[s+2*w+1]),k=4369*L,F=(240&F|F>>4)*k>>16;o[s+2*w+0]=(240&_|_>>4)*k>>16&240|(15&_|_<<4)*k>>16>>4&15,o[s+2*w+1]=240&F|L}s+=v}}function ss(o,s,d,m,v,w,_,L){var k,F,H=255;for(F=0;F<v;++F){for(k=0;k<m;++k){var tt=o[s+k];w[_+4*k]=tt,H&=tt}s+=d,_+=L}return H!=255}function Qa(o,s,d,m,v){var w;for(w=0;w<v;++w)d[m+w]=o[s+w]>>8}function bi(){Pa=Cn,El=Gn,Il=ss,Ol=Qa}function tn(o,s,d){R[o]=function(m,v,w,_,L,k,F,H,tt,E,K,X,ut,Q,ct,gt,_t){var bt,Tt=_t-1>>1,Ft=L[k+0]|F[H+0]<<16,se=tt[E+0]|K[X+0]<<16;t(m!=null);var Ot=3*Ft+se+131074>>2;for(s(m[v+0],255&Ot,Ot>>16,ut,Q),w!=null&&(Ot=3*se+Ft+131074>>2,s(w[_+0],255&Ot,Ot>>16,ct,gt)),bt=1;bt<=Tt;++bt){var ce=L[k+bt]|F[H+bt]<<16,or=tt[E+bt]|K[X+bt]<<16,le=Ft+ce+se+or+524296,Dt=le+2*(ce+se)>>3;Ot=Dt+Ft>>1,Ft=(le=le+2*(Ft+or)>>3)+ce>>1,s(m[v+2*bt-1],255&Ot,Ot>>16,ut,Q+(2*bt-1)*d),s(m[v+2*bt-0],255&Ft,Ft>>16,ut,Q+(2*bt-0)*d),w!=null&&(Ot=le+se>>1,Ft=Dt+or>>1,s(w[_+2*bt-1],255&Ot,Ot>>16,ct,gt+(2*bt-1)*d),s(w[_+2*bt+0],255&Ft,Ft>>16,ct,gt+(2*bt+0)*d)),Ft=ce,se=or}1&_t||(Ot=3*Ft+se+131074>>2,s(m[v+_t-1],255&Ot,Ot>>16,ut,Q+(_t-1)*d),w!=null&&(Ot=3*se+Ft+131074>>2,s(w[_+_t-1],255&Ot,Ot>>16,ct,gt+(_t-1)*d)))}}function wa(){Yr[no]=Yf,Yr[io]=Wl,Yr[Ml]=Jf,Yr[ao]=Gl,Yr[oo]=Vl,Yr[hs]=Kl,Yr[ql]=$f,Yr[fs]=Wl,Yr[cs]=Gl,Yr[so]=Vl,Yr[ds]=Kl}function Ri(o){return o&-16384?0>o?0:255:o>>Xf}function Vn(o,s){return Ri((19077*o>>8)+(26149*s>>8)-14234)}function hr(o,s,d){return Ri((19077*o>>8)-(6419*s>>8)-(13320*d>>8)+8708)}function Fn(o,s){return Ri((19077*o>>8)+(33050*s>>8)-17685)}function En(o,s,d,m,v){m[v+0]=Vn(o,d),m[v+1]=hr(o,s,d),m[v+2]=Fn(o,s)}function Mi(o,s,d,m,v){m[v+0]=Fn(o,s),m[v+1]=hr(o,s,d),m[v+2]=Vn(o,d)}function xa(o,s,d,m,v){var w=hr(o,s,d);s=w<<3&224|Fn(o,s)>>3,m[v+0]=248&Vn(o,d)|w>>5,m[v+1]=s}function Ar(o,s,d,m,v){var w=240&Fn(o,s)|15;m[v+0]=240&Vn(o,d)|hr(o,s,d)>>4,m[v+1]=w}function to(o,s,d,m,v){m[v+0]=255,En(o,s,d,m,v+1)}function qi(o,s,d,m,v){Mi(o,s,d,m,v),m[v+3]=255}function eo(o,s,d,m,v){En(o,s,d,m,v),m[v+3]=255}function ar(o,s){return 0>o?0:o>s?s:o}function Vr(o,s,d){R[o]=function(m,v,w,_,L,k,F,H,tt){for(var E=H+(-2&tt)*d;H!=E;)s(m[v+0],w[_+0],L[k+0],F,H),s(m[v+1],w[_+0],L[k+0],F,H+d),v+=2,++_,++k,H+=2*d;1&tt&&s(m[v+0],w[_+0],L[k+0],F,H)}}function _a(o,s,d){return d==0?o==0?s==0?6:5:s==0?4:0:d}function Aa(o,s,d,m,v){switch(o>>>30){case 3:Ye(s,d,m,v,0);break;case 2:On(s,d,m,v);break;case 1:bn(s,d,m,v)}}function Kn(o,s){var d,m,v=s.M,w=s.Nb,_=o.oc,L=o.pc+40,k=o.oc,F=o.pc+584,H=o.oc,tt=o.pc+600;for(d=0;16>d;++d)_[L+32*d-1]=129;for(d=0;8>d;++d)k[F+32*d-1]=129,H[tt+32*d-1]=129;for(0<v?_[L-1-32]=k[F-1-32]=H[tt-1-32]=129:(i(_,L-32-1,127,21),i(k,F-32-1,127,9),i(H,tt-32-1,127,9)),m=0;m<o.za;++m){var E=s.ya[s.aa+m];if(0<m){for(d=-1;16>d;++d)n(_,L+32*d-4,_,L+32*d+12,4);for(d=-1;8>d;++d)n(k,F+32*d-4,k,F+32*d+4,4),n(H,tt+32*d-4,H,tt+32*d+4,4)}var K=o.Gd,X=o.Hd+m,ut=E.ad,Q=E.Hc;if(0<v&&(n(_,L-32,K[X].y,0,16),n(k,F-32,K[X].f,0,8),n(H,tt-32,K[X].ea,0,8)),E.Za){var ct=_,gt=L-32+16;for(0<v&&(m>=o.za-1?i(ct,gt,K[X].y[15],4):n(ct,gt,K[X+1].y,0,4)),d=0;4>d;d++)ct[gt+128+d]=ct[gt+256+d]=ct[gt+384+d]=ct[gt+0+d];for(d=0;16>d;++d,Q<<=2)ct=_,gt=L+Jl[d],en[E.Ob[d]](ct,gt),Aa(Q,ut,16*+d,ct,gt)}else if(ct=_a(m,v,E.Ob[0]),$n[ct](_,L),Q!=0)for(d=0;16>d;++d,Q<<=2)Aa(Q,ut,16*+d,_,L+Jl[d]);for(d=E.Gc,ct=_a(m,v,E.Dd),Dn[ct](k,F),Dn[ct](H,tt),Q=ut,ct=k,gt=F,255&(E=0|d)&&(170&E?Gi(Q,256,ct,gt):ka(Q,256,ct,gt)),E=H,Q=tt,255&(d>>=8)&&(170&d?Gi(ut,320,E,Q):ka(ut,320,E,Q)),v<o.Ub-1&&(n(K[X].y,0,_,L+480,16),n(K[X].f,0,k,F+224,8),n(K[X].ea,0,H,tt+224,8)),d=8*w*o.B,K=o.sa,X=o.ta+16*m+16*w*o.R,ut=o.qa,E=o.ra+8*m+d,Q=o.Ha,ct=o.Ia+8*m+d,d=0;16>d;++d)n(K,X+d*o.R,_,L+32*d,16);for(d=0;8>d;++d)n(ut,E+d*o.B,k,F+32*d,8),n(Q,ct+d*o.B,H,tt+32*d,8)}}function Ui(o,s,d,m,v,w,_,L,k){var F=[0],H=[0],tt=0,E=k!=null?k.kd:0,K=k!=null?k:new Kt;if(o==null||12>d)return 7;K.data=o,K.w=s,K.ha=d,s=[s],d=[d],K.gb=[K.gb];t:{var X=s,ut=d,Q=K.gb;if(t(o!=null),t(ut!=null),t(Q!=null),Q[0]=0,12<=ut[0]&&!e(o,X[0],"RIFF")){if(e(o,X[0]+8,"WEBP")){Q=3;break t}var ct=st(o,X[0]+4);if(12>ct||4294967286<ct){Q=3;break t}if(E&&ct>ut[0]-8){Q=7;break t}Q[0]=ct,X[0]+=12,ut[0]-=12}Q=0}if(Q!=0)return Q;for(ct=0<K.gb[0],d=d[0];;){t:{var gt=o;ut=s,Q=d;var _t=F,bt=H,Tt=X=[0];if((Ot=tt=[tt])[0]=0,8>Q[0])Q=7;else{if(!e(gt,ut[0],"VP8X")){if(st(gt,ut[0]+4)!=10){Q=3;break t}if(18>Q[0]){Q=7;break t}var Ft=st(gt,ut[0]+8),se=1+Ct(gt,ut[0]+12);if(2147483648<=se*(gt=1+Ct(gt,ut[0]+15))){Q=3;break t}Tt!=null&&(Tt[0]=Ft),_t!=null&&(_t[0]=se),bt!=null&&(bt[0]=gt),ut[0]+=18,Q[0]-=18,Ot[0]=1}Q=0}}if(tt=tt[0],X=X[0],Q!=0)return Q;if(ut=!!(2&X),!ct&&tt)return 3;if(w!=null&&(w[0]=!!(16&X)),_!=null&&(_[0]=ut),L!=null&&(L[0]=0),_=F[0],X=H[0],tt&&ut&&k==null){Q=0;break}if(4>d){Q=7;break}if(ct&&tt||!ct&&!tt&&!e(o,s[0],"ALPH")){d=[d],K.na=[K.na],K.P=[K.P],K.Sa=[K.Sa];t:{Ft=o,Q=s,ct=d;var Ot=K.gb;_t=K.na,bt=K.P,Tt=K.Sa,se=22,t(Ft!=null),t(ct!=null),gt=Q[0];var ce=ct[0];for(t(_t!=null),t(Tt!=null),_t[0]=null,bt[0]=null,Tt[0]=0;;){if(Q[0]=gt,ct[0]=ce,8>ce){Q=7;break t}var or=st(Ft,gt+4);if(4294967286<or){Q=3;break t}var le=8+or+1&-2;if(se+=le,0<Ot&&se>Ot){Q=3;break t}if(!e(Ft,gt,"VP8 ")||!e(Ft,gt,"VP8L")){Q=0;break t}if(ce[0]<le){Q=7;break t}e(Ft,gt,"ALPH")||(_t[0]=Ft,bt[0]=gt+8,Tt[0]=or),gt+=le,ce-=le}}if(d=d[0],K.na=K.na[0],K.P=K.P[0],K.Sa=K.Sa[0],Q!=0)break}d=[d],K.Ja=[K.Ja],K.xa=[K.xa];t:if(Ot=o,Q=s,ct=d,_t=K.gb[0],bt=K.Ja,Tt=K.xa,Ft=Q[0],gt=!e(Ot,Ft,"VP8 "),se=!e(Ot,Ft,"VP8L"),t(Ot!=null),t(ct!=null),t(bt!=null),t(Tt!=null),8>ct[0])Q=7;else{if(gt||se){if(Ot=st(Ot,Ft+4),12<=_t&&Ot>_t-12){Q=3;break t}if(E&&Ot>ct[0]-8){Q=7;break t}bt[0]=Ot,Q[0]+=8,ct[0]-=8,Tt[0]=se}else Tt[0]=5<=ct[0]&&Ot[Ft+0]==47&&!(Ot[Ft+4]>>5),bt[0]=ct[0];Q=0}if(d=d[0],K.Ja=K.Ja[0],K.xa=K.xa[0],s=s[0],Q!=0)break;if(4294967286<K.Ja)return 3;if(L==null||ut||(L[0]=K.xa?2:1),_=[_],X=[X],K.xa){if(5>d){Q=7;break}L=_,E=X,ut=w,o==null||5>d?o=0:5<=d&&o[s+0]==47&&!(o[s+4]>>5)?(ct=[0],Ot=[0],_t=[0],ft(bt=new B,o,s,d),Nn(bt,ct,Ot,_t)?(L!=null&&(L[0]=ct[0]),E!=null&&(E[0]=Ot[0]),ut!=null&&(ut[0]=_t[0]),o=1):o=0):o=0}else{if(10>d){Q=7;break}L=X,o==null||10>d||!Ja(o,s+3,d-3)?o=0:(E=o[s+0]|o[s+1]<<8|o[s+2]<<16,ut=16383&(o[s+7]<<8|o[s+6]),o=16383&(o[s+9]<<8|o[s+8]),1&E||3<(E>>1&7)||!(E>>4&1)||E>>5>=K.Ja||!ut||!o?o=0:(_&&(_[0]=ut),L&&(L[0]=o),o=1))}if(!o||(_=_[0],X=X[0],tt&&(F[0]!=_||H[0]!=X)))return 3;k!=null&&(k[0]=K,k.offset=s-k.w,t(4294967286>s-k.w),t(k.offset==k.ha-d));break}return Q==0||Q==7&&tt&&k==null?(w!=null&&(w[0]|=K.na!=null&&0<K.na.length),m!=null&&(m[0]=_),v!=null&&(v[0]=X),0):Q}function kr(o,s,d){var m=s.width,v=s.height,w=0,_=0,L=m,k=v;if(s.Da=o!=null&&0<o.Da,s.Da&&(L=o.cd,k=o.bd,w=o.v,_=o.j,11>d||(w&=-2,_&=-2),0>w||0>_||0>=L||0>=k||w+L>m||_+k>v))return 0;if(s.v=w,s.j=_,s.va=w+L,s.o=_+k,s.U=L,s.T=k,s.da=o!=null&&0<o.da,s.da){if(!Ht(L,k,d=[o.ib],w=[o.hb]))return 0;s.ib=d[0],s.hb=w[0]}return s.ob=o!=null&&o.ob,s.Kb=o==null||!o.Sd,s.da&&(s.ob=s.ib<3*m/4&&s.hb<3*v/4,s.Kb=0),1}function Pr(o){if(o==null)return 2;if(11>o.S){var s=o.f.RGBA;s.fb+=(o.height-1)*s.A,s.A=-s.A}else s=o.f.kb,o=o.height,s.O+=(o-1)*s.fa,s.fa=-s.fa,s.N+=(o-1>>1)*s.Ab,s.Ab=-s.Ab,s.W+=(o-1>>1)*s.Db,s.Db=-s.Db,s.F!=null&&(s.J+=(o-1)*s.lb,s.lb=-s.lb);return 0}function yi(o,s,d,m){if(m==null||0>=o||0>=s)return 2;if(d!=null){if(d.Da){var v=d.cd,w=d.bd,_=-2&d.v,L=-2&d.j;if(0>_||0>L||0>=v||0>=w||_+v>o||L+w>s)return 2;o=v,s=w}if(d.da){if(!Ht(o,s,v=[d.ib],w=[d.hb]))return 2;o=v[0],s=w[0]}}m.width=o,m.height=s;t:{var k=m.width,F=m.height;if(o=m.S,0>=k||0>=F||!(o>=no&&13>o))o=2;else{if(0>=m.Rd&&m.sd==null){_=w=v=s=0;var H=(L=k*$l[o])*F;if(11>o||(w=(F+1)/2*(s=(k+1)/2),o==12&&(_=(v=k)*F)),(F=a(H+2*w+_))==null){o=1;break t}m.sd=F,11>o?((k=m.f.RGBA).eb=F,k.fb=0,k.A=L,k.size=H):((k=m.f.kb).y=F,k.O=0,k.fa=L,k.Fd=H,k.f=F,k.N=0+H,k.Ab=s,k.Cd=w,k.ea=F,k.W=0+H+w,k.Db=s,k.Ed=w,o==12&&(k.F=F,k.J=0+H+2*w),k.Tc=_,k.lb=v)}if(s=1,v=m.S,w=m.width,_=m.height,v>=no&&13>v)if(11>v)o=m.f.RGBA,s&=(L=Math.abs(o.A))*(_-1)+w<=o.size,s&=L>=w*$l[v],s&=o.eb!=null;else{o=m.f.kb,L=(w+1)/2,H=(_+1)/2,k=Math.abs(o.fa),F=Math.abs(o.Ab);var tt=Math.abs(o.Db),E=Math.abs(o.lb),K=E*(_-1)+w;s&=k*(_-1)+w<=o.Fd,s&=F*(H-1)+L<=o.Cd,s=(s&=tt*(H-1)+L<=o.Ed)&k>=w&F>=L&tt>=L,s&=o.y!=null,s&=o.f!=null,s&=o.ea!=null,v==12&&(s&=E>=w,s&=K<=o.Tc,s&=o.F!=null)}else s=0;o=s?0:2}}return o!=0||d!=null&&d.fd&&(o=Pr(m)),o}var pn=64,Yn=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],La=24,Jn=32,Ve=8,ls=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];kt("Predictor0","PredictorAdd0"),R.Predictor0=function(){return **********},R.Predictor1=function(o){return o},R.Predictor2=function(o,s,d){return s[d+0]},R.Predictor3=function(o,s,d){return s[d+1]},R.Predictor4=function(o,s,d){return s[d-1]},R.Predictor5=function(o,s,d){return zt(zt(o,s[d+1]),s[d+0])},R.Predictor6=function(o,s,d){return zt(o,s[d-1])},R.Predictor7=function(o,s,d){return zt(o,s[d+0])},R.Predictor8=function(o,s,d){return zt(s[d-1],s[d+0])},R.Predictor9=function(o,s,d){return zt(s[d+0],s[d+1])},R.Predictor10=function(o,s,d){return zt(zt(o,s[d-1]),zt(s[d+0],s[d+1]))},R.Predictor11=function(o,s,d){var m=s[d+0];return 0>=Qt(m>>24&255,o>>24&255,(s=s[d-1])>>24&255)+Qt(m>>16&255,o>>16&255,s>>16&255)+Qt(m>>8&255,o>>8&255,s>>8&255)+Qt(255&m,255&o,255&s)?m:o},R.Predictor12=function(o,s,d){var m=s[d+0];return(ge((o>>24&255)+(m>>24&255)-((s=s[d-1])>>24&255))<<24|ge((o>>16&255)+(m>>16&255)-(s>>16&255))<<16|ge((o>>8&255)+(m>>8&255)-(s>>8&255))<<8|ge((255&o)+(255&m)-(255&s)))>>>0},R.Predictor13=function(o,s,d){var m=s[d-1];return(Zt((o=zt(o,s[d+0]))>>24&255,m>>24&255)<<24|Zt(o>>16&255,m>>16&255)<<16|Zt(o>>8&255,m>>8&255)<<8|Zt(255&o,255&m))>>>0};var gn=R.PredictorAdd0;R.PredictorAdd1=Ae,kt("Predictor2","PredictorAdd2"),kt("Predictor3","PredictorAdd3"),kt("Predictor4","PredictorAdd4"),kt("Predictor5","PredictorAdd5"),kt("Predictor6","PredictorAdd6"),kt("Predictor7","PredictorAdd7"),kt("Predictor8","PredictorAdd8"),kt("Predictor9","PredictorAdd9"),kt("Predictor10","PredictorAdd10"),kt("Predictor11","PredictorAdd11"),kt("Predictor12","PredictorAdd12"),kt("Predictor13","PredictorAdd13");var mn=R.PredictorAdd2;Ut("ColorIndexInverseTransform","MapARGB","32b",function(o){return o>>8&255},function(o){return o}),Ut("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(o){return o},function(o){return o>>8&255});var zi,Na=R.ColorIndexInverseTransform,Hi=R.MapARGB,Wi=R.VP8LColorIndexInverseTransformAlpha,wi=R.MapAlpha,xi=R.VP8LPredictorsAdd=[];xi.length=16,(R.VP8LPredictors=[]).length=16,(R.VP8LPredictorsAdd_C=[]).length=16,(R.VP8LPredictors_C=[]).length=16;var In,Sa,Or,vn,Fe,Ee,Ke,Ye,On,Gi,bn,ka,yl,wl,xl,_l,Al,Ll,Nl,Sl,kl,Pl,Cl,Fl,Pa,El,Il,Ol,Dl=a(511),Bl=a(2041),jl=a(225),Tl=a(767),Rl=0,us=Bl,ro=jl,Dr=Tl,Kr=Dl,no=0,io=1,Ml=2,ao=3,oo=4,hs=5,ql=6,fs=7,cs=8,so=9,ds=10,Df=[2,3,7],Bf=[3,3,11],Ul=[280,256,256,256,40],jf=[0,1,1,1,0],Tf=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Rf=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Mf=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],qf=8,ps=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],gs=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],Ca=null,Uf=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],zf=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],zl=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],Hf=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],Wf=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],Gf=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],Vf=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],$n=[],en=[],Dn=[],Kf=1,Hl=2,Xn=[],Yr=[];tn("UpsampleRgbLinePair",En,3),tn("UpsampleBgrLinePair",Mi,3),tn("UpsampleRgbaLinePair",eo,4),tn("UpsampleBgraLinePair",qi,4),tn("UpsampleArgbLinePair",to,4),tn("UpsampleRgba4444LinePair",Ar,2),tn("UpsampleRgb565LinePair",xa,2);var Yf=R.UpsampleRgbLinePair,Jf=R.UpsampleBgrLinePair,Wl=R.UpsampleRgbaLinePair,Gl=R.UpsampleBgraLinePair,Vl=R.UpsampleArgbLinePair,Kl=R.UpsampleRgba4444LinePair,$f=R.UpsampleRgb565LinePair,lo=16,uo=1<<lo-1,Fa=-227,ms=482,Xf=6,Yl=0,Zf=a(256),Qf=a(256),tc=a(256),ec=a(256),rc=a(ms-Fa),nc=a(ms-Fa);Vr("YuvToRgbRow",En,3),Vr("YuvToBgrRow",Mi,3),Vr("YuvToRgbaRow",eo,4),Vr("YuvToBgraRow",qi,4),Vr("YuvToArgbRow",to,4),Vr("YuvToRgba4444Row",Ar,2),Vr("YuvToRgb565Row",xa,2);var Jl=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],ho=[0,2,8],ic=[8,7,6,4,4,2,2,2,1,1,1,1],ac=1;this.WebPDecodeRGBA=function(o,s,d,m,v){var w=io,_=new Bt,L=new Se;_.ba=L,L.S=w,L.width=[L.width],L.height=[L.height];var k=L.width,F=L.height,H=new Jt;if(H==null||o==null)var tt=2;else t(H!=null),tt=Ui(o,s,d,H.width,H.height,H.Pd,H.Qd,H.format,null);if(tt!=0?k=0:(k!=null&&(k[0]=H.width[0]),F!=null&&(F[0]=H.height[0]),k=1),k){L.width=L.width[0],L.height=L.height[0],m!=null&&(m[0]=L.width),v!=null&&(v[0]=L.height);t:{if(m=new Oi,(v=new Kt).data=o,v.w=s,v.ha=d,v.kd=1,s=[0],t(v!=null),((o=Ui(v.data,v.w,v.ha,null,null,null,s,null,v))==0||o==7)&&s[0]&&(o=4),(s=o)==0){if(t(_!=null),m.data=v.data,m.w=v.w+v.offset,m.ha=v.ha-v.offset,m.put=Gt,m.ac=un,m.bc=Ln,m.ma=_,v.xa){if((o=li())==null){_=1;break t}if((function(E,K){var X=[0],ut=[0],Q=[0];e:for(;;){if(E==null)return 0;if(K==null)return E.a=2,0;if(E.l=K,E.a=0,ft(E.m,K.data,K.w,K.ha),!Nn(E.m,X,ut,Q)){E.a=3;break e}if(E.xb=Hl,K.width=X[0],K.height=ut[0],!fn(X[0],ut[0],1,E,null))break e;return 1}return t(E.a!=0),0})(o,m)){if(m=(s=yi(m.width,m.height,_.Oa,_.ba))==0){e:{m=o;r:for(;;){if(m==null){m=0;break e}if(t(m.s.yc!=null),t(m.s.Ya!=null),t(0<m.s.Wb),t((d=m.l)!=null),t((v=d.ma)!=null),m.xb!=0){if(m.ca=v.ba,m.tb=v.tb,t(m.ca!=null),!kr(v.Oa,d,ao)){m.a=2;break r}if(!ui(m,d.width)||d.da)break r;if((d.da||ee(m.ca.S))&&bi(),11>m.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),m.ca.f.kb.F!=null&&bi()),m.Pb&&0<m.s.ua&&m.s.vb.X==null&&!j(m.s.vb,m.s.Wa.Xa)){m.a=1;break r}m.xb=0}if(!Zr(m,m.V,m.Ba,m.c,m.i,d.o,zn))break r;v.Dc=m.Ma,m=1;break e}t(m.a!=0),m=0}m=!m}m&&(s=o.a)}else s=o.a}else{if((o=new Vo)==null){_=1;break t}if(o.Fa=v.na,o.P=v.P,o.qc=v.Sa,$a(o,m)){if((s=yi(m.width,m.height,_.Oa,_.ba))==0){if(o.Aa=0,d=_.Oa,t((v=o)!=null),d!=null){if(0<(k=0>(k=d.Md)?0:100<k?255:255*k/100)){for(F=H=0;4>F;++F)12>(tt=v.pb[F]).lc&&(tt.ia=k*ic[0>tt.lc?0:tt.lc]>>3),H|=tt.ia;H&&(alert("todo:VP8InitRandom"),v.ia=1)}v.Ga=d.Id,100<v.Ga?v.Ga=100:0>v.Ga&&(v.Ga=0)}(function(E,K){if(E==null)return 0;if(K==null)return tr(E,2,"NULL VP8Io parameter in VP8Decode().");if(!E.cb&&!$a(E,K))return 0;if(t(E.cb),K.ac==null||K.ac(K)){K.ob&&(E.L=0);var X=ho[E.L];if(E.L==2?(E.yb=0,E.zb=0):(E.yb=K.v-X>>4,E.zb=K.j-X>>4,0>E.yb&&(E.yb=0),0>E.zb&&(E.zb=0)),E.Va=K.o+15+X>>4,E.Hb=K.va+15+X>>4,E.Hb>E.za&&(E.Hb=E.za),E.Va>E.Ub&&(E.Va=E.Ub),0<E.L){var ut=E.ed;for(X=0;4>X;++X){var Q;if(E.Qa.Cb){var ct=E.Qa.Lb[X];E.Qa.Fb||(ct+=ut.Tb)}else ct=ut.Tb;for(Q=0;1>=Q;++Q){var gt=E.gd[X][Q],_t=ct;if(ut.Pc&&(_t+=ut.vd[0],Q&&(_t+=ut.od[0])),0<(_t=0>_t?0:63<_t?63:_t)){var bt=_t;0<ut.wb&&(bt=4<ut.wb?bt>>2:bt>>1)>9-ut.wb&&(bt=9-ut.wb),1>bt&&(bt=1),gt.dd=bt,gt.tc=2*_t+bt,gt.ld=40<=_t?2:15<=_t?1:0}else gt.tc=0;gt.La=Q}}}X=0}else tr(E,6,"Frame setup failed"),X=E.a;if(X=X==0){if(X){E.$c=0,0<E.Aa||(E.Ic=ac);e:{X=E.Ic,ut=4*(bt=E.za);var Tt=32*bt,Ft=bt+1,se=0<E.L?bt*(0<E.Aa?2:1):0,Ot=(E.Aa==2?2:1)*bt;if((gt=ut+832+(Q=3*(16*X+ho[E.L])/2*Tt)+(ct=E.Fa!=null&&0<E.Fa.length?E.Kc.c*E.Kc.i:0))!=gt)X=0;else{if(gt>E.Vb){if(E.Vb=0,E.Ec=a(gt),E.Fc=0,E.Ec==null){X=tr(E,1,"no memory during frame initialization.");break e}E.Vb=gt}gt=E.Ec,_t=E.Fc,E.Ac=gt,E.Bc=_t,_t+=ut,E.Gd=u(Tt,Ka),E.Hd=0,E.rb=u(Ft+1,sa),E.sb=1,E.wa=se?u(se,Gr):null,E.Y=0,E.D.Nb=0,E.D.wa=E.wa,E.D.Y=E.Y,0<E.Aa&&(E.D.Y+=bt),t(!0),E.oc=gt,E.pc=_t,_t+=832,E.ya=u(Ot,la),E.aa=0,E.D.ya=E.ya,E.D.aa=E.aa,E.Aa==2&&(E.D.aa+=bt),E.R=16*bt,E.B=8*bt,bt=(Tt=ho[E.L])*E.R,Tt=Tt/2*E.B,E.sa=gt,E.ta=_t+bt,E.qa=E.sa,E.ra=E.ta+16*X*E.R+Tt,E.Ha=E.qa,E.Ia=E.ra+8*X*E.B+Tt,E.$c=0,_t+=Q,E.mb=ct?gt:null,E.nb=ct?_t:null,t(_t+ct<=E.Fc+E.Vb),hi(E),i(E.Ac,E.Bc,0,ut),X=1}}if(X){if(K.ka=0,K.y=E.sa,K.O=E.ta,K.f=E.qa,K.N=E.ra,K.ea=E.Ha,K.Vd=E.Ia,K.fa=E.R,K.Rc=E.B,K.F=null,K.J=0,!Rl){for(X=-255;255>=X;++X)Dl[255+X]=0>X?-X:X;for(X=-1020;1020>=X;++X)Bl[1020+X]=-128>X?-128:127<X?127:X;for(X=-112;112>=X;++X)jl[112+X]=-16>X?-16:15<X?15:X;for(X=-255;510>=X;++X)Tl[255+X]=0>X?0:255<X?255:X;Rl=1}Ke=Za,Ye=Yo,Gi=Jo,bn=cn,ka=Xa,On=Ko,yl=c,wl=b,xl=J,_l=pt,Al=I,Ll=q,Nl=yt,Sl=It,kl=va,Pl=vi,Cl=ba,Fl=ya,en[0]=rs,en[1]=ha,en[2]=ts,en[3]=es,en[4]=ns,en[5]=Pn,en[6]=kn,en[7]=is,en[8]=Di,en[9]=as,$n[0]=Wn,$n[1]=Xo,$n[2]=Zo,$n[3]=fa,$n[4]=Qo,$n[5]=di,$n[6]=ca,Dn[0]=ji,Dn[1]=$o,Dn[2]=da,Dn[3]=pi,Dn[4]=pa,Dn[5]=os,Dn[6]=ga,X=1}else X=0}X&&(X=(function(ce,or){for(ce.M=0;ce.M<ce.Va;++ce.M){var le,Dt=ce.Jc[ce.M&ce.Xb],St=ce.m,Je=ce;for(le=0;le<Je.za;++le){var re=St,de=Je,Oe=de.Ac,pr=de.Bc+4*le,Lr=de.zc,Ue=de.ya[de.aa+le];if(de.Qa.Bb?Ue.$b=mt(re,de.Pa.jb[0])?2+mt(re,de.Pa.jb[2]):mt(re,de.Pa.jb[1]):Ue.$b=0,de.kc&&(Ue.Ad=mt(re,de.Bd)),Ue.Za=!mt(re,145)+0,Ue.Za){var br=Ue.Ob,Nr=0;for(de=0;4>de;++de){var sr,pe=Lr[0+de];for(sr=0;4>sr;++sr){pe=Wf[Oe[pr+sr]][pe];for(var ye=zl[mt(re,pe[0])];0<ye;)ye=zl[2*ye+mt(re,pe[ye])];pe=-ye,Oe[pr+sr]=pe}n(br,Nr,Oe,pr,4),Nr+=4,Lr[0+de]=pe}}else pe=mt(re,156)?mt(re,128)?1:3:mt(re,163)?2:0,Ue.Ob[0]=pe,i(Oe,pr,pe,4),i(Lr,0,pe,4);Ue.Dd=mt(re,142)?mt(re,114)?mt(re,183)?1:3:2:0}if(Je.m.Ka)return tr(ce,7,"Premature end-of-partition0 encountered.");for(;ce.ja<ce.za;++ce.ja){if(Je=Dt,re=(St=ce).rb[St.sb-1],Oe=St.rb[St.sb+St.ja],le=St.ya[St.aa+St.ja],pr=St.kc?le.Ad:0)re.la=Oe.la=0,le.Za||(re.Na=Oe.Na=0),le.Hc=0,le.Gc=0,le.ia=0;else{var ze,we;if(re=Oe,Oe=Je,pr=St.Pa.Xc,Lr=St.ya[St.aa+St.ja],Ue=St.pb[Lr.$b],de=Lr.ad,br=0,Nr=St.rb[St.sb-1],pe=sr=0,i(de,br,0,384),Lr.Za)var gr=0,rn=pr[3];else{ye=a(16);var $e=re.Na+Nr.Na;if($e=Ca(Oe,pr[1],$e,Ue.Eb,0,ye,0),re.Na=Nr.Na=(0<$e)+0,1<$e)Ke(ye,0,de,br);else{var Cr=ye[0]+3>>3;for(ye=0;256>ye;ye+=16)de[br+ye]=Cr}gr=1,rn=pr[0]}var ke=15&re.la,lr=15&Nr.la;for(ye=0;4>ye;++ye){var Br=1&lr;for(Cr=we=0;4>Cr;++Cr)ke=ke>>1|(Br=($e=Ca(Oe,rn,$e=Br+(1&ke),Ue.Sc,gr,de,br))>gr)<<7,we=we<<2|(3<$e?3:1<$e?2:de[br+0]!=0),br+=16;ke>>=4,lr=lr>>1|Br<<7,sr=(sr<<8|we)>>>0}for(rn=ke,gr=lr>>4,ze=0;4>ze;ze+=2){for(we=0,ke=re.la>>4+ze,lr=Nr.la>>4+ze,ye=0;2>ye;++ye){for(Br=1&lr,Cr=0;2>Cr;++Cr)$e=Br+(1&ke),ke=ke>>1|(Br=0<($e=Ca(Oe,pr[2],$e,Ue.Qc,0,de,br)))<<3,we=we<<2|(3<$e?3:1<$e?2:de[br+0]!=0),br+=16;ke>>=2,lr=lr>>1|Br<<5}pe|=we<<4*ze,rn|=ke<<4<<ze,gr|=(240&lr)<<ze}re.la=rn,Nr.la=gr,Lr.Hc=sr,Lr.Gc=pe,Lr.ia=43690&pe?0:Ue.ia,pr=!(sr|pe)}if(0<St.L&&(St.wa[St.Y+St.ja]=St.gd[le.$b][le.Za],St.wa[St.Y+St.ja].La|=!pr),Je.Ka)return tr(ce,7,"Premature end-of-file encountered.")}if(hi(ce),St=or,Je=1,le=(Dt=ce).D,re=0<Dt.L&&Dt.M>=Dt.zb&&Dt.M<=Dt.Va,Dt.Aa==0)e:{if(le.M=Dt.M,le.uc=re,Kn(Dt,le),Je=1,le=(we=Dt.D).Nb,re=(pe=ho[Dt.L])*Dt.R,Oe=pe/2*Dt.B,ye=16*le*Dt.R,Cr=8*le*Dt.B,pr=Dt.sa,Lr=Dt.ta-re+ye,Ue=Dt.qa,de=Dt.ra-Oe+Cr,br=Dt.Ha,Nr=Dt.Ia-Oe+Cr,lr=(ke=we.M)==0,sr=ke>=Dt.Va-1,Dt.Aa==2&&Kn(Dt,we),we.uc)for(Br=($e=Dt).D.M,t($e.D.uc),we=$e.yb;we<$e.Hb;++we){gr=we,rn=Br;var Sr=(jr=(mr=$e).D).Nb;ze=mr.R;var jr=jr.wa[jr.Y+gr],Tr=mr.sa,Fr=mr.ta+16*Sr*ze+16*gr,Rr=jr.dd,Ie=jr.tc;if(Ie!=0)if(t(3<=Ie),mr.L==1)0<gr&&Pl(Tr,Fr,ze,Ie+4),jr.La&&Fl(Tr,Fr,ze,Ie),0<rn&&kl(Tr,Fr,ze,Ie+4),jr.La&&Cl(Tr,Fr,ze,Ie);else{var Mr=mr.B,nn=mr.qa,_i=mr.ra+8*Sr*Mr+8*gr,Bn=mr.Ha,mr=mr.Ia+8*Sr*Mr+8*gr;Sr=jr.ld,0<gr&&(wl(Tr,Fr,ze,Ie+4,Rr,Sr),_l(nn,_i,Bn,mr,Mr,Ie+4,Rr,Sr)),jr.La&&(Ll(Tr,Fr,ze,Ie,Rr,Sr),Sl(nn,_i,Bn,mr,Mr,Ie,Rr,Sr)),0<rn&&(yl(Tr,Fr,ze,Ie+4,Rr,Sr),xl(nn,_i,Bn,mr,Mr,Ie+4,Rr,Sr)),jr.La&&(Al(Tr,Fr,ze,Ie,Rr,Sr),Nl(nn,_i,Bn,mr,Mr,Ie,Rr,Sr))}}if(Dt.ia&&alert("todo:DitherRow"),St.put!=null){if(we=16*ke,ke=16*(ke+1),lr?(St.y=Dt.sa,St.O=Dt.ta+ye,St.f=Dt.qa,St.N=Dt.ra+Cr,St.ea=Dt.Ha,St.W=Dt.Ia+Cr):(we-=pe,St.y=pr,St.O=Lr,St.f=Ue,St.N=de,St.ea=br,St.W=Nr),sr||(ke-=pe),ke>St.o&&(ke=St.o),St.F=null,St.J=null,Dt.Fa!=null&&0<Dt.Fa.length&&we<ke&&(St.J=dr(Dt,St,we,ke-we),St.F=Dt.mb,St.F==null&&St.F.length==0)){Je=tr(Dt,3,"Could not decode alpha data.");break e}we<St.j&&(pe=St.j-we,we=St.j,t(!(1&pe)),St.O+=Dt.R*pe,St.N+=Dt.B*(pe>>1),St.W+=Dt.B*(pe>>1),St.F!=null&&(St.J+=St.width*pe)),we<ke&&(St.O+=St.v,St.N+=St.v>>1,St.W+=St.v>>1,St.F!=null&&(St.J+=St.v),St.ka=we-St.j,St.U=St.va-St.v,St.T=ke-we,Je=St.put(St))}le+1!=Dt.Ic||sr||(n(Dt.sa,Dt.ta-re,pr,Lr+16*Dt.R,re),n(Dt.qa,Dt.ra-Oe,Ue,de+8*Dt.B,Oe),n(Dt.Ha,Dt.Ia-Oe,br,Nr+8*Dt.B,Oe))}if(!Je)return tr(ce,6,"Output aborted.")}return 1})(E,K)),K.bc!=null&&K.bc(K),X&=1}return X?(E.cb=0,X):0})(o,m)||(s=o.a)}}else s=o.a}s==0&&_.Oa!=null&&_.Oa.fd&&(s=Pr(_.ba))}_=s}w=_!=0?null:11>w?L.f.RGBA.eb:L.f.kb.y}else w=null;return w};var $l=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function f(R,et){for(var N="",O=0;O<4;O++)N+=String.fromCharCode(R[et++]);return N}function p(R,et){return R[et+0]|R[et+1]<<8}function y(R,et){return(R[et+0]|R[et+1]<<8|R[et+2]<<16)>>>0}function x(R,et){return(R[et+0]|R[et+1]<<8|R[et+2]<<16|R[et+3]<<24)>>>0}new h;var g=[0],P=[0],C=[],D=new h,S=r,W=(function(R,et){var N={},O=0,G=!1,U=0,nt=0;if(N.frames=[],!(function(A,B){for(var T=0;T<4;T++)if(A[B+T]!="RIFF".charCodeAt(T))return!0;return!1})(R,et)){for(x(R,et+=4),et+=8;et<R.length;){var ht=f(R,et),dt=x(R,et+=4);et+=4;var rt=dt+(1&dt);switch(ht){case"VP8 ":case"VP8L":N.frames[O]===void 0&&(N.frames[O]={}),(wt=N.frames[O]).src_off=G?nt:et-8,wt.src_size=U+dt+8,O++,G&&(G=!1,U=0,nt=0);break;case"VP8X":(wt=N.header={}).feature_flags=R[et];var ft=et+4;wt.canvas_width=1+y(R,ft),ft+=3,wt.canvas_height=1+y(R,ft),ft+=3;break;case"ALPH":G=!0,U=rt+8,nt=et-8;break;case"ANIM":(wt=N.header).bgcolor=x(R,et),ft=et+4,wt.loop_count=p(R,ft),ft+=2;break;case"ANMF":var Nt,wt;(wt=N.frames[O]={}).offset_x=2*y(R,et),et+=3,wt.offset_y=2*y(R,et),et+=3,wt.width=1+y(R,et),et+=3,wt.height=1+y(R,et),et+=3,wt.duration=y(R,et),et+=3,Nt=R[et++],wt.dispose=1&Nt,wt.blend=Nt>>1&1}ht!="ANMF"&&(et+=rt)}return N}})(S,0);W.response=S,W.rgbaoutput=!0,W.dataurl=!1;var z=W.header?W.header:null,M=W.frames?W.frames:null;if(z){z.loop_counter=z.loop_count,g=[z.canvas_height],P=[z.canvas_width];for(var at=0;at<M.length&&M[at].blend!=0;at++);}var vt=M[0],ot=D.WebPDecodeRGBA(S,vt.src_off,vt.src_size,P,g);vt.rgba=ot,vt.imgwidth=P[0],vt.imgheight=g[0];for(var $=0;$<P[0]*g[0]*4;$++)C[$]=ot[$];return this.width=P,this.height=g,this.data=C,this}(function(r){var t,e,n,i,a,u,l,h,f,p=function(A){return A=A||{},this.isStrokeTransparent=A.isStrokeTransparent||!1,this.strokeOpacity=A.strokeOpacity||1,this.strokeStyle=A.strokeStyle||"#000000",this.fillStyle=A.fillStyle||"#000000",this.isFillTransparent=A.isFillTransparent||!1,this.fillOpacity=A.fillOpacity||1,this.font=A.font||"10px sans-serif",this.textBaseline=A.textBaseline||"alphabetic",this.textAlign=A.textAlign||"left",this.lineWidth=A.lineWidth||1,this.lineJoin=A.lineJoin||"miter",this.lineCap=A.lineCap||"butt",this.path=A.path||[],this.transform=A.transform!==void 0?A.transform.clone():new h,this.globalCompositeOperation=A.globalCompositeOperation||"normal",this.globalAlpha=A.globalAlpha||1,this.clip_path=A.clip_path||[],this.currentPoint=A.currentPoint||new u,this.miterLimit=A.miterLimit||10,this.lastPoint=A.lastPoint||new u,this.lineDashOffset=A.lineDashOffset||0,this.lineDash=A.lineDash||[],this.margin=A.margin||[0,0,0,0],this.prevPageLastElemOffset=A.prevPageLastElemOffset||0,this.ignoreClearRect=typeof A.ignoreClearRect!="boolean"||A.ignoreClearRect,this};r.events.push(["initialized",function(){this.context2d=new y(this),t=this.internal.f2,e=this.internal.getCoordinateString,n=this.internal.getVerticalCoordinateString,i=this.internal.getHorizontalCoordinate,a=this.internal.getVerticalCoordinate,u=this.internal.Point,l=this.internal.Rectangle,h=this.internal.Matrix,f=new p}]);var y=function(A){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var B=A;Object.defineProperty(this,"pdf",{get:function(){return B}});var T=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return T},set:function(st){T=!!st}});var V=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return V},set:function(st){V=!!st}});var Y=0;Object.defineProperty(this,"posX",{get:function(){return Y},set:function(st){isNaN(st)||(Y=st)}});var Z=0;Object.defineProperty(this,"posY",{get:function(){return Z},set:function(st){isNaN(st)||(Z=st)}}),Object.defineProperty(this,"margin",{get:function(){return f.margin},set:function(st){var j;typeof st=="number"?j=[st,st,st,st]:((j=new Array(4))[0]=st[0],j[1]=st.length>=2?st[1]:j[0],j[2]=st.length>=3?st[2]:j[0],j[3]=st.length>=4?st[3]:j[1]),f.margin=j}});var lt=!1;Object.defineProperty(this,"autoPaging",{get:function(){return lt},set:function(st){lt=st}});var it=0;Object.defineProperty(this,"lastBreak",{get:function(){return it},set:function(st){it=st}});var mt=[];Object.defineProperty(this,"pageBreaks",{get:function(){return mt},set:function(st){mt=st}}),Object.defineProperty(this,"ctx",{get:function(){return f},set:function(st){st instanceof p&&(f=st)}}),Object.defineProperty(this,"path",{get:function(){return f.path},set:function(st){f.path=st}});var Lt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Lt},set:function(st){Lt=st}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(st){var j;j=x(st),this.ctx.fillStyle=j.style,this.ctx.isFillTransparent=j.a===0,this.ctx.fillOpacity=j.a,this.pdf.setFillColor(j.r,j.g,j.b,{a:j.a}),this.pdf.setTextColor(j.r,j.g,j.b,{a:j.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(st){var j=x(st);this.ctx.strokeStyle=j.style,this.ctx.isStrokeTransparent=j.a===0,this.ctx.strokeOpacity=j.a,j.a===0?this.pdf.setDrawColor(255,255,255):(j.a,this.pdf.setDrawColor(j.r,j.g,j.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(st){["butt","round","square"].indexOf(st)!==-1&&(this.ctx.lineCap=st,this.pdf.setLineCap(st))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(st){isNaN(st)||(this.ctx.lineWidth=st,this.pdf.setLineWidth(st))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(st){["bevel","round","miter"].indexOf(st)!==-1&&(this.ctx.lineJoin=st,this.pdf.setLineJoin(st))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(st){isNaN(st)||(this.ctx.miterLimit=st,this.pdf.setMiterLimit(st))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(st){this.ctx.textBaseline=st}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(st){["right","end","center","left","start"].indexOf(st)!==-1&&(this.ctx.textAlign=st)}});var Pt=null,Ct=null;Object.defineProperty(this,"fontFaces",{get:function(){return Ct},set:function(st){Pt=null,Ct=st}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(st){var j;if(this.ctx.font=st,(j=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(st))!==null){var he=j[1];j[2];var ne=j[3],Ht=j[4];j[5];var At=j[6],Mt=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(Ht)[2];Ht=Math.floor(Mt==="px"?parseFloat(Ht)*this.pdf.internal.scaleFactor:Mt==="em"?parseFloat(Ht)*this.pdf.getFontSize():parseFloat(Ht)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(Ht);var kt=(function(Vt){var Ut,Ce,me=[],jt=Vt.trim();if(jt==="")return Gs;if(jt in oh)return[oh[jt]];for(;jt!=="";){switch(Ce=null,Ut=(jt=lh(jt)).charAt(0)){case'"':case"'":Ce=z2(jt.substring(1),Ut);break;default:Ce=H2(jt)}if(Ce===null||(me.push(Ce[0]),(jt=lh(Ce[1]))!==""&&jt.charAt(0)!==","))return Gs;jt=jt.replace(/^,/,"")}return me})(At);if(this.fontFaces){var ie=(function(Vt,Ut){if(Pt===null){var Ce=(function(me){var jt=[];return Object.keys(me).forEach(function(te){me[te].forEach(function(Ne){var Wt=null;switch(Ne){case"bold":Wt={family:te,weight:"bold"};break;case"italic":Wt={family:te,style:"italic"};break;case"bolditalic":Wt={family:te,weight:"bold",style:"italic"};break;case"":case"normal":Wt={family:te}}Wt!==null&&(Wt.ref={name:te,style:Ne},jt.push(Wt))})}),jt})(Vt.getFontList());Pt=(function(me){for(var jt={},te=0;te<me.length;++te){var Ne=Ws(me[te]),Wt=Ne.family,fe=Ne.stretch,ee=Ne.style,We=Ne.weight;jt[Wt]=jt[Wt]||{},jt[Wt][fe]=jt[Wt][fe]||{},jt[Wt][fe][ee]=jt[Wt][fe][ee]||{},jt[Wt][fe][ee][We]=Ne}return jt})(Ce.concat(Ut))}return Pt})(this.pdf,this.fontFaces),zt=kt.map(function(Vt){return{family:Vt,stretch:"normal",weight:ne,style:he}}),ge=(function(Vt,Ut,Ce){for(var me=(Ce=Ce||{}).defaultFontFamily||"times",jt=Object.assign({},U2,Ce.genericFontFamilies||{}),te=null,Ne=null,Wt=0;Wt<Ut.length;++Wt)if(jt[(te=Ws(Ut[Wt])).family]&&(te.family=jt[te.family]),Vt.hasOwnProperty(te.family)){Ne=Vt[te.family];break}if(!(Ne=Ne||Vt[me]))throw new Error("Could not find a font-family for the rule '"+sh(te)+"' and default family '"+me+"'.");if(Ne=(function(fe,ee){if(ee[fe])return ee[fe];var We=hl[fe],nr=We<=hl.normal?-1:1,Se=ah(ee,vf,We,nr);if(!Se)throw new Error("Could not find a matching font-stretch value for "+fe);return Se})(te.stretch,Ne),Ne=(function(fe,ee){if(ee[fe])return ee[fe];for(var We=mf[fe],nr=0;nr<We.length;++nr)if(ee[We[nr]])return ee[We[nr]];throw new Error("Could not find a matching font-style for "+fe)})(te.style,Ne),!(Ne=(function(fe,ee){if(ee[fe])return ee[fe];if(fe===400&&ee[500])return ee[500];if(fe===500&&ee[400])return ee[400];var We=q2[fe],nr=ah(ee,bf,We,fe<400?-1:1);if(!nr)throw new Error("Could not find a matching font-weight for value "+fe);return nr})(te.weight,Ne)))throw new Error("Failed to resolve a font for the rule '"+sh(te)+"'.");return Ne})(ie,zt);this.pdf.setFont(ge.ref.name,ge.ref.style)}else{var Zt="";(ne==="bold"||parseInt(ne,10)>=700||he==="bold")&&(Zt="bold"),he==="italic"&&(Zt+="italic"),Zt.length===0&&(Zt="normal");for(var Qt="",Ae={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ae=0;ae<kt.length;ae++){if(this.pdf.internal.getFont(kt[ae],Zt,{noFallback:!0,disableWarning:!0})!==void 0){Qt=kt[ae];break}if(Zt==="bolditalic"&&this.pdf.internal.getFont(kt[ae],"bold",{noFallback:!0,disableWarning:!0})!==void 0)Qt=kt[ae],Zt="bold";else if(this.pdf.internal.getFont(kt[ae],"normal",{noFallback:!0,disableWarning:!0})!==void 0){Qt=kt[ae],Zt="normal";break}}if(Qt===""){for(var qt=0;qt<kt.length;qt++)if(Ae[kt[qt]]){Qt=Ae[kt[qt]];break}}Qt=Qt===""?"Times":Qt,this.pdf.setFont(Qt,Zt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(st){this.ctx.globalCompositeOperation=st}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(st){this.ctx.globalAlpha=st}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(st){this.ctx.lineDashOffset=st,wt.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(st){this.ctx.lineDash=st,wt.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(st){this.ctx.ignoreClearRect=!!st}})};y.prototype.setLineDash=function(A){this.lineDash=A},y.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},y.prototype.fill=function(){M.call(this,"fill",!1)},y.prototype.stroke=function(){M.call(this,"stroke",!1)},y.prototype.beginPath=function(){this.path=[{type:"begin"}]},y.prototype.moveTo=function(A,B){if(isNaN(A)||isNaN(B))throw Pe.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var T=this.ctx.transform.applyToPoint(new u(A,B));this.path.push({type:"mt",x:T.x,y:T.y}),this.ctx.lastPoint=new u(A,B)},y.prototype.closePath=function(){var A=new u(0,0),B=0;for(B=this.path.length-1;B!==-1;B--)if(this.path[B].type==="begin"&&_e(this.path[B+1])==="object"&&typeof this.path[B+1].x=="number"){A=new u(this.path[B+1].x,this.path[B+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new u(A.x,A.y)},y.prototype.lineTo=function(A,B){if(isNaN(A)||isNaN(B))throw Pe.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var T=this.ctx.transform.applyToPoint(new u(A,B));this.path.push({type:"lt",x:T.x,y:T.y}),this.ctx.lastPoint=new u(T.x,T.y)},y.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),M.call(this,null,!0)},y.prototype.quadraticCurveTo=function(A,B,T,V){if(isNaN(T)||isNaN(V)||isNaN(A)||isNaN(B))throw Pe.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var Y=this.ctx.transform.applyToPoint(new u(T,V)),Z=this.ctx.transform.applyToPoint(new u(A,B));this.path.push({type:"qct",x1:Z.x,y1:Z.y,x:Y.x,y:Y.y}),this.ctx.lastPoint=new u(Y.x,Y.y)},y.prototype.bezierCurveTo=function(A,B,T,V,Y,Z){if(isNaN(Y)||isNaN(Z)||isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V))throw Pe.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var lt=this.ctx.transform.applyToPoint(new u(Y,Z)),it=this.ctx.transform.applyToPoint(new u(A,B)),mt=this.ctx.transform.applyToPoint(new u(T,V));this.path.push({type:"bct",x1:it.x,y1:it.y,x2:mt.x,y2:mt.y,x:lt.x,y:lt.y}),this.ctx.lastPoint=new u(lt.x,lt.y)},y.prototype.arc=function(A,B,T,V,Y,Z){if(isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V)||isNaN(Y))throw Pe.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(Z=!!Z,!this.ctx.transform.isIdentity){var lt=this.ctx.transform.applyToPoint(new u(A,B));A=lt.x,B=lt.y;var it=this.ctx.transform.applyToPoint(new u(0,T)),mt=this.ctx.transform.applyToPoint(new u(0,0));T=Math.sqrt(Math.pow(it.x-mt.x,2)+Math.pow(it.y-mt.y,2))}Math.abs(Y-V)>=2*Math.PI&&(V=0,Y=2*Math.PI),this.path.push({type:"arc",x:A,y:B,radius:T,startAngle:V,endAngle:Y,counterclockwise:Z})},y.prototype.arcTo=function(A,B,T,V,Y){throw new Error("arcTo not implemented.")},y.prototype.rect=function(A,B,T,V){if(isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V))throw Pe.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(A,B),this.lineTo(A+T,B),this.lineTo(A+T,B+V),this.lineTo(A,B+V),this.lineTo(A,B),this.lineTo(A+T,B),this.lineTo(A,B)},y.prototype.fillRect=function(A,B,T,V){if(isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V))throw Pe.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!g.call(this)){var Y={};this.lineCap!=="butt"&&(Y.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(Y.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(A,B,T,V),this.fill(),Y.hasOwnProperty("lineCap")&&(this.lineCap=Y.lineCap),Y.hasOwnProperty("lineJoin")&&(this.lineJoin=Y.lineJoin)}},y.prototype.strokeRect=function(A,B,T,V){if(isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V))throw Pe.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");P.call(this)||(this.beginPath(),this.rect(A,B,T,V),this.stroke())},y.prototype.clearRect=function(A,B,T,V){if(isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V))throw Pe.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(A,B,T,V))},y.prototype.save=function(A){A=typeof A!="boolean"||A;for(var B=this.pdf.internal.getCurrentPageInfo().pageNumber,T=0;T<this.pdf.internal.getNumberOfPages();T++)this.pdf.setPage(T+1),this.pdf.internal.out("q");if(this.pdf.setPage(B),A){this.ctx.fontSize=this.pdf.internal.getFontSize();var V=new p(this.ctx);this.ctxStack.push(this.ctx),this.ctx=V}},y.prototype.restore=function(A){A=typeof A!="boolean"||A;for(var B=this.pdf.internal.getCurrentPageInfo().pageNumber,T=0;T<this.pdf.internal.getNumberOfPages();T++)this.pdf.setPage(T+1),this.pdf.internal.out("Q");this.pdf.setPage(B),A&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},y.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var x=function(A){var B,T,V,Y;if(A.isCanvasGradient===!0&&(A=A.getColor()),!A)return{r:0,g:0,b:0,a:0,style:A};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(A))B=0,T=0,V=0,Y=0;else{var Z=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(A);if(Z!==null)B=parseInt(Z[1]),T=parseInt(Z[2]),V=parseInt(Z[3]),Y=1;else if((Z=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(A))!==null)B=parseInt(Z[1]),T=parseInt(Z[2]),V=parseInt(Z[3]),Y=parseFloat(Z[4]);else{if(Y=1,typeof A=="string"&&A.charAt(0)!=="#"){var lt=new uf(A);A=lt.ok?lt.toHex():"#000000"}A.length===4?(B=A.substring(1,2),B+=B,T=A.substring(2,3),T+=T,V=A.substring(3,4),V+=V):(B=A.substring(1,3),T=A.substring(3,5),V=A.substring(5,7)),B=parseInt(B,16),T=parseInt(T,16),V=parseInt(V,16)}}return{r:B,g:T,b:V,a:Y,style:A}},g=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},P=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};y.prototype.fillText=function(A,B,T,V){if(isNaN(B)||isNaN(T)||typeof A!="string")throw Pe.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(V=isNaN(V)?void 0:V,!g.call(this)){var Y=rt(this.ctx.transform.rotation),Z=this.ctx.transform.scaleX;O.call(this,{text:A,x:B,y:T,scale:Z,angle:Y,align:this.textAlign,maxWidth:V})}},y.prototype.strokeText=function(A,B,T,V){if(isNaN(B)||isNaN(T)||typeof A!="string")throw Pe.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!P.call(this)){V=isNaN(V)?void 0:V;var Y=rt(this.ctx.transform.rotation),Z=this.ctx.transform.scaleX;O.call(this,{text:A,x:B,y:T,scale:Z,renderingMode:"stroke",angle:Y,align:this.textAlign,maxWidth:V})}},y.prototype.measureText=function(A){if(typeof A!="string")throw Pe.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var B=this.pdf,T=this.pdf.internal.scaleFactor,V=B.internal.getFontSize(),Y=B.getStringUnitWidth(A)*V/B.internal.scaleFactor;return new function(Z){var lt=(Z=Z||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return lt}}),this}({width:Y*=Math.round(96*T/72*1e4)/1e4})},y.prototype.scale=function(A,B){if(isNaN(A)||isNaN(B))throw Pe.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var T=new h(A,0,0,B,0,0);this.ctx.transform=this.ctx.transform.multiply(T)},y.prototype.rotate=function(A){if(isNaN(A))throw Pe.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var B=new h(Math.cos(A),Math.sin(A),-Math.sin(A),Math.cos(A),0,0);this.ctx.transform=this.ctx.transform.multiply(B)},y.prototype.translate=function(A,B){if(isNaN(A)||isNaN(B))throw Pe.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var T=new h(1,0,0,1,A,B);this.ctx.transform=this.ctx.transform.multiply(T)},y.prototype.transform=function(A,B,T,V,Y,Z){if(isNaN(A)||isNaN(B)||isNaN(T)||isNaN(V)||isNaN(Y)||isNaN(Z))throw Pe.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var lt=new h(A,B,T,V,Y,Z);this.ctx.transform=this.ctx.transform.multiply(lt)},y.prototype.setTransform=function(A,B,T,V,Y,Z){A=isNaN(A)?1:A,B=isNaN(B)?0:B,T=isNaN(T)?0:T,V=isNaN(V)?1:V,Y=isNaN(Y)?0:Y,Z=isNaN(Z)?0:Z,this.ctx.transform=new h(A,B,T,V,Y,Z)};var C=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};y.prototype.drawImage=function(A,B,T,V,Y,Z,lt,it,mt){var Lt=this.pdf.getImageProperties(A),Pt=1,Ct=1,st=1,j=1;V!==void 0&&it!==void 0&&(st=it/V,j=mt/Y,Pt=Lt.width/V*it/V,Ct=Lt.height/Y*mt/Y),Z===void 0&&(Z=B,lt=T,B=0,T=0),V!==void 0&&it===void 0&&(it=V,mt=Y),V===void 0&&it===void 0&&(it=Lt.width,mt=Lt.height);for(var he,ne=this.ctx.transform.decompose(),Ht=rt(ne.rotate.shx),At=new h,Mt=(At=(At=(At=At.multiply(ne.translate)).multiply(ne.skew)).multiply(ne.scale)).applyToRectangle(new l(Z-B*st,lt-T*j,V*Pt,Y*Ct)),kt=D.call(this,Mt),ie=[],zt=0;zt<kt.length;zt+=1)ie.indexOf(kt[zt])===-1&&ie.push(kt[zt]);if(z(ie),this.autoPaging)for(var ge=ie[0],Zt=ie[ie.length-1],Qt=ge;Qt<Zt+1;Qt++){this.pdf.setPage(Qt);var Ae=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],ae=Qt===1?this.posY+this.margin[0]:this.margin[0],qt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Vt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],Ut=Qt===1?0:qt+(Qt-2)*Vt;if(this.ctx.clip_path.length!==0){var Ce=this.path;he=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=W(he,this.posX+this.margin[3],-Ut+ae+this.ctx.prevPageLastElemOffset),at.call(this,"fill",!0),this.path=Ce}var me=JSON.parse(JSON.stringify(Mt));me=W([me],this.posX+this.margin[3],-Ut+ae+this.ctx.prevPageLastElemOffset)[0];var jt=(Qt>ge||Qt<Zt)&&C.call(this);jt&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],Ae,Vt,null).clip().discardPath()),this.pdf.addImage(A,"JPEG",me.x,me.y,me.w,me.h,null,null,Ht),jt&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(A,"JPEG",Mt.x,Mt.y,Mt.w,Mt.h,null,null,Ht)};var D=function(A,B,T){var V=[];B=B||this.pdf.internal.pageSize.width,T=T||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var Y=this.posY+this.ctx.prevPageLastElemOffset;switch(A.type){default:case"mt":case"lt":V.push(Math.floor((A.y+Y)/T)+1);break;case"arc":V.push(Math.floor((A.y+Y-A.radius)/T)+1),V.push(Math.floor((A.y+Y+A.radius)/T)+1);break;case"qct":var Z=ft(this.ctx.lastPoint.x,this.ctx.lastPoint.y,A.x1,A.y1,A.x,A.y);V.push(Math.floor((Z.y+Y)/T)+1),V.push(Math.floor((Z.y+Z.h+Y)/T)+1);break;case"bct":var lt=Nt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,A.x1,A.y1,A.x2,A.y2,A.x,A.y);V.push(Math.floor((lt.y+Y)/T)+1),V.push(Math.floor((lt.y+lt.h+Y)/T)+1);break;case"rect":V.push(Math.floor((A.y+Y)/T)+1),V.push(Math.floor((A.y+A.h+Y)/T)+1)}for(var it=0;it<V.length;it+=1)for(;this.pdf.internal.getNumberOfPages()<V[it];)S.call(this);return V},S=function(){var A=this.fillStyle,B=this.strokeStyle,T=this.font,V=this.lineCap,Y=this.lineWidth,Z=this.lineJoin;this.pdf.addPage(),this.fillStyle=A,this.strokeStyle=B,this.font=T,this.lineCap=V,this.lineWidth=Y,this.lineJoin=Z},W=function(A,B,T){for(var V=0;V<A.length;V++)switch(A[V].type){case"bct":A[V].x2+=B,A[V].y2+=T;case"qct":A[V].x1+=B,A[V].y1+=T;default:A[V].x+=B,A[V].y+=T}return A},z=function(A){return A.sort(function(B,T){return B-T})},M=function(A,B){for(var T,V,Y=this.fillStyle,Z=this.strokeStyle,lt=this.lineCap,it=this.lineWidth,mt=Math.abs(it*this.ctx.transform.scaleX),Lt=this.lineJoin,Pt=JSON.parse(JSON.stringify(this.path)),Ct=JSON.parse(JSON.stringify(this.path)),st=[],j=0;j<Ct.length;j++)if(Ct[j].x!==void 0)for(var he=D.call(this,Ct[j]),ne=0;ne<he.length;ne+=1)st.indexOf(he[ne])===-1&&st.push(he[ne]);for(var Ht=0;Ht<st.length;Ht++)for(;this.pdf.internal.getNumberOfPages()<st[Ht];)S.call(this);if(z(st),this.autoPaging)for(var At=st[0],Mt=st[st.length-1],kt=At;kt<Mt+1;kt++){this.pdf.setPage(kt),this.fillStyle=Y,this.strokeStyle=Z,this.lineCap=lt,this.lineWidth=mt,this.lineJoin=Lt;var ie=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],zt=kt===1?this.posY+this.margin[0]:this.margin[0],ge=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Zt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],Qt=kt===1?0:ge+(kt-2)*Zt;if(this.ctx.clip_path.length!==0){var Ae=this.path;T=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=W(T,this.posX+this.margin[3],-Qt+zt+this.ctx.prevPageLastElemOffset),at.call(this,A,!0),this.path=Ae}if(V=JSON.parse(JSON.stringify(Pt)),this.path=W(V,this.posX+this.margin[3],-Qt+zt+this.ctx.prevPageLastElemOffset),B===!1||kt===0){var ae=(kt>At||kt<Mt)&&C.call(this);ae&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ie,Zt,null).clip().discardPath()),at.call(this,A,B),ae&&this.pdf.restoreGraphicsState()}this.lineWidth=it}else this.lineWidth=mt,at.call(this,A,B),this.lineWidth=it;this.path=Pt},at=function(A,B){if((A!=="stroke"||B||!P.call(this))&&(A==="stroke"||B||!g.call(this))){for(var T,V,Y=[],Z=this.path,lt=0;lt<Z.length;lt++){var it=Z[lt];switch(it.type){case"begin":Y.push({begin:!0});break;case"close":Y.push({close:!0});break;case"mt":Y.push({start:it,deltas:[],abs:[]});break;case"lt":var mt=Y.length;if(Z[lt-1]&&!isNaN(Z[lt-1].x)&&(T=[it.x-Z[lt-1].x,it.y-Z[lt-1].y],mt>0)){for(;mt>=0;mt--)if(Y[mt-1].close!==!0&&Y[mt-1].begin!==!0){Y[mt-1].deltas.push(T),Y[mt-1].abs.push(it);break}}break;case"bct":T=[it.x1-Z[lt-1].x,it.y1-Z[lt-1].y,it.x2-Z[lt-1].x,it.y2-Z[lt-1].y,it.x-Z[lt-1].x,it.y-Z[lt-1].y],Y[Y.length-1].deltas.push(T);break;case"qct":var Lt=Z[lt-1].x+2/3*(it.x1-Z[lt-1].x),Pt=Z[lt-1].y+2/3*(it.y1-Z[lt-1].y),Ct=it.x+2/3*(it.x1-it.x),st=it.y+2/3*(it.y1-it.y),j=it.x,he=it.y;T=[Lt-Z[lt-1].x,Pt-Z[lt-1].y,Ct-Z[lt-1].x,st-Z[lt-1].y,j-Z[lt-1].x,he-Z[lt-1].y],Y[Y.length-1].deltas.push(T);break;case"arc":Y.push({deltas:[],abs:[],arc:!0}),Array.isArray(Y[Y.length-1].abs)&&Y[Y.length-1].abs.push(it)}}V=B?null:A==="stroke"?"stroke":"fill";for(var ne=!1,Ht=0;Ht<Y.length;Ht++)if(Y[Ht].arc)for(var At=Y[Ht].abs,Mt=0;Mt<At.length;Mt++){var kt=At[Mt];kt.type==="arc"?$.call(this,kt.x,kt.y,kt.radius,kt.startAngle,kt.endAngle,kt.counterclockwise,void 0,B,!ne):G.call(this,kt.x,kt.y),ne=!0}else if(Y[Ht].close===!0)this.pdf.internal.out("h"),ne=!1;else if(Y[Ht].begin!==!0){var ie=Y[Ht].start.x,zt=Y[Ht].start.y;U.call(this,Y[Ht].deltas,ie,zt),ne=!0}V&&R.call(this,V),B&&et.call(this)}},vt=function(A){var B=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,T=B*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return A-T;case"top":return A+B-T;case"hanging":return A+B-2*T;case"middle":return A+B/2-T;default:return A}},ot=function(A){return A+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};y.prototype.createLinearGradient=function(){var A=function(){};return A.colorStops=[],A.addColorStop=function(B,T){this.colorStops.push([B,T])},A.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},A.isCanvasGradient=!0,A},y.prototype.createPattern=function(){return this.createLinearGradient()},y.prototype.createRadialGradient=function(){return this.createLinearGradient()};var $=function(A,B,T,V,Y,Z,lt,it,mt){for(var Lt=ht.call(this,T,V,Y,Z),Pt=0;Pt<Lt.length;Pt++){var Ct=Lt[Pt];Pt===0&&(mt?N.call(this,Ct.x1+A,Ct.y1+B):G.call(this,Ct.x1+A,Ct.y1+B)),nt.call(this,A,B,Ct.x2,Ct.y2,Ct.x3,Ct.y3,Ct.x4,Ct.y4)}it?et.call(this):R.call(this,lt)},R=function(A){switch(A){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},et=function(){this.pdf.clip(),this.pdf.discardPath()},N=function(A,B){this.pdf.internal.out(e(A)+" "+n(B)+" m")},O=function(A){var B;switch(A.align){case"right":case"end":B="right";break;case"center":B="center";break;default:B="left"}var T=this.pdf.getTextDimensions(A.text),V=vt.call(this,A.y),Y=ot.call(this,V)-T.h,Z=this.ctx.transform.applyToPoint(new u(A.x,V)),lt=this.ctx.transform.decompose(),it=new h;it=(it=(it=it.multiply(lt.translate)).multiply(lt.skew)).multiply(lt.scale);for(var mt,Lt,Pt,Ct=this.ctx.transform.applyToRectangle(new l(A.x,V,T.w,T.h)),st=it.applyToRectangle(new l(A.x,Y,T.w,T.h)),j=D.call(this,st),he=[],ne=0;ne<j.length;ne+=1)he.indexOf(j[ne])===-1&&he.push(j[ne]);if(z(he),this.autoPaging)for(var Ht=he[0],At=he[he.length-1],Mt=Ht;Mt<At+1;Mt++){this.pdf.setPage(Mt);var kt=Mt===1?this.posY+this.margin[0]:this.margin[0],ie=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],zt=this.pdf.internal.pageSize.height-this.margin[2],ge=zt-this.margin[0],Zt=this.pdf.internal.pageSize.width-this.margin[1],Qt=Zt-this.margin[3],Ae=Mt===1?0:ie+(Mt-2)*ge;if(this.ctx.clip_path.length!==0){var ae=this.path;mt=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=W(mt,this.posX+this.margin[3],-1*Ae+kt),at.call(this,"fill",!0),this.path=ae}var qt=W([JSON.parse(JSON.stringify(st))],this.posX+this.margin[3],-Ae+kt+this.ctx.prevPageLastElemOffset)[0];A.scale>=.01&&(Lt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Lt*A.scale),Pt=this.lineWidth,this.lineWidth=Pt*A.scale);var Vt=this.autoPaging!=="text";if(Vt||qt.y+qt.h<=zt){if(Vt||qt.y>=kt&&qt.x<=Zt){var Ut=Vt?A.text:this.pdf.splitTextToSize(A.text,A.maxWidth||Zt-qt.x)[0],Ce=W([JSON.parse(JSON.stringify(Ct))],this.posX+this.margin[3],-Ae+kt+this.ctx.prevPageLastElemOffset)[0],me=Vt&&(Mt>Ht||Mt<At)&&C.call(this);me&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],Qt,ge,null).clip().discardPath()),this.pdf.text(Ut,Ce.x,Ce.y,{angle:A.angle,align:B,renderingMode:A.renderingMode}),me&&this.pdf.restoreGraphicsState()}}else qt.y<zt&&(this.ctx.prevPageLastElemOffset+=zt-qt.y);A.scale>=.01&&(this.pdf.setFontSize(Lt),this.lineWidth=Pt)}else A.scale>=.01&&(Lt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Lt*A.scale),Pt=this.lineWidth,this.lineWidth=Pt*A.scale),this.pdf.text(A.text,Z.x+this.posX,Z.y+this.posY,{angle:A.angle,align:B,renderingMode:A.renderingMode,maxWidth:A.maxWidth}),A.scale>=.01&&(this.pdf.setFontSize(Lt),this.lineWidth=Pt)},G=function(A,B,T,V){T=T||0,V=V||0,this.pdf.internal.out(e(A+T)+" "+n(B+V)+" l")},U=function(A,B,T){return this.pdf.lines(A,B,T,null,null)},nt=function(A,B,T,V,Y,Z,lt,it){this.pdf.internal.out([t(i(T+A)),t(a(V+B)),t(i(Y+A)),t(a(Z+B)),t(i(lt+A)),t(a(it+B)),"c"].join(" "))},ht=function(A,B,T,V){for(var Y=2*Math.PI,Z=Math.PI/2;B>T;)B-=Y;var lt=Math.abs(T-B);lt<Y&&V&&(lt=Y-lt);for(var it=[],mt=V?-1:1,Lt=B;lt>1e-5;){var Pt=Lt+mt*Math.min(lt,Z);it.push(dt.call(this,A,Lt,Pt)),lt-=Math.abs(Pt-Lt),Lt=Pt}return it},dt=function(A,B,T){var V=(T-B)/2,Y=A*Math.cos(V),Z=A*Math.sin(V),lt=Y,it=-Z,mt=lt*lt+it*it,Lt=mt+lt*Y+it*Z,Pt=4/3*(Math.sqrt(2*mt*Lt)-Lt)/(lt*Z-it*Y),Ct=lt-Pt*it,st=it+Pt*lt,j=Ct,he=-st,ne=V+B,Ht=Math.cos(ne),At=Math.sin(ne);return{x1:A*Math.cos(B),y1:A*Math.sin(B),x2:Ct*Ht-st*At,y2:Ct*At+st*Ht,x3:j*Ht-he*At,y3:j*At+he*Ht,x4:A*Math.cos(T),y4:A*Math.sin(T)}},rt=function(A){return 180*A/Math.PI},ft=function(A,B,T,V,Y,Z){var lt=A+.5*(T-A),it=B+.5*(V-B),mt=Y+.5*(T-Y),Lt=Z+.5*(V-Z),Pt=Math.min(A,Y,lt,mt),Ct=Math.max(A,Y,lt,mt),st=Math.min(B,Z,it,Lt),j=Math.max(B,Z,it,Lt);return new l(Pt,st,Ct-Pt,j-st)},Nt=function(A,B,T,V,Y,Z,lt,it){var mt,Lt,Pt,Ct,st,j,he,ne,Ht,At,Mt,kt,ie,zt,ge=T-A,Zt=V-B,Qt=Y-T,Ae=Z-V,ae=lt-Y,qt=it-Z;for(Lt=0;Lt<41;Lt++)Ht=(he=(Pt=A+(mt=Lt/40)*ge)+mt*((st=T+mt*Qt)-Pt))+mt*(st+mt*(Y+mt*ae-st)-he),At=(ne=(Ct=B+mt*Zt)+mt*((j=V+mt*Ae)-Ct))+mt*(j+mt*(Z+mt*qt-j)-ne),Lt==0?(Mt=Ht,kt=At,ie=Ht,zt=At):(Mt=Math.min(Mt,Ht),kt=Math.min(kt,At),ie=Math.max(ie,Ht),zt=Math.max(zt,At));return new l(Math.round(Mt),Math.round(kt),Math.round(ie-Mt),Math.round(zt-kt))},wt=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var A,B,T=(A=this.ctx.lineDash,B=this.ctx.lineDashOffset,JSON.stringify({lineDash:A,lineDashOffset:B}));this.prevLineDash!==T&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=T)}}})(Rt.API),(function(r){var t=function(u){var l,h,f,p,y,x,g,P,C,D;for(h=[],f=0,p=(u+=l="\0\0\0\0".slice(u.length%4||4)).length;p>f;f+=4)(y=(u.charCodeAt(f)<<24)+(u.charCodeAt(f+1)<<16)+(u.charCodeAt(f+2)<<8)+u.charCodeAt(f+3))!==0?(x=(y=((y=((y=((y=(y-(D=y%85))/85)-(C=y%85))/85)-(P=y%85))/85)-(g=y%85))/85)%85,h.push(x+33,g+33,P+33,C+33,D+33)):h.push(122);return(function(S,W){for(var z=W;z>0;z--)S.pop()})(h,l.length),String.fromCharCode.apply(String,h)+"~>"},e=function(u){var l,h,f,p,y,x=String,g="length",P=255,C="charCodeAt",D="slice",S="replace";for(u[D](-2),u=u[D](0,-2)[S](/\s/g,"")[S]("z","!!!!!"),f=[],p=0,y=(u+=l="uuuuu"[D](u[g]%5||5))[g];y>p;p+=5)h=52200625*(u[C](p)-33)+614125*(u[C](p+1)-33)+7225*(u[C](p+2)-33)+85*(u[C](p+3)-33)+(u[C](p+4)-33),f.push(P&h>>24,P&h>>16,P&h>>8,P&h);return(function(W,z){for(var M=z;M>0;M--)W.pop()})(f,l[g]),x.fromCharCode.apply(x,f)},n=function(u){return u.split("").map(function(l){return("0"+l.charCodeAt().toString(16)).slice(-2)}).join("")+">"},i=function(u){var l=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((u=u.replace(/\s/g,"")).indexOf(">")!==-1&&(u=u.substr(0,u.indexOf(">"))),u.length%2&&(u+="0"),l.test(u)===!1)return"";for(var h="",f=0;f<u.length;f+=2)h+=String.fromCharCode("0x"+(u[f]+u[f+1]));return h},a=function(u){for(var l=new Uint8Array(u.length),h=u.length;h--;)l[h]=u.charCodeAt(h);return(l=nl(l)).reduce(function(f,p){return f+String.fromCharCode(p)},"")};r.processDataByFilters=function(u,l){var h=0,f=u||"",p=[];for(typeof(l=l||[])=="string"&&(l=[l]),h=0;h<l.length;h+=1)switch(l[h]){case"ASCII85Decode":case"/ASCII85Decode":f=e(f),p.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=t(f),p.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=i(f),p.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=n(f),p.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=a(f),p.push("/FlateDecode");break;default:throw new Error('The filter: "'+l[h]+'" is not implemented')}return{data:f,reverseChain:p.reverse().join(" ")}}})(Rt.API),(function(r){r.loadFile=function(t,e,n){return(function(i,a,u){a=a!==!1,u=typeof u=="function"?u:function(){};var l=void 0;try{l=(function(h,f,p){var y=new XMLHttpRequest,x=0,g=function(P){var C=P.length,D=[],S=String.fromCharCode;for(x=0;x<C;x+=1)D.push(S(255&P.charCodeAt(x)));return D.join("")};if(y.open("GET",h,!f),y.overrideMimeType("text/plain; charset=x-user-defined"),f===!1&&(y.onload=function(){y.status===200?p(g(this.responseText)):p(void 0)}),y.send(null),f&&y.status===200)return g(y.responseText)})(i,a,u)}catch(h){}return l})(t,e,n)},r.loadImageFile=r.loadFile})(Rt.API),(function(r){function t(){return(Yt.html2canvas?Promise.resolve(Yt.html2canvas):Ns(()=>import("./html2canvas.esm-CfNIm60U.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load html2canvas: "+l))}).then(function(l){return l.default?l.default:l})}function e(){return(Yt.DOMPurify?Promise.resolve(Yt.DOMPurify):Ns(()=>import("./purify.es-Dt4Oscm4.js"),[])).catch(function(l){return Promise.reject(new Error("Could not load dompurify: "+l))}).then(function(l){return l.default?l.default:l})}var n=function(l){var h=_e(l);return h==="undefined"?"undefined":h==="string"||l instanceof String?"string":h==="number"||l instanceof Number?"number":h==="function"||l instanceof Function?"function":l&&l.constructor===Array?"array":l&&l.nodeType===1?"element":h==="object"?"object":"unknown"},i=function(l,h){var f=document.createElement(l);for(var p in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[p]=h.style[p];return f},a=function l(h,f){for(var p=h.nodeType===3?document.createTextNode(h.nodeValue):h.cloneNode(!1),y=h.firstChild;y;y=y.nextSibling)f!==!0&&y.nodeType===1&&y.nodeName==="SCRIPT"||p.appendChild(l(y,f));return h.nodeType===1&&(h.nodeName==="CANVAS"?(p.width=h.width,p.height=h.height,p.getContext("2d").drawImage(h,0,0)):h.nodeName!=="TEXTAREA"&&h.nodeName!=="SELECT"||(p.value=h.value),p.addEventListener("load",function(){p.scrollTop=h.scrollTop,p.scrollLeft=h.scrollLeft},!0)),p},u=function l(h){var f=Object.assign(l.convert(Promise.resolve()),JSON.parse(JSON.stringify(l.template))),p=l.convert(Promise.resolve(),f);return(p=p.setProgress(1,l,1,[l])).set(h)};(u.prototype=Object.create(Promise.prototype)).constructor=u,u.convert=function(l,h){return l.__proto__=h||u.prototype,l},u.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},u.prototype.from=function(l,h){return this.then(function(){switch(h=h||(function(f){switch(n(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}})(l),h){case"string":return this.then(e).then(function(f){return this.set({src:i("div",{innerHTML:l,dompurify:f})})});case"element":return this.set({src:l});case"canvas":return this.set({canvas:l});case"img":return this.set({img:l});default:return this.error("Unknown source type.")}})},u.prototype.to=function(l){switch(l){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},u.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var l={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=a(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(l.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=i("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=i("div",{className:"html2pdf__container",style:l}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(i("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},u.prototype.toCanvas=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(t).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},u.prototype.toContext2d=function(){var l=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(l).then(t).then(function(h){var f=this.opt.jsPDF,p=this.opt.fontFaces,y=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,x=Object.assign({async:!0,allowTaint:!0,scale:y,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete x.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=p,p)for(var g=0;g<p.length;++g){var P=p[g],C=P.src.find(function(D){return D.format==="truetype"});C&&f.addFont(C.url,P.ref.name,P.ref.style)}return x.windowHeight=x.windowHeight||0,x.windowHeight=x.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):x.windowHeight,f.context2d.save(!0),h(this.prop.container,x)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},u.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var l=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=l})},u.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},u.prototype.output=function(l,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(l,h):this.outputPdf(l,h)},u.prototype.outputPdf=function(l,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(l,h)})},u.prototype.outputImg=function(l){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(l){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+l+'" is not supported.'}})},u.prototype.save=function(l){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(l?{filename:l}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},u.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},u.prototype.set=function(l){if(n(l)!=="object")return this;var h=Object.keys(l||{}).map(function(f){if(f in u.template.prop)return function(){this.prop[f]=l[f]};switch(f){case"margin":return this.setMargin.bind(this,l.margin);case"jsPDF":return function(){return this.opt.jsPDF=l.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,l.pageSize);default:return function(){this.opt[f]=l[f]}}},this);return this.then(function(){return this.thenList(h)})},u.prototype.get=function(l,h){return this.then(function(){var f=l in u.template.prop?this.prop[l]:this.opt[l];return h?h(f):f})},u.prototype.setMargin=function(l){return this.then(function(){switch(n(l)){case"number":l=[l,l,l,l];case"array":if(l.length===2&&(l=[l[0],l[1],l[0],l[1]]),l.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=l}).then(this.setPageSize)},u.prototype.setPageSize=function(l){function h(f,p){return Math.floor(f*p/72*96)}return this.then(function(){(l=l||Rt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(l.inner={width:l.width-this.opt.margin[1]-this.opt.margin[3],height:l.height-this.opt.margin[0]-this.opt.margin[2]},l.inner.px={width:h(l.inner.width,l.k),height:h(l.inner.height,l.k)},l.inner.ratio=l.inner.height/l.inner.width),this.prop.pageSize=l})},u.prototype.setProgress=function(l,h,f,p){return l!=null&&(this.progress.val=l),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),p!=null&&(this.progress.stack=p),this.progress.ratio=this.progress.val/this.progress.state,this},u.prototype.updateProgress=function(l,h,f,p){return this.setProgress(l?this.progress.val+l:null,h||null,f?this.progress.n+f:null,p?this.progress.stack.concat(p):null)},u.prototype.then=function(l,h){var f=this;return this.thenCore(l,h,function(p,y){return f.updateProgress(null,null,1,[p]),Promise.prototype.then.call(this,function(x){return f.updateProgress(null,p),x}).then(p,y).then(function(x){return f.updateProgress(1),x})})},u.prototype.thenCore=function(l,h,f){f=f||Promise.prototype.then;var p=this;l&&(l=l.bind(p)),h&&(h=h.bind(p));var y=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?p:u.convert(Object.assign({},p),Promise.prototype),x=f.call(y,l,h);return u.convert(x,p.__proto__)},u.prototype.thenExternal=function(l,h){return Promise.prototype.then.call(this,l,h)},u.prototype.thenList=function(l){var h=this;return l.forEach(function(f){h=h.thenCore(f)}),h},u.prototype.catch=function(l){l&&(l=l.bind(this));var h=Promise.prototype.catch.call(this,l);return u.convert(h,this)},u.prototype.catchExternal=function(l){return Promise.prototype.catch.call(this,l)},u.prototype.error=function(l){return this.then(function(){throw new Error(l)})},u.prototype.using=u.prototype.set,u.prototype.saveAs=u.prototype.save,u.prototype.export=u.prototype.output,u.prototype.run=u.prototype.then,Rt.getPageSize=function(l,h,f){if(_e(l)==="object"){var p=l;l=p.orientation,h=p.unit||h,f=p.format||f}h=h||"mm",f=f||"a4",l=(""+(l||"P")).toLowerCase();var y,x=(""+f).toLowerCase(),g={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":y=1;break;case"mm":y=72/25.4;break;case"cm":y=72/2.54;break;case"in":y=72;break;case"px":y=.75;break;case"pc":case"em":y=12;break;case"ex":y=6;break;default:throw"Invalid unit: "+h}var P,C=0,D=0;if(g.hasOwnProperty(x))C=g[x][1]/y,D=g[x][0]/y;else try{C=f[1],D=f[0]}catch(S){throw new Error("Invalid format: "+f)}if(l==="p"||l==="portrait")l="p",D>C&&(P=D,D=C,C=P);else{if(l!=="l"&&l!=="landscape")throw"Invalid orientation: "+l;l="l",C>D&&(P=D,D=C,C=P)}return{width:D,height:C,unit:h,k:y,orientation:l}},r.html=function(l,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(Ws):null;var f=new u(h);return h.worker?f:f.from(l).doCallback()}})(Rt.API),Rt.API.addJS=function(r){return hh=r,this.internal.events.subscribe("postPutResources",function(){Po=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(Po+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),uh=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+hh+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){Po!==void 0&&uh!==void 0&&this.internal.out("/Names <</JavaScript "+Po+" 0 R>>")}),this},(function(r){var t;r.events.push(["postPutResources",function(){var e=this,n=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var i=e.outline.render().split(/\r\n/),a=0;a<i.length;a++){var u=i[a],l=n.exec(u);if(l!=null){var h=l[1];e.internal.newObjectDeferredBegin(h,!1)}e.internal.write(u)}if(this.outline.createNamedDestinations){var f=this.internal.pages.length,p=[];for(a=0;a<f;a++){var y=e.internal.newObject();p.push(y);var x=e.internal.getPageInfo(a+1);e.internal.write("<< /D["+x.objId+" 0 R /XYZ null null null]>> endobj")}var g=e.internal.newObject();for(e.internal.write("<< /Names [ "),a=0;a<p.length;a++)e.internal.write("(page_"+(a+1)+")"+p[a]+" 0 R");e.internal.write(" ] >>","endobj"),t=e.internal.newObject(),e.internal.write("<< /Dests "+g+" 0 R"),e.internal.write(">>","endobj")}}]),r.events.push(["putCatalog",function(){var e=this;e.outline.root.children.length>0&&(e.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&e.internal.write("/Names "+t+" 0 R"))}]),r.events.push(["initialized",function(){var e=this;e.outline={createNamedDestinations:!1,root:{children:[]}},e.outline.add=function(n,i,a){var u={title:i,options:a,children:[]};return n==null&&(n=this.root),n.children.push(u),u},e.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=e,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},e.outline.genIds_r=function(n){n.id=e.internal.newObjectDeferred();for(var i=0;i<n.children.length;i++)this.genIds_r(n.children[i])},e.outline.renderRoot=function(n){this.objStart(n),this.line("/Type /Outlines"),n.children.length>0&&(this.line("/First "+this.makeRef(n.children[0])),this.line("/Last "+this.makeRef(n.children[n.children.length-1]))),this.line("/Count "+this.count_r({count:0},n)),this.objEnd()},e.outline.renderItems=function(n){for(var i=this.ctx.pdf.internal.getVerticalCoordinateString,a=0;a<n.children.length;a++){var u=n.children[a];this.objStart(u),this.line("/Title "+this.makeString(u.title)),this.line("/Parent "+this.makeRef(n)),a>0&&this.line("/Prev "+this.makeRef(n.children[a-1])),a<n.children.length-1&&this.line("/Next "+this.makeRef(n.children[a+1])),u.children.length>0&&(this.line("/First "+this.makeRef(u.children[0])),this.line("/Last "+this.makeRef(u.children[u.children.length-1])));var l=this.count=this.count_r({count:0},u);if(l>0&&this.line("/Count "+l),u.options&&u.options.pageNumber){var h=e.internal.getPageInfo(u.options.pageNumber);this.line("/Dest ["+h.objId+" 0 R /XYZ 0 "+i(0)+" 0]")}this.objEnd()}for(var f=0;f<n.children.length;f++)this.renderItems(n.children[f])},e.outline.line=function(n){this.ctx.val+=n+"\r\n"},e.outline.makeRef=function(n){return n.id+" 0 R"},e.outline.makeString=function(n){return"("+e.internal.pdfEscape(n)+")"},e.outline.objStart=function(n){this.ctx.val+="\r\n"+n.id+" 0 obj\r\n<<\r\n"},e.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},e.outline.count_r=function(n,i){for(var a=0;a<i.children.length;a++)n.count++,this.count_r(n,i.children[a]);return n.count}}])})(Rt.API),(function(r){var t=[192,193,194,195,196,197,198,199];r.processJPEG=function(e,n,i,a,u,l){var h,f=this.decode.DCT_DECODE,p=null;if(typeof e=="string"||this.__addimage__.isArrayBuffer(e)||this.__addimage__.isArrayBufferView(e)){switch(e=u||e,e=this.__addimage__.isArrayBuffer(e)?new Uint8Array(e):e,h=(function(y){for(var x,g=256*y.charCodeAt(4)+y.charCodeAt(5),P=y.length,C={width:0,height:0,numcomponents:1},D=4;D<P;D+=2){if(D+=g,t.indexOf(y.charCodeAt(D+1))!==-1){x=256*y.charCodeAt(D+5)+y.charCodeAt(D+6),C={width:256*y.charCodeAt(D+7)+y.charCodeAt(D+8),height:x,numcomponents:y.charCodeAt(D+9)};break}g=256*y.charCodeAt(D+2)+y.charCodeAt(D+3)}return C})(e=this.__addimage__.isArrayBufferView(e)?this.__addimage__.arrayBufferToBinaryString(e):e),h.numcomponents){case 1:l=this.color_spaces.DEVICE_GRAY;break;case 4:l=this.color_spaces.DEVICE_CMYK;break;case 3:l=this.color_spaces.DEVICE_RGB}p={data:e,width:h.width,height:h.height,colorSpace:l,bitsPerComponent:8,filter:f,index:n,alias:i}}return p}})(Rt.API),Rt.API.processPNG=function(r,t,e,n){if(this.__addimage__.isArrayBuffer(r)&&(r=new Uint8Array(r)),this.__addimage__.isArrayBufferView(r)){var i,a=S2(r,{checkCrc:!0}),u=a.width,l=a.height,h=a.channels,f=a.palette,p=a.depth;i=f&&h===1?(function(ot){for(var $=ot.width,R=ot.height,et=ot.data,N=ot.palette,O=ot.depth,G=!1,U=[],nt=[],ht=void 0,dt=!1,rt=0,ft=0;ft<N.length;ft++){var Nt=pu(N[ft],4),wt=Nt[0],A=Nt[1],B=Nt[2],T=Nt[3];U.push(wt,A,B),T!=null&&(T===0?(rt++,nt.length<1&&nt.push(ft)):T<255&&(dt=!0))}if(dt||rt>1){G=!0,nt=void 0;var V=$*R;ht=new Uint8Array(V);for(var Y=new DataView(et.buffer),Z=0;Z<V;Z++){var lt=Vs(Y,Z,O),it=pu(N[lt],4)[3];ht[Z]=it}}return{colorSpace:"Indexed",colorsPerPixel:1,colorBytes:et,alphaBytes:ht,needSMask:G,palette:U,mask:nt}})(a):h===2||h===4?(function(ot){for(var $=ot.data,R=ot.width,et=ot.height,N=ot.channels,O=ot.depth,G=N===2?"DeviceGray":"DeviceRGB",U=N-1,nt=R*et,ht=U,dt=nt*ht,rt=1*nt,ft=Math.ceil(dt*O/8),Nt=Math.ceil(rt*O/8),wt=new Uint8Array(ft),A=new Uint8Array(Nt),B=new DataView($.buffer),T=new DataView(wt.buffer),V=new DataView(A.buffer),Y=!1,Z=0;Z<nt;Z++){for(var lt=Z*N,it=0;it<ht;it++)yh(T,Vs(B,lt+it,O),Z*ht+it,O);var mt=Vs(B,lt+ht,O);mt<(1<<O)-1&&(Y=!0),yh(V,mt,1*Z,O)}return{colorSpace:G,colorsPerPixel:U,colorBytes:wt,alphaBytes:A,needSMask:Y}})(a):(function(ot){var $=ot.data,R=ot.channels===1?"DeviceGray":"DeviceRGB";return{colorSpace:R,colorsPerPixel:R==="DeviceGray"?1:3,colorBytes:$ instanceof Uint8Array?$:new Uint8Array($.buffer),needSMask:!1}})(a);var y,x,g,P=i,C=P.colorSpace,D=P.colorsPerPixel,S=P.colorBytes,W=P.alphaBytes,z=P.needSMask,M=P.palette,at=P.mask,vt=null;return n!==Rt.API.image_compression.NONE&&typeof nl=="function"?(vt=(function(ot){var $;switch(ot){case Rt.API.image_compression.FAST:$=11;break;case Rt.API.image_compression.MEDIUM:$=13;break;case Rt.API.image_compression.SLOW:$=14;break;default:$=12}return $})(n),y=this.decode.FLATE_DECODE,x="/Predictor ".concat(vt," "),r=ph(S,u*D,D,n),z&&(g=ph(W,u,1,n))):(y=void 0,x="",r=S,z&&(g=W)),x+="/Colors ".concat(D," /BitsPerComponent ").concat(p," /Columns ").concat(u),(this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r))&&(r=this.__addimage__.arrayBufferToBinaryString(r)),(g&&this.__addimage__.isArrayBuffer(g)||this.__addimage__.isArrayBufferView(g))&&(g=this.__addimage__.arrayBufferToBinaryString(g)),{alias:e,data:r,index:t,filter:y,decodeParameters:x,transparency:at,palette:M,sMask:g,predictor:vt,width:u,height:l,bitsPerComponent:p,colorSpace:C}}},(function(r){r.processGIF89A=function(t,e,n,i){var a=new K2(t),u=a.width,l=a.height,h=[];a.decodeAndBlitFrameRGBA(0,h);var f={data:h,width:u,height:l},p=new Ks(100).encode(f,100);return r.processJPEG.call(this,p,e,n,i)},r.processGIF87A=r.processGIF89A})(Rt.API),an.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var r=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(r);for(var t=0;t<r;t++){var e=this.datav.getUint8(this.pos++,!0),n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0);this.palette[t]={red:i,green:n,blue:e,quad:a}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},an.prototype.parseBGR=function(){this.pos=this.offset;try{var r="bit"+this.bitPP,t=this.width*this.height*4;this.data=new Uint8Array(t),this[r]()}catch(e){Pe.log("bit decode error:"+e)}},an.prototype.bit1=function(){var r,t=Math.ceil(this.width/8),e=t%4;for(r=this.height-1;r>=0;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<t;i++)for(var a=this.datav.getUint8(this.pos++,!0),u=n*this.width*4+8*i*4,l=0;l<8&&8*i+l<this.width;l++){var h=this.palette[a>>7-l&1];this.data[u+4*l]=h.blue,this.data[u+4*l+1]=h.green,this.data[u+4*l+2]=h.red,this.data[u+4*l+3]=255}e!==0&&(this.pos+=4-e)}},an.prototype.bit4=function(){for(var r=Math.ceil(this.width/2),t=r%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,i=0;i<r;i++){var a=this.datav.getUint8(this.pos++,!0),u=n*this.width*4+2*i*4,l=a>>4,h=15&a,f=this.palette[l];if(this.data[u]=f.blue,this.data[u+1]=f.green,this.data[u+2]=f.red,this.data[u+3]=255,2*i+1>=this.width)break;f=this.palette[h],this.data[u+4]=f.blue,this.data[u+4+1]=f.green,this.data[u+4+2]=f.red,this.data[u+4+3]=255}t!==0&&(this.pos+=4-t)}},an.prototype.bit8=function(){for(var r=this.width%4,t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var i=this.datav.getUint8(this.pos++,!0),a=e*this.width*4+4*n;if(i<this.palette.length){var u=this.palette[i];this.data[a]=u.red,this.data[a+1]=u.green,this.data[a+2]=u.blue,this.data[a+3]=255}else this.data[a]=255,this.data[a+1]=255,this.data[a+2]=255,this.data[a+3]=255}r!==0&&(this.pos+=4-r)}},an.prototype.bit15=function(){for(var r=this.width%3,t=parseInt("11111",2),e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,i=0;i<this.width;i++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var u=(a&t)/t*255|0,l=(a>>5&t)/t*255|0,h=(a>>10&t)/t*255|0,f=a>>15?255:0,p=n*this.width*4+4*i;this.data[p]=h,this.data[p+1]=l,this.data[p+2]=u,this.data[p+3]=f}this.pos+=r}},an.prototype.bit16=function(){for(var r=this.width%3,t=parseInt("11111",2),e=parseInt("111111",2),n=this.height-1;n>=0;n--){for(var i=this.bottom_up?n:this.height-1-n,a=0;a<this.width;a++){var u=this.datav.getUint16(this.pos,!0);this.pos+=2;var l=(u&t)/t*255|0,h=(u>>5&e)/e*255|0,f=(u>>11)/t*255|0,p=i*this.width*4+4*a;this.data[p]=f,this.data[p+1]=h,this.data[p+2]=l,this.data[p+3]=255}this.pos+=r}},an.prototype.bit24=function(){for(var r=this.height-1;r>=0;r--){for(var t=this.bottom_up?r:this.height-1-r,e=0;e<this.width;e++){var n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),u=t*this.width*4+4*e;this.data[u]=a,this.data[u+1]=i,this.data[u+2]=n,this.data[u+3]=255}this.pos+=this.width%4}},an.prototype.bit32=function(){for(var r=this.height-1;r>=0;r--)for(var t=this.bottom_up?r:this.height-1-r,e=0;e<this.width;e++){var n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),l=t*this.width*4+4*e;this.data[l]=a,this.data[l+1]=i,this.data[l+2]=n,this.data[l+3]=u}},an.prototype.getData=function(){return this.data},(function(r){r.processBMP=function(t,e,n,i){var a=new an(t,!1),u=a.width,l=a.height,h={data:a.getData(),width:u,height:l},f=new Ks(100).encode(h,100);return r.processJPEG.call(this,f,e,n,i)}})(Rt.API),xh.prototype.getData=function(){return this.data},(function(r){r.processWEBP=function(t,e,n,i){var a=new xh(t),u=a.width,l=a.height,h={data:a.getData(),width:u,height:l},f=new Ks(100).encode(h,100);return r.processJPEG.call(this,f,e,n,i)}})(Rt.API),Rt.API.processRGBA=function(r,t,e){for(var n=r.data,i=n.length,a=new Uint8Array(i/4*3),u=new Uint8Array(i/4),l=0,h=0,f=0;f<i;f+=4){var p=n[f],y=n[f+1],x=n[f+2],g=n[f+3];a[l++]=p,a[l++]=y,a[l++]=x,u[h++]=g}var P=this.__addimage__.arrayBufferToBinaryString(a);return{alpha:this.__addimage__.arrayBufferToBinaryString(u),data:P,index:t,alias:e,colorSpace:"DeviceRGB",bitsPerComponent:8,width:r.width,height:r.height}},Rt.API.setLanguage=function(r){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[r]!==void 0&&(this.internal.languageSettings.languageCode=r,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},Ji=Rt.API,Co=Ji.getCharWidthsArray=function(r,t){var e,n,i=(t=t||{}).font||this.internal.getFont(),a=t.fontSize||this.internal.getFontSize(),u=t.charSpace||this.internal.getCharSpace(),l=t.widths?t.widths:i.metadata.Unicode.widths,h=l.fof?l.fof:1,f=t.kerning?t.kerning:i.metadata.Unicode.kerning,p=f.fof?f.fof:1,y=t.doKerning!==!1,x=0,g=r.length,P=0,C=l[0]||h,D=[];for(e=0;e<g;e++)n=r.charCodeAt(e),typeof i.metadata.widthOfString=="function"?D.push((i.metadata.widthOfGlyph(i.metadata.characterToGlyph(n))+u*(1e3/a)||0)/1e3):(x=y&&_e(f[n])==="object"&&!isNaN(parseInt(f[n][P],10))?f[n][P]/p:0,D.push((l[n]||C)/h+x)),P=n;return D},fh=Ji.getStringUnitWidth=function(r,t){var e=(t=t||{}).fontSize||this.internal.getFontSize(),n=t.font||this.internal.getFont(),i=t.charSpace||this.internal.getCharSpace();return Ji.processArabic&&(r=Ji.processArabic(r)),typeof n.metadata.widthOfString=="function"?n.metadata.widthOfString(r,e,i)/e:Co.apply(this,arguments).reduce(function(a,u){return a+u},0)},ch=function(r,t,e,n){for(var i=[],a=0,u=r.length,l=0;a!==u&&l+t[a]<e;)l+=t[a],a++;i.push(r.slice(0,a));var h=a;for(l=0;a!==u;)l+t[a]>n&&(i.push(r.slice(h,a)),l=0,h=a),l+=t[a],a++;return h!==a&&i.push(r.slice(h,a)),i},dh=function(r,t,e){e||(e={});var n,i,a,u,l,h,f,p=[],y=[p],x=e.textIndent||0,g=0,P=0,C=r.split(" "),D=Co.apply(this,[" ",e])[0];if(h=e.lineIndent===-1?C[0].length+2:e.lineIndent||0){var S=Array(h).join(" "),W=[];C.map(function(M){(M=M.split(/\s*\n/)).length>1?W=W.concat(M.map(function(at,vt){return(vt&&at.length?"\n":"")+at})):W.push(M[0])}),C=W,h=fh.apply(this,[S,e])}for(a=0,u=C.length;a<u;a++){var z=0;if(n=C[a],h&&n[0]=="\n"&&(n=n.substr(1),z=1),x+g+(P=(i=Co.apply(this,[n,e])).reduce(function(M,at){return M+at},0))>t||z){if(P>t){for(l=ch.apply(this,[n,i,t-(x+g),t]),p.push(l.shift()),p=[l.pop()];l.length;)y.push([l.shift()]);P=i.slice(n.length-(p[0]?p[0].length:0)).reduce(function(M,at){return M+at},0)}else p=[n];y.push(p),x=P+h,g=D}else p.push(n),x+=g+P,g=D}return f=h?function(M,at){return(at?S:"")+M.join(" ")}:function(M){return M.join(" ")},y.map(f)},Ji.splitTextToSize=function(r,t,e){var n,i=(e=e||{}).fontSize||this.internal.getFontSize(),a=(function(p){if(p.widths&&p.kerning)return{widths:p.widths,kerning:p.kerning};var y=this.internal.getFont(p.fontName,p.fontStyle),x="Unicode";return y.metadata[x]?{widths:y.metadata[x].widths||{0:1},kerning:y.metadata[x].kerning||{}}:{font:y.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,e);n=Array.isArray(r)?r:String(r).split(/\r?\n/);var u=1*this.internal.scaleFactor*t/i;a.textIndent=e.textIndent?1*e.textIndent*this.internal.scaleFactor/i:0,a.lineIndent=e.lineIndent;var l,h,f=[];for(l=0,h=n.length;l<h;l++)f=f.concat(dh.apply(this,[n[l],u,a]));return f},(function(r){r.__fontmetrics__=r.__fontmetrics__||{};for(var t="0123456789abcdef",e="klmnopqrstuvwxyz",n={},i={},a=0;a<16;a++)n[e[a]]=t[a],i[t[a]]=e[a];var u=function(x){return"0x"+parseInt(x,10).toString(16)},l=r.__fontmetrics__.compress=function(x){var g,P,C,D,S=["{"];for(var W in x){if(g=x[W],isNaN(parseInt(W,10))?P="'"+W+"'":(W=parseInt(W,10),P=(P=u(W).slice(2)).slice(0,-1)+i[P.slice(-1)]),typeof g=="number")g<0?(C=u(g).slice(3),D="-"):(C=u(g).slice(2),D=""),C=D+C.slice(0,-1)+i[C.slice(-1)];else{if(_e(g)!=="object")throw new Error("Don't know what to do with value type "+_e(g)+".");C=l(g)}S.push(P+C)}return S.push("}"),S.join("")},h=r.__fontmetrics__.uncompress=function(x){if(typeof x!="string")throw new Error("Invalid argument passed to uncompress.");for(var g,P,C,D,S={},W=1,z=S,M=[],at="",vt="",ot=x.length-1,$=1;$<ot;$+=1)(D=x[$])=="'"?g?(C=g.join(""),g=void 0):g=[]:g?g.push(D):D=="{"?(M.push([z,C]),z={},C=void 0):D=="}"?((P=M.pop())[0][P[1]]=z,C=void 0,z=P[0]):D=="-"?W=-1:C===void 0?n.hasOwnProperty(D)?(at+=n[D],C=parseInt(at,16)*W,W=1,at=""):at+=D:n.hasOwnProperty(D)?(vt+=n[D],z[C]=parseInt(vt,16)*W,W=1,C=void 0,vt=""):vt+=D;return S},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},p={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},y={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};r.events.push(["addFont",function(x){var g=x.font,P=y.Unicode[g.postScriptName];P&&(g.metadata.Unicode={},g.metadata.Unicode.widths=P.widths,g.metadata.Unicode.kerning=P.kerning);var C=p.Unicode[g.postScriptName];C&&(g.metadata.Unicode.encoding=C,g.encoding=C.codePages[0])}])})(Rt.API),(function(r){var t=function(e){for(var n=e.length,i=new Uint8Array(n),a=0;a<n;a++)i[a]=e.charCodeAt(a);return i};r.API.events.push(["addFont",function(e){var n=void 0,i=e.font,a=e.instance;if(!i.isStandardFont){if(a===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+i.postScriptName+"').");if(typeof(n=a.existsFileInVFS(i.postScriptName)===!1?a.loadFile(i.postScriptName):a.getFileFromVFS(i.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+i.postScriptName+"').");(function(u,l){l=/^\x00\x01\x00\x00/.test(l)?t(l):t(Eo(l)),u.metadata=r.API.TTFFont.open(l),u.metadata.Unicode=u.metadata.Unicode||{encoding:{},kerning:{},widths:[]},u.metadata.glyIdsUsed=[0]})(i,n)}}])})(Rt),Rt.API.addSvgAsImage=function(r,t,e,n,i,a,u,l){if(isNaN(t)||isNaN(e))throw Pe.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(n)||isNaN(i))throw Pe.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var h=document.createElement("canvas");h.width=n,h.height=i;var f=h.getContext("2d");f.fillStyle="#fff",f.fillRect(0,0,h.width,h.height);var p={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},y=this;return(Yt.canvg?Promise.resolve(Yt.canvg):Ns(()=>import("./index.es-CvtAQiqk.js"),__vite__mapDeps([0,1,2,3]))).catch(function(x){return Promise.reject(new Error("Could not load canvg: "+x))}).then(function(x){return x.default?x.default:x}).then(function(x){return x.fromString(f,r,p)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(x){return x.render(p)}).then(function(){y.addImage(h.toDataURL("image/jpeg",1),t,e,n,i,u,l)})},Rt.API.putTotalPages=function(r){var t,e=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(t=new RegExp(r,"g"),e=this.internal.getNumberOfPages()):(t=new RegExp(this.pdfEscape16(r,this.internal.getFont()),"g"),e=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var n=1;n<=this.internal.getNumberOfPages();n++)for(var i=0;i<this.internal.pages[n].length;i++)this.internal.pages[n][i]=this.internal.pages[n][i].replace(t,e);return this},Rt.API.viewerPreferences=function(r,t){var e;r=r||{},t=t||!1;var n,i,a,u={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},l=Object.keys(u),h=[],f=0,p=0,y=0;function x(P,C){var D,S=!1;for(D=0;D<P.length;D+=1)P[D]===C&&(S=!0);return S}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(u)),this.internal.viewerpreferences.isSubscribed=!1),e=this.internal.viewerpreferences.configuration,r==="reset"||t===!0){var g=l.length;for(y=0;y<g;y+=1)e[l[y]].value=e[l[y]].defaultValue,e[l[y]].explicitSet=!1}if(_e(r)==="object"){for(i in r)if(a=r[i],x(l,i)&&a!==void 0){if(e[i].type==="boolean"&&typeof a=="boolean")e[i].value=a;else if(e[i].type==="name"&&x(e[i].valueSet,a))e[i].value=a;else if(e[i].type==="integer"&&Number.isInteger(a))e[i].value=a;else if(e[i].type==="array"){for(f=0;f<a.length;f+=1)if(n=!0,a[f].length===1&&typeof a[f][0]=="number")h.push(String(a[f]-1));else if(a[f].length>1){for(p=0;p<a[f].length;p+=1)typeof a[f][p]!="number"&&(n=!1);n===!0&&h.push([a[f][0]-1,a[f][1]-1].join(" "))}e[i].value="["+h.join(" ")+"]"}else e[i].value=e[i].defaultValue;e[i].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var P,C=[];for(P in e)e[P].explicitSet===!0&&(e[P].type==="name"?C.push("/"+P+" /"+e[P].value):C.push("/"+P+" "+e[P].value));C.length!==0&&this.internal.write("/ViewerPreferences\n<<\n"+C.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=e,this},(function(r){var t=function(){var n='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',i=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),a=unescape(encodeURIComponent(n)),u=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),l=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),h=unescape(encodeURIComponent("</x:xmpmeta>")),f=a.length+u.length+l.length+i.length+h.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+f+" >>"),this.internal.write("stream"),this.internal.write(i+a+u+l+h),this.internal.write("endstream"),this.internal.write("endobj")},e=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};r.addMetadata=function(n,i){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:n,namespaceuri:i||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",e),this.internal.events.subscribe("postPutResources",t)),this}})(Rt.API),(function(r){var t=r.API,e=t.pdfEscape16=function(a,u){for(var l,h=u.metadata.Unicode.widths,f=["","0","00","000","0000"],p=[""],y=0,x=a.length;y<x;++y){if(l=u.metadata.characterToGlyph(a.charCodeAt(y)),u.metadata.glyIdsUsed.push(l),u.metadata.toUnicode[l]=a.charCodeAt(y),h.indexOf(l)==-1&&(h.push(l),h.push([parseInt(u.metadata.widthOfGlyph(l),10)])),l=="0")return p.join("");l=l.toString(16),p.push(f[4-l.length],l)}return p.join("")},n=function(a){var u,l,h,f,p,y,x;for(p="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",h=[],y=0,x=(l=Object.keys(a).sort(function(g,P){return g-P})).length;y<x;y++)u=l[y],h.length>=100&&(p+="\n"+h.length+" beginbfchar\n"+h.join("\n")+"\nendbfchar",h=[]),a[u]!==void 0&&a[u]!==null&&typeof a[u].toString=="function"&&(f=("0000"+a[u].toString(16)).slice(-4),u=("0000"+(+u).toString(16)).slice(-4),h.push("<"+u+"><"+f+">"));return h.length&&(p+="\n"+h.length+" beginbfchar\n"+h.join("\n")+"\nendbfchar\n"),p+"endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};t.events.push(["putFont",function(a){(function(u){var l=u.font,h=u.out,f=u.newObject,p=u.putStream;if(l.metadata instanceof r.API.TTFFont&&l.encoding==="Identity-H"){for(var y=l.metadata.Unicode.widths,x=l.metadata.subset.encode(l.metadata.glyIdsUsed,1),g="",P=0;P<x.length;P++)g+=String.fromCharCode(x[P]);var C=f();p({data:g,addLength1:!0,objectId:C}),h("endobj");var D=f();p({data:n(l.metadata.toUnicode),addLength1:!0,objectId:D}),h("endobj");var S=f();h("<<"),h("/Type /FontDescriptor"),h("/FontName /"+Zi(l.fontName)),h("/FontFile2 "+C+" 0 R"),h("/FontBBox "+r.API.PDFObject.convert(l.metadata.bbox)),h("/Flags "+l.metadata.flags),h("/StemV "+l.metadata.stemV),h("/ItalicAngle "+l.metadata.italicAngle),h("/Ascent "+l.metadata.ascender),h("/Descent "+l.metadata.decender),h("/CapHeight "+l.metadata.capHeight),h(">>"),h("endobj");var W=f();h("<<"),h("/Type /Font"),h("/BaseFont /"+Zi(l.fontName)),h("/FontDescriptor "+S+" 0 R"),h("/W "+r.API.PDFObject.convert(y)),h("/CIDToGIDMap /Identity"),h("/DW 1000"),h("/Subtype /CIDFontType2"),h("/CIDSystemInfo"),h("<<"),h("/Supplement 0"),h("/Registry (Adobe)"),h("/Ordering ("+l.encoding+")"),h(">>"),h(">>"),h("endobj"),l.objectNumber=f(),h("<<"),h("/Type /Font"),h("/Subtype /Type0"),h("/ToUnicode "+D+" 0 R"),h("/BaseFont /"+Zi(l.fontName)),h("/Encoding /"+l.encoding),h("/DescendantFonts ["+W+" 0 R]"),h(">>"),h("endobj"),l.isAlreadyPutted=!0}})(a)}]),t.events.push(["putFont",function(a){(function(u){var l=u.font,h=u.out,f=u.newObject,p=u.putStream;if(l.metadata instanceof r.API.TTFFont&&l.encoding==="WinAnsiEncoding"){for(var y=l.metadata.rawData,x="",g=0;g<y.length;g++)x+=String.fromCharCode(y[g]);var P=f();p({data:x,addLength1:!0,objectId:P}),h("endobj");var C=f();p({data:n(l.metadata.toUnicode),addLength1:!0,objectId:C}),h("endobj");var D=f();h("<<"),h("/Descent "+l.metadata.decender),h("/CapHeight "+l.metadata.capHeight),h("/StemV "+l.metadata.stemV),h("/Type /FontDescriptor"),h("/FontFile2 "+P+" 0 R"),h("/Flags 96"),h("/FontBBox "+r.API.PDFObject.convert(l.metadata.bbox)),h("/FontName /"+Zi(l.fontName)),h("/ItalicAngle "+l.metadata.italicAngle),h("/Ascent "+l.metadata.ascender),h(">>"),h("endobj"),l.objectNumber=f();for(var S=0;S<l.metadata.hmtx.widths.length;S++)l.metadata.hmtx.widths[S]=parseInt(l.metadata.hmtx.widths[S]*(1e3/l.metadata.head.unitsPerEm));h("<</Subtype/TrueType/Type/Font/ToUnicode "+C+" 0 R/BaseFont/"+Zi(l.fontName)+"/FontDescriptor "+D+" 0 R/Encoding/"+l.encoding+" /FirstChar 29 /LastChar 255 /Widths "+r.API.PDFObject.convert(l.metadata.hmtx.widths)+">>"),h("endobj"),l.isAlreadyPutted=!0}})(a)}]);var i=function(a){var u,l=a.text||"",h=a.x,f=a.y,p=a.options||{},y=a.mutex||{},x=y.pdfEscape,g=y.activeFontKey,P=y.fonts,C=g,D="",S=0,W="",z=P[C].encoding;if(P[C].encoding!=="Identity-H")return{text:l,x:h,y:f,options:p,mutex:y};for(W=l,C=g,Array.isArray(l)&&(W=l[0]),S=0;S<W.length;S+=1)P[C].metadata.hasOwnProperty("cmap")&&(u=P[C].metadata.cmap.unicode.codeMap[W[S].charCodeAt(0)]),u||W[S].charCodeAt(0)<256&&P[C].metadata.hasOwnProperty("Unicode")?D+=W[S]:D+="";var M="";return parseInt(C.slice(1))<14||z==="WinAnsiEncoding"?M=x(D,C).split("").map(function(at){return at.charCodeAt(0).toString(16)}).join(""):z==="Identity-H"&&(M=e(D,P[C])),y.isHex=!0,{text:M,x:h,y:f,options:p,mutex:y}};t.events.push(["postProcessText",function(a){var u=a.text||"",l=[],h={text:u,x:a.x,y:a.y,options:a.options,mutex:a.mutex};if(Array.isArray(u)){var f=0;for(f=0;f<u.length;f+=1)Array.isArray(u[f])&&u[f].length===3?l.push([i(Object.assign({},h,{text:u[f][0]})).text,u[f][1],u[f][2]]):l.push(i(Object.assign({},h,{text:u[f]})).text);a.text=l}else a.text=i(Object.assign({},h,{text:u})).text}])})(Rt),(function(r){var t=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};r.existsFileInVFS=function(e){return t.call(this),this.internal.vFS[e]!==void 0},r.addFileToVFS=function(e,n){return t.call(this),this.internal.vFS[e]=n,this},r.getFileFromVFS=function(e){return t.call(this),this.internal.vFS[e]!==void 0?this.internal.vFS[e]:null}})(Rt.API),(function(r){r.__bidiEngine__=r.prototype.__bidiEngine__=function(n){var i,a,u,l,h,f,p,y=t,x=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],g=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],P={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},C={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},D=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],S=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),W=!1,z=0;this.__bidiEngine__={};var M=function(N){var O=N.charCodeAt(),G=O>>8,U=C[G];return U!==void 0?y[256*U+(255&O)]:G===252||G===253?"AL":S.test(G)?"L":G===8?"R":"N"},at=function(N){for(var O,G=0;G<N.length;G++){if((O=M(N.charAt(G)))==="L")return!1;if(O==="R")return!0}return!1},vt=function(N,O,G,U){var nt,ht,dt,rt,ft=O[U];switch(ft){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":W=!1;break;case"N":case"AN":break;case"EN":W&&(ft="AN");break;case"AL":W=!0,ft="R";break;case"WS":case"BN":ft="N";break;case"CS":U<1||U+1>=O.length||(nt=G[U-1])!=="EN"&&nt!=="AN"||(ht=O[U+1])!=="EN"&&ht!=="AN"?ft="N":W&&(ht="AN"),ft=ht===nt?ht:"N";break;case"ES":ft=(nt=U>0?G[U-1]:"B")==="EN"&&U+1<O.length&&O[U+1]==="EN"?"EN":"N";break;case"ET":if(U>0&&G[U-1]==="EN"){ft="EN";break}if(W){ft="N";break}for(dt=U+1,rt=O.length;dt<rt&&O[dt]==="ET";)dt++;ft=dt<rt&&O[dt]==="EN"?"EN":"N";break;case"NSM":if(u&&!l){for(rt=O.length,dt=U+1;dt<rt&&O[dt]==="NSM";)dt++;if(dt<rt){var Nt=N[U],wt=Nt>=1425&&Nt<=2303||Nt===64286;if(nt=O[dt],wt&&(nt==="R"||nt==="AL")){ft="R";break}}}ft=U<1||(nt=O[U-1])==="B"?"N":G[U-1];break;case"B":W=!1,i=!0,ft=z;break;case"S":a=!0,ft="N"}return ft},ot=function(N,O,G){var U=N.split("");return G&&$(U,G,{hiLevel:z}),U.reverse(),O&&O.reverse(),U.join("")},$=function(N,O,G){var U,nt,ht,dt,rt,ft=-1,Nt=N.length,wt=0,A=[],B=z?g:x,T=[];for(W=!1,i=!1,a=!1,nt=0;nt<Nt;nt++)T[nt]=M(N[nt]);for(ht=0;ht<Nt;ht++){if(rt=wt,A[ht]=vt(N,T,A,ht),U=240&(wt=B[rt][P[A[ht]]]),wt&=15,O[ht]=dt=B[wt][5],U>0)if(U===16){for(nt=ft;nt<ht;nt++)O[nt]=1;ft=-1}else ft=-1;if(B[wt][6])ft===-1&&(ft=ht);else if(ft>-1){for(nt=ft;nt<ht;nt++)O[nt]=dt;ft=-1}T[ht]==="B"&&(O[ht]=0),G.hiLevel|=dt}a&&(function(V,Y,Z){for(var lt=0;lt<Z;lt++)if(V[lt]==="S"){Y[lt]=z;for(var it=lt-1;it>=0&&V[it]==="WS";it--)Y[it]=z}})(T,O,Nt)},R=function(N,O,G,U,nt){if(!(nt.hiLevel<N)){if(N===1&&z===1&&!i)return O.reverse(),void(G&&G.reverse());for(var ht,dt,rt,ft,Nt=O.length,wt=0;wt<Nt;){if(U[wt]>=N){for(rt=wt+1;rt<Nt&&U[rt]>=N;)rt++;for(ft=wt,dt=rt-1;ft<dt;ft++,dt--)ht=O[ft],O[ft]=O[dt],O[dt]=ht,G&&(ht=G[ft],G[ft]=G[dt],G[dt]=ht);wt=rt}wt++}}},et=function(N,O,G){var U=N.split(""),nt={hiLevel:z};return G||(G=[]),$(U,G,nt),(function(ht,dt,rt){if(rt.hiLevel!==0&&p)for(var ft,Nt=0;Nt<ht.length;Nt++)dt[Nt]===1&&(ft=D.indexOf(ht[Nt]))>=0&&(ht[Nt]=D[ft+1])})(U,G,nt),R(2,U,O,G,nt),R(1,U,O,G,nt),U.join("")};return this.__bidiEngine__.doBidiReorder=function(N,O,G){if((function(nt,ht){if(ht)for(var dt=0;dt<nt.length;dt++)ht[dt]=dt;l===void 0&&(l=at(nt)),f===void 0&&(f=at(nt))})(N,O),u||!h||f)if(u&&h&&l^f)z=l?1:0,N=ot(N,O,G);else if(!u&&h&&f)z=l?1:0,N=et(N,O,G),N=ot(N,O);else if(!u||l||h||f){if(u&&!h&&l^f)N=ot(N,O),l?(z=0,N=et(N,O,G)):(z=1,N=et(N,O,G),N=ot(N,O));else if(u&&l&&!h&&f)z=1,N=et(N,O,G),N=ot(N,O);else if(!u&&!h&&l^f){var U=p;l?(z=1,N=et(N,O,G),z=0,p=!1,N=et(N,O,G),p=U):(z=0,N=et(N,O,G),N=ot(N,O),z=1,p=!1,N=et(N,O,G),p=U,N=ot(N,O))}}else z=0,N=et(N,O,G);else z=l?1:0,N=et(N,O,G);return N},this.__bidiEngine__.setOptions=function(N){N&&(u=N.isInputVisual,h=N.isOutputVisual,l=N.isInputRtl,f=N.isOutputRtl,p=N.isSymmetricSwapping)},this.__bidiEngine__.setOptions(n),this.__bidiEngine__};var t=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],e=new r.__bidiEngine__({isInputVisual:!0});r.API.events.push(["postProcessText",function(n){var i=n.text;n.x,n.y;var a=n.options||{};n.mutex,a.lang;var u=[];if(a.isInputVisual=typeof a.isInputVisual!="boolean"||a.isInputVisual,e.setOptions(a),Object.prototype.toString.call(i)==="[object Array]"){var l=0;for(u=[],l=0;l<i.length;l+=1)Object.prototype.toString.call(i[l])==="[object Array]"?u.push([e.doBidiReorder(i[l][0]),i[l][1],i[l][2]]):u.push([e.doBidiReorder(i[l])]);n.text=u}else n.text=e.doBidiReorder(i);e.setOptions({isInputVisual:!0})}])})(Rt),Rt.API.TTFFont=(function(){function r(t){var e;if(this.rawData=t,e=this.contents=new ai(t),this.contents.pos=4,e.readString(4)==="ttcf")throw new Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new l5(this),this.registerTTF()}return r.open=function(t){return new r(t)},r.prototype.parse=function(){return this.directory=new Y2(this.contents),this.head=new $2(this),this.name=new e5(this),this.cmap=new wf(this),this.toUnicode={},this.hhea=new X2(this),this.maxp=new r5(this),this.hmtx=new n5(this),this.post=new Q2(this),this.os2=new Z2(this),this.loca=new s5(this),this.glyf=new i5(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},r.prototype.registerTTF=function(){var t,e,n,i,a;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var u,l,h,f;for(f=[],u=0,l=(h=this.bbox).length;u<l;u++)t=h[u],f.push(Math.round(t*this.scaleFactor));return f}).call(this),this.stemV=0,this.post.exists?(n=255&(i=this.post.italic_angle),32768&(e=i>>16)&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+n)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(a=this.familyClass)===1||a===2||a===3||a===4||a===5||a===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},r.prototype.characterToGlyph=function(t){var e;return((e=this.cmap.unicode)!=null?e.codeMap[t]:void 0)||0},r.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},r.prototype.widthOfString=function(t,e,n){var i,a,u,l;for(u=0,a=0,l=(t=""+t).length;0<=l?a<l:a>l;a=0<=l?++a:--a)i=t.charCodeAt(a),u+=this.widthOfGlyph(this.characterToGlyph(i))+n*(1e3/e)||0;return u*(e/1e3)},r.prototype.lineHeight=function(t,e){var n;return e==null&&(e=!1),n=e?this.lineGap:0,(this.ascender+n-this.decender)/1e3*t},r})();var ln,ai=(function(){function r(t){this.data=t!=null?t:[],this.pos=0,this.length=this.data.length}return r.prototype.readByte=function(){return this.data[this.pos++]},r.prototype.writeByte=function(t){return this.data[this.pos++]=t},r.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},r.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},r.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=2147483648?t-4294967296:t},r.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},r.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},r.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},r.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},r.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},r.prototype.readString=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)n[e]=String.fromCharCode(this.readByte());return n.join("")},r.prototype.writeString=function(t){var e,n,i;for(i=[],e=0,n=t.length;0<=n?e<n:e>n;e=0<=n?++e:--e)i.push(this.writeByte(t.charCodeAt(e)));return i},r.prototype.readShort=function(){return this.readInt16()},r.prototype.writeShort=function(t){return this.writeInt16(t)},r.prototype.readLongLong=function(){var t,e,n,i,a,u,l,h;return t=this.readByte(),e=this.readByte(),n=this.readByte(),i=this.readByte(),a=this.readByte(),u=this.readByte(),l=this.readByte(),h=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^n)+4294967296*(255^i)+16777216*(255^a)+65536*(255^u)+256*(255^l)+(255^h)+1):72057594037927940*t+281474976710656*e+1099511627776*n+4294967296*i+16777216*a+65536*u+256*l+h},r.prototype.writeLongLong=function(t){var e,n;return e=Math.floor(t/4294967296),n=**********&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},r.prototype.readInt=function(){return this.readInt32()},r.prototype.writeInt=function(t){return this.writeInt32(t)},r.prototype.read=function(t){var e,n;for(e=[],n=0;0<=t?n<t:n>t;n=0<=t?++n:--n)e.push(this.readByte());return e},r.prototype.write=function(t){var e,n,i,a;for(a=[],n=0,i=t.length;n<i;n++)e=t[n],a.push(this.writeByte(e));return a},r})(),Y2=(function(){var r;function t(e){var n,i,a;for(this.scalarType=e.readInt(),this.tableCount=e.readShort(),this.searchRange=e.readShort(),this.entrySelector=e.readShort(),this.rangeShift=e.readShort(),this.tables={},i=0,a=this.tableCount;0<=a?i<a:i>a;i=0<=a?++i:--i)n={tag:e.readString(4),checksum:e.readInt(),offset:e.readInt(),length:e.readInt()},this.tables[n.tag]=n}return t.prototype.encode=function(e){var n,i,a,u,l,h,f,p,y,x,g,P,C;for(C in g=Object.keys(e).length,h=Math.log(2),y=16*Math.floor(Math.log(g)/h),u=Math.floor(y/h),p=16*g-y,(i=new ai).writeInt(this.scalarType),i.writeShort(g),i.writeShort(y),i.writeShort(u),i.writeShort(p),a=16*g,f=i.pos+a,l=null,P=[],e)for(x=e[C],i.writeString(C),i.writeInt(r(x)),i.writeInt(f),i.writeInt(x.length),P=P.concat(x),C==="head"&&(l=f),f+=x.length;f%4;)P.push(0),f++;return i.write(P),n=2981146554-r(i.data),i.pos=l+8,i.writeUInt32(n),i.data},r=function(e){var n,i,a,u;for(e=xf.call(e);e.length%4;)e.push(0);for(a=new ai(e),i=0,n=0,u=e.length;n<u;n=n+=4)i+=a.readUInt32();return **********&i},t})(),J2={}.hasOwnProperty,_n=function(r,t){for(var e in t)J2.call(t,e)&&(r[e]=t[e]);function n(){this.constructor=r}return n.prototype=t.prototype,r.prototype=new n,r.__super__=t.prototype,r};ln=(function(){function r(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return r.prototype.parse=function(){},r.prototype.encode=function(){},r.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},r})();var $2=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="head",r.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},r.prototype.encode=function(t){var e;return(e=new ai).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},r})(),_h=(function(){function r(t,e){var n,i,a,u,l,h,f,p,y,x,g,P,C,D,S,W,z;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),y=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(h=0;h<256;++h)this.codeMap[h]=t.readByte();break;case 4:for(g=t.readUInt16(),x=g/2,t.pos+=6,a=(function(){var M,at;for(at=[],h=M=0;0<=x?M<x:M>x;h=0<=x?++M:--M)at.push(t.readUInt16());return at})(),t.pos+=2,C=(function(){var M,at;for(at=[],h=M=0;0<=x?M<x:M>x;h=0<=x?++M:--M)at.push(t.readUInt16());return at})(),f=(function(){var M,at;for(at=[],h=M=0;0<=x?M<x:M>x;h=0<=x?++M:--M)at.push(t.readUInt16());return at})(),p=(function(){var M,at;for(at=[],h=M=0;0<=x?M<x:M>x;h=0<=x?++M:--M)at.push(t.readUInt16());return at})(),i=(this.length-t.pos+this.offset)/2,l=(function(){var M,at;for(at=[],h=M=0;0<=i?M<i:M>i;h=0<=i?++M:--M)at.push(t.readUInt16());return at})(),h=S=0,z=a.length;S<z;h=++S)for(D=a[h],n=W=P=C[h];P<=D?W<=D:W>=D;n=P<=D?++W:--W)p[h]===0?u=n+f[h]:(u=l[p[h]/2+(n-P)-(x-h)]||0)!==0&&(u+=f[h]),this.codeMap[n]=65535&u}t.pos=y}return r.encode=function(t,e){var n,i,a,u,l,h,f,p,y,x,g,P,C,D,S,W,z,M,at,vt,ot,$,R,et,N,O,G,U,nt,ht,dt,rt,ft,Nt,wt,A,B,T,V,Y,Z,lt,it,mt,Lt,Pt;switch(U=new ai,u=Object.keys(t).sort(function(Ct,st){return Ct-st}),e){case"macroman":for(C=0,D=(function(){var Ct=[];for(P=0;P<256;++P)Ct.push(0);return Ct})(),W={0:0},a={},nt=0,ft=u.length;nt<ft;nt++)W[it=t[i=u[nt]]]==null&&(W[it]=++C),a[i]={old:t[i],new:W[t[i]]},D[i]=W[t[i]];return U.writeUInt16(1),U.writeUInt16(0),U.writeUInt32(12),U.writeUInt16(0),U.writeUInt16(262),U.writeUInt16(0),U.write(D),{charMap:a,subtable:U.data,maxGlyphID:C+1};case"unicode":for(O=[],y=[],z=0,W={},n={},S=f=null,ht=0,Nt=u.length;ht<Nt;ht++)W[at=t[i=u[ht]]]==null&&(W[at]=++z),n[i]={old:at,new:W[at]},l=W[at]-i,S!=null&&l===f||(S&&y.push(S),O.push(i),f=l),S=i;for(S&&y.push(S),y.push(65535),O.push(65535),et=2*(R=O.length),$=2*Math.pow(Math.log(R)/Math.LN2,2),x=Math.log($/2)/Math.LN2,ot=2*R-$,h=[],vt=[],g=[],P=dt=0,wt=O.length;dt<wt;P=++dt){if(N=O[P],p=y[P],N===65535){h.push(0),vt.push(0);break}if(N-(G=n[N].new)>=32768)for(h.push(0),vt.push(2*(g.length+R-P)),i=rt=N;N<=p?rt<=p:rt>=p;i=N<=p?++rt:--rt)g.push(n[i].new);else h.push(G-N),vt.push(0)}for(U.writeUInt16(3),U.writeUInt16(1),U.writeUInt32(12),U.writeUInt16(4),U.writeUInt16(16+8*R+2*g.length),U.writeUInt16(0),U.writeUInt16(et),U.writeUInt16($),U.writeUInt16(x),U.writeUInt16(ot),Z=0,A=y.length;Z<A;Z++)i=y[Z],U.writeUInt16(i);for(U.writeUInt16(0),lt=0,B=O.length;lt<B;lt++)i=O[lt],U.writeUInt16(i);for(mt=0,T=h.length;mt<T;mt++)l=h[mt],U.writeUInt16(l);for(Lt=0,V=vt.length;Lt<V;Lt++)M=vt[Lt],U.writeUInt16(M);for(Pt=0,Y=g.length;Pt<Y;Pt++)C=g[Pt],U.writeUInt16(C);return{charMap:n,subtable:U.data,maxGlyphID:z+1}}},r})(),wf=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="cmap",r.prototype.parse=function(t){var e,n,i;for(t.pos=this.offset,this.version=t.readUInt16(),i=t.readUInt16(),this.tables=[],this.unicode=null,n=0;0<=i?n<i:n>i;n=0<=i?++n:--n)e=new _h(t,this.offset),this.tables.push(e),e.isUnicode&&this.unicode==null&&(this.unicode=e);return!0},r.encode=function(t,e){var n,i;return e==null&&(e="macroman"),n=_h.encode(t,e),(i=new ai).writeUInt16(0),i.writeUInt16(1),n.table=i.data.concat(n.subtable),n},r})(),X2=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="hhea",r.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},r})(),Z2=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="OS/2",r.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=(function(){var e,n;for(n=[],e=0;e<10;++e)n.push(t.readByte());return n})(),this.charRange=(function(){var e,n;for(n=[],e=0;e<4;++e)n.push(t.readInt());return n})(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=(function(){var e,n;for(n=[],e=0;e<2;e=++e)n.push(t.readInt());return n})(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},r})(),Q2=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="post",r.prototype.parse=function(t){var e,n,i;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:case 196608:break;case 131072:var a;for(n=t.readUInt16(),this.glyphNameIndex=[],a=0;0<=n?a<n:a>n;a=0<=n?++a:--a)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],i=[];t.pos<this.offset+this.length;)e=t.readByte(),i.push(this.names.push(t.readString(e)));return i;case 151552:return n=t.readUInt16(),this.offsets=t.read(n);case 262144:return this.map=(function(){var u,l,h;for(h=[],a=u=0,l=this.file.maxp.numGlyphs;0<=l?u<l:u>l;a=0<=l?++u:--u)h.push(t.readUInt32());return h}).call(this)}},r})(),t5=function(r,t){this.raw=r,this.length=r.length,this.platformID=t.platformID,this.encodingID=t.encodingID,this.languageID=t.languageID},e5=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="name",r.prototype.parse=function(t){var e,n,i,a,u,l,h,f,p,y,x;for(t.pos=this.offset,t.readShort(),e=t.readShort(),l=t.readShort(),n=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)n.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+l+t.readShort()});for(h={},a=p=0,y=n.length;p<y;a=++p)i=n[a],t.pos=i.offset,f=t.readString(i.length),u=new t5(f,i),h[x=i.nameID]==null&&(h[x]=[]),h[i.nameID].push(u);this.strings=h,this.copyright=h[0],this.fontFamily=h[1],this.fontSubfamily=h[2],this.uniqueSubfamily=h[3],this.fontName=h[4],this.version=h[5];try{this.postscriptName=h[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(g){this.postscriptName=h[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=h[7],this.manufacturer=h[8],this.designer=h[9],this.description=h[10],this.vendorUrl=h[11],this.designerUrl=h[12],this.license=h[13],this.licenseUrl=h[14],this.preferredFamily=h[15],this.preferredSubfamily=h[17],this.compatibleFull=h[18],this.sampleText=h[19]},r})(),r5=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="maxp",r.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},r})(),n5=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="hmtx",r.prototype.parse=function(t){var e,n,i,a,u,l,h;for(t.pos=this.offset,this.metrics=[],e=0,l=this.file.hhea.numberOfMetrics;0<=l?e<l:e>l;e=0<=l?++e:--e)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(i=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=(function(){var f,p;for(p=[],e=f=0;0<=i?f<i:f>i;e=0<=i?++f:--f)p.push(t.readInt16());return p})(),this.widths=(function(){var f,p,y,x;for(x=[],f=0,p=(y=this.metrics).length;f<p;f++)a=y[f],x.push(a.advance);return x}).call(this),n=this.widths[this.widths.length-1],h=[],e=u=0;0<=i?u<i:u>i;e=0<=i?++u:--u)h.push(this.widths.push(n));return h},r.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},r})(),xf=[].slice,i5=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="glyf",r.prototype.parse=function(){return this.cache={}},r.prototype.glyphFor=function(t){var e,n,i,a,u,l,h,f,p,y;return t in this.cache?this.cache[t]:(a=this.file.loca,e=this.file.contents,n=a.indexOf(t),(i=a.lengthOf(t))===0?this.cache[t]=null:(e.pos=this.offset+n,u=(l=new ai(e.read(i))).readShort(),f=l.readShort(),y=l.readShort(),h=l.readShort(),p=l.readShort(),this.cache[t]=u===-1?new o5(l,f,y,h,p):new a5(l,u,f,y,h,p),this.cache[t]))},r.prototype.encode=function(t,e,n){var i,a,u,l,h;for(u=[],a=[],l=0,h=e.length;l<h;l++)i=t[e[l]],a.push(u.length),i&&(u=u.concat(i.encode(n)));return a.push(u.length),{table:u,offsets:a}},r})(),a5=(function(){function r(t,e,n,i,a,u){this.raw=t,this.numberOfContours=e,this.xMin=n,this.yMin=i,this.xMax=a,this.yMax=u,this.compound=!1}return r.prototype.encode=function(){return this.raw.data},r})(),o5=(function(){function r(t,e,n,i,a){var u,l;for(this.raw=t,this.xMin=e,this.yMin=n,this.xMax=i,this.yMax=a,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],u=this.raw;l=u.readShort(),this.glyphOffsets.push(u.pos),this.glyphIDs.push(u.readUInt16()),32&l;)u.pos+=1&l?4:2,128&l?u.pos+=8:64&l?u.pos+=4:8&l&&(u.pos+=2)}return r.prototype.encode=function(){var t,e,n;for(e=new ai(xf.call(this.raw.data)),t=0,n=this.glyphIDs.length;t<n;++t)e.pos=this.glyphOffsets[t];return e.data},r})(),s5=(function(){function r(){return r.__super__.constructor.apply(this,arguments)}return _n(r,ln),r.prototype.tag="loca",r.prototype.parse=function(t){var e,n;return t.pos=this.offset,e=this.file.head.indexToLocFormat,this.offsets=e===0?(function(){var i,a;for(a=[],n=0,i=this.length;n<i;n+=2)a.push(2*t.readUInt16());return a}).call(this):(function(){var i,a;for(a=[],n=0,i=this.length;n<i;n+=4)a.push(t.readUInt32());return a}).call(this)},r.prototype.indexOf=function(t){return this.offsets[t]},r.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},r.prototype.encode=function(t,e){for(var n=new Uint32Array(this.offsets.length),i=0,a=0,u=0;u<n.length;++u)if(n[u]=i,a<e.length&&e[a]==u){++a,n[u]=i;var l=this.offsets[u],h=this.offsets[u+1]-l;h>0&&(i+=h)}for(var f=new Array(4*n.length),p=0;p<n.length;++p)f[4*p+3]=255&n[p],f[4*p+2]=(65280&n[p])>>8,f[4*p+1]=(16711680&n[p])>>16,f[4*p]=(**********&n[p])>>24;return f},r})(),l5=(function(){function r(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return r.prototype.generateCmap=function(){var t,e,n,i,a;for(e in i=this.font.cmap.tables[0].codeMap,t={},a=this.subset)n=a[e],t[e]=i[n];return t},r.prototype.glyphsFor=function(t){var e,n,i,a,u,l,h;for(i={},u=0,l=t.length;u<l;u++)i[a=t[u]]=this.font.glyf.glyphFor(a);for(a in e=[],i)(n=i[a])!=null&&n.compound&&e.push.apply(e,n.glyphIDs);if(e.length>0)for(a in h=this.glyphsFor(e))n=h[a],i[a]=n;return i},r.prototype.encode=function(t,e){var n,i,a,u,l,h,f,p,y,x,g,P,C,D,S;for(i in n=wf.encode(this.generateCmap(),"unicode"),u=this.glyphsFor(t),g={0:0},S=n.charMap)g[(h=S[i]).old]=h.new;for(P in x=n.maxGlyphID,u)P in g||(g[P]=x++);return p=(function(W){var z,M;for(z in M={},W)M[W[z]]=z;return M})(g),y=Object.keys(p).sort(function(W,z){return W-z}),C=(function(){var W,z,M;for(M=[],W=0,z=y.length;W<z;W++)l=y[W],M.push(p[l]);return M})(),a=this.font.glyf.encode(u,C,g),f=this.font.loca.encode(a.offsets,C),D={cmap:this.font.cmap.raw(),glyf:a.table,loca:f,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(D["OS/2"]=this.font.os2.raw()),this.font.directory.encode(D)},r})();Rt.API.PDFObject=(function(){var r;function t(){}return r=function(e,n){return(Array(n+1).join("0")+e).slice(-n)},t.convert=function(e){var n,i,a,u;if(Array.isArray(e))return"["+(function(){var l,h,f;for(f=[],l=0,h=e.length;l<h;l++)n=e[l],f.push(t.convert(n));return f})().join(" ")+"]";if(typeof e=="string")return"/"+e;if(e!=null&&e.isString)return"("+e+")";if(e instanceof Date)return"(D:"+r(e.getUTCFullYear(),4)+r(e.getUTCMonth(),2)+r(e.getUTCDate(),2)+r(e.getUTCHours(),2)+r(e.getUTCMinutes(),2)+r(e.getUTCSeconds(),2)+"Z)";if({}.toString.call(e)==="[object Object]"){for(i in a=["<<"],e)u=e[i],a.push("/"+i+" "+t.convert(u));return a.push(">>"),a.join("\n")}return""+e},t})();function _f(r,t,e,n,i){n=n||{};var a=1.15,u=i.internal.scaleFactor,l=i.internal.getFontSize()/u,h=i.getLineHeightFactor?i.getLineHeightFactor():a,f=l*h,p=/\r\n|\r|\n/g,y="",x=1;if((n.valign==="middle"||n.valign==="bottom"||n.halign==="center"||n.halign==="right")&&(y=typeof r=="string"?r.split(p):r,x=y.length||1),e+=l*(2-a),n.valign==="middle"?e-=x/2*f:n.valign==="bottom"&&(e-=x*f),n.halign==="center"||n.halign==="right"){var g=l;if(n.halign==="center"&&(g*=.5),y&&x>=1){for(var P=0;P<y.length;P++)i.text(y[P],t-i.getStringUnitWidth(y[P])*g,e),e+=f;return i}t-=i.getStringUnitWidth(r)*g}return n.halign==="justify"?i.text(r,t,e,{maxWidth:n.maxWidth||100,align:"justify"}):i.text(r,t,e),i}var Ah={},ia=(function(){function r(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return r.setDefaults=function(t,e){e===void 0&&(e=null),e?e.__autoTableDocumentDefaults=t:Ah=t},r.unifyColor=function(t){return Array.isArray(t)?t:typeof t=="number"?[t,t,t]:typeof t=="string"?[t]:null},r.prototype.applyStyles=function(t,e){var n,i,a;e===void 0&&(e=!1),t.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(t.fontStyle);var u=this.jsPDFDocument.internal.getFont(),l=u.fontStyle,h=u.fontName;if(t.font&&(h=t.font),t.fontStyle){l=t.fontStyle;var f=this.getFontList()[h];f&&f.indexOf(l)===-1&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(f[0]),l=f[0])}if(this.jsPDFDocument.setFont(h,l),t.fontSize&&this.jsPDFDocument.setFontSize(t.fontSize),!e){var p=r.unifyColor(t.fillColor);p&&(n=this.jsPDFDocument).setFillColor.apply(n,p),p=r.unifyColor(t.textColor),p&&(i=this.jsPDFDocument).setTextColor.apply(i,p),p=r.unifyColor(t.lineColor),p&&(a=this.jsPDFDocument).setDrawColor.apply(a,p),typeof t.lineWidth=="number"&&this.jsPDFDocument.setLineWidth(t.lineWidth)}},r.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},r.prototype.rect=function(t,e,n,i,a){return this.jsPDFDocument.rect(t,e,n,i,a)},r.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},r.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},r.prototype.getDocument=function(){return this.jsPDFDocument},r.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},r.prototype.addPage=function(){return this.jsPDFDocument.addPage()},r.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},r.prototype.getGlobalOptions=function(){return Ah||{}},r.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},r.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return t.width==null&&(t={width:t.getWidth(),height:t.getHeight()}),t},r.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},r.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},r.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},r.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},r})(),fl=function(r,t){return fl=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,n){e.__proto__=n}||function(e,n){for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])},fl(r,t)};function Af(r,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");fl(r,t);function e(){this.constructor=r}r.prototype=t===null?Object.create(t):(e.prototype=t.prototype,new e)}var Lf=(function(r){Af(t,r);function t(e){var n=r.call(this)||this;return n._element=e,n}return t})(Array);function u5(r){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/r,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}function h5(r){var t={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}};return t[r]}function qo(r,t,e){e.applyStyles(t,!0);var n=Array.isArray(r)?r:[r],i=n.map(function(a){return e.getTextWidth(a)}).reduce(function(a,u){return Math.max(a,u)},0);return i}function Nf(r,t,e,n){var i=t.settings.tableLineWidth,a=t.settings.tableLineColor;r.applyStyles({lineWidth:i,lineColor:a});var u=Sf(i,!1);u&&r.rect(e.x,e.y,t.getWidth(r.pageSize().width),n.y-e.y,u)}function Sf(r,t){var e=r>0,n=t||t===0;return e&&n?"DF":e?"S":n?"F":null}function Wo(r,t){var e,n,i,a;if(r=r||t,Array.isArray(r)){if(r.length>=4)return{top:r[0],right:r[1],bottom:r[2],left:r[3]};if(r.length===3)return{top:r[0],right:r[1],bottom:r[2],left:r[1]};if(r.length===2)return{top:r[0],right:r[1],bottom:r[0],left:r[1]};r.length===1?r=r[0]:r=t}return typeof r=="object"?(typeof r.vertical=="number"&&(r.top=r.vertical,r.bottom=r.vertical),typeof r.horizontal=="number"&&(r.right=r.horizontal,r.left=r.horizontal),{left:(e=r.left)!==null&&e!==void 0?e:t,top:(n=r.top)!==null&&n!==void 0?n:t,right:(i=r.right)!==null&&i!==void 0?i:t,bottom:(a=r.bottom)!==null&&a!==void 0?a:t}):(typeof r!="number"&&(r=t),{top:r,right:r,bottom:r,left:r})}function kf(r,t){var e=Wo(t.settings.margin,0);return r.pageSize().width-(e.left+e.right)}function f5(r,t,e,n,i){var a={},u=1.3333333333333333,l=Ys(t,function(z){return i.getComputedStyle(z).backgroundColor});l!=null&&(a.fillColor=l);var h=Ys(t,function(z){return i.getComputedStyle(z).color});h!=null&&(a.textColor=h);var f=d5(n,e);f&&(a.cellPadding=f);var p="borderTopColor",y=u*e,x=n.borderTopWidth;if(n.borderBottomWidth===x&&n.borderRightWidth===x&&n.borderLeftWidth===x){var g=(parseFloat(x)||0)/y;g&&(a.lineWidth=g)}else a.lineWidth={top:(parseFloat(n.borderTopWidth)||0)/y,right:(parseFloat(n.borderRightWidth)||0)/y,bottom:(parseFloat(n.borderBottomWidth)||0)/y,left:(parseFloat(n.borderLeftWidth)||0)/y},a.lineWidth.top||(a.lineWidth.right?p="borderRightColor":a.lineWidth.bottom?p="borderBottomColor":a.lineWidth.left&&(p="borderLeftColor"));var P=Ys(t,function(z){return i.getComputedStyle(z)[p]});P!=null&&(a.lineColor=P);var C=["left","right","center","justify"];C.indexOf(n.textAlign)!==-1&&(a.halign=n.textAlign),C=["middle","bottom","top"],C.indexOf(n.verticalAlign)!==-1&&(a.valign=n.verticalAlign);var D=parseInt(n.fontSize||"");isNaN(D)||(a.fontSize=D/u);var S=c5(n);S&&(a.fontStyle=S);var W=(n.fontFamily||"").toLowerCase();return r.indexOf(W)!==-1&&(a.font=W),a}function c5(r){var t="";return(r.fontWeight==="bold"||r.fontWeight==="bolder"||parseInt(r.fontWeight)>=700)&&(t="bold"),(r.fontStyle==="italic"||r.fontStyle==="oblique")&&(t+="italic"),t}function Ys(r,t){var e=Pf(r,t);if(!e)return null;var n=e.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!n||!Array.isArray(n))return null;var i=[parseInt(n[1]),parseInt(n[2]),parseInt(n[3])],a=parseInt(n[4]);return a===0||isNaN(i[0])||isNaN(i[1])||isNaN(i[2])?null:i}function Pf(r,t){var e=t(r);return e==="rgba(0, 0, 0, 0)"||e==="transparent"||e==="initial"||e==="inherit"?r.parentElement==null?null:Pf(r.parentElement,t):e}function d5(r,t){var e=[r.paddingTop,r.paddingRight,r.paddingBottom,r.paddingLeft],n=96/(72/t),i=(parseInt(r.lineHeight)-parseInt(r.fontSize))/t/2,a=e.map(function(l){return parseInt(l||"0")/n}),u=Wo(a,0);return i>u.top&&(u.top=i),i>u.bottom&&(u.bottom=i),u}function Cf(r,t,e,n,i){var a,u;n===void 0&&(n=!1),i===void 0&&(i=!1);var l;typeof t=="string"?l=e.document.querySelector(t):l=t;var h=Object.keys(r.getFontList()),f=r.scaleFactor(),p=[],y=[],x=[];if(!l)return console.error("Html table could not be found with input: ",t),{head:p,body:y,foot:x};for(var g=0;g<l.rows.length;g++){var P=l.rows[g],C=(u=(a=P==null?void 0:P.parentElement)===null||a===void 0?void 0:a.tagName)===null||u===void 0?void 0:u.toLowerCase(),D=p5(h,f,e,P,n,i);D&&(C==="thead"?p.push(D):C==="tfoot"?x.push(D):y.push(D))}return{head:p,body:y,foot:x}}function p5(r,t,e,n,i,a){for(var u=new Lf(n),l=0;l<n.cells.length;l++){var h=n.cells[l],f=e.getComputedStyle(h);if(i||f.display!=="none"){var p=void 0;a&&(p=f5(r,h,t,f,e)),u.push({rowSpan:h.rowSpan,colSpan:h.colSpan,styles:p,_element:h,content:g5(h)})}}var y=e.getComputedStyle(n);if(u.length>0&&(i||y.display!=="none"))return u}function g5(r){var t=r.cloneNode(!0);return t.innerHTML=t.innerHTML.replace(/\n/g,"").replace(/ +/g," "),t.innerHTML=t.innerHTML.split(/<br.*?>/).map(function(e){return e.trim()}).join("\n"),t.innerText||t.textContent||""}function m5(r,t,e){for(var n=0,i=[r,t,e];n<i.length;n++){var a=i[n];a&&typeof a!="object"&&console.error("The options parameter should be of type object, is: "+typeof a),a.startY&&typeof a.startY!="number"&&(console.error("Invalid value for startY option",a.startY),delete a.startY)}}function qn(r,t,e,n,i){if(r==null)throw new TypeError("Cannot convert undefined or null to object");for(var a=Object(r),u=1;u<arguments.length;u++){var l=arguments[u];if(l!=null)for(var h in l)Object.prototype.hasOwnProperty.call(l,h)&&(a[h]=l[h])}return a}function v5(r,t){var e=new ia(r),n=e.getDocumentOptions(),i=e.getGlobalOptions();m5(i,n,t);var a=qn({},i,n,t),u;typeof window<"u"&&(u=window);var l=b5(i,n,t),h=y5(i,n,t),f=w5(e,a),p=_5(e,a,u);return{id:t.tableId,content:p,hooks:h,styles:l,settings:f}}function b5(r,t,e){for(var n={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},i=function(h){if(h==="columnStyles"){var f=r[h],p=t[h],y=e[h];n.columnStyles=qn({},f,p,y)}else{var x=[r,t,e],g=x.map(function(P){return P[h]||{}});n[h]=qn({},g[0],g[1],g[2])}},a=0,u=Object.keys(n);a<u.length;a++){var l=u[a];i(l)}return n}function y5(r,t,e){for(var n=[r,t,e],i={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},a=0,u=n;a<u.length;a++){var l=u[a];l.didParseCell&&i.didParseCell.push(l.didParseCell),l.willDrawCell&&i.willDrawCell.push(l.willDrawCell),l.didDrawCell&&i.didDrawCell.push(l.didDrawCell),l.willDrawPage&&i.willDrawPage.push(l.willDrawPage),l.didDrawPage&&i.didDrawPage.push(l.didDrawPage)}return i}function w5(r,t){var e,n,i,a,u,l,h,f,p,y,x,g,P=Wo(t.margin,40/r.scaleFactor()),C=(e=x5(r,t.startY))!==null&&e!==void 0?e:P.top,D;t.showFoot===!0?D="everyPage":t.showFoot===!1?D="never":D=(n=t.showFoot)!==null&&n!==void 0?n:"everyPage";var S;t.showHead===!0?S="everyPage":t.showHead===!1?S="never":S=(i=t.showHead)!==null&&i!==void 0?i:"everyPage";var W=(a=t.useCss)!==null&&a!==void 0?a:!1,z=t.theme||(W?"plain":"striped"),M=!!t.horizontalPageBreak,at=(u=t.horizontalPageBreakRepeat)!==null&&u!==void 0?u:null;return{includeHiddenHtml:(l=t.includeHiddenHtml)!==null&&l!==void 0?l:!1,useCss:W,theme:z,startY:C,margin:P,pageBreak:(h=t.pageBreak)!==null&&h!==void 0?h:"auto",rowPageBreak:(f=t.rowPageBreak)!==null&&f!==void 0?f:"auto",tableWidth:(p=t.tableWidth)!==null&&p!==void 0?p:"auto",showHead:S,showFoot:D,tableLineWidth:(y=t.tableLineWidth)!==null&&y!==void 0?y:0,tableLineColor:(x=t.tableLineColor)!==null&&x!==void 0?x:200,horizontalPageBreak:M,horizontalPageBreakRepeat:at,horizontalPageBreakBehaviour:(g=t.horizontalPageBreakBehaviour)!==null&&g!==void 0?g:"afterAllRows"}}function x5(r,t){var e=r.getLastAutoTable(),n=r.scaleFactor(),i=r.pageNumber(),a=!1;if(e&&e.startPageNumber){var u=e.startPageNumber+e.pageNumber-1;a=u===i}return typeof t=="number"?t:(t==null||t===!1)&&a&&(e==null?void 0:e.finalY)!=null?e.finalY+20/n:null}function _5(r,t,e){var n=t.head||[],i=t.body||[],a=t.foot||[];if(t.html){var u=t.includeHiddenHtml;if(e){var l=Cf(r,t.html,e,u,t.useCss)||{};n=l.head||n,i=l.body||n,a=l.foot||n}else console.error("Cannot parse html in non browser environment")}var h=t.columns||A5(n,i,a);return{columns:h,head:n,body:i,foot:a}}function A5(r,t,e){var n=r[0]||t[0]||e[0]||[],i=[];return Object.keys(n).filter(function(a){return a!=="_element"}).forEach(function(a){var u=1,l;Array.isArray(n)?l=n[parseInt(a)]:l=n[a],typeof l=="object"&&!Array.isArray(l)&&(u=(l==null?void 0:l.colSpan)||1);for(var h=0;h<u;h++){var f=void 0;Array.isArray(n)?f=i.length:f=a+(h>0?"_".concat(h):"");var p={dataKey:f};i.push(p)}}),i}var cl=(function(){function r(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()}return r})(),L5=(function(r){Af(t,r);function t(e,n,i,a,u,l){var h=r.call(this,e,n,l)||this;return h.cell=i,h.row=a,h.column=u,h.section=a.section,h}return t})(cl),N5=(function(){function r(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return r.prototype.getHeadHeight=function(t){return this.head.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},r.prototype.getFootHeight=function(t){return this.foot.reduce(function(e,n){return e+n.getMaxCellHeight(t)},0)},r.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},r.prototype.callCellHooks=function(t,e,n,i,a,u){for(var l=0,h=e;l<h.length;l++){var f=h[l],p=new L5(t,this,n,i,a,u),y=f(p)===!1;if(n.text=Array.isArray(n.text)?n.text:[n.text],y)return!1}return!0},r.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,i=this.hooks.didDrawPage;n<i.length;n++){var a=i[n];a(new cl(t,this,e))}},r.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,i=this.hooks.willDrawPage;n<i.length;n++){var a=i[n];a(new cl(t,this,e))}},r.prototype.getWidth=function(t){if(typeof this.settings.tableWidth=="number")return this.settings.tableWidth;if(this.settings.tableWidth==="wrap"){var e=this.columns.reduce(function(i,a){return i+a.wrappedWidth},0);return e}else{var n=this.settings.margin;return t-n.left-n.right}},r})(),Ff=(function(){function r(t,e,n,i,a){a===void 0&&(a=!1),this.height=0,this.raw=t,t instanceof Lf&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=i,this.spansMultiplePages=a}return r.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce(function(n,i){var a;return Math.max(n,((a=e.cells[i.index])===null||a===void 0?void 0:a.height)||0)},0)},r.prototype.hasRowSpan=function(t){var e=this;return t.filter(function(n){var i=e.cells[n.index];return i?i.rowSpan>1:!1}).length>0},r.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},r.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce(function(i,a){var u=n.cells[a.index];if(!u)return 0;var l=e.getLineHeight(u.styles.fontSize),h=u.padding("vertical"),f=h+l;return f>i?f:i},0)},r})(),Ef=(function(){function r(t,e,n){var i;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var a=t;t!=null&&typeof t=="object"&&!Array.isArray(t)?(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,a=(i=t.content)!==null&&i!==void 0?i:t,t._element&&(this.raw=t._element)):(this.rowSpan=1,this.colSpan=1);var u=a!=null?""+a:"",l=/\r\n|\r|\n/g;this.text=u.split(l)}return r.prototype.getTextPos=function(){var t;if(this.styles.valign==="top")t=this.y+this.padding("top");else if(this.styles.valign==="bottom")t=this.y+this.height-this.padding("bottom");else{var e=this.height-this.padding("vertical");t=this.y+e/2+this.padding("top")}var n;if(this.styles.halign==="right")n=this.x+this.width-this.padding("right");else if(this.styles.halign==="center"){var i=this.width-this.padding("horizontal");n=this.x+i/2+this.padding("left")}else n=this.x+this.padding("left");return{x:n,y:t}},r.prototype.getContentHeight=function(t,e){e===void 0&&(e=1.15);var n=Array.isArray(this.text)?this.text.length:1,i=this.styles.fontSize/t*e,a=n*i+this.padding("vertical");return Math.max(a,this.styles.minCellHeight)},r.prototype.padding=function(t){var e=Wo(this.styles.cellPadding,0);return t==="vertical"?e.top+e.bottom:t==="horizontal"?e.left+e.right:e[t]},r})(),S5=(function(){function r(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return r.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,i=t.allRows();n<i.length;n++){var a=i[n],u=a.cells[this.index];u&&typeof u.styles.cellWidth=="number"&&(e=Math.max(e,u.styles.cellWidth))}return e},r})();function k5(r,t){P5(r,t);var e=[],n=0;t.columns.forEach(function(a){var u=a.getMaxCustomCellWidth(t);u?a.width=u:(a.width=a.wrappedWidth,e.push(a)),n+=a.width});var i=t.getWidth(r.pageSize().width)-n;i&&(i=dl(e,i,function(a){return Math.max(a.minReadableWidth,a.minWidth)})),i&&(i=dl(e,i,function(a){return a.minWidth})),i=Math.abs(i),!t.settings.horizontalPageBreak&&i>.1/r.scaleFactor()&&(i=i<1?i:Math.round(i),console.warn("Of the table content, ".concat(i," units width could not fit page"))),F5(t),E5(t,r),C5(t)}function P5(r,t){var e=r.scaleFactor(),n=t.settings.horizontalPageBreak,i=kf(r,t);t.allRows().forEach(function(a){for(var u=0,l=t.columns;u<l.length;u++){var h=l[u],f=a.cells[h.index];if(f){var p=t.hooks.didParseCell;t.callCellHooks(r,p,f,a,h,null);var y=f.padding("horizontal");f.contentWidth=qo(f.text,f.styles,r)+y;var x=qo(f.text.join(" ").split(/[^\S\u00A0]+/),f.styles,r);if(f.minReadableWidth=x+f.padding("horizontal"),typeof f.styles.cellWidth=="number")f.minWidth=f.styles.cellWidth,f.wrappedWidth=f.styles.cellWidth;else if(f.styles.cellWidth==="wrap"||n===!0)f.contentWidth>i?(f.minWidth=i,f.wrappedWidth=i):(f.minWidth=f.contentWidth,f.wrappedWidth=f.contentWidth);else{var g=10/e;f.minWidth=f.styles.minCellWidth||g,f.wrappedWidth=f.contentWidth,f.minWidth>f.wrappedWidth&&(f.wrappedWidth=f.minWidth)}}}}),t.allRows().forEach(function(a){for(var u=0,l=t.columns;u<l.length;u++){var h=l[u],f=a.cells[h.index];if(f&&f.colSpan===1)h.wrappedWidth=Math.max(h.wrappedWidth,f.wrappedWidth),h.minWidth=Math.max(h.minWidth,f.minWidth),h.minReadableWidth=Math.max(h.minReadableWidth,f.minReadableWidth);else{var p=t.styles.columnStyles[h.dataKey]||t.styles.columnStyles[h.index]||{},y=p.cellWidth||p.minCellWidth;y&&typeof y=="number"&&(h.minWidth=y,h.wrappedWidth=y)}f&&(f.colSpan>1&&!h.minWidth&&(h.minWidth=f.minWidth),f.colSpan>1&&!h.wrappedWidth&&(h.wrappedWidth=f.minWidth))}})}function dl(r,t,e){for(var n=t,i=r.reduce(function(g,P){return g+P.wrappedWidth},0),a=0;a<r.length;a++){var u=r[a],l=u.wrappedWidth/i,h=n*l,f=u.width+h,p=e(u),y=f<p?p:f;t-=y-u.width,u.width=y}if(t=Math.round(t*1e10)/1e10,t){var x=r.filter(function(g){return t<0?g.width>e(g):!0});x.length&&(t=dl(x,t,e))}return t}function C5(r){for(var t={},e=1,n=r.allRows(),i=0;i<n.length;i++)for(var a=n[i],u=0,l=r.columns;u<l.length;u++){var h=l[u],f=t[h.index];if(e>1)e--,delete a.cells[h.index];else if(f)f.cell.height+=a.height,e=f.cell.colSpan,delete a.cells[h.index],f.left--,f.left<=1&&delete t[h.index];else{var p=a.cells[h.index];if(!p)continue;if(p.height=a.height,p.rowSpan>1){var y=n.length-i,x=p.rowSpan>y?y:p.rowSpan;t[h.index]={cell:p,left:x,row:a}}}}}function F5(r){for(var t=r.allRows(),e=0;e<t.length;e++)for(var n=t[e],i=null,a=0,u=0,l=0;l<r.columns.length;l++){var h=r.columns[l];if(u-=1,u>1&&r.columns[l+1])a+=h.width,delete n.cells[h.index];else if(i){var f=i;delete n.cells[h.index],i=null,f.width=h.width+a}else{var f=n.cells[h.index];if(!f)continue;if(u=f.colSpan,a=0,f.colSpan>1){i=f,a+=h.width;continue}f.width=h.width+a}}}function E5(r,t){for(var e={count:0,height:0},n=0,i=r.allRows();n<i.length;n++){for(var a=i[n],u=0,l=r.columns;u<l.length;u++){var h=l[u],f=a.cells[h.index];if(f){t.applyStyles(f.styles,!0);var p=f.width-f.padding("horizontal");if(f.styles.overflow==="linebreak")f.text=t.splitTextToSize(f.text,p+1/t.scaleFactor(),{fontSize:f.styles.fontSize});else if(f.styles.overflow==="ellipsize")f.text=Lh(f.text,p,f.styles,t,"...");else if(f.styles.overflow==="hidden")f.text=Lh(f.text,p,f.styles,t,"");else if(typeof f.styles.overflow=="function"){var y=f.styles.overflow(f.text,p);typeof y=="string"?f.text=[y]:f.text=y}f.contentHeight=f.getContentHeight(t.scaleFactor(),t.getLineHeightFactor());var x=f.contentHeight/f.rowSpan;f.rowSpan>1&&e.count*e.height<x*f.rowSpan?e={height:x,count:f.rowSpan}:e&&e.count>0&&e.height>x&&(x=e.height),x>a.height&&(a.height=x)}}e.count--}}function Lh(r,t,e,n,i){return r.map(function(a){return I5(a,t,e,n,i)})}function I5(r,t,e,n,i){var a=1e4*n.scaleFactor();if(t=Math.ceil(t*a)/a,t>=qo(r,e,n))return r;for(;t<qo(r+i,e,n)&&!(r.length<=1);)r=r.substring(0,r.length-1);return r.trim()+i}function O5(r,t){var e=new ia(r),n=D5(t,e.scaleFactor()),i=new N5(t,n);return k5(e,i),e.applyStyles(e.userStyles),i}function D5(r,t){var e=r.content,n=j5(e.columns);if(e.head.length===0){var i=Nh(n,"head");i&&e.head.push(i)}if(e.foot.length===0){var i=Nh(n,"foot");i&&e.foot.push(i)}var a=r.settings.theme,u=r.styles;return{columns:n,head:Js("head",e.head,n,u,a,t),body:Js("body",e.body,n,u,a,t),foot:Js("foot",e.foot,n,u,a,t)}}function Js(r,t,e,n,i,a){var u={},l=t.map(function(h,f){for(var p=0,y={},x=0,g=0,P=0,C=e;P<C.length;P++){var D=C[P];if(u[D.index]==null||u[D.index].left===0)if(g===0){var S=void 0;Array.isArray(h)?S=h[D.index-x-p]:S=h[D.dataKey];var W={};typeof S=="object"&&!Array.isArray(S)&&(W=(S==null?void 0:S.styles)||{});var z=T5(r,D,f,i,n,a,W),M=new Ef(S,z,r);y[D.dataKey]=M,y[D.index]=M,g=M.colSpan-1,u[D.index]={left:M.rowSpan-1,times:g}}else g--,x++;else u[D.index].left--,g=u[D.index].times,p++}return new Ff(h,f,r,y)});return l}function Nh(r,t){var e={};return r.forEach(function(n){if(n.raw!=null){var i=B5(t,n.raw);i!=null&&(e[n.dataKey]=i)}}),Object.keys(e).length>0?e:null}function B5(r,t){if(r==="head"){if(typeof t=="object")return t.header||null;if(typeof t=="string"||typeof t=="number")return t}else if(r==="foot"&&typeof t=="object")return t.footer;return null}function j5(r){return r.map(function(t,e){var n,i;return typeof t=="object"?i=(n=t.dataKey)!==null&&n!==void 0?n:e:i=e,new S5(i,t,e)})}function T5(r,t,e,n,i,a,u){var l=h5(n),h;r==="head"?h=i.headStyles:r==="body"?h=i.bodyStyles:r==="foot"&&(h=i.footStyles);var f=qn({},l.table,l[r],i.styles,h),p=i.columnStyles[t.dataKey]||i.columnStyles[t.index]||{},y=r==="body"?p:{},x=r==="body"&&e%2===0?qn({},l.alternateRow,i.alternateRowStyles):{},g=u5(a),P=qn({},g,f,x,y);return qn(P,u)}function R5(r,t,e){var n;e===void 0&&(e={});var i=kf(r,t),a=new Map,u=[],l=[],h=[];Array.isArray(t.settings.horizontalPageBreakRepeat)?h=t.settings.horizontalPageBreakRepeat:(typeof t.settings.horizontalPageBreakRepeat=="string"||typeof t.settings.horizontalPageBreakRepeat=="number")&&(h=[t.settings.horizontalPageBreakRepeat]),h.forEach(function(x){var g=t.columns.find(function(P){return P.dataKey===x||P.index===x});g&&!a.has(g.index)&&(a.set(g.index,!0),u.push(g.index),l.push(t.columns[g.index]),i-=g.wrappedWidth)});for(var f=!0,p=(n=e==null?void 0:e.start)!==null&&n!==void 0?n:0;p<t.columns.length;){if(a.has(p)){p++;continue}var y=t.columns[p].wrappedWidth;if(f||i>=y)f=!1,u.push(p),l.push(t.columns[p]),i-=y;else break;p++}return{colIndexes:u,columns:l,lastIndex:p-1}}function M5(r,t){for(var e=[],n=0;n<t.columns.length;n++){var i=R5(r,t,{start:n});i.columns.length&&(e.push(i),n=i.lastIndex)}return e}function q5(r,t){var e=t.settings,n=e.startY,i=e.margin,a={x:i.left,y:n},u=t.getHeadHeight(t.columns)+t.getFootHeight(t.columns),l=n+i.bottom+u;if(e.pageBreak==="avoid"){var h=t.body,f=h.reduce(function(x,g){return x+g.height},0);l+=f}var p=new ia(r);(e.pageBreak==="always"||e.startY!=null&&l>p.pageSize().height)&&(Of(p),a.y=i.top),t.callWillDrawPageHooks(p,a);var y=qn({},a);t.startPageNumber=p.pageNumber(),e.horizontalPageBreak?U5(p,t,y,a):(p.applyStyles(p.userStyles),(e.showHead==="firstPage"||e.showHead==="everyPage")&&t.head.forEach(function(x){return Un(p,t,x,a,t.columns)}),p.applyStyles(p.userStyles),t.body.forEach(function(x,g){var P=g===t.body.length-1;Uo(p,t,x,P,y,a,t.columns)}),p.applyStyles(p.userStyles),(e.showFoot==="lastPage"||e.showFoot==="everyPage")&&t.foot.forEach(function(x){return Un(p,t,x,a,t.columns)})),Nf(p,t,y,a),t.callEndPageHooks(p,a),t.finalY=a.y,r.lastAutoTable=t,p.applyStyles(p.userStyles)}function U5(r,t,e,n){var i=M5(r,t),a=t.settings;if(a.horizontalPageBreakBehaviour==="afterAllRows")i.forEach(function(f,p){r.applyStyles(r.userStyles),p>0?Ra(r,t,e,n,f.columns,!0):Sh(r,t,n,f.columns),z5(r,t,e,n,f.columns),$s(r,t,n,f.columns)});else for(var u=-1,l=i[0],h=function(){var f=u;if(l){r.applyStyles(r.userStyles);var p=l.columns;u>=0?Ra(r,t,e,n,p,!0):Sh(r,t,n,p),f=kh(r,t,u+1,n,p),$s(r,t,n,p)}var y=f-u;i.slice(1).forEach(function(x){r.applyStyles(r.userStyles),Ra(r,t,e,n,x.columns,!0),kh(r,t,u+1,n,x.columns,y),$s(r,t,n,x.columns)}),u=f};u<t.body.length-1;)h()}function Sh(r,t,e,n){var i=t.settings;r.applyStyles(r.userStyles),(i.showHead==="firstPage"||i.showHead==="everyPage")&&t.head.forEach(function(a){return Un(r,t,a,e,n)})}function z5(r,t,e,n,i){r.applyStyles(r.userStyles),t.body.forEach(function(a,u){var l=u===t.body.length-1;Uo(r,t,a,l,e,n,i)})}function kh(r,t,e,n,i,a){r.applyStyles(r.userStyles),a=a!=null?a:t.body.length;var u=Math.min(e+a,t.body.length),l=-1;return t.body.slice(e,u).forEach(function(h,f){var p=e+f===t.body.length-1,y=If(r,t,p,n);h.canEntireRowFit(y,i)&&(Un(r,t,h,n,i),l=e+f)}),l}function $s(r,t,e,n){var i=t.settings;r.applyStyles(r.userStyles),(i.showFoot==="lastPage"||i.showFoot==="everyPage")&&t.foot.forEach(function(a){return Un(r,t,a,e,n)})}function H5(r,t,e){var n=e.getLineHeight(r.styles.fontSize),i=r.padding("vertical"),a=Math.floor((t-i)/n);return Math.max(0,a)}function W5(r,t,e,n){var i={};r.spansMultiplePages=!0,r.height=0;for(var a=0,u=0,l=e.columns;u<l.length;u++){var h=l[u],f=r.cells[h.index];if(f){Array.isArray(f.text)||(f.text=[f.text]);var p=new Ef(f.raw,f.styles,f.section);p=qn(p,f),p.text=[];var y=H5(f,t,n);f.text.length>y&&(p.text=f.text.splice(y,f.text.length));var x=n.scaleFactor(),g=n.getLineHeightFactor();f.contentHeight=f.getContentHeight(x,g),f.contentHeight>=t&&(f.contentHeight=t,p.styles.minCellHeight-=t),f.contentHeight>r.height&&(r.height=f.contentHeight),p.contentHeight=p.getContentHeight(x,g),p.contentHeight>a&&(a=p.contentHeight),i[h.index]=p}}var P=new Ff(r.raw,-1,r.section,i,!0);P.height=a;for(var C=0,D=e.columns;C<D.length;C++){var h=D[C],p=P.cells[h.index];p&&(p.height=P.height);var f=r.cells[h.index];f&&(f.height=r.height)}return P}function G5(r,t,e,n){var i=r.pageSize().height,a=n.settings.margin,u=a.top+a.bottom,l=i-u;t.section==="body"&&(l-=n.getHeadHeight(n.columns)+n.getFootHeight(n.columns));var h=t.getMinimumRowHeight(n.columns,r),f=h<e;if(h>l)return console.error("Will not be able to print row ".concat(t.index," correctly since it's minimum height is larger than page height")),!0;if(!f)return!1;var p=t.hasRowSpan(n.columns),y=t.getMaxCellHeight(n.columns)>l;return y?(p&&console.error("The content of row ".concat(t.index," will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.")),!0):!(p||n.settings.rowPageBreak==="avoid")}function Uo(r,t,e,n,i,a,u){var l=If(r,t,n,a);if(e.canEntireRowFit(l,u))Un(r,t,e,a,u);else if(G5(r,e,l,t)){var h=W5(e,l,t,r);Un(r,t,e,a,u),Ra(r,t,i,a,u),Uo(r,t,h,n,i,a,u)}else Ra(r,t,i,a,u),Uo(r,t,e,n,i,a,u)}function Un(r,t,e,n,i){n.x=t.settings.margin.left;for(var a=0,u=i;a<u.length;a++){var l=u[a],h=e.cells[l.index];if(!h){n.x+=l.width;continue}r.applyStyles(h.styles),h.x=n.x,h.y=n.y;var f=t.callCellHooks(r,t.hooks.willDrawCell,h,e,l,n);if(f===!1){n.x+=l.width;continue}V5(r,h,n);var p=h.getTextPos();_f(h.text,p.x,p.y,{halign:h.styles.halign,valign:h.styles.valign,maxWidth:Math.ceil(h.width-h.padding("left")-h.padding("right"))},r.getDocument()),t.callCellHooks(r,t.hooks.didDrawCell,h,e,l,n),n.x+=l.width}n.y+=e.height}function V5(r,t,e){var n=t.styles;if(r.getDocument().setFillColor(r.getDocument().getFillColor()),typeof n.lineWidth=="number"){var i=Sf(n.lineWidth,n.fillColor);i&&r.rect(t.x,e.y,t.width,t.height,i)}else typeof n.lineWidth=="object"&&(n.fillColor&&r.rect(t.x,e.y,t.width,t.height,"F"),K5(r,t,e,n.lineWidth))}function K5(r,t,e,n){var i,a,u,l;n.top&&(i=e.x,a=e.y,u=e.x+t.width,l=e.y,n.right&&(u+=.5*n.right),n.left&&(i-=.5*n.left),h(n.top,i,a,u,l)),n.bottom&&(i=e.x,a=e.y+t.height,u=e.x+t.width,l=e.y+t.height,n.right&&(u+=.5*n.right),n.left&&(i-=.5*n.left),h(n.bottom,i,a,u,l)),n.left&&(i=e.x,a=e.y,u=e.x,l=e.y+t.height,n.top&&(a-=.5*n.top),n.bottom&&(l+=.5*n.bottom),h(n.left,i,a,u,l)),n.right&&(i=e.x+t.width,a=e.y,u=e.x+t.width,l=e.y+t.height,n.top&&(a-=.5*n.top),n.bottom&&(l+=.5*n.bottom),h(n.right,i,a,u,l));function h(f,p,y,x,g){r.getDocument().setLineWidth(f),r.getDocument().line(p,y,x,g,"S")}}function If(r,t,e,n){var i=t.settings.margin.bottom,a=t.settings.showFoot;return(a==="everyPage"||a==="lastPage"&&e)&&(i+=t.getFootHeight(t.columns)),r.pageSize().height-n.y-i}function Ra(r,t,e,n,i,a){i===void 0&&(i=[]),a===void 0&&(a=!1),r.applyStyles(r.userStyles),t.settings.showFoot==="everyPage"&&!a&&t.foot.forEach(function(l){return Un(r,t,l,n,i)}),t.callEndPageHooks(r,n);var u=t.settings.margin;Nf(r,t,e,n),Of(r),t.pageNumber++,n.x=u.left,n.y=u.top,e.y=u.top,t.callWillDrawPageHooks(r,n),t.settings.showHead==="everyPage"&&(t.head.forEach(function(l){return Un(r,t,l,n,i)}),r.applyStyles(r.userStyles))}function Of(r){var t=r.pageNumber();r.setPage(t+1);var e=r.pageNumber();return e===t?(r.addPage(),!0):!1}function Y5(r){r.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=t[0],i=v5(this,n),a=O5(this,i);return q5(this,a),this},r.API.lastAutoTable=!1,r.API.autoTableText=function(t,e,n,i){_f(t,e,n,i,this)},r.API.autoTableSetDefaults=function(t){return ia.setDefaults(t,this),this},r.autoTableSetDefaults=function(t,e){ia.setDefaults(t,e)},r.API.autoTableHtmlToJson=function(t,e){var n;if(e===void 0&&(e=!1),typeof window>"u")return console.error("Cannot run autoTableHtmlToJson in non browser environment"),null;var i=new ia(this),a=Cf(i,t,window,e,!1),u=a.head,l=a.body,h=((n=u[0])===null||n===void 0?void 0:n.map(function(f){return f.content}))||[];return{columns:h,rows:l,data:l}}}var Xs;try{if(typeof window<"u"&&window){var Ph=window,Ch=Ph.jsPDF||((Xs=Ph.jspdf)===null||Xs===void 0?void 0:Xs.jsPDF);Ch&&Y5(Ch)}}catch(r){console.error("Could not apply autoTable plugin",r)}const J5={name:"FilterSection",props:{filters:{type:Object,required:!0},uniqueDesa:{type:Array,required:!0},uniqueKelompok:{type:Array,required:!0}},emits:["filter-change"],data(){return{localFilters:{...this.filters}}},watch:{filters:{handler(r){this.localFilters={...r}},deep:!0}},methods:{emitFilterChange(){this.$emit("filter-change",{...this.localFilters})}}},$5={class:"filter-item"},X5=["value"],Z5=["value"];function Q5(r,t,e,n,i,a){return Me(),Re("section",null,[xt("div",$5,[xt("div",null,[t[7]||(t[7]=xt("label",{for:"desaFilter"},"Filter Desa:",-1)),Ss(xt("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>i.localFilters.sambung_desa=u),onChange:t[1]||(t[1]=(...u)=>a.emitFilterChange&&a.emitFilterChange(...u)),id:"desaFilter"},[t[6]||(t[6]=xt("option",{value:""},"Semua",-1)),(Me(!0),Re(Zs,null,Qs(e.uniqueDesa,u=>(Me(),Re("option",{key:u,value:u},ue(u),9,X5))),128))],544),[[lu,i.localFilters.sambung_desa]])]),xt("div",null,[t[9]||(t[9]=xt("label",{for:"kelompokFilter"},"Filter Kelompok:",-1)),Ss(xt("select",{"onUpdate:modelValue":t[2]||(t[2]=u=>i.localFilters.sambung_kelompok=u),onChange:t[3]||(t[3]=(...u)=>a.emitFilterChange&&a.emitFilterChange(...u)),id:"kelompokFilter"},[t[8]||(t[8]=xt("option",{value:""},"Semua",-1)),(Me(!0),Re(Zs,null,Qs(e.uniqueKelompok,u=>(Me(),Re("option",{key:u,value:u},ue(u),9,Z5))),128))],544),[[lu,i.localFilters.sambung_kelompok]])]),xt("div",null,[t[10]||(t[10]=xt("label",{for:"namaFilter"},"Filter Nama:",-1)),Ss(xt("input",{type:"text","onUpdate:modelValue":t[4]||(t[4]=u=>i.localFilters.nama=u),onInput:t[5]||(t[5]=(...u)=>a.emitFilterChange&&a.emitFilterChange(...u)),placeholder:"Cari nama...",id:"namaFilter"},null,544),[[yc,i.localFilters.nama]])])])])}const td=Ua(J5,[["render",Q5],["__scopeId","data-v-b60a221c"]]),ed={name:"StatisticsSection",props:{stats:{type:Object,required:!0,default:()=>({total_count:0,male_count:0,female_count:0,with_photo_count:0,without_photo_count:0})}}},rd={class:"statistics-section"},nd={class:"stats-container"},id={class:"stat-card"},ad={class:"stat-number"},od={class:"stat-card"},sd={class:"stat-number"},ld={class:"stat-card"},ud={class:"stat-number"},hd={class:"stat-card"},fd={class:"stat-number"},cd={class:"stat-card"},dd={class:"stat-number"};function pd(r,t,e,n,i,a){return Me(),Re("section",rd,[xt("div",nd,[xt("div",id,[t[0]||(t[0]=xt("h3",null,"Total",-1)),xt("p",ad,ue(e.stats.total_count),1)]),xt("div",od,[t[1]||(t[1]=xt("h3",null,"Laki-laki",-1)),xt("p",sd,ue(e.stats.male_count),1)]),xt("div",ld,[t[2]||(t[2]=xt("h3",null,"Perempuan",-1)),xt("p",ud,ue(e.stats.female_count),1)]),xt("div",hd,[t[3]||(t[3]=xt("h3",null,"Dengan Foto",-1)),xt("p",fd,ue(e.stats.with_photo_count),1)]),xt("div",cd,[t[4]||(t[4]=xt("h3",null,"Tanpa Foto",-1)),xt("p",dd,ue(e.stats.without_photo_count),1)])])])}const gd=Ua(ed,[["render",pd],["__scopeId","data-v-2f07d2d7"]]),md={name:"BiodataTable",props:{data:{type:Array,required:!0},sortKey:{type:String,default:""},sortOrder:{type:String,default:"asc"}},emits:["sort","row-click"]},vd={class:"table-container"},bd={id:"biodataTable"},yd={key:0},wd={key:0},xd={key:0},_d={key:0},Ad={key:0},Ld=["onClick"];function Nd(r,t,e,n,i,a){return Me(),Re("div",vd,[xt("table",bd,[xt("thead",null,[xt("tr",null,[t[10]||(t[10]=xt("th",null,"No.",-1)),xt("th",{onClick:t[0]||(t[0]=u=>r.$emit("sort","nama_lengkap"))},[t[5]||(t[5]=je(" Nama Lengkap ",-1)),e.sortKey==="nama_lengkap"?(Me(),Re("span",yd,ue(e.sortOrder==="asc"?"↑":"↓"),1)):$i("",!0)]),xt("th",{onClick:t[1]||(t[1]=u=>r.$emit("sort","nama_panggilan"))},[t[6]||(t[6]=je(" Nama Panggilan ",-1)),e.sortKey==="nama_panggilan"?(Me(),Re("span",wd,ue(e.sortOrder==="asc"?"↑":"↓"),1)):$i("",!0)]),xt("th",{onClick:t[2]||(t[2]=u=>r.$emit("sort","sambung_desa"))},[t[7]||(t[7]=je(" Desa ",-1)),e.sortKey==="sambung_desa"?(Me(),Re("span",xd,ue(e.sortOrder==="asc"?"↑":"↓"),1)):$i("",!0)]),xt("th",{onClick:t[3]||(t[3]=u=>r.$emit("sort","sambung_kelompok"))},[t[8]||(t[8]=je(" Kelompok ",-1)),e.sortKey==="sambung_kelompok"?(Me(),Re("span",_d,ue(e.sortOrder==="asc"?"↑":"↓"),1)):$i("",!0)]),xt("th",{onClick:t[4]||(t[4]=u=>r.$emit("sort","jenis_kelamin"))},[t[9]||(t[9]=je(" Jenis Kelamin ",-1)),e.sortKey==="jenis_kelamin"?(Me(),Re("span",Ad,ue(e.sortOrder==="asc"?"↑":"↓"),1)):$i("",!0)])])]),xt("tbody",null,[(Me(!0),Re(Zs,null,Qs(e.data,(u,l)=>(Me(),Re("tr",{key:l,onClick:h=>r.$emit("row-click",u.biodata_id),class:"clickable-row"},[xt("td",null,ue(l+1),1),xt("td",null,ue(u.nama_lengkap),1),xt("td",null,ue(u.nama_panggilan),1),xt("td",null,ue(u.sambung_desa),1),xt("td",null,ue(u.sambung_kelompok),1),xt("td",null,ue(u.jenis_kelamin),1)],8,Ld))),128))])])])}const Sd=Ua(md,[["render",Nd],["__scopeId","data-v-78faf7ea"]]),kd={name:"BiodataDetailModal",props:{show:{type:Boolean,default:!1},data:{type:Object,default:null},apiKey:{type:String,required:!0}},emits:["close"],data(){return{photoUrl:null,loadingPhoto:!1}},watch:{data:{handler(r){r&&r.foto_filename?this.loadPhoto(r.foto_filename):(this.photoUrl=null,this.loadingPhoto=!1)},immediate:!0},show(r){r||this.photoUrl&&(URL.revokeObjectURL(this.photoUrl),this.photoUrl=null)}},methods:{async loadPhoto(r){this.loadingPhoto=!0,this.photoUrl=null;const t="/api/biodata/generus/foto/".concat(r);console.log("🔍 [PHOTO DEBUG] Starting photo load process"),console.log("📁 [PHOTO DEBUG] Filename:",r),console.log("🌐 [PHOTO DEBUG] Full URL:",t),console.log("🔑 [PHOTO DEBUG] API Key (first 10 chars):",this.apiKey?this.apiKey.substring(0,10)+"...":"NOT PROVIDED"),console.log("📋 [PHOTO DEBUG] Request headers:",{Authorization:"ApiKey ".concat(this.apiKey)});try{console.log("🚀 [PHOTO DEBUG] Making fetch request...");const e=await fetch(t,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(console.log("📡 [PHOTO DEBUG] Response received"),console.log("📊 [PHOTO DEBUG] Response status:",e.status),console.log("📊 [PHOTO DEBUG] Response status text:",e.statusText),console.log("📊 [PHOTO DEBUG] Response headers:",Object.fromEntries(e.headers.entries())),console.log("📊 [PHOTO DEBUG] Response URL:",e.url),console.log("📊 [PHOTO DEBUG] Response type:",e.type),!e.ok){console.error("❌ [PHOTO DEBUG] Response not OK"),console.error("❌ [PHOTO DEBUG] Status:",e.status),console.error("❌ [PHOTO DEBUG] Status text:",e.statusText);try{const i=await e.text();console.error("❌ [PHOTO DEBUG] Error response body:",i)}catch(i){console.error("❌ [PHOTO DEBUG] Could not read error response body:",i)}throw new Error("HTTP error! status: ".concat(e.status," - ").concat(e.statusText))}console.log("✅ [PHOTO DEBUG] Response OK, converting to blob...");const n=await e.blob();console.log("📦 [PHOTO DEBUG] Blob created, size:",n.size,"bytes"),console.log("📦 [PHOTO DEBUG] Blob type:",n.type),this.photoUrl=URL.createObjectURL(n),console.log("🖼️ [PHOTO DEBUG] Photo URL created successfully:",this.photoUrl)}catch(e){console.error("💥 [PHOTO DEBUG] Error in loadPhoto method:"),console.error("💥 [PHOTO DEBUG] Error type:",e.constructor.name),console.error("💥 [PHOTO DEBUG] Error message:",e.message),console.error("💥 [PHOTO DEBUG] Full error object:",e),console.error("💥 [PHOTO DEBUG] Stack trace:",e.stack),this.photoUrl=null}finally{this.loadingPhoto=!1,console.log("🏁 [PHOTO DEBUG] Photo loading process completed")}},formatDate(r){return r?new Date(r).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric"}):"-"},formatHobi(r){return r?typeof r=="object"?Object.values(r).join(", ")||"-":r:"-"},handleImageError(r){console.error("Image failed to load"),this.photoUrl=null}}},Pd={class:"modal-header"},Cd={class:"modal-body"},Fd={key:0,class:"detail-grid"},Ed={class:"detail-section"},Id={class:"detail-section"},Od={class:"detail-section"},Dd={key:0,class:"detail-section photo-section"},Bd=["src","alt"],jd={key:1,class:"loading-photo"},Td={key:2,class:"no-photo"},Rd={key:1,class:"detail-section"},Md={key:1,class:"loading-content"};function qd(r,t,e,n,i,a){return e.show?(Me(),Re("div",{key:0,class:"modal-overlay",onClick:t[3]||(t[3]=u=>r.$emit("close"))},[xt("div",{class:"modal-content",onClick:t[2]||(t[2]=wc(()=>{},["stop"]))},[xt("div",Pd,[t[4]||(t[4]=xt("h3",null,"Detail Biodata",-1)),xt("button",{class:"close-button",onClick:t[0]||(t[0]=u=>r.$emit("close"))},"×")]),xt("div",Cd,[e.data?(Me(),Re("div",Fd,[xt("div",Ed,[t[14]||(t[14]=xt("h4",null,"Informasi Pribadi",-1)),xt("p",null,[t[5]||(t[5]=xt("strong",null,"Nama Lengkap:",-1)),je(" "+ue(e.data.nama_lengkap),1)]),xt("p",null,[t[6]||(t[6]=xt("strong",null,"Nama Panggilan:",-1)),je(" "+ue(e.data.nama_panggilan),1)]),xt("p",null,[t[7]||(t[7]=xt("strong",null,"Tempat Lahir:",-1)),je(" "+ue(e.data.kelahiran_tempat),1)]),xt("p",null,[t[8]||(t[8]=xt("strong",null,"Tanggal Lahir:",-1)),je(" "+ue(a.formatDate(e.data.kelahiran_tanggal)),1)]),xt("p",null,[t[9]||(t[9]=xt("strong",null,"Alamat:",-1)),je(" "+ue(e.data.alamat_tinggal),1)]),xt("p",null,[t[10]||(t[10]=xt("strong",null,"Sekolah/Kelas:",-1)),je(" "+ue(e.data.sekolah_kelas),1)]),xt("p",null,[t[11]||(t[11]=xt("strong",null,"No. HP:",-1)),je(" "+ue(e.data.nomor_hape||"-"),1)]),xt("p",null,[t[12]||(t[12]=xt("strong",null,"Daerah:",-1)),je(" "+ue(e.data.daerah),1)]),xt("p",null,[t[13]||(t[13]=xt("strong",null,"Jenis Kelamin:",-1)),je(" "+ue(e.data.jenis_kelamin),1)])]),xt("div",Id,[t[21]||(t[21]=xt("h4",null,"Informasi Keluarga",-1)),xt("p",null,[t[15]||(t[15]=xt("strong",null,"Nama Ayah:",-1)),je(" "+ue(e.data.nama_ayah),1)]),xt("p",null,[t[16]||(t[16]=xt("strong",null,"Status Ayah:",-1)),je(" "+ue(e.data.status_ayah),1)]),xt("p",null,[t[17]||(t[17]=xt("strong",null,"No. HP Ayah:",-1)),je(" "+ue(e.data.nomor_hape_ayah||"-"),1)]),xt("p",null,[t[18]||(t[18]=xt("strong",null,"Nama Ibu:",-1)),je(" "+ue(e.data.nama_ibu),1)]),xt("p",null,[t[19]||(t[19]=xt("strong",null,"Status Ibu:",-1)),je(" "+ue(e.data.status_ibu),1)]),xt("p",null,[t[20]||(t[20]=xt("strong",null,"No. HP Ibu:",-1)),je(" "+ue(e.data.nomor_hape_ibu||"-"),1)])]),xt("div",Od,[t[26]||(t[26]=xt("h4",null,"Lainnya",-1)),xt("p",null,[t[22]||(t[22]=xt("strong",null,"Sambung Desa:",-1)),je(" "+ue(e.data.sambung_desa),1)]),xt("p",null,[t[23]||(t[23]=xt("strong",null,"Sambung Kelompok:",-1)),je(" "+ue(e.data.sambung_kelompok),1)]),xt("p",null,[t[24]||(t[24]=xt("strong",null,"Hobi:",-1)),je(" "+ue(a.formatHobi(e.data.hobi)),1)]),xt("p",null,[t[25]||(t[25]=xt("strong",null,"Tanggal Pendataan:",-1)),je(" "+ue(a.formatDate(e.data.pendataan_tanggal)),1)])]),e.data.foto_filename?(Me(),Re("div",Dd,[t[28]||(t[28]=xt("h4",null,"Foto",-1)),i.photoUrl?(Me(),Re("img",{key:0,src:i.photoUrl,alt:"Foto ".concat(e.data.nama_lengkap),class:"biodata-photo",onError:t[1]||(t[1]=(...u)=>a.handleImageError&&a.handleImageError(...u))},null,40,Bd)):i.loadingPhoto?(Me(),Re("div",jd,[...t[27]||(t[27]=[xt("p",null,"Memuat foto...",-1)])])):(Me(),Re("p",Td,"Foto tidak tersedia"))])):(Me(),Re("div",Rd,[...t[29]||(t[29]=[xt("h4",null,"Foto",-1),xt("p",{class:"no-photo"},"Foto tidak tersedia",-1)])]))])):(Me(),Re("div",Md,[...t[30]||(t[30]=[xt("p",null,"Memuat detail...",-1)])]))])])])):$i("",!0)}const Ud=Ua(kd,[["render",qd],["__scopeId","data-v-30322fc9"]]),zd={components:{FilterSection:td,StatisticsSection:gd,BiodataTable:Sd,BiodataDetailModal:Ud},data(){return{biodataData:[],detailedData:{},showModal:!1,selectedDetailData:null,filters:{sambung_desa:"",sambung_kelompok:"",nama:""},apiKey:"",sortKey:"",sortOrder:"asc"}},computed:{uniqueDesa(){return[...new Set(this.biodataData.map(r=>r.sambung_desa))].sort()},uniqueKelompok(){return[...new Set(this.biodataData.map(r=>r.sambung_kelompok))].sort()},filteredData(){const r=this.biodataData.filter(t=>{const e=!this.filters.sambung_desa||t.sambung_desa===this.filters.sambung_desa,n=!this.filters.sambung_kelompok||t.sambung_kelompok===this.filters.sambung_kelompok,i=!this.filters.nama||t.nama_lengkap.toLowerCase().includes(this.filters.nama.toLowerCase())||t.nama_panggilan.toLowerCase().includes(this.filters.nama.toLowerCase());return e&&n&&i});return this.sortKey&&r.sort((t,e)=>{let n=this.sortKey==="index"?1:t[this.sortKey],i=this.sortKey==="index"?1:e[this.sortKey];return typeof n=="string"&&(n=n.toLowerCase()),typeof i=="string"&&(i=i.toLowerCase()),n<i?this.sortOrder==="asc"?-1:1:n>i?this.sortOrder==="asc"?1:-1:0}),r},filteredStats(){const r=this.filteredData,t=new Set,e=new Set;Object.keys(this.detailedData).forEach(u=>{var l;(l=this.detailedData[u])!=null&&l.foto_filename?t.add(u):e.add(u)});const n=r.filter(u=>t.has(u.biodata_id)).length,i=r.filter(u=>e.has(u.biodata_id)).length,a=r.length-n-i;return{total_count:r.length,male_count:r.filter(u=>u.jenis_kelamin.toLowerCase()==="laki-laki").length,female_count:r.filter(u=>u.jenis_kelamin.toLowerCase()==="perempuan").length,with_photo_count:n,without_photo_count:i+a}}},methods:{handleFilterChange(r){this.filters={...r}},async fetchBiodataGenerus(){const r="/api/biodata/generus/";try{const t=await fetch(r,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(!t.ok)throw new Error("HTTP error! status: ".concat(t.status));this.biodataData=await t.json()}catch(t){console.error("Error fetching data:",t)}},async fetchDetailedData(r){if(console.log("🔍 [FETCH DEBUG] Fetching detailed data for biodata ID:",r),this.detailedData[r])return console.log("📋 [FETCH DEBUG] Data already cached, returning cached data"),this.detailedData[r];const t="/api/biodata/generus/".concat(r);console.log("🌐 [FETCH DEBUG] API URL:",t),console.log("🔑 [FETCH DEBUG] API Key (first 10 chars):",this.apiKey?this.apiKey.substring(0,10)+"...":"NOT PROVIDED");try{console.log("🚀 [FETCH DEBUG] Making fetch request...");const e=await fetch(t,{headers:{Authorization:"ApiKey ".concat(this.apiKey)}});if(console.log("📡 [FETCH DEBUG] Response received, status:",e.status),!e.ok)throw console.error("❌ [FETCH DEBUG] Response not OK, status:",e.status),new Error("HTTP error! status: ".concat(e.status));const n=await e.json();return console.log("📋 [FETCH DEBUG] Data parsed successfully:",n),console.log("📸 [FETCH DEBUG] Photo filename in response:",n.foto_filename),this.detailedData[r]=n,n}catch(e){return console.error("💥 [FETCH DEBUG] Error fetching detailed data:",e),null}},async openDetailModal(r){console.log("🔍 [MODAL DEBUG] Opening detail modal for biodata ID:",r),this.showModal=!0,this.selectedDetailData=null;const t=await this.fetchDetailedData(r);t?(console.log("📋 [MODAL DEBUG] Detail data received:",t),console.log("📸 [MODAL DEBUG] Photo filename:",t.foto_filename),console.log("🔑 [MODAL DEBUG] API Key available:",!!this.apiKey),this.selectedDetailData=t):console.error("❌ [MODAL DEBUG] No data received for biodata ID:",r)},closeModal(){this.showModal=!1,this.selectedDetailData=null},downloadPDF(){const r=new Rt({unit:"cm",format:"a4",margins:{top:1,bottom:1,left:1,right:1}});r.setFont("times","normal"),r.setFontSize(20),r.text("Laporan Biodata Generus",r.internal.pageSize.getWidth()/2,2,{align:"center"}),r.setFontSize(16),r.text("".concat(this.filters.sambung_desa||"Semua Desa"),r.internal.pageSize.getWidth()/2,2.8,{align:"center"}),r.autoTable({head:[["No.","Nama Lengkap","Nama Panggilan","Desa","Kelompok","Jenis Kelamin"]],body:this.filteredData.map((e,n)=>[n+1,e.nama_lengkap,e.nama_panggilan,e.sambung_desa,e.sambung_kelompok,e.jenis_kelamin]),startY:4,margin:{top:1,right:1,left:1,bottom:2},styles:{fontSize:10,cellPadding:.5},pageBreak:"auto",bodyStyles:{minCellHeight:.5},didDrawPage:e=>{const n=e.pageNumber,i=r.internal.pageSize.height,a=r.internal.pageSize.width;r.setFontSize(10);const u=i-1.5;r.setDrawColor(200,200,200),r.setLineWidth(.02),r.line(1,u,a-1,u);const l="BIODATA GENERUS - ".concat(this.filters.sambung_desa||"Semua Desa"," - Halaman ").concat(n);r.text(l,a-1,u+.5,{align:"right"})}});const t="Laporan-Biodata-Generus-".concat(this.filters.sambung_desa||"semua",".pdf");r.save(t)},sortTable(r){this.sortKey===r?this.sortOrder=this.sortOrder==="asc"?"desc":"asc":(this.sortKey=r,this.sortOrder="asc")}},mounted(){const r=new URLSearchParams(window.location.search);this.apiKey=r.get("key"),this.fetchBiodataGenerus(),document.title="Pantauan Biodata Generus"}},Hd={id:"app"},Wd={class:"button-container"};function Gd(r,t,e,n,i,a){const u=wo("FilterSection"),l=wo("StatisticsSection"),h=wo("BiodataTable"),f=wo("BiodataDetailModal");return Me(),Re("div",Hd,[t[1]||(t[1]=xt("h1",null,"Pantauan Biodata Generus",-1)),yo(u,{filters:i.filters,"unique-desa":a.uniqueDesa,"unique-kelompok":a.uniqueKelompok,onFilterChange:a.handleFilterChange},null,8,["filters","unique-desa","unique-kelompok","onFilterChange"]),yo(l,{stats:a.filteredStats},null,8,["stats"]),yo(h,{data:a.filteredData,"sort-key":i.sortKey,"sort-order":i.sortOrder,onSort:a.sortTable,onRowClick:a.openDetailModal},null,8,["data","sort-key","sort-order","onSort","onRowClick"]),xt("div",Wd,[xt("button",{onClick:t[0]||(t[0]=(...p)=>a.downloadPDF&&a.downloadPDF(...p))},"Download as PDF")]),yo(f,{show:i.showModal,data:i.selectedDetailData,"api-key":i.apiKey,onClose:a.closeModal},null,8,["show","data","api-key","onClose"])])}const Vd=Ua(zd,[["render",Gd]]),$d=Object.freeze(Object.defineProperty({__proto__:null,default:Vd},Symbol.toStringTag,{value:"Module"}));export{_e as _,$d as p};
