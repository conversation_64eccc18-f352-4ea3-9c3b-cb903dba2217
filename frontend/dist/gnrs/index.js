const API_HOST = "api.var.my.id";
const ALLOWED_PATHS = [
  "/auth",
  // Authentication endpoints (Django)
  "/biodata",
  // Student biodata endpoints (FastAPI)
  "/data/daerah",
  // Region data
  "/data/hobi",
  // Hobby data
  "/data/kelas-sekolah",
  // School class data
  "/url",
  // URL shortener endpoints (FastAPI)
  "/wilayah"
  // Area/region endpoints (FastAPI)
];
const index = {
  fetch(request, env) {
    const url = new URL(request.url);
    if (url.pathname.startsWith("/s/")) {
      return handleShortUrl(request, url);
    }
    if (url.pathname.startsWith("/api/")) {
      const apiPath = url.pathname.substring(4);
      if (!ALLOWED_PATHS.some((p) => apiPath.startsWith(p))) {
        return new Response("Forbidden: Endpoint not allowed", { status: 403 });
      }
      const targetPath = url.pathname.replace(/^\/api\//, "/");
      const targetUrl = "https://".concat(API_HOST).concat(targetPath).concat(url.search);
      const headers = new Headers(request.headers);
      if (apiPath.includes("/foto/")) {
        headers.set("Referer", "https://".concat(API_HOST, "/"));
        headers.set("User-Agent", "GNRS-Cloudflare-Worker/1.0");
        headers.set("Origin", "https://".concat(API_HOST));
      }
      return fetch(targetUrl, {
        method: request.method,
        headers,
        body: request.body,
        redirect: "follow"
      });
    }
    if (env.ASSETS) {
      return env.ASSETS.fetch(request).then((assetResponse) => {
        if (assetResponse.status !== 404) return assetResponse;
        const indexUrl = new URL(request.url);
        indexUrl.pathname = "/index.html";
        return env.ASSETS.fetch(
          new Request(indexUrl.toString(), {
            method: request.method,
            headers: request.headers
          })
        );
      }).catch((err) => {
        console.error("Error serving assets:", err);
        return fetch(request);
      });
    }
    return fetch(request);
  }
};
async function handleShortUrl(_request, url) {
  const urlId = url.pathname.substring(3);
  if (!urlId) {
    return new Response("Bad Request: Missing URL ID", { status: 400 });
  }
  try {
    const targetUrl = "https://".concat(API_HOST, "/url/").concat(urlId).concat(url.search);
    const response = await fetch(targetUrl, {
      headers: { "User-Agent": "Cloudflare-Worker/1.0" }
    });
    if (!response.ok) {
      return new Response("URL not found", { status: 404 });
    }
    const { url: original } = await response.json();
    return Response.redirect(original, 302);
  } catch (err) {
    console.error("Error fetching short URL:", err);
    return new Response("Internal Server Error", { status: 500 });
  }
}
export {
  index as default
};
