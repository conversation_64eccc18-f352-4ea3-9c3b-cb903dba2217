{"configPath": "/home/<USER>/deve/gnrs/wrangler.jsonc", "userConfigPath": "/home/<USER>/deve/gnrs/wrangler.jsonc", "topLevelName": "gnrs", "legacy_env": true, "compatibility_date": "2025-06-07", "compatibility_flags": [], "jsx_factory": "React.createElement", "jsx_fragment": "React.Fragment", "rules": [{"type": "ESModule", "globs": ["**/*.js", "**/*.mjs"]}], "name": "gnrs", "main": "index.js", "triggers": {}, "assets": {"directory": "../client", "binding": "ASSETS"}, "preview_urls": true, "vars": {}, "durable_objects": {"bindings": []}, "workflows": [], "migrations": [], "kv_namespaces": [], "cloudchamber": {}, "send_email": [], "queues": {"producers": [], "consumers": []}, "r2_buckets": [], "d1_databases": [], "vectorize": [], "hyperdrive": [], "services": [], "analytics_engine_datasets": [], "dispatch_namespaces": [], "mtls_certificates": [], "pipelines": [], "secrets_store_secrets": [], "unsafe_hello_world": [], "logfwdr": {"bindings": []}, "observability": {"enabled": true}, "dev": {"ip": "localhost", "local_protocol": "http", "upstream_protocol": "http", "enable_containers": true}, "no_bundle": true}