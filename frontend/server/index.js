// Cloudflare Workers as API proxy for besb project
const API_HOST = "api.var.my.id";

// Whitelist of allowed API endpoint prefixes based on actual backend structure
// These correspond to the FastAPI and Django backend endpoints
const ALLOWED_PATHS = [
  "/auth", // Authentication endpoints (Django)
  "/biodata", // Student biodata endpoints (FastAPI)
  "/data/daerah", // Region data
  "/data/hobi", // Hobby data
  "/data/kelas-sekolah", // School class data
  "/url", // URL shortener endpoints (FastAPI)
  "/wilayah", // Area/region endpoints (FastAPI)
];

// Refactored to avoid top-level await so CPU time stops once the
// promise is returned to Cloudflare. Only the short-URL branch does
// additional async work in a helper.
export default {
  fetch(request, env) {
    const url = new URL(request.url);

    // Handle shortened URLs: /s/<slug>
    if (url.pathname.startsWith("/s/")) {
      return handleShortUrl(request, url);
    }

    // Handle API proxy requests: /api/*
    if (url.pathname.startsWith("/api/")) {
      const apiPath = url.pathname.substring(4); // drop '/api'

      // Whitelist check – very fast .some on a small array
      if (!ALLOWED_PATHS.some((p) => apiPath.startsWith(p))) {
        return new Response("Forbidden: Endpoint not allowed", { status: 403 });
      }

      const targetPath = url.pathname.replace(/^\/api\//, "/");
      const targetUrl = `https://${API_HOST}${targetPath}${url.search}`;

      // Create headers object and add anti-hotlinking headers for photo requests
      const headers = new Headers(request.headers);

      // Add headers to bypass hotlinking protection, especially for photo endpoints
      if (apiPath.includes('/foto/')) {
        headers.set('Referer', `https://${API_HOST}/`);
        headers.set('User-Agent', 'GNRS-Cloudflare-Worker/1.0');
        headers.set('Origin', `https://${API_HOST}`);
      }

      // Return the fetch promise directly so JS stops executing
      return fetch(targetUrl, {
        method: request.method,
        headers: headers,
        body: request.body,
        redirect: "follow",
      });
    }

    // For everything else, attempt to serve static asset first.
    if (env.ASSETS) {
      return env.ASSETS.fetch(request)
        .then((assetResponse) => {
          if (assetResponse.status !== 404) return assetResponse;

          // Fallback to SPA index.html so client-side routing works.
          const indexUrl = new URL(request.url);
          indexUrl.pathname = "/index.html";
          return env.ASSETS.fetch(
            new Request(indexUrl.toString(), {
              method: request.method,
              headers: request.headers,
            }),
          );
        })
        .catch((err) => {
          console.error("Error serving assets:", err);
          return fetch(request);
        });
    }

    // If no ASSETS binding, just proxy the request as-is.
    return fetch(request);
  },
};

// --- Helper -------------------------------------------------------------
async function handleShortUrl(_request, url) {
  const urlId = url.pathname.substring(3); // remove '/s/'
  if (!urlId) {
    return new Response("Bad Request: Missing URL ID", { status: 400 });
  }

  try {
    const targetUrl = `https://${API_HOST}/url/${urlId}${url.search}`;
    const response = await fetch(targetUrl, {
      headers: { "User-Agent": "Cloudflare-Worker/1.0" },
    });

    if (!response.ok) {
      return new Response("URL not found", { status: 404 });
    }

    const { url: original } = await response.json();
    return Response.redirect(original, 302);
  } catch (err) {
    console.error("Error fetching short URL:", err);
    return new Response("Internal Server Error", { status: 500 });
  }
}
