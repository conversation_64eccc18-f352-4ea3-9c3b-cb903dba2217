<template>
    <FormContainer>
        <div class="form-title">FORMULIR PENDATAAN</div>
        <div class="form-subtitle">GENERUS {{ daerahParam || "SKC" }}</div>

        <div v-if="!showReview && !showSuccess">
            <PersonalInfoForm :formData="formData" />

            <KelompokSelector
                :formData="formData"
                :kelompokOptions="kelompokData.options"
                :flattenedKelompok="flattenedKelompok"
                :dataLoaded="kelompokData.dataLoaded"
                :isLoading="kelompokData.isLoading"
                :loadError="kelompokData.loadError"
                @input-change="handleKelompokInputChange"
            />

            <SekolahKelasSelector
                :formData="formData"
                :sekolahKelasOptions="sekolahKelasOptions"
            />

            <HobiSelector
                :hobiOptions="hobiData.options"
                :selectedHobi="selectedHobi"
            />

            <PhotoUpload v-model="selectedPhoto" />

            <div class="spaci"></div>

            <ParentInfoForm :formData="formData" />

            <button @click="showReviewData">Review Data</button>
        </div>

        <!-- Review section -->
        <ReviewDataSection
            v-if="showReview && !showSuccess"
            :formData="formData"
            :selectedHobi="selectedHobi"
            :selectedPhoto="selectedPhoto"
            :isSubmitting="isSubmitting"
            @submit="submitToAPI"
            @edit="editForm"
        />

        <!-- Success message -->
        <div v-if="showSuccess" class="confirmation-container">
            <div class="confirmation-message">
                Data berhasil dikirim!<br />
                Alhamdulillah Jazaa Kumullohu Khoiro.
            </div>
            <button @click="resetForm">Isi Formulir Baru</button>
        </div>
    </FormContainer>
</template>

<script>
import FormContainer from "./components/FormContainer.vue";
import PersonalInfoForm from "./components/PersonalInfoForm.vue";
import ParentInfoForm from "./components/ParentInfoForm.vue";
import HobiSelector from "./components/HobiSelector.vue";
import KelompokSelector from "./components/KelompokSelector.vue";
import SekolahKelasSelector from "./components/SekolahKelasSelector.vue";
import ReviewDataSection from "./components/ReviewDataSection.vue";
import PhotoUpload from "./components/PhotoUpload.vue";
import BiodataFormMixin from "./mixins/BiodataFormMixin";

export default {
    components: {
        FormContainer,
        PersonalInfoForm,
        ParentInfoForm,
        HobiSelector,
        KelompokSelector,
        SekolahKelasSelector,
        ReviewDataSection,
        PhotoUpload,
    },
    mixins: [BiodataFormMixin],
    data() {
        return {
            formData: {
                nama_lengkap: "",
                nama_panggilan: "",
                jenis_kelamin: "",
                kelahiran_tempat: "",
                kelahiran_tanggal: "",
                alamat_tinggal: "",
                pendataan_tanggal: this.getCurrentDate(),
                sambung_desa: "",
                sambung_kelompok: "",
                hobi: "", // This will now be set as a JSON string during submission
                sekolah_kelas: "",
                nomor_hape: "",
                nama_ayah: "",
                nama_ibu: "",
                status_ayah: "",
                status_ibu: "",
                nomor_hape_ayah: "",
                nomor_hape_ibu: "",
                daerah: "", // Field for daerah parameter
            },
            daerahParam: "", // To store and display the daerah parameter
            selectedPhoto: null, // Store the selected photo file
            showSuccess: false,
            showReview: false,
            isSubmitting: false,
            apiKey: null, // Store the API key from URLAPI key is missing
            showApiKeyError: false, // Show error when API key is missing
            // Use separate objects for kelompok and hobi API state
            kelompokData: {
                options: {},
                isLoading: true,
                loadError: null,
                dataLoaded: false,
            },
            hobiData: {
                options: [],
                isLoading: true,
                loadError: null,
                dataLoaded: false,
            },
            sekolahKelasOptions: [],
            selectedHobi: [],
        };
    },
    computed: {
        flattenedKelompok() {
            // Flatten the nested structure into an array of { desa, kelompok }
            return Object.entries(this.kelompokData.options).flatMap(
                ([desa, kelompoks]) =>
                    kelompoks.map((kelompok) => ({ desa, kelompok })),
            );
        },
    },
    mounted() {
        document.title = this.daerahParam
            ? `PENDATAAN GENERUS ${this.daerahParam}`
            : "PENDATAAN GENERUS";

        // Pre-fill today's date for pendataan_tanggal
        this.formData.pendataan_tanggal = this.getCurrentDate();

        // Extract URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const dataParam = urlParams.get("data");

        // Get daerah parameter and set it to formData
        this.daerahParam = urlParams.get("daerah");
        if (this.daerahParam) {
            this.formData.daerah = this.daerahParam;
            // Update title again after daerahParam is set
            document.title = `PENDATAAN GENERUS ${this.daerahParam}`;
        }

        // Extract API key from URL parameters
        this.apiKey = urlParams.get("key");

        // Fetch all needed data from APIs
        this.fetchKelompokData(dataParam);
        this.fetchHobiData();
        this.fetchSekolahKelasData();

        // Ensure alamat_tinggal is initialized properly
        this.$nextTick(() => {
            if (!this.formData.alamat_tinggal) {
                this.formData.alamat_tinggal = "";
            }
        });
    },
    methods: {
        // Add or modify the showReviewData method
        showReviewData() {
            console.log(
                "About to show review with alamat_tinggal:",
                this.formData.alamat_tinggal,
            );
            // Save the current alamat_tinggal value explicitly before showing review
            if (this.formData.alamat_tinggal) {
                const savedAlamat = this.formData.alamat_tinggal;
                this.showReview = true;
                // Ensure alamat_tinggal is still set after toggling view
                this.$nextTick(() => {
                    if (!this.formData.alamat_tinggal) {
                        console.log("Restoring saved alamat_tinggal");
                        this.formData.alamat_tinggal = savedAlamat;
                    }
                });
            } else {
                this.showReview = true;
            }
        },

        // Complete implementation of submitToAPI using JSON
        async submitToAPI() {
            console.log("=== SUBMISSION DEBUG START ===");

            try {
                // Ensure we have an API key first
                if (!this.apiKey || this.apiKey.trim() === "") {
                    console.error("API key is missing or empty");
                    this.showApiKeyError = true;
                    alert(
                        "API key is required but missing from the URL. Please check your link.",
                    );
                    return;
                }

                // Set isSubmitting to true to show loading state
                this.isSubmitting = true;

                // Fix for missing alamat_tinggal
                if (!this.formData.alamat_tinggal) {
                    console.log("Fixing missing alamat_tinggal");
                    this.formData.alamat_tinggal = "Not specified";
                }

                console.log("API Key present:", !!this.apiKey);
                console.log("API Key length:", this.apiKey.length);
                console.log(
                    "API Key first 4 chars:",
                    this.apiKey ? `${this.apiKey.substring(0, 4)}...` : "none",
                );
                console.log("Starting form submission process");

                // Process hobi data - Fix format to match API expectations
                const hobiData = {};
                if (this.selectedHobi.length > 0) {
                    // Group hobbies by category
                    for (const item of this.selectedHobi) {
                        if (!hobiData[item.kategori]) {
                            hobiData[item.kategori] = [];
                        }
                        // Ensure each hobby is a string value, not an object
                        hobiData[item.kategori].push(String(item.hobi));
                    }

                    for (const category of Object.keys(hobiData)) {
                        hobiData[category] = hobiData[category].join(", ");
                    }

                    console.log(
                        "Hobi processed:",
                        JSON.stringify(hobiData, null, 2),
                    );
                }

                const url = "/api/biodata/generus/";

                // Prepare FormData payload to support file uploads
                const formData = new FormData();

                // Add all form fields to FormData
                for (const key in this.formData) {
                    if (this.formData[key] !== null && this.formData[key] !== undefined && this.formData[key] !== "") {
                        formData.append(key, this.formData[key]);
                    }
                }

                // Add hobi data as JSON string
                formData.append("hobi", JSON.stringify(hobiData));

                // Add photo if selected
                if (this.selectedPhoto) {
                    formData.append("foto", this.selectedPhoto);
                    console.log("Photo added to form data:", this.selectedPhoto.name, this.selectedPhoto.size);
                }

                console.log("FormData prepared with photo support");

                // Headers with proper Authorization format (no Content-Type for FormData)
                const headers = {
                    Authorization: `ApiKey ${this.apiKey}`,
                };

                console.log("Headers to be sent:", headers);

                console.log("Using fetch with FormData payload");

                const response = await fetch(url, {
                    method: "POST",
                    mode: "cors",
                    headers: headers,
                    redirect: "manual",
                    body: formData,
                    credentials: "include", // Include credentials for CORS
                });

                console.log("Response status:", response.status);

                // Handle the response
                if (!response.ok) {
                    const errorText = await response.text();
                    let errorData;

                    try {
                        errorData = JSON.parse(errorText);
                        console.log(
                            "Error response from server:",
                            JSON.stringify(errorData, null, 2),
                        );
                    } catch (e) {
                        console.log("Error response (not JSON):", errorText);
                        errorData = { detail: errorText };
                    }

                    // Specific handling for authorization errors
                    if (response.status === 401) {
                        console.error(
                            "Authorization failed - API key may be invalid",
                        );
                        alert(
                            "Authorization failed. Please check if your API key is valid and try again.",
                        );
                    } else {
                        alert(
                            `Server error (${response.status}): ${errorData.detail || "Unknown error"}`,
                        );
                    }

                    throw new Error(
                        `Server error: ${response.status} - ${JSON.stringify(errorData)}`,
                    );
                }

                // Handle success
                console.log("Data submitted successfully!");
                this.showSuccess = true;
                this.showReview = false;
            } catch (error) {
                console.error("Error in submission process:", error);
                console.error("Error stack:", error.stack);

                // More specific error messages
                if (error.message.includes("Network error")) {
                    alert(
                        "Network error occurred. Please check your internet connection and try again.",
                    );
                } else if (error.message.includes("API key")) {
                    alert("API key error. Please check your access link.");
                } else {
                    alert(
                        "Terjadi kesalahan saat mengirim data. Silakan coba lagi.",
                    );
                }
            } finally {
                this.isSubmitting = false;
                console.log("=== SUBMISSION DEBUG END ===");
            }
        },

        // ...existing methods...

        resetForm() {
            // Reset all form fields
            this.formData = {
                nama_lengkap: "",
                nama_panggilan: "",
                jenis_kelamin: "",
                kelahiran_tempat: "",
                kelahiran_tanggal: "",
                alamat_tinggal: "",
                pendataan_tanggal: this.getCurrentDate(),
                sambung_desa: "",
                sambung_kelompok: "",
                hobi: "",
                sekolah_kelas: "",
                nomor_hape: "",
                nama_ayah: "",
                nama_ibu: "",
                status_ayah: "",
                status_ibu: "",
                nomor_hape_ayah: "",
                nomor_hape_ibu: "",
                daerah: this.daerahParam || "",
            };

            // Reset other state
            this.selectedHobi = [];
            this.selectedPhoto = null; // Reset photo selection
            this.showSuccess = false;
            this.showReview = false;
            this.isSubmitting = false;
        },
    },
    watch: {
        "formData.alamat_tinggal": (newVal) => {
            console.log(
                "biodata-generus detected alamat_tinggal change:",
                newVal,
            );
        },
    },
};
</script>

<style>
body {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
    line-height: 1;
    box-sizing: border-box;
    background-color: #ffffff;
    color: #2c4a3e;
    width: 100%;
}

.form-container {
    width: 500px;
    border-radius: 20px;
    background-color: #f9f9f9;
    box-shadow:
        0 10px 25px rgba(44, 74, 62, 0.2),
        0 6px 12px rgba(44, 74, 62, 0.15);
    box-sizing: border-box;
    text-align: center;
    margin: 10px auto;
    transition: opacity 0.3s ease-in-out;
    padding: 20px;
}

.hidden {
    opacity: 0;
    pointer-events: none;
    position: absolute;
    left: -9999px;
    visibility: hidden;
    display: none;
    z-index: -1;
}

.form-title {
    font-size: 22px;
    font-weight: bold;
    color: #2e5a35;
    margin-bottom: 10px;
}

.form-subtitle {
    font-size: 20px;
    color: #2e5a35;
    font-weight: bold;
    margin-bottom: 20px;
}

.section-title {
    font-size: 21px;
    font-weight: bold;
    color: #2e5a35;
    margin: 30px 20% 25px 20%;
    padding-bottom: 10px;
    border-bottom: 2px solid rgba(46, 90, 53, 0.3);
    text-align: center;
    align-items: center;
}

form {
    text-align: left;
}

.form-group {
    margin-bottom: 15px;
}

input,
select,
textarea {
    width: 90%;
    padding: 10px;
    margin-bottom: 15px;
    margin-left: 8%;
    margin-right: 5%;
    border: none;
    border-bottom: 2px solid #2e5a35;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    box-sizing: border-box;
    appearance: none;
    background-color: rgba(46, 90, 53, 0.001);
    color: #2c4a3e;
}

textarea {
    height: 100px;
    resize: vertical;
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: 2px solid #2e5a35;
    border-radius: 8px;
}

label {
    display: block;
    margin-bottom: 8px;
    color: #2c4a3e;
    font-weight: 650;
    font-size: 16px;
    text-align: left;
    padding-top: 10px;
}

input::placeholder,
textarea::placeholder {
    color: #2e5a35;
    opacity: 0.7;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-bottom: 2px solid #3d7a47;
    box-shadow: none;
    background-color: rgba(46, 90, 53, 0.05);
}

select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%232e5a35' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 40px;
}

button {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 20px;
    background-color: #2e5a35;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-sizing: border-box;
    margin-top: 20px;
    font-weight: 600;
}

button:hover {
    background-color: #3d7a47;
}

button.secondary {
    background-color: #ffffff;
    color: #2e5a35;
    border: 2px solid #2e5a35;
    margin-top: 10px;
}

button.secondary:hover {
    background-color: #f0f0f0;
}

.confirmation-message {
    font-size: 20px;
    font-weight: bold;
    color: #2e5a35;
    margin: 20px 0;
    line-height: 1.5;
}

/* Review Section Styles */
.review-data {
    text-align: left;
    padding: 10px;
    background-color: #ffffff;
    border-radius: 15px;
    margin: 20px 0;
}

.review-item {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(46, 90, 53, 0.1);
    font-size: 16px;
    line-height: 1.5;
}

.review-item:last-child {
    border-bottom: none;
}

.suggestions-container {
    position: relative;
    flex: 1;
}

.suggestions {
    position: absolute;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #2e5a35;
    border-radius: 10px;
    z-index: 10;
    margin-top: -10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    text-align: left;
}

.suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid rgba(46, 90, 53, 0.1);
}

.suggestion-item:hover {
    background-color: rgba(46, 90, 53, 0.1);
}

.review-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

@media (max-width: 520px) {
    .form-container {
        width: 95%;
        padding: 15px;
    }
}

/* Hobi Tag Styles */
.selected-hobi-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.hobi-tag {
    background-color: #e8f5e9;
    border: 1px solid #2e5a35;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 14px;
    color: #2c4a3e;
    display: flex;
    align-items: center;
}

.remove-hobi {
    margin-left: 6px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    color: #2e5a35;
    line-height: 1;
}

.remove-hobi:hover {
    color: #d32f2f;
}

.review-hobi-item {
    margin: 5px 0;
    padding-left: 10px;
    border-left: 2px solid rgba(46, 90, 53, 0.3);
}

.spaci {
    /* width: 95%; */
    margin: 20px 0 30px 0;
    padding: 0 5% 0 5%;
    border-bottom: 2px solid rgba(46, 90, 53, 0.1);
}
</style>
